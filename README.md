# Maverick Frontend #

Please use the [documentation](documentation) folder for details.

## WARNINGS ##

1.  For some reason, webpack isn't importing css. Until this is solved, please remain importing scss only.

## DO NOT UPDATE NPM ##

Please do not update NPM unless nessecary. Updates are made automatically before releases, so you may need to run 'npm install' if needed.


## Standards ##

* Use Absolute Path: src/path/to/component
* CSS/SCSS: Nested Media Queries.
* INDENTION: 4 Spaces.

## BRANCHING STRATEGY ##

We use release branches within production, master is rarely updated or maintained. Release branches are maintained with feature branches, and feature branches are maintained with working branches.

Testing (QA) is done in two stages, preflight and approach. Preflight sites use non-production data, where as approach sites do use production data. Only working branches may be commited to preflight, and only feature branches may be commited to approach. After a working branch passes preflight testing, merge it into the feature branch. After a feature branch passes QA on approach, it is ready to be merged into the next release branch. Remember to update feature branches though your working branches.

You can find out the latest release branch by viewing the source of a production site.

## FEATURE BRANCHES ##

If the feature branch does not exist, create one off the latest release branch in production. If the feature branch does exist, please keep it updated with the latest release. If a feature branch has already been merged into a release branch and hasn't been updated within a month, please delete it and recreate it in the future as needed.

Naming is based on the folder structure. Example, if you're working on the "Call To Action" (or CTA) module, the feature branch would be "modules/call-to-action". Very important, do not make any changes outside the scope of the folders worked. If you're working on the CTA module, only make changes within that module folder scope. If you need to make changes outside of the folder scope, use the other feature branch that needs to be worked. Example, if you need to make any changes to SCSS within the "src/scss" folder, use the "scss" branch.

## WORKING BRANCHES ##

To work on a feature branch, make sure it is rebased with the latest release branch on production and then create a working branch off the feature branch that includes your username (first part of your Imaginuity email address without the @imaginuity.com) like "jbannon/modules/call-to-action". Do not merge your working branch back into the feature branch until it has completely passed all preflight. If you need to use a more descriptive branch name (optional), use an underscore at the end with whatever else is needed to identifify what was worked on. An example would be "jbannon/modules/call-to-action_font-size".

Please update the Asana ticket(s) worked on with the working branch(es) that were used.

## MERGING INTO PREFLIGHT & APPROACH ##

Please merge your working branch (ie, jbannon/modules/call-to-action) into preflight and approach for testing, do not merge your working branch into the feature branch until it has pass preflight testing.

## CREATING A RELEASE BRANCH ##

Create a new branch off the latest production branch with the approved version number, example: release/1.1.17. If you haven't already, merge all the working branches that passed all stages of QA into their respective feature branches. Merge the feature branches into the new release branch. UPDATE: it is no longer necessary to rebuild or update the public/index.html file.

## Touching for testing ##
