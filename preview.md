#enabled post types
${url}wp-json/mvk_custom_rest/v1/mvk_types" | jq -c '.' > "./$domain/content_types.json"; #get all post types

#settings
${url}wp-json/mvk_custom_rest/v1/mvk_settings" | jq -c '.' > "./$domain/settings.json"; #get all settings

#main menu
${url}wp-json/mvk_custom_rest/v1/mvk_menus/main?per_page=1000" | jq -c '.' > "./$domain/nav.main.json"; #get main nav

#main menu 2
${url}wp-json/mvk_custom_rest/v1/mvk_menus/main_2?per_page=1000" | jq -c '.' > "./$domain/nav.main_2.json"; #get main nav

#utility menu
${url}wp-json/mvk_custom_rest/v1/mvk_menus/utility?per_page=1000" | jq -c '.' > "./$domain/nav.utility.json"; #get utility nav

#footer nav
${url}wp-json/mvk_custom_rest/v1/mvk_menus/footer?per_page=1000" | jq -c '.' > "./$domain/nav.footer.json"; #get footer nav

#footer utility
${url}wp-json/mvk_custom_rest/v1/mvk_menus/footer_utility?per_page=1000" | jq -c '.' > "./$domain/nav.footer_utility.json"; #get footer nav

#for each post type
${url}wp-json/wp/v2/$post/?per_page=1000&mvk_min" | jq 'map({ (.mvk_slug): . }) | add' | jq -c '.' > "./$domain/$post.json"; #get rest endpoint per domain and content type

#key
ofbF3oDWLpjLXxmnDyLJvfZbaFQPtHoFqdpoo88szJsh7


Post Types
https://goose.preflight.mypylot.io/wp-json/mvk_custom_rest/v1/mvk_types

Settings
https://goose.preflight.mypylot.io/wp-json/mvk_custom_rest/v1/mvk_settings

Main Menu
https://goose.preflight.mypylot.io/wp-json/mvk_custom_rest/v1/mvk_menus/main?per_page=1000

Main Menu 2
https://goose.preflight.mypylot.io/wp-json/mvk_custom_rest/v1/mvk_menus/main_2?per_page=1000

Utility Menu
https://goose.preflight.mypylot.io/wp-json/mvk_custom_rest/v1/mvk_menus/utility?per_page=1000

Footer Menu
https://goose.preflight.mypylot.io/wp-json/mvk_custom_rest/v1/mvk_menus/footer?per_page=1000

Pages
https://goose.preflight.mypylot.io/wp-json/wp/v2/pages/?per_page=1000&mvk_min


https://goose.preflight.mypylot.io/
https://goose.preflight.mypylot.io/
https://goose.preflight.mypylot.io/
https://goose.preflight.mypylot.io/
https://goose.preflight.mypylot.io/
https://goose.preflight.mypylot.io/
https://goose.preflight.mypylot.io/
