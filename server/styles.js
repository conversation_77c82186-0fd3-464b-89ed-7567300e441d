module.exports = function (data, req, domain, settingsResponse) {

    let fullUrl = 'https://' + req.get('host') + req.originalUrl;
    fullUrl = fullUrl.replace(`:3000`, '').replace('.dev.', '.preflight.').toLowerCase();
    let originalUrl = fullUrl ? new URL(fullUrl).pathname : false;
    originalUrl = originalUrl.toLowerCase();
    let hreflangs = `<link title="lang-tag" rel="alternate" hreflang="x-default" href="https://${domain}${originalUrl}" /><link title="lang-tag" rel="alternate" hreflang="en" href="https://${domain}${originalUrl}" />`;
    const languages = settingsResponse.data.translations;
    const webDetails = settingsResponse.data?.sei_settings?.spot?.webDetails;
    var headerFont = '';
    var bodyFont = '';
    var favicon = settingsResponse?.data?.branding?.favicon ? settingsResponse.data.branding.favicon.url : false;
    if (webDetails) {
        if (settingsResponse?.data?.design?.colors) {
            updateSettings(settingsResponse?.data?.design?.colors, webDetails, [
                { 'primary_color': 'primaryColor' },
                { 'secondary_color': 'secondaryColor' },
                { 'tertiary_color': 'tertiaryColor' },
                { 'background_color': 'backgroundColor' },
                { 'body_copy_color': 'bodyCopyColor' },
                { 'main_nav.bottom_mobile_bar_background_color': 'bottomMobileBarBackgroundColor' },
                { 'main_nav.bottom_mobile_bar_text_color': 'bottomMobileBarTextColor' },
                { 'main_nav.nav_bg_color': 'navigationBackgroundColor' },
                { 'main_nav.nav_mobile_bg_color': 'navigationMobileBackgroundColor' },
                { 'main_nav.nav_mobile_txt_color': 'navigationMobileTextColor' },
                { 'main_nav.nav_txt_color': 'navigationTextColor' },
                { 'main_nav.nav_txt_hv_color': 'navigationTextHoverColor' },
                { 'util_nav.ut_nav_bg_color': 'utilityNavigationBackgroundColor' },
                { 'util_nav.ut_nav_txt_color': 'utilityNavigationTextColor' },
                { 'util_nav.ut_nav_txt_hv_color': 'utilityNavigationTextHoverColor' }
            ]);
        };
        if (webDetails.favicon) {
            favicon = webDetails.favicon; 
        } 
    }
    if (languages) {
        Object.keys(languages)?.map((key) => {
            hreflangs += `<link title="lang-tag" rel="alternate" hreflang="${key}" href="https://${domain}${originalUrl}?lang=${key}" />`;
        });
    }
    if (settingsResponse?.data?.design?.fonts && settingsResponse?.data?.design?.fonts?.heading?.heading_google_font_link && !settingsResponse?.data?.design?.fonts?.heading_google_font_select) {
        headerFont = together(settingsResponse?.data?.design?.fonts?.heading?.heading_google_font_name, settingsResponse?.data?.design?.fonts?.heading?.heading_font_properties);
        hreflangs += `<link type="text/css" rel="stylesheet" href="${settingsResponse?.data?.design?.fonts?.heading?.heading_google_font_link}" />`;
    }
    if (settingsResponse?.data?.design?.fonts && settingsResponse?.data?.design?.fonts?.body?.body_google_font_link && !settingsResponse?.data?.design?.fonts?.body_google_font_select) {
        bodyFont = together(settingsResponse?.data?.design?.fonts?.body?.body_google_font_name, settingsResponse?.data?.design?.fonts?.body?.body_font_properties);
        hreflangs += `<link type="text/css" rel="stylesheet" href="${settingsResponse?.data?.design?.fonts?.body?.body_google_font_link}" />`;
    }
    // FAVICON
    if (favicon) {
        hreflangs += `<link rel="shortcut icon" href="${favicon}" />`;
    }
    // Layout: site max-width - offset 
    let restrictedWidth = Math.round(settingsResponse?.data?.mvk_theme_config?.layout?.site_max_width * (100 - (settingsResponse?.data?.mvk_theme_config?.layout?.restrict_max_width_offset ? settingsResponse?.data?.mvk_theme_config?.layout?.restrict_max_width_offset : 17)) / 100);
    var StylesString = '';
    if (headerFont) {
        StylesString += ` .h1, .h2, .h3, .h4, .h5, .h6, h1, h2, h3, h4, h5, h6 { font-family: ${headerFont}; font-weight: ${settingsResponse?.data?.design?.fonts?.heading?.heading_font_weight}; } `;

    }
    if (bodyFont) {
        StylesString += ` body { font-family:${bodyFont}; font-weight: ${settingsResponse?.data?.design?.fonts?.body?.body_font_weight}; } `;
    }
    StylesString += ` body { color: ${settingsResponse?.data?.design?.colors?.body_copy_color}; background-color:${settingsResponse?.data?.mvk_theme_config?.other?.body_background_color ? settingsResponse?.data?.mvk_theme_config?.other?.body_background_color : '#fff'}; } `;
    StylesString += ` a { color: ${settingsResponse?.data?.design?.colors?.secondary_color}; } `;
    StylesString += ` body .grid-container { max-width: ${settingsResponse?.data?.mvk_theme_config?.layout?.site_max_width ? settingsResponse?.data?.mvk_theme_config?.layout?.site_max_width : '75'}rem }`;
    StylesString += ` body .grid-container.restricted { max-width: ${restrictedWidth}rem }`;

    StylesString += ` .primary-txt { color: ${settingsResponse?.data?.design?.colors?.primary_color}; } `;
    StylesString += ` .secondary-txt { color: ${settingsResponse?.data?.design?.colors?.secondary_color}; } `;
    StylesString += ` .tertiary-txt { color: ${settingsResponse?.data?.design?.colors?.tertiary_color}; } `;
    StylesString += ` .background-txt { color: ${settingsResponse?.data?.design?.colors?.background_color}; } `;
    StylesString += ` .body-copy-txt { color: ${settingsResponse?.data?.design?.colors?.body_copy_color} !important;} `;
    StylesString += ` .white-txt {color: #ffffff !important; } `;

    StylesString += ` .primary-bg { background-color: ${settingsResponse?.data?.design?.colors?.primary_color}; } `;
    StylesString += ` .secondary-bg { background-color: ${settingsResponse?.data?.design?.colors?.secondary_color}; } `;
    StylesString += ` .tertiary-bg { background-color: ${settingsResponse?.data?.design?.colors?.tertiary_color}; } `;
    StylesString += ` .background-bg { background-color: ${settingsResponse?.data?.design?.colors?.background_color}; } `;
    StylesString += ` .body-copy-bg { background-color: ${settingsResponse?.data?.design?.colors?.body_copy_color}; } `;
    StylesString += ` .white-bg { background-color: #fff; } `;

    StylesString += ` .primary-fill { fill: ${settingsResponse?.data?.design?.colors?.primary_color}; } `;
    StylesString += ` .secondary-fill { fill: ${settingsResponse?.data?.design?.colors?.secondary_color}; } `;
    StylesString += ` .tertiary-fill { fill: ${settingsResponse?.data?.design?.colors?.tertiary_color}; } `;
    StylesString += ` .background-fill { fill: ${settingsResponse?.data?.design?.colors?.background_color}; } `;

    //border colors
    StylesString += ` .primary-border { border-color: ${settingsResponse?.data?.design?.colors?.primary_color}; } `;
    StylesString += ` .secondary-border { border-color: ${settingsResponse?.data?.design?.colors?.secondary_color}; } `;
    StylesString += ` .tertiary-border { border-color: ${settingsResponse?.data?.design?.colors?.tertiary_color}; } `;
    StylesString += ` .background-border { border-color: ${settingsResponse?.data?.design?.colors?.background_color}; } `;
    StylesString += ` .body-copy-border { border-color: ${settingsResponse?.data?.design?.colors?.body_copy_color}; } `;
    StylesString += ` .white-border { border-color: #fff !important; } `;

    // box shadow styles
    StylesString += ` .drop-shadow-light:hover { box-shadow: 5px 5px 15px rgba(0, 0, 0, .3) !important; }`;
    StylesString += ` .drop-shadow-medium:hover { box-shadow: 7px 7px 17px rgba(0, 0, 0, .5) !important; }`;
    StylesString += ` .drop-shadow-heavy:hover { box-shadow: 9px 9px 19px rgba(0, 0, 0, .7) !important; }`;

    // search and filter - no results styles
    StylesString += ` #noresults { margin: 9.375rem 0; display: flex; flex-flow: column nowrap; align-items: center; }`;
    StylesString += ` #noresults .title { width: fit-content; margin-bottom: 1.25rem; font-size: 1.625rem; }`;
    StylesString += ` #noresults .showfull { with: fit-content; text-decoration: underline; cursor: pointer; font-size: 1rem; }`;

    StylesString += ` #header .hover a, #header .hover div { color: ${settingsResponse?.data?.design?.colors?.main_nav?.nav_txt_color}; } `;
    StylesString += ` #header .hover:hover a, #header .hover:hover div { color: ${settingsResponse?.data?.design?.colors?.main_nav?.nav_txt_hv_color}; } `;
    StylesString += ` .button.tertiary { margin:0px; padding:10px; color: ${settingsResponse?.data?.design?.colors?.body_copy_color} !important; background-color: ${settingsResponse?.data?.design?.colors?.tertiary_color}; border-radius:${settingsResponse?.data?.mvk_theme_config?.other?.border_radius_size}px; } `;
    StylesString += ` .hero .slick-dots li.slick-active button:before { background-color: ${settingsResponse?.data?.design?.colors?.primary_color}; } `;
    StylesString += ` .image-carousel-hero__module .slick-slider.multiple-slides .slick-dots li.slick-active button:before { background-color: ${settingsResponse?.data?.design?.colors?.primary_color} !important; } `;

    StylesString += ` .mvk-responsive-video { position: relative; height: 0; }`;
    StylesString += ` .mvk-responsive-video iframe { position: absolute; top: 0; left: 0; width: 100%; height: 100%; }`;
    StylesString += ` .mvk-responsive-video.vertical { padding-bottom: 177.777%; }`;
    StylesString += ` .mvk-responsive-video.square { padding-bottom: 100%; }`;
    StylesString += ` .mvk-responsive-video.standard { padding-bottom: 75%; }`;
    StylesString += ` .mvk-responsive-video.widescreen { padding-bottom: 56.25%; }`;
    StylesString += ` .mvk-responsive-video.cinema { padding-bottom: 42.55%; }`;

    // Color Splashes
    const cs_color = settingsResponse?.data?.mvk_theme_config?.other?.cs_color;
    let hr_color = '#000';

    switch (cs_color) {
        case 'primary-bg':
            hr_color = settingsResponse?.data?.design?.colors?.primary_color;
            break;
        case 'secondary-bg':
            hr_color = settingsResponse?.data?.design?.colors?.secondary_color;
            break;
        case 'tertiary-bg':
            hr_color = settingsResponse?.data?.design?.colors?.tertiary_color;
            break;
        case 'custom':
            hr_color = settingsResponse?.data?.mvk_theme_config?.other?.cs_custom_color;
            break;
        default:
            break;
    }
    StylesString += ` .color-splash-hr { height: 5px; width: 100px; border: none; margin: 1rem 0; background-color: ${hr_color};}`;

    if (settingsResponse?.data?.mvk_theme_config?.other?.enable_color_splashes) {
        StylesString += ` .color-splash-header { height: 8px; background-color: ${hr_color}}`;
    }

    // Header Bottom Border
    if (settingsResponse?.data?.mvk_theme_config?.header?.enable_bottom_border) {
        StylesString += ` .header__bottom-border {height: ${settingsResponse?.data?.mvk_theme_config?.header?.border_height}; background-color: ${settingsResponse?.data?.mvk_theme_config?.header?.border_color}; width: 100%;}`;
    }

    // Footer Bottom Border
    if (settingsResponse?.data?.mvk_theme_config?.footer?.enable_footer_bottom_border) {
        StylesString += ` .footer__bottom-border {height: ${settingsResponse?.data?.mvk_theme_config?.footer?.footer_border_height}; background-color: ${settingsResponse?.data?.mvk_theme_config?.footer?.bottom_border_color}; width: 100%;}`;
    }

    // Separator Line Color - Content Area
    const option = settingsResponse?.data?.mvk_theme_config?.other?.separator_line_color;
    var lineColor = '';
    switch (option) {
        case 'primary_color':
            var lineColor = `${settingsResponse?.data?.design?.colors?.primary_color}, ${settingsResponse?.data?.design?.colors?.primary_color}`;
            break;
        case 'secondary_color':
            var lineColor = `${settingsResponse?.data?.design?.colors?.secondary_color}, ${settingsResponse?.data?.design?.colors?.secondary_color}`;
            break;
        case 'tertiary_color':
            var lineColor = `${settingsResponse?.data?.design?.colors?.tertiary_color}, ${settingsResponse?.data?.design?.colors?.tertiary_color}`;
            break;
        case 'background_color':
            var lineColor = `${settingsResponse?.data?.design?.colors?.background_color}, ${settingsResponse?.data?.design?.colors?.background_color}`;
            break;
        case 'gradient':
            var lineColor = `${settingsResponse?.data?.design?.colors?.gradient_color_1}, ${settingsResponse?.data?.design?.colors?.gradient_color_2}`;
            break;
        default:
            break;
    }

    StylesString += ` @media screen and (max-width: 639px) {
        .content-area .separator-lines:after {
            content: "";
            position: absolute;
            bottom: 0;
            left: 0;
            display: block;
            width: 100%;
            height: 2px;
            background: linear-gradient(to right, ${lineColor});
        } 
    }`;

    StylesString += ` @media screen and (min-width: 640px) {
        .content-area .separator-lines:after {
            content: "";
            position: absolute;
            top: 0;
            right: 0;
            display: block;
            width: 2px;
            height: 100%;
            background: linear-gradient(to bottom, ${lineColor});
        } 
    }`;

    StylesString += ` hr:not(.color-splash-hr) { background: linear-gradient(to right, ${lineColor}); height: 1px; border: transparent; }`;

    if (settingsResponse?.data?.mvk_theme_config?.layout?.mobile_content_margins) {
        StylesString += ` @media (max-width: 639px) {#modules-container .grid-container:not(.full) { padding-left: ${settingsResponse?.data?.mvk_theme_config?.layout?.mobile_content_margins}rem; padding-right: ${settingsResponse?.data?.mvk_theme_config?.layout?.mobile_content_margins}rem; }}`;
    }
    if (settingsResponse?.data?.mvk_theme_config?.layout?.tablet_content_margins) {
        StylesString += ` @media (min-width: 640px) {#modules-container .grid-container:not(.full) { padding-left: ${settingsResponse?.data?.mvk_theme_config?.layout?.tablet_content_margins}rem; padding-right: ${settingsResponse?.data?.mvk_theme_config?.layout?.tablet_content_margins}rem; }}`;
    }
    if (settingsResponse?.data?.design?.advanced?.font_customizations) {
        if (settingsResponse?.data?.design?.fonts?.heading_google_font_select || settingsResponse?.data?.design?.fonts?.body_google_font_select) {
            let fontUrl = `https://fonts.googleapis.com/css?family=`;

            if (settingsResponse?.data?.design?.fonts?.heading_google_font_select) {
                fontUrl += settingsResponse?.data?.design?.fonts?.heading_google_font_select.label;
                if (settingsResponse?.data?.design?.fonts?.heading_font_variants) {
                    let variants = '';
                    settingsResponse?.data?.design?.fonts?.heading_font_variants.forEach((variant, index, array) => {
                        variants += `${variant}${index !== array.length - 1 ? ',' : ''}`;
                    });
                    fontUrl += `:${variants}`;
                }
                headerFont = settingsResponse?.data?.design?.fonts?.heading_google_font_select.value;
                StylesString += `.h1, .h2, .h3, .h4, .h5, .h6, h1, h2, h3, h4, h5, h6 { font-family: ${settingsResponse?.data?.design?.fonts?.heading_google_font_select.value};}`
            }

            if (settingsResponse?.data?.design?.fonts?.body_google_font_select) {
                fontUrl += `|${settingsResponse?.data?.design?.fonts?.body_google_font_select.label}`;
                if (settingsResponse?.data?.design?.fonts?.body_font_variants) {
                    let variants = '';
                    settingsResponse?.data?.design?.fonts?.body_font_variants.forEach((variant, index, array) => {
                        variants += `${variant}${index !== array.length - 1 ? ',' : ''}`;
                    });
                    fontUrl += `:${variants}`;
                }
                bodyFont = settingsResponse?.data?.design?.fonts?.body_google_font_select.value;
                StylesString += `body { font-family: ${settingsResponse?.data?.design?.fonts?.body_google_font_select.value};}`
            }
            fontUrl += `&display=swap`;
            hreflangs += `<link type="text/css" rel="stylesheet" href="${fontUrl}" />`;
        }

        const fontStyles = settingsResponse?.data?.design?.advanced;
        StylesString += `#app #header #dropdown, #app #header #dropdown a, #app #header nav a, #app #header nav .menu-dropdown { font-family: ${fontStyles.nav?.font_family === 'heading' ? headerFont : bodyFont}; font-size: ${pxToRem(fontStyles.nav?.size_small)}rem; font-weight: ${fontStyles.nav?.weight}; letter-spacing: ${fontStyles.nav?.letter_spacing}px; }`;
        StylesString += `#app #head-utility, #app #head-utility a, #app #head-utility p { font-family: ${fontStyles.utility?.font_family === 'heading' ? headerFont : bodyFont}; font-size: ${pxToRem(fontStyles.utility?.size_small)}rem; font-weight: ${fontStyles.utility?.weight}; letter-spacing: ${fontStyles.utility?.letter_spacing}px; }`;
        StylesString += `#app h1 { font-size: ${pxToRem(fontStyles.h1?.size_small)}rem; font-weight: ${fontStyles.h1?.weight}; line-height: ${fontStyles.h1?.line_height}; letter-spacing: ${fontStyles.h1?.letter_spacing}px; }`;
        StylesString += `#app h2 { font-size: ${pxToRem(fontStyles.h2?.size_small)}rem; font-weight: ${fontStyles.h2?.weight}; line-height: ${fontStyles.h2?.line_height}; letter-spacing: ${fontStyles.h2?.letter_spacing}px; }`;
        StylesString += `#app h3 { font-size: ${pxToRem(fontStyles.h3?.size_small)}rem; font-weight: ${fontStyles.h3?.weight}; line-height: ${fontStyles.h3?.line_height}; letter-spacing: ${fontStyles.h3?.letter_spacing}px; }`;
        StylesString += `#app h4 { font-size: ${pxToRem(fontStyles.h4?.size_small)}rem; font-weight: ${fontStyles.h4?.weight}; line-height: ${fontStyles.h4?.line_height}; letter-spacing: ${fontStyles.h4?.letter_spacing}px; }`;
        StylesString += `#app h5 { font-size: ${pxToRem(fontStyles.h5?.size_small)}rem; font-weight: ${fontStyles.h5?.weight}; line-height: ${fontStyles.h5?.line_height}; letter-spacing: ${fontStyles.h5?.letter_spacing}px; }`;
        StylesString += `#app h6 { font-size: ${pxToRem(fontStyles.h6?.size_small)}rem; font-weight: ${fontStyles.h6?.weight}; line-height: ${fontStyles.h6?.line_height}; letter-spacing: ${fontStyles.h6?.letter_spacing}px; }`;
        StylesString += `#app a, #app button { font-size: ${pxToRem(fontStyles.links?.size_small)}rem; font-weight: ${fontStyles.links?.weight}; line-height: ${fontStyles.links?.line_height}; letter-spacing: ${fontStyles.links?.letter_spacing}px; }`;
        StylesString += `#app p, #app ol, #app ul { font-size: ${pxToRem(fontStyles.paragraph?.size_small)}rem; font-weight: ${fontStyles.paragraph?.weight}; line-height: ${fontStyles.paragraph?.line_height}; letter-spacing: ${fontStyles.paragraph?.letter_spacing}px; }`;
        StylesString += `#app .location-selector a, #app .location-selector p { font-size: ${fontPercentage(fontStyles?.paragraph?.size_small, 81)}rem; }`;
        // > 768px
        StylesString += `@media (min-width: 768px) { 
            #app h1 { font-size: ${pxToRem(fontStyles.h1?.size_large)}rem;}
            #app h2 { font-size: ${pxToRem(fontStyles.h2?.size_large)}rem;}
            #app h3 { font-size: ${pxToRem(fontStyles.h3?.size_large)}rem;}
            #app h4 { font-size: ${pxToRem(fontStyles.h4?.size_large)}rem;}
            #app h5 { font-size: ${pxToRem(fontStyles.h5?.size_large)}rem;}
            #app h6 { font-size: ${pxToRem(fontStyles.h6?.size_large)}rem;}
            #app a, #app button { font-size: ${pxToRem(fontStyles.links?.size_large)}rem;}
            #app p, #app ol, #app ul { font-size: ${pxToRem(fontStyles.paragraph?.size_large)}rem;}

        }`;
        // > 1200px 
        StylesString += `@media (min-width: 1200px) {
            #app #header #nav-expanded,
            #app #header #nav-expanded a,
            #app #header nav a,
            #app #header nav .menu-dropdown,
            #app #header #style-one #nav-expanded .nav-primary-items .nav-primary-item .nav-primary-item-main .nav-primary-item-main-title { font-family: ${fontStyles.nav?.font_family === 'heading' ? headerFont : bodyFont}; font-size: ${pxToRem(fontStyles.nav?.size_large)}rem; font-weight: ${fontStyles.nav?.weight} !important; letter-spacing: ${fontStyles.nav?.letter_spacing}px; }
            #app #head-utility, #app #head-utility a, #app #head-utility p { font-size: ${pxToRem(fontStyles.utility?.size_large)}rem;}
            #app .location-selector a, #app .location-selector p { font-size: ${fontPercentage(fontStyles?.paragraph?.size_large, 81)}rem;}
        }`;

    }
    // custom CSS - Super Config > Advanced
    StylesString += settingsResponse?.data?.design?.advanced?.custom_css ? settingsResponse?.data?.design?.advanced?.custom_css : '';
    // replace lang tag placeholder with and links
    data = data.replace('<link title="lang-tag" />', hreflangs);
    // replace styles placeholder with styles
    data = data.replace('<style id="ssr-styles"></style>', `<style id='ssr-styles'>${minifyCSS(StylesString)}</style>`);
    return data;

}

function together(font, props) {
    var stuff = `'${font}'`;
    if (props?.length > 0) {
        stuff = stuff.concat(', ', props.join());
    } else {
        stuff = stuff + ', sans-serif'
    }
    return stuff;
}

function pxToRem(value) {
    let rem = (value / 16);
    return rem;
}
function fontPercentage(fontSize, percentage) {
    let pecentSize = (parseInt(fontSize) * parseInt(percentage)) / 100;
    return pxToRem(pecentSize); 
}
function minifyCSS(css) {
    return css
        // Remove comments
        .replace(/\/\*[\s\S]*?\*\//g, '')
        // Remove newlines and excess spaces
        .replace(/\s+/g, ' ')
        // Remove space around symbols like { } : ; , > + ~
        .replace(/\s*([{}:;>,~])\s*/g, '$1')
        // Remove trailing semicolons before closing braces
        .replace(/;}/g, '}')
        // Trim leading/trailing spaces
        .trim();
}

function updateSettings(target, source, fields) {
    if (!target || !source) return;
    fields.forEach(field => {
        if (typeof field === 'string') {
            if (source && source[field] !== undefined && source[field] !== null) {
                target[field] = source[field];
            }
        } else if (typeof field === 'object') {
            const [targetPath, sourceField] = Object.entries(field)[0];
            const sourceValue = source?.[sourceField]; 
            const targetKeys = targetPath.split('.');
            let targetRef = target;
            for (let i = 0; i < targetKeys.length - 1; i++) {
                const key = targetKeys[i];
                if (!targetRef[key]) targetRef[key] = {};
                targetRef = targetRef[key];
            }
            const lastKey = targetKeys[targetKeys.length - 1];
            if (sourceValue !== undefined && sourceValue !== null) {
                targetRef[lastKey] = sourceValue;
            }
        }
    });
};