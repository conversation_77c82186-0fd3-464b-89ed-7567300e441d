import axios from 'axios';
import { URL } from 'url';

module.exports = function (res, req, base, domain, endpoint) {
    return API(base, domain).get(endpoint)
        .then(({ data }) => {
            const sortedRedirects = Object.values(data).sort((a, b) => a.position - b.position);

            for (const redirect of sortedRedirects) {
                if (!redirect.enabled) continue;

                const originalUrl = req.originalUrl;
                const parsedUrl = new URL(`http://${req.headers.host}${originalUrl}`);

                let stripped = parsedUrl.pathname.replace(/\/$/, '');
                let queryParams = parsedUrl.search;

                const matchData = redirect.match_data?.source || {};
                const redirectUrl = redirect.url.replace(/\/$/, '');

                if (matchData.flag_query === "ignore") {
                    queryParams = "";
                }

                if (redirect.regex) {
                    if (originalUrl.match(redirect.url)) {
                        console.log('Redirect:', redirect.action_code, redirect.action_data.url);
                        res.redirect(redirect.action_code, redirect.action_data.url);
                        return;
                    }
                } else {
                    if (matchData.flag_query === "exact" || matchData.flag_query === "exactorder") {
                        const pathPlusQuery = parsedUrl.pathname + parsedUrl.search;

                        if (pathPlusQuery === redirect.url) {
                            console.log('Redirect:', redirect.action_code, redirect.action_data.url);
                            res.redirect(redirect.action_code, redirect.action_data.url);
                            return;
                        }
                    }

                    else if (stripped === redirectUrl) {
                        let finalRedirectUrl = redirect.action_data.url;

                        if (matchData.flag_query === "pass" && queryParams) {
                            finalRedirectUrl += queryParams;
                        }

                        console.log('Redirect:', redirect.action_code, finalRedirectUrl);
                        res.redirect(redirect.action_code, finalRedirectUrl);
                        return;
                    }
                }
            }
        })
        .catch((e) => {
            console.log(`Redirects endpoint failed: ${e}`);
        });
};

export const API = (base, domain) => axios.create({
    baseURL: `http://${base}/mvk-api/v1/content/${domain}/`,
    headers: { 'Content-Type': 'application/json' },
    withCredentials: false
});
