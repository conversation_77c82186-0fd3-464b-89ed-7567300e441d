import axios from 'axios';
import express from 'express';
import cookieParser from 'cookie-parser';
import fs from 'fs';
import path from 'path';
const trailingSlash = require('trailing-slash');
const app = express();
const port = process.env.PORT || 3000;
app.use(cookieParser());

app.use(['^/$', '^/:slug/$', '^/:first/:slug/$', '^/:first/:second/:slug/$', '^/:first/:second/:third/:slug/$', '^/:first/:second/:third/:forth/:slug/$', '^/:first/:second/:third/:forth/:fifth/:slug/$'], async (req, res, next) => {
    fs.readFile(path.resolve('./public/index.html'), 'utf-8', async (err, data) => {
        if (err) {
            console.log(err)
            return res.status(500).send("Error reading index.html in server.js");
        }

        let domain = req.headers.host.replace(`:${port}`, '').replace('dev', 'preflight');
        domain = domain.replace('mypylot.dev.', 'www.');
        domain = domain.replace('mypylot.approach.', 'www.');
        domain = domain.replace('.approach', '');
        domain = domain.replace('approach.dev.', 'www.');
        domain = domain.replace('approach.', 'www.');
        domain = domain.replace('test', 'com');
        // use localhost:79 on server
        // let base = (domain.includes('preflight')) ? 'api.preflight.mypylot.io' : 'api.mypylot.io';
        let base = 'localhost:79';

        // check for translation code either in query string or cookie
        let transCode = '';
        let translation = req.query.lang ? req.query.lang : req.cookies.translation;
        if (translation && translation !== 'en' && (translation === 'fr' || translation === 'zh-hans')) {
            transCode = `.${translation}`;
        }
        console.log(domain);
        console.log(transCode);
        try {
            const settingsResponse = await API(base, domain).get(`settings${transCode}.json`);
            if(settingsResponse.headers['content-length'] < 10){
                data = data.replace('<meta content="__PLACEHOLDER__" />', `<!-- Error: PLT417 settings${transCode}.json -->`);
                return res.status(417).send(data);
            }

            const params = req.params;
            if (params && params.first !== 'media' && params.first !== 'javascript' && params.first !== 'dist' && params.slug !== 'robots.txt') {

                var paramData = {
                    slug: (params.slug) ? params.slug : false,
                    first: (params.first) ? params.first : false,
                    second: (params.second) ? params.second : false,
                    third: (params.third) ? params.third : false,
                    fourth: (params.fourth) ? params.fourth : false,
                    fifth: (params.fifth) ? params.fifth : false
                };

                let isSubsite = false;
                let subsite = params.first ? params.first : params.slug;
                let pagePath = params.slug ? params.slug : settingsResponse.data.front_page;
                // Create hreflang tags
                data = data.replace('<html lang="en">', `<html lang="${translation ? translation : 'en'}">`);
                data = require('./styles')(data, req, domain, settingsResponse);
                // check redirects - no trans code
                await require('./redirects')(res, req, base, domain, `redirects.json`);
                // get content
                require('./getContent')(data, res, req, base, domain, paramData, params, isSubsite, subsite, pagePath, transCode, settingsResponse);
                // }
            } else {
                next();
            }
        } catch (error) {
            console.log(`settings endpoint has failed`);
            data = data.replace('<meta content="__PLACEHOLDER__" />', '<!-- SETTINGS FAIL -->');
            next();
        }
    });
});


app.use(express.static(path.resolve(__dirname, '../public')));
// add trailing slash - 301 redirect
app.use(trailingSlash({ slash: true, status: 301 }));

app.get('*', function (request, response) {
    const filePath = path.resolve(__dirname, '../public', 'index.html');
    return response.sendFile(filePath);
});

app.listen(port, () => console.log(`Listening on port ${port}`));

export const API = (base, domain) => axios.create({
    baseURL: `http://${base}/mvk-api/v1/content/${domain}/`,
    withCredentials: false,
    headers: {
        'Content-Type': 'application/json'
    }
});
