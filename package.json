{"name": "Pylot-Frontend", "version": "1.120", "description": "React front-end for <PERSON><PERSON><PERSON>.", "license": "UNLICENSED", "author": "Imaginuity", "repository": {"type": "git", "url": "https://git02.calise.partners/maverick/frontend/maverick-frontend.git"}, "scripts": {"build": "webpack --mode production", "start": "webpack --mode production --watch", "local": "NODE_ENV=local webpack-dev-server --hot --inline --host 127.0.0.1 --progress --watch-poll --history-api-fallback --content-base ./dist", "dev": "webpack-dev-server --config webpack.dev.js", "test": "echo \"Error: no test specified\" && exit 1", "stop": "taskkill -F -IM node.exe", "server": "npm run build&&node server/index.js", "pm2-start": "pm2 start server/index.js", "nodemon": "nodemon server/index.js", "ssl-proxy": "local-ssl-proxy --source 443 --target 3000"}, "dependencies": {"@babel/compat-data": "^7.25.2", "@babel/core": "^7.25.2", "@babel/plugin-proposal-class-properties": "^7.18.6", "@babel/plugin-proposal-decorators": "^7.24.7", "@babel/plugin-proposal-private-methods": "^7.18.6", "@babel/plugin-proposal-private-property-in-object": "^7.21.11", "@babel/plugin-transform-private-methods": "^7.24.7", "@babel/plugin-transform-private-property-in-object": "^7.24.7", "@babel/plugin-transform-runtime": "^7.24.7", "@babel/preset-env": "^7.25.3", "@babel/preset-react": "^7.24.7", "@babel/register": "^7.24.6", "@datadog/browser-rum": "^5.23.3", "@fortawesome/fontawesome-free-brands": "^5.0.13", "@fortawesome/fontawesome-svg-core": "^6.6.0", "@fortawesome/free-brands-svg-icons": "^6.6.0", "@fortawesome/free-regular-svg-icons": "^6.6.0", "@fortawesome/free-solid-svg-icons": "^6.6.0", "@fortawesome/react-fontawesome": "^0.2.2", "@fullcalendar/core": "^6.1.15", "@fullcalendar/daygrid": "^6.1.15", "@fullcalendar/react": "^6.1.15", "@googlemaps/markerclusterer": "^2.5.3", "@ubilabs/google-maps-react-hooks": "^2.0.2", "acorn": "^8.7.1", "add-to-calendar-button-react": "^2.6.18", "amazon-cognito-identity-js": "^5.2.10", "autoprefixer": "^10.4.13", "aws-sdk": "^2.1232.0", "axios": "^1.6.2", "babel-loader": "^9.1.2", "base-64": "^1.0.0", "clean-webpack-plugin": "^4.0.0", "cookie-parser": "^1.4.6", "core-js": "^3.37.1", "countup.js": "^2.8.0", "crypto-browserify": "^3.10.0", "css-loader": "^6.7.3", "express": "^4.19.2", "file-loader": "^6.2.0", "foundation-sites": "^6.7.5", "google-map-react": "^2.2.1", "html-entities": "^2.5.2", "html-react-parser": "^5.1.12", "html-webpack-plugin": "^5.6.0", "http-proxy-middleware": "^3.0.3", "ignore-styles": "^5.0.1", "postcss": "^8.4.40", "postscribe": "^2.0.8", "react": "^18.3.1", "react-async-script": "^1.2.0", "react-cookie-consent": "^9.0.0", "react-datepicker": "^7.3.0", "react-device-detect": "^2.2.3", "react-dom": "^18.3.1", "react-helmet-async": "^2.0.5", "react-intersection-observer": "^9.13.0", "react-paginate": "^8.2.0", "react-parallax": "^3.5.1", "react-router": "^6.25.1", "react-router-dom": "^6.25.1", "react-select": "^5.8.0", "react-slick": "^0.30.2", "sass": "^1.77.8", "sass-loader": "^13.2.0", "slick-carousel": "^1.8.1", "source-map-loader": "^4.0.1", "style-loader": "^3.3.1", "styled-components": "^6.1.12", "trailing-slash": "^2.0.2", "url-loader": "^4.1.1", "webpack": "^5.93.0", "webpack-cli": "^5.1.4", "webpack-dev-server": "^5.0.4"}, "devDependencies": {"local-ssl-proxy": "^2.0.5", "nodemon": "^3.1.9", "react-google-recaptcha": "^3.1.0"}}