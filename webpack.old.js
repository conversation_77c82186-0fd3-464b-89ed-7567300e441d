var webpack = require('webpack');
var path = require('path');
const { CleanWebpackPlugin } = require('clean-webpack-plugin');
const HtmlWebpackPlugin = require('html-webpack-plugin');

var parentDir = path.join(__dirname, '../');

module.exports = (env) => {
    env = (!env) ? {} : env;

    return {
        mode: (env.mode) ? 'development' : 'production',
        entry: {
            start: './src/start.js'
        },
        plugins: [
            new CleanWebpackPlugin(),
            new HtmlWebpackPlugin({
                title: 'Pylot',
                filename: './index.html',
                inject: 'head'
            })
        ],
        optimization: {
            minimize: true
        },
        output: {
            path: __dirname + '/public/dist',
            publicPath: '/dist/',
            filename: '[name].js',
            chunkFilename: '[chunkhash].js'
        },
        devServer: {
            contentBase: path.join(__dirname, 'dist'),
            // port: 3000,
            // writeToDisk: true,
            // historyApiFallback: true,
            // contentBase: path.join(__dirname, 'dist'),
            // contentBasePublicPath: 'http://localhost:3000/',
            // publicPath: 'http://localhost:3000/',
            // watchContentBase: true,
            // allowedHosts: ['maverick-frontend.imag-dev.com'],
        },
        module: {
            rules: [{
                test: /\.(js|jsx)$/,
                exclude: /node_modules/,
                use: {
                    loader: 'babel-loader',
                    options: {
                        cacheDirectory: true,
                        plugins: [
                            ['@babel/plugin-proposal-decorators', {
                                'legacy': true
                            }],
                            ["@babel/plugin-proposal-class-properties", {
                                "loose": true
                            }]
                        ],
                        presets: ['@babel/preset-env']
                    }
                }
            }, {
                test: /\.css$/,
                loaders: ["style-loader", "css-loader"]
            },
            {
                test: /\.scss$/,
                enforce: 'pre',
                use: ["style-loader", "css-loader", "sass-loader", "source-map-loader"]
            },
            {
                test: /\.png$/,
                loader: "url-loader"
            },
            {
                test: /\.jpg$/,
                loader: "file-loader"
            },
            {
                test: /\.gif$/,
                loader: "file-loader"
            },
            {
                test: /\.svg$/,
                loader: "file-loader"
            },
            {
                test: /\.ico$/,
                loader: "file-loader"
            },
            {
                test: /\.woff($|\?)|\.woff2($|\?)|\.ttf($|\?)|\.eot($|\?)|\.svg($|\?)/,
                loader: 'url-loader'
            },
            {
                test: /\.svg(\?v=\d+\.\d+\.\d+)?$/,
                loader: "url-loader?limit=10000&mimetype=image/svg+xml"
            }
        ]
        },
        resolve: {
            alias: {
                src: path.resolve(__dirname + '/src')
            }
        },
        watch: (env.watch) ? true : false
    }
}
