Important Reminders:
	• Never merge the stage (preflight) branch into qa (approach) or any of the other branches - the stage (preflight) branch will likely have many unapproved changes on it
	• Never merge the qa (approach) branch into release or other branches - this could have unapproved items on it still
	• If you have questions, always reference our Git process flow chart

Dev/Stage (preflight) process:
	• Create new feature and/or bug branches off of master - *please follow branch naming convention outlined by <PERSON><PERSON>
	• Work on new branch locally
	• Add, commit, push to said branch
	• Merge new branch into stage (preflight) branch to test on stage (preflight) sites
	• fix merge conflicts (if any)
	• npm start to build the chunks
	• Add, commit, push to see changes on stage (preflight) sites - (https://{domain}.preflight.mypylot.com/)
	
Feature/bug branches to qa (approach) branch process:
	• Checkout qa (approach)
	• Merge all branches into qa (approach)
	• Fix merge conflicts
	• Npm start to rebuild
	• Add, commit, push
	• Message Boggs or Bosley to pull qa (approach) to see the changes on the qa (approach) sites - (https://{domain}.approach.mypylot.com/)

Hotfix branch process:
	• If doing a hotfix from a previous release
	• Branch off of the release branch that has that bug in it
	• Work on task and send the hotfix through our normal git process
	• Once approved - Merge changes into new release

Release process:
	• Create branch from the most up to date release branch and increment the new branch version accordingly
	• Merge the feature and/or bug branches that are ready for release into the new release branch
	• Fix merge conflicts
    • Npm run start to build chunks and then commit the result