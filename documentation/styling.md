# Styling Classes #

These classes apply custom styling affects to elements used on.

## Text Colors ##

* 'primary-txt' applies the primary color to fonts.
* 'secondary-txt' applies the secondary color to fonts.
* 'tertiary-txt' applies the tertiary color to fonts.
* 'background-txt' applies the background color to fonts.
* 'body-copy-txt' applies the body copy color to fonts.
* 'white' applies a white text color to fonts.
* 'black' applies a black text color to fonts.

## Background Colors ##

* 'primary-bg' applies the primary color to backgrounds.
* 'secondary-bg' applies the secondary color to backgrounds.
* 'tertiary-bg' applies the tertiary color to backgrounds.
* 'background-bg' applies the background color to backgrounds.
* 'body-copy-bg' applies the body copy color to backgrounds.

## Text Transformations ##

* 'capitalize', or 'cap', will capitalize text applied to.
* 'underline' will underline text applied to.
* 'uppercase', or 'upper', will uppercase all text applied to.

## Padding ##

* 'p-10' applies universal padding of 10 pixels.
* 'p-20' applies universal padding of 20 pixels.

## Padding, Horizontal ##

* 'ph-20' applies left and right padding of 20 pixels.
* 'ph-25' applies left and right padding of 25 pixels.
* 'ph-30' applies left and right padding of 30 pixels.

## Padding, Vertical ##

* 'pv-10' applies top and bottom padding of 10 pixels.

## Border Radius ##

* 'br-5' applies universal border radius of 5 pixels.
* 'br-10' applies universal border radius of 10 pixels.