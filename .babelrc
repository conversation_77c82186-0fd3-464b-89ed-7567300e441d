{"presets": [["@babel/preset-env", {"useBuiltIns": "entry"}], "@babel/preset-react"], "plugins": [["@babel/plugin-proposal-decorators", {"legacy": true}], ["@babel/plugin-proposal-class-properties", {"loose": true}], ["@babel/plugin-transform-runtime", {"loose": true}], ["@babel/plugin-proposal-private-property-in-object", {"loose": true}], ["@babel/plugin-transform-spread", {"loose": true}], ["@babel/plugin-proposal-private-methods", {"loose": true}]]}