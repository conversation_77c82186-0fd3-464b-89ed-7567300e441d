<link rel="stylesheet" href="https://app.propertyware.com/pw/website/widgets/pw_widgets_v2.css" type="text/css" />
<link rel="stylesheet" href="https://app.propertyware.com/pw/website/widgets/pw_widgets_responsive.css"
    type="text/css" />
<script type='text/javascript'
    src='https://maps.googleapis.com/maps/api/js?sensor=false&amp;key=AIzaSyBOjL2m6Z30FkTZPwEq7Ms6B9xQ9PI8B3g'></script>
<script type='text/javascript'> var googleMapsAPIVersion = 3;</script>
<script type='text/javascript' src='https://app.propertyware.com/pw/website/widgets/config.jsp?wid=610893824'></script>
<script type='text/javascript'
    src='https://app.propertyware.com/pw/website/widgets/pw_widgets_v2.js?sid=604241920'></script>

<script type='text/javascript'>
    var listingWidgetForSaleMode = true;
    var listingWidgetDefaultMapVisible = true;
</script>
<script>
    /* Read a page's GET URL variables and return them as an associative array. */
    function getUrlVars() {
        var vars = [], hash;
        var hashes = window.location.href.slice(window.location.href.indexOf('?') + 1).split('&');
        for (var i = 0; i < hashes.length; i++) {
            hash = hashes[i].split('=');
            vars.push(hash[0]);
            vars[hash[0]] = hash[1];
        }
        return vars;
    }

    var isEnterpriseWidget = getUrlVars()["isEnterpriseWidget"];

    var scriptLink;
    if (isEnterpriseWidget == "true") {
            scriptLink = '<script type="text/javascript" src="https://app.propertyware.com/pw/website/widgets/pw_enterprise_listing_widget.js"><\/script>';
        } else {
            scriptLink = "<script type='text/javascript' src='https://app.propertyware.com/pw/website/widgets/pw_listing_widget.js?v=3'><\/script>";
            scriptLink += "<script type='text/javascript' src='https://app.propertyware.com/pw/javascript/underscore/underscore.js'><\/script>";
            scriptLink += "<script type='text/javascript' src='https://app.propertyware.com/pw/javascript/propertyware/propertyware.js'><\/script>";
            scriptLink += "<script type='text/javascript' src='https://app.propertyware.com/pw/javascript/propertyware/ui/propertyware-ui.js'><\/script>";
        }
document.write( scriptLink );

</script>
<div id="pw_listing_widget">
    <div id="pw_listing_widget_search_div" class="ui-widget ui-widget-content ui-corner-all">
        <form id="pw_listing_widget_searchForm" name="searchRentals" action="" method="get">
            <input type="hidden" id="sortField" name="sort" value="" />
            <input type="hidden" id="pw_listing_widget_searchFormSelectedTab" name="selectedTab"
                value="pw_listing_widget_tabs_map" size="100" />
            <table id="searchForm" border="0" cellpadding="0" cellspacing="0">
                <tbody>
                    <tr>
                        <td>
                            <table cellspacing="2">
                                <tbody>
                                    <tr id="pSearchFieldLabels">
                                        <th colspan="3">Location / Price Range</th>
                                        <th class="min_max" colspan="2" align="center">Min / Max</th>
                                    </tr>
                                    <tr id="pSearchInputFields">
                                        <td colspan="3"><input id="addrField" onfocus="value='';" name="addr"
                                                style="width:98%;" type="text"
                                                placeholder="City & State, Zip, Address or Neighborhood"></td>
                                        <td align="center" colspan="2">
                                            $ <input id="minField" onfocus="value='';" name="min" value="" type="text"
                                                placeholder="Min"> to $ <input id="maxField" onfocus="value='';"
                                                name="max" value="" type="text" placeholder="Max">
                                        </td>
                                    </tr>
                                    <tr>
                                        <th colspan="3" id="pSearchTypeLabel">Type Available / Beds &amp; Baths</th>
                                        <th colspan="2">&nbsp;</th>
                                    </tr>
                                    <tr>
                                        <td colspan="3" id="typeFieldTd">
                                            <select id="typeField" name="type" width="10px;">

                                            </select>

                                            <select id="bedsField" name="beds">
                                                <option value="">1+</option>
                                                <option value="2">2+</option>
                                                <option value="3">3+</option>
                                                <option value="4">4+</option>
                                                <option value="5">5+</option>
                                                <option value="6">6+</option>
                                            </select>

                                            <select id="bathsField" name="baths">
                                                <option value="">1+</option>
                                                <option value="2">2+</option>
                                                <option value="3">3+</option>
                                                <option value="4">4+</option>
                                            </select>
                                        </td>
                                        <td class="type_date" colspan="2">
                                            <input id="availableAnyDateRadio" type="radio" name="anyDate" checked="true"
                                                value="true" /> Any Date
                                            <input id="availableSpecificDateRadio" type="radio" name="specificDate"
                                                value="true" /> Specific Date<span class="date_devider"><br /></span>
                                            <input id="availableDatePicker" type="text" name="availableDate"
                                                disabled="disabled" value="" />
                                            <script language="javascript">
        function toggleAvailableDateRadios(event) {

            var unselected = event.target.id === 'availableSpecificDateRadio' ? 'availableAnyDateRadio' : 'availableSpecificDateRadio';
            var selected = event.target.id === 'availableSpecificDateRadio' ? 'availableSpecificDateRadio' : 'availableAnyDateRadio';
            if (event.target.checked) {
                jQuery('#' + unselected).removeAttr('checked');
                var datePicker = jQuery('#availableDatePicker');
                if (unselected === 'availableSpecificDateRadio') {
                    datePicker.attr('disabled', 'disabled');
                    datePicker.val("");
                } else {
                    datePicker.removeAttr('disabled');
                }
            }
        }

        var parametersMap = getUrlVars();
        if (!parametersMap.anyDate && parametersMap.specificDate) {
            jQuery('#availableDatePicker').val(decodeURIComponent(parametersMap.availableDate));
            jQuery('#availableDatePicker').removeAttr('disabled');
            jQuery('#availableAnyDateRadio').removeAttr('checked');
            jQuery('#availableSpecificDateRadio').attr("checked", "true");
        }

        jQuery('#availableDatePicker').datepicker();
        jQuery('#availableSpecificDateRadio').change(toggleAvailableDateRadios);
        jQuery('#availableAnyDateRadio').change(toggleAvailableDateRadios);
                                            </script>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </td>
                    </tr>
                    <tr>
                        <th id="moreSearchOptionsTd">
                            <span id="moreSearchOptsSpan">More Search Options</span><br style="clear: both;">
                        </th>
                    </tr>
                    <tr id="amenititesTr">
                        <td>
                            <div id="moreOptsRow" style="display:none">
                                <div id="pw_listing_widget_search_leasing_contacts_div">
                                    <span style="font-weight: bold;">Leasing Contacts</span><br>
                                    <ul id="pw_listing_widget_search_leasing_contacts_list"></ul>
                                </div>

                                <br style="clear: both;">
                                <br style="clear: both;">

                                <div id="pw_listing_widget_search_amenities_div">
                                    <span style="font-weight: bold;">Amenities</span><br>
                                    <ul id="pw_listing_widget_search_amenities_list"></ul>
                                </div>
                                <br style="clear: both;">
                            </div>
                            <br style="clear: both;">
                        </td>
                    </tr>
                    <tr>
                        <td id="pSeachButtonTd"><input id="pSeachButton" value="Search" class="btn primary"
                                type="submit"></td>
                    </tr>
                </tbody>
            </table>
        </form>
    </div>

    <br />

    <div id="pw_listing_widget_tabs">
        <ul>
            <li><a id="pw_listing_widget_tabs_map_link" rel="history" href="#pw_listing_widget_tabs_map">Map</a></li>
            <li><a id="pw_listing_widget_tabs_list_link" rel="history" href="#pw_listing_widget_tabs_list">List</a></li>
            <li><a id="pw_listing_widget_tabs_detail_link" rel="history"
                    href="#pw_listing_widget_tabs_detail">Detail</a></li>
        </ul>

        <!-- Google maps tab go here -->
        <div id="pw_listing_widget_tabs_map">
            <div style="text-align: right">
                <a class="prevNav" href="javascript:showPreviousOnMap()"><img
                        src="https://app.propertyware.com/pw/website/widgets/images/pw_listing_widget_tabs_detail_detailImageLeft.png"
                        alt="" border="0" align="top" /><span class="desk_nav">Prev</span>
                    <div class="mob_nav">&#xf104;</div>
                </a><span class="devider">|</span>
                <a class="nextNav" href="javascript:showNextOnMap()"><span class="desk_nav">Next</span> <img
                        src="https://app.propertyware.com/pw/website/widgets/images/pw_listing_widget_tabs_detail_detailImageRight.png"
                        alt="" border="0" align="top" />
                    <div class="mob_nav">&#xf105;</div>
                </a>
            </div>
            <h3><span id="pw_listing_widget_tabs_map_number_listings"></span>&#160;Available Listings</h3>
            <p>Filter the results shown on the map by entering your search criteria above.</p>
            <br />
            <div class="pw_listing_widget_content_section">
                <div class="pw_listing_widget_content_bar">
                    <a id="pw_listing_widget_tabs_map_BrochureLink"
                        href="javascript:viewListingBrochure()"><span>Brochure</span></a>
                    <a id="pw_listing_widget_tabs_map_NotifyMe"
                        href="javascript:showWaitListform(false,true)"><span>Notify Me</span></a>
                </div>
                <div id="pw_listing_widget_tabs_main_map" style="width: 100%; height: 350px"></div>
            </div>
        </div>

        <!-- Listing goes here -->
        <div id="pw_listing_widget_tabs_list">
            <h3><span id="pw_listing_widget_tabs_list_number_listings"></span>&#160;Available Listings</h3>
            <p>Filter the results shown on the map by entering your search criteria above.</p>
            <br />
            <div class="pw_listing_widget_content_section">
                <div class="pw_listing_widget_content_bar">
                    <table style="width:100%; font-size: 1em">
                        <tr>
                            <td style="margin: 0; padding:0">
                                <a id="pw_listing_widget_tabs_list_BrochureLink"
                                    href="javascript:viewListingBrochure()"><span>Brochure</span></a>
                                <a id="pw_listing_widget_tabs_list_NotifyMe"
                                    href="javascript:showWaitListform(false,true)"><span>Notify Me</span></a>
                            </td>
                            <td style="margin: 0; padding:0; text-align: right">
                                <form action="" name="sortForm" style="margin:0; padding:0">
                                    <select id="sortSelect" name="sort">
                                        <option selected="true" value="">Sort:</option>
                                        <option value="pHl">Price (high to low)</option>
                                        <option value="pLh">Price (low to high)</option>
                                        <option value="availableSooner">Available (soonest)</option>
                                        <option value="availableLater">Available (latest)</option>
                                        <option value="sqHl">SqFt (high to low)</option>
                                        <option value="sqLh">SqFt (low to high)</option>
                                        <option value="pSqHl">Price/SqFt (high to low)</option>
                                        <option value="pSqlH">Price/SqFt (low to high)</option>
                                        <option value="bedHl">Bedrooms (high to low)</option>
                                        <option value="bedLh">Bedrooms (low to high)</option>
                                        <option value="baHl">Bathrooms (high to low)</option>
                                        <option value="baLh">Bathrooms (low to high)</option>
                                    </select>
                                </form>
                            </td>
                        </tr>
                    </table>
                </div>
                <ul id="pw_listing_widget_tabs_list_ul"></ul>
            </div>
        </div>

        <!-- Unit Detail goes here -->
        <div id="pw_listing_widget_tabs_detail">
            <table class="detailsNavMain" width="100%">
                <tr>
                    <td>
                        <div id="pw_listing_widget_tabs_detail_back_to_building">
                            <a href="javascript:goBackToBuilding()"><img
                                    src="https://app.propertyware.com/pw/website/widgets/images/pw_listing_widget_tabs_detail_detailImageLeft.png"
                                    alt="" border="0" align="top" />
                                <div><span class="mob_nav">&#xf104;</span>Back to Building</div>
                            </a>
                        </div>
                    </td>
                    <td class="detailsNav" style="text-align: right;">
                        <a class="prevNav" href="javascript:gotoPreviousBuilding()"><img
                                src="https://app.propertyware.com/pw/website/widgets/images/pw_listing_widget_tabs_detail_detailImageLeft.png"
                                alt="" border="0" align="top" /><span class="desk_nav">Prev</span>
                            <div class="mob_nav">&#xf104;</div>
                        </a> <span class="devider">|</span>
                        <a class="nextNav" href="javascript:gotoNextBuilding()"><span class="desk_nav">Next</span><img
                                src="https://app.propertyware.com/pw/website/widgets/images/pw_listing_widget_tabs_detail_detailImageRight.png"
                                alt="" border="0" align="top" />
                            <div class="mob_nav">&#xf105;</div>
                        </a>
                    </td>
                </tr>
            </table>

            <div id="pw_listing_widget_tabs_detail_container" style="display: none">
                <div>
                    <div>
                        <h3 id="pw_listing_widget_tabs_detail_posting_title"></h3>
                        <p id="pw_listing_widget_tabs_detail_address"></p>
                    </div>
                </div>

                <br />

                <div class="pw_listing_widget_content_section">
                    <div class="pw_listing_widget_content_bar">
                        <table style="width:100%; font-size: 1em" border="0">
                            <tbody>
                                <tr>
                                    <td>
                                        <div>
                                            <a id="pw_listing_widget_tabs_detail_detailBrochureLink"
                                                href="javascript:viewDetailBrochure()">Brochure</a>
                                            <a id="pw_listing_widget_tabs_detail_detailMapsAndDirectiosLink"
                                                href="javascript:getDirections()">Directions</a>
                                            <a id="pw_listing_widget_tabs_detail_addThisAnchor"
                                                href="https://www.addthis.com/bookmark.php?v=250"
                                                onmouseover="return addthis_open(this, '', 'https://webreq.propertyware.com/pw/website/widgets/addthis/'+listingWidgetCurrentBuildingOrUnit.id, 'Property info');"
                                                onmouseout="addthis_close();" onclick="return addthis_sendto();"><img
                                                    align="bottom"
                                                    src="https://s7.addthis.com/static/btn/lg-share-en.gif"
                                                    alt="Bookmark and Share" style="border: 0 none ;" width="125"
                                                    height="16" /></a>
                                            <script type="text/javascript"
                                                src="https://s7.addthis.com/js/250/addthis_widget.js?pub=xa-4a2557820e2be226"></script>
                                        </div>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div id="pw_listing_widget_tabs_detail_top_section">
                        <table width="100%">
                            <tr>
                                <td valign="top" style="width: 200px; text-align: center; ">
                                    <div id="pw_listing_widget_tabs_detail_image_browser">
                                        <div id="pw_listing_widget_tabs_detail_imageContainer"><img
                                                id="pw_listing_widget_tabs_detail_image"
                                                src="https://app.propertyware.com/pw/website/widgets/images/pw_listing_widget_no_photo.png"
                                                alt="Unit Image" width="181" height="181"></div>
                                        <div id="pw_listing_widget_tabs_detail_imageControls">
                                            <table width="100%">
                                                <tr>
                                                    <td>
                                                        <a class="prevNav"
                                                            id="pw_listing_widget_tabs_detail_detailImageLeft"><img
                                                                src="https://app.propertyware.com/pw/website/widgets/images/pw_listing_widget_tabs_detail_detailImageLeft.png"
                                                                alt="" border="0" />
                                                            <div class="mob_nav">&#xf104;</div>
                                                        </a>
                                                        <a id="pw_listing_widget_tabs_detail_detailImageCounter"><img
                                                                src="https://app.propertyware.com/pw/website/widgets/images/pw_listing_widget_tabs_detail_detailImageCounter.png"
                                                                alt="" border="0" /></a>
                                                        <a class="nextNav"
                                                            id="pw_listing_widget_tabs_detail_detailImageRight"><img
                                                                src="https://app.propertyware.com/pw/website/widgets/images/pw_listing_widget_tabs_detail_detailImageRight.png"
                                                                alt="" border="0" />
                                                            <div class="mob_nav">&#xf105;</div>
                                                        </a>
                                                    </td>
                                                </tr>
                                            </table>
                                        </div>
                                    </div>
                                </td>
                                <td valign="top">
                                    <h3 id="pw_listing_widget_tabs_detail_price"></h3>
                                    <table id="pw_listing_widget_tabs_detail_summary" width="100%">
                                        <tbody>
                                            <tr id="pw_listing_widget_tabs_detail_available_row">
                                                <th scope="row">Available:</th>
                                                <td id="pw_listing_widget_tabs_detail_available"></td>
                                            </tr>
                                            <tr>
                                                <th scope="row">Unit Type:</th>
                                                <td id="pw_listing_widget_tabs_detail_type"></td>
                                            </tr>
                                            <tr>
                                                <th scope="row">Total Area:</th>
                                                <td id="pw_listing_widget_tabs_detail_area"></td>
                                            </tr>
                                            <tr class="hideWhenMultiBuilding">
                                                <th scope="row">Bedrooms:</th>
                                                <td id="pw_listing_widget_tabs_detail_bed"></td>
                                            </tr>
                                            <tr class="hideWhenMultiBuilding">
                                                <th scope="row">Bathrooms:</th>
                                                <td id="pw_listing_widget_tabs_detail_bath"></td>
                                            </tr>
                                            <tr>
                                                <th scope="row">Year Built:</th>
                                                <td id="pw_listing_widget_tabs_detail_year_built"></td>
                                            </tr>
                                            <tr>
                                                <th scope="row">MLS Number:</th>
                                                <td id="pw_listing_widget_tabs_detail_mls"></td>
                                            </tr>
                                            <tr>
                                                <th scope="row">Sale Terms:</th>
                                                <td id="pw_listing_widget_tabs_detail_sale_terms"></td>
                                            </tr>
                                            <tr>
                                                <th scope="row">Sale Status:</th>
                                                <td id="pw_listing_widget_tabs_detail_sale_status"></td>
                                            </tr>
                                        </tbody>
                                    </table>
                                    <div>
                                        <a id="pw_listing_widget_tabs_detail_detailContactAgentLink"
                                            href="javascript:showContactAgentTab()"><img
                                                src="https://app.propertyware.com/pw/website/widgets/images/chat2.png"
                                                alt="" border="0" align="middle" />Contact Agent</a>
                                    </div>
                                </td>
                                <td valign="top">
                                    <img class="showWhenMultiBuilding" style="display:none"
                                        src="https://app.propertyware.com/pw/website/widgets/images/multi-unit.png"
                                        alt="" border="0" align="middle" />
                                </td>
                            </tr>
                        </table>
                    </div>
                </div>

                <br />
                <div class="inner_tabs">
                    <div id="pw_listing_widget_tabs_detail_inner_tabs">

                        <ul class="tabs">
                            <li><a href="#tabs-1" class="active">Details</a></li>
                            <li><a href="#tabs-2">Maps &amp; Explore</a></li>
                            <li><a href="#tabs-3">Community Info</a></li>
                            <li><a href="#tabs-4">Tools</a></li>
                            <li id="contactAgentTabHeader"><a id="contactAgentAnchor" href="#tabs-5">Contact Agent</a>
                            </li>
                        </ul>

                        <div class="tabContents" id="tabs-1">
                            <div id="pw_listing_widget_tabs_detail_avalilable_units"
                                class="detailContent showWhenMultiBuilding">
                                <h3>Available Units</h3>
                                <table style="width: 100%">
                                    <thead>
                                        <tr id="pw_listing_widget_tabs_detail_avalilable_units_header_tr"></tr>
                                    </thead>
                                    <tbody id="pw_listing_widget_tabs_detail_avalilable_units_header_tbody"></tbody>
                                </table>
                            </div>

                            <div id="pw_listing_widget_tabs_detail_video" class="detailContent" style="display:none">
                                <h3>Video</h3>
                                <div id="pw_listing_widget_tabs_detail_video_div"></div>
                            </div>

                            <div id="pw_listing_widget_tabs_detail_description" class="detailContent">
                                <h3>Description</h3>
                                <p id="pw_listing_widget_tabs_detail_description_p"></p>
                            </div>

                            <div id="pw_listing_widget_tabs_detail_driving_directions" class="detailContent">
                                <h3>Driving Directions</h3>
                                <p id="pw_listing_widget_tabs_detail_driving_directions_p"></p>
                            </div>

                            <div id="pw_listing_widget_tabs_detail_description_amenities" class="detailContent">
                                <h3>Amenities</h3>
                                <ul id="pw_listing_widget_tabs_detail_description_amenities_ul"></ul>
                            </div>

                            <div id="pw_listing_widget_tabs_detail_other" class="detailContent" style="display: none">
                                <h3>Other Information</h3>
                                <ul id="pw_listing_widget_tabs_detail_other_ul"></ul>
                            </div>

                            <div id="leasingContactDiv" class="detailContent leasing-contact">
                                <h3>Sales Contact</h3>
                                <table id="pw_listing_widget_tabs_detail_leasingContactTable">
                                    <tbody>
                                        <tr>
                                            <td><img alt="" id="pw_listing_widget_tabs_detail_leasingContactImgTag"
                                                    src="https://app.propertyware.com/pw/https://app.propertyware.com/pw/website/widgets/images/leasingContactPic.png"
                                                    width="70" height="80"></td>
                                            <td>
                                                <span id="pw_listing_widget_tabs_detail_detailLeaseContactName"
                                                    class="leasingContactName"></span>
                                                <span
                                                    id="pw_listing_widget_tabs_detail_detailLeaseContactCompany"></span>
                                                <span id="pw_listing_widget_tabs_detail_detailLeaseContactEmail"></span>
                                                <span id="pw_listing_widget_tabs_detail_detailLeaseContactPhone"></span>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <div class="tabContents" id="tabs-2">
                            <div>
                                <div id="pw_listing_widget_tabs_detail_map" style="width: 100%; height:350px"></div>
                                <div id="pw_listing_widget_tabs_detail_streetview" style="width: 100%; height:350px">
                                </div>
                            </div>
                        </div>

                        <div class="tabContents" id="tabs-3">
                            <div id="pw_listing_widget_tabs_detail_detailCommunityInfo">
                                <ul>
                                    <li><a id="pw_listing_widget_tabs_detail_detailTools_Schools_Nearby"
                                            href="https://nces.ed.gov/ccd/schoolsearch/school_list.asp"
                                            target="_blank">Schools Nearby</a></li>
                                    <li><a id="pw_listing_widget_tabs_detail_detailTools_Neighborhood_Profile"
                                            href="https://www.walkscore.com/score/" target="_blank">Neighborhood
                                            Profile</a></li>
                                    <li><a id="pw_listing_widget_tabs_detail_detailTools_Stores_Nearby"
                                            href="https://www.yelp.com/search?find_desc=shopping&ns=1&find_loc="
                                            target="_blank">Stores Nearby</a></li>
                                    <li><a id="pw_listing_widget_tabs_detail_detailTools_Restaurants_Nearby"
                                            href="https://www.yelp.com/search?find_desc=&amp;find_loc=&amp;x=41&amp;y=13&amp;cflt=restaurants"
                                            target="_blank">Restaurants Nearby</a></li>
                                </ul>
                            </div>
                        </div>

                        <div class="tabContents" id="tabs-4">
                            <div id="pw_listing_widget_tabs_detail_detailTools">
                                <ul id="pw_listing_widget_tabs_detail_detailTools_ul">
                                    <li><a id="pw_listing_widget_tabs_detail_detailTools_Walkscore"
                                            href="https://www.walkscore.com/get-score.php" target="_blank">Walkscore</a>
                                    </li>
                                    <li><a id="pw_listing_widget_tabs_detail_detailTools_Homethinking"
                                            href="https://neighborhoods.homethinking.com/"
                                            target="_blank">Homethinking</a></li>
                                    <li><a id="pw_listing_widget_tabs_detail_detailTools_CityData"
                                            href="https://www.city-data.com/" target="_blank">City Data</a></li>
                                    <li>Listing Link: <input id="pw_listing_widget_tabs_detail_detailTools_ListingLink"
                                            size="60" value="" type="text"></li>
                                </ul>
                            </div>
                        </div>

                        <div class="tabContents" id="tabs-5">
                            <div id="errors" align="center"></div>
                            <form id="guestCardForm" name="contactAgentForm" class="" action="#" method="post"
                                onsubmit="return validateContactAgent();">
                                <input name="sid" id="sid" value="" type="hidden">
                                <input name="cid" id="cid" value="" type="hidden">
                                <input name="uid" id="uid" value="" type="hidden">
                                <input name="retUrl" id="retUrl" value="" type="hidden">
                                <input name="frm" id="frm" value="guestCardForm" type="hidden">
                                <input name="ajaxMode" id="ajaxMode" value="true" type="hidden">
                                <input name="pwreferrer" id="pwreferrer" value="true" type="hidden" />
                                <input name="pwcampaign" id="pwcampaign" value="true" type="hidden" />

                                <div class="legend" style="margin-right: 45px;" align="right">Required Fields</div>
                                <fieldset id="contactUsFieldset">

                                    <div class="autoscroll">

                                        <table id="contactUsTable" class="edit">
                                            <tbody>
                                                <tr>
                                                    <th class="required">First Name</th>
                                                    <td>
                                                        <input name="webform.guest.primaryContact.firstName"
                                                            id="webform.guest.primaryContact.firstName" size="16"
                                                            maxlength="30" class="text" tabindex="1" type="text">
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <th class="required">Last Name</th>
                                                    <td>
                                                        <input name="webform.guest.primaryContact.lastName"
                                                            id="webform.guest.primaryContact.lastName" size="16"
                                                            maxlength="30" class="text" tabindex="2" type="text">
                                                    </td>
                                                </tr>

                                                <tr>
                                                    <th>Email Address</th>
                                                    <td>
                                                        <input name="webform.guest.primaryContact.email"
                                                            id="webform.guest.primaryContact.email" size="40"
                                                            maxlength="60" class="text" tabindex="3" type="text">
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <th>Home Phone</th>
                                                    <td>
                                                        <input name="webform.guest.primaryContact.homePhone"
                                                            id="webform.guest.primaryContact.homePhone" size="16"
                                                            maxlength="20" class="text" tabindex="4" type="text"
                                                            onchange="formatPhone(this)" />
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <th>Mobile Phone</th>
                                                    <td>
                                                        <input name="webform.guest.primaryContact.mobilePhone"
                                                            id="webform.guest.primaryContact.mobilePhone" size="16"
                                                            maxlength="20" class="text" tabindex="5" type="text"
                                                            onchange="formatPhone(this)" />
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <th>Fax</th>
                                                    <td>
                                                        <input name="webform.guest.primaryContact.fax"
                                                            id="webform.guest.primaryContact.fax" size="16"
                                                            maxlength="20" class="text" tabindex="6" type="text"
                                                            onchange="formatPhone(this)" />
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <th class="required">Preferred Contact Method</th>
                                                    <td>
                                                        <select name="webform.guest.preferredContactMethod"
                                                            id="webform.guest.preferredContactMethod" class="text"
                                                            tabindex="7" />
                                                        <option value="">Preferred Contact Method</option>
                                                        <option value="Email">Email</option>
                                                        <option value="Home Phone">Home Phone</option>
                                                        <option value="Mobile Phone">Mobile Phone</option>
                                                        </select>
                                                    </td>
                                                </tr>
                                                <tr id="questionMoveInDate">
                                                    <th>What is your desired move in date?</th>
                                                    <td>
                                                        <input name="webform.guest.moveInDateAsString"
                                                            id="moveInDateAsString" class="text" type="text"
                                                            tabindex="8" />
                                                        <script>
                                                                $(function () {
                                                                    $("#moveInDateAsString").datepicker();
                                                                });
                                                        </script>
                                                    </td>
                                                </tr>
                                                <tr id="questionWhichCity">
                                                    <th>Which city are you interested in?</th>
                                                    <td>
                                                        <select name="webform.guest.cityDesired"
                                                            id="webform_guest_cityDesired" class="text" tabindex="9" />
                                                        <option value="">Which city are you interested in?</option>
                                                        </select>
                                                    </td>
                                                </tr>
                                                <tr id="questionWhichNeighborhood">
                                                    <th>Which neighborhood are you interested in?</th>
                                                    <td>
                                                        <select name="webform.guest.neighborhoodDesired"
                                                            id="webform_guest_neighborhoodDesired" class="text"
                                                            tabindex="10" />
                                                        <option value="">Which neighborhood are you interested in?
                                                        </option>
                                                        </select>
                                                    </td>
                                                </tr>
                                                <tr id="questionWhichTypeOfUnit">
                                                    <th>What is your preferred Unit Type?</th>
                                                    <td>
                                                        <select name="webform.guest.unitTypeDesired"
                                                            id="webform_guest_unitTypeDesired" class="text"
                                                            tabindex="11">
                                                            <option value="">What is your preferred Unit Type?</option>
                                                        </select>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <th>What is your desired price range?</th>
                                                    <td>
                                                        <input type="text" placeholder="min"
                                                            name="webform.guest.rentMinAsString"
                                                            id="webform.guest.rentMinAsString" class="text minmax"
                                                            tabindex="12" size="10" onchange="formatCurrency(this)" />
                                                        To
                                                        <input type="text" placeholder="max"
                                                            name="webform.guest.rentMaxAsString"
                                                            id="webform.guest.rentMaxAsString" class="text minmax"
                                                            tabindex="13" size="10" onchange="formatCurrency(this)" />
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <th>What is your current home type?</th>
                                                    <td>
                                                        <select name="webform.guest.currentHomeType"
                                                            id="webform.guest.currentHomeType" class="text"
                                                            tabindex="14">
                                                            <option value="" style="display: none">What is your current
                                                                home type?</option>
                                                            <option value="Single Family Home">Single Family Home
                                                            </option>
                                                            <option value="Apartment">Apartment</option>
                                                            <option value="Duplex/Quad">Duplex/Quad</option>
                                                            <option value="Other">Other</option>
                                                        </select>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <th>
                                                        How long have you lived at your current residence?
                                                    </th>
                                                    <td>
                                                        <select name="webform.guest.timeAtCurrentResidence"
                                                            id="webform.guest.timeAtCurrentResidence" class="text"
                                                            tabindex="15">
                                                            <option value="" style="display: none">How long have you
                                                                lived at your current residence?</option>
                                                            <option value="Less than 1 year">Less than 1 year</option>
                                                            <option value="1 - 2 years">1 - 2 years</option>
                                                            <option value="3 - 5 years">3 - 5 years</option>
                                                            <option value="5+ years">5+ years</option>
                                                        </select>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <th>
                                                        What is your current rent/mortgage?
                                                    </th>
                                                    <td>
                                                        <input type="text"
                                                            name="webform.guest.currentRentMortgageAsString"
                                                            id="webform.guest.currentRentMortgageAsString" class="text"
                                                            size="15" onchange="formatCurrency(this)" maxlength="14"
                                                            tabindex="16" />
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <th>What is your reason for moving?</th>
                                                    <td>
                                                        <input type="text" name="webform.guest.reasonForMoving"
                                                            id="webform.guest.reasonForMoving" class="text" size="32"
                                                            maxlength="30" tabindex="17" />
                                                    </td>
                                                </tr>
                                                <tr id="questionHavePets">
                                                    <th>Do you have pets?</th>
                                                    <td>
                                                        <select name="webform.guest.hasPets" id="webform.guest.hasPets"
                                                            class="text" tabindex="18"
                                                            onchange="showHidePetQuestions(this)">
                                                            <option value="">Do you have pets?</option>
                                                            <option value="false">No</option>
                                                            <option value="true">Yes</option>
                                                        </select>
                                                    </td>
                                                </tr>
                                                <tr id="questionNumberOfPets" style="display: none;">
                                                    <th>Number of Pets?</th>
                                                    <td>
                                                        <input type="text" name="webform.guest.numberOfPets"
                                                            id="webform.guest.numberOfPets" class="text" size="5"
                                                            maxlength="3" tabindex="19" />
                                                    </td>
                                                </tr>
                                                <tr id="questionPetType" style="display: none;">
                                                    <th>Pet Type</th>
                                                    <td>
                                                        <select name="webform.guest.petType" id="webform.guest.petType"
                                                            class="text" tabindex="20">
                                                            <option value="">Pet Type</option>
                                                            <option value="None">None</option>
                                                            <option value="Dog">Dog</option>
                                                            <option value="Cat">Cat</option>
                                                            <option value="Dog & Cat">Dog & Cat</option>
                                                            <option value="Bird">Bird</option>
                                                            <option value="Hamster">Hamster</option>
                                                            <option value="Other">Other</option>
                                                        </select>
                                                    </td>
                                                </tr>
                                                <tr id="questionPetWeight" style="display: none;">
                                                    <th>What is the weight (in pounds) of the heaviest pet?</th>
                                                    <td>
                                                        <input type="text" name="webform.guest.petWeights"
                                                            id="webform.guest.petWeights" class="number" size="12"
                                                            maxlength="15" tabindex="21" />
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <th>Comments</th>
                                                    <td>
                                                        <textarea name="webform.guest.comments"
                                                            id="webform.guest.comments" cols="40" rows="5"
                                                            class="Textarea" tabindex="22"></textarea>
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>

                                        <br>

                                        <div class="primaryButtons">
                                            <input id="contactUsButton" class="button" value="Submit" tabindex="23"
                                                type="submit">
                                        </div>

                                        <br>

                                    </div>
                                </fieldset>

                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div id="pw_listing_widget_tabs_detail_directions_dialog" title="Get Directions" style="display:none">
        <form action="#" name="pw_listing_widget_tabs_detail_directionsForm"
            id="pw_listing_widget_tabs_detail_directionsForm" title="Get Directions">
            <table style="width: 100%">
                <tbody>
                    <tr>
                        <th>From:</th>
                        <td><input id="pw_listing_widget_tabs_detail_directions_dialog_fromAddress"
                                name="pw_listing_widget_tabs_detail_directions_dialog_fromAddress" type="text"></td>
                    </tr>
                </tbody>
            </table>
            <div class="primaryButtons">
                <input id="pw_listing_widget_tabs_detail_directionsForm_Go" value="Go" type="button">
                <input id="pw_listing_widget_tabs_detail_directionsForm_Cancel" value="Cancel" type="button">
            </div>
        </form>
    </div>

    <div id="pw_listing_widget_wait_list_dialog" title="Notify Me" style="display: none">
        <div class="waitListDiv">
            <div id="waitListDiv">
                <form id="waitListForm" class="webform" action="" method="post" onsubmit="">
                    <table class="edit" id="waitListTable">
                        <tbody>
                            <tr>
                                <td class="required">First Name</td>
                                <td><input name="webform.contact.contact.firstName" size="16" maxlength="30"
                                        class="text" type="text" /></td>
                            </tr>
                            <tr>
                                <td class="required">Last Name</td>
                                <td><input name="webform.contact.contact.lastName" size="16" maxlength="30" class="text"
                                        type="text" /></td>
                            </tr>
                            <tr>
                                <td class="required">Email</td>
                                <td><input name="webform.contact.contact.email" size="40" maxlength="60" class="text"
                                        type="text"></td>
                            </tr>
                            <tr>
                                <td>Work Phone</td>
                                <td><input name="webform.contact.contact.workPhone"
                                        id="webform.contact.contact.workPhone" size="16" maxlength="20" class="text"
                                        type="text" /></td>
                            </tr>
                            <tr>
                                <th class="required"> Desired City/State or Province/Zip or Postal Code</th>
                                <td><input name="webform.contact.contact.city" size="20" maxlength="60" class="text"
                                        type="text"> <input name="webform.contact.contact.state" size="2" maxlength="2"
                                        class="text" type="text" />
                                    <input name="webform.contact.contact.zip" size="10" class="text" type="text" />
                                </td>
                            </tr>
                            <tr>
                                <td> Monthly Rent</td>
                                <td> $<input name="webform.contact.contact.rentMin" size="5" class="text" type="text" />
                                    to
                                    $<input name="webform.contact.contact.rentMax" size="5" class="text" type="text" />
                                </td>
                            </tr>
                            <tr>

                                <td> Bedrooms / Bathrooms</td>
                                <td><input name="webform.contact.contact.beds" size="5" class="text" type="text" /> /
                                    <input name="webform.contact.contact.baths" size="5" class="text" type="text" />
                                </td>
                            </tr>
                            <tr>

                                <td>Desired move in date?</td>
                                <td>
                                    <input name="webform.contact.moveInDateAsString"
                                        id="webform.contact.moveInDateAsString" size="15" maxlength="10"
                                        class="date hasDatepicker" type="text" /> (i.e. 01/13/2006)
                                </td>
                            </tr>
                            <tr>
                                <td valign="top">Comments</td>
                                <td><textarea name="webform.contact.comments" cols="40" rows="5"
                                        class="Textarea"></textarea></td>
                            </tr>
                        </tbody>
                    </table>
                    <div class="primaryButtons">
                        <input type="hidden" id="forSalePage" value="true" />
                        <input class="button" id="submitTenantWaitListForm" value="Submit" type="button" />
                        <input class="button" id="cancelTenantWaitListForm" value="Cancel" type="button" />
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div id="pw_listing_widget_wait_list_dialog_confirmation" title="Confirmation" style="display:none">
        <p>Your request has been received. We will contact you as soon as possible. Thank you.</p>
    </div>

    <div class="pw_listing_widget_tabs_map_info_window_template" style="display: none;  ">
        <div style="height:150px; width: 150px">
            <table class="infoWindowTable">
                <tbody>
                    <tr>
                        <td><img width="50" height="50" class="infoWindowImg"
                                src='https://app.propertyware.com/pw/website/widgets/images/pw_listing_widget_no_photo.png'
                                alt="Thumbnail" /></td>
                        <td><span class="infoWindowAddrSpan"></span><br /><span
                                class="infoWindowCityStateZipSpan"></span></td>
                    </tr>
                    <tr>
                        <td colspan='2'><b>Price:</b> <span class="infoWindowPriceSpan"></span></td>
                    </tr>
                    <tr>
                        <td colspan='2'><b>Type:</b> <span class="infoWindowTypeSpan"></span></td>
                    </tr>
                    <tr>
                        <td colspan='2'><span class="infoWindowDescSpan"></span></td>
                    </tr>
                    <tr>
                        <td colspan='2'><span class="infoWindowMoreDetails">More Details</span></td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    <li class="pw_listing_widget_tabs_list_item_template" style="display:none; ">
        <table class="listTable">
            <tr>
                <td class="listItemImgTd" align="center"><a class="listItemImgTdUnitLink" href="#"><img
                            class="pw_listing_widget_tabs_list_item_img"
                            src='https://app.propertyware.com/pw/website/widgets/images/pw_listing_widget_no_photo.png'
                            width="160" height="160" alt="Thumbnail" border="0" /></a> <br /> <a
                        class="listItemImgTdMapLink" href="">Map</a></td>
                <td class="listItemDescTd" valign="top"></td>
                <td valign="top" style="width: 48px">
                    <img class="showWhenMultiBuilding" width="48" height="48" style="display:none"
                        src="https://app.propertyware.com/pw/website/widgets/images/multi-unit.png" alt="" border="0"
                        align="middle" />
                </td>
            </tr>
        </table>
    </li>


    <div id="pw_listing_widget_image_popup"
        style="background-color:transparent;opacity:100;z-index:2001;text-align:center;position:absolute;display:none;">
        <table>
            <tr>
                <td style="text-align:right;"><img style="cursor:pointer" id="imageClose" alt="Close"
                        src="https://app.propertyware.com/pw/website/widgets/images/close.png"></td>
            </tr>
            <tr>
                <td style="text-align:center;"><img style="border: 5px solid white" alt="No Photo" id="popupImg"
                        src="https://app.propertyware.com/pw/website/widgets/images/pw_listing_widget_no_photo.png" />
                </td>
            </tr>
            <tr>
                <td style="text-align:center;">
                    <img id="zoomLeft" style="cursor:pointer" alt="Left"
                        src="https://app.propertyware.com/pw/website/widgets/images/left.png" />
                    <img id="zoomRight" style="cursor:pointer" alt="Right"
                        src="https://app.propertyware.com/pw/website/widgets/images/right.png" />
                </td>
            </tr>
        </table>
    </div>
</div>