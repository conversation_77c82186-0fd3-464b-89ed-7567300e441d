DirectoryIndex /index.html
ErrorDocument 404 /index.html
ErrorDocument 400 /index.html
<IfModule mod_rewrite.c>
    RewriteEngine On
    RewriteBase /
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_URI} !\.(css|gif|ico|jpg|jpeg|webp|js|png|swf|txt|svg|woff|ttf|eot|pdf|xml)$
    RewriteRule ^(.+[^/])$ https://%{HTTP_HOST}%{REQUEST_URI}/ [R=301,L]
</IfModule>
<IfModule mod_rewrite.c>
    RewriteEngine On
    RewriteCond %{REQUEST_URI} !^/index.html$
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_URI} !\.(css|gif|ico|jpg|jpeg|webp|js|png|swf|txt|svg|woff|ttf|eot|pdf|xml)$
    RewriteCond %{REQUEST_URI} \/+[^\.]+$
    RewriteRule . index.html [L]
</IfModule>

Redirect 301  /media/pmi/v1/174/2022/01/Resident-Benefits-Package-1.pdf https://www.propertymanagementinc.com/media/pmi/v1/174/2022/03/PMI-KC-Metro-25-Resident-Benefits-Package.pdf
