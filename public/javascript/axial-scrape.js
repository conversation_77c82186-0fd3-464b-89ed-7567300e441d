// SELECTORS.
// dropdown-item
// dropdown-item-children
// REQUIREMENTS / ONLOAD
// 1) Close hamburger icon.
// 2) Remove active class from primary menu items.
// 3) Hide all open children menus.
// REQUIREMENTS / EVENTS.
// 3) Create listener for hamburger.
// 4) Create listener for primary items that have children.
// 5) Create listener for search glass.
// 6) Create listeners for hovering on menus.

document.addEventListener('DOMContentLoaded', (e) => {
    const hamburger = document.getElementById('hamburger');
    // 1) ON LOAD, MAKE HAMBURGER IN-ACTIVE.
    if (hamburger) hamburger.classList.remove('is-active');

    // 2, 3
    const primaries = document.querySelectorAll('.dropdown-item');
    primaries.forEach(item => {
        [ n1, n2, number ] = item.id.split('-');
        const ePrimary = document.getElementById(`dropdown-item-${number}`);
        const eChildren = document.getElementById(`dropdown-children-${number}`);
        // 2) ON LOAD, <PERSON><PERSON>OVE ACTIVE CLASS TO PRIMARY.
        if (ePrimary) ePrimary.classList.remove('active');
        // 3) ON LOAD, ADD HIDE CLASS TO CHILDREN.
        if (eChildren) ePrimary.classList.add('hide');
    });

    // 3) LISTENER FOR HAMBURGER
    hamburger.addEventListener('click', function (e) {
        hamburger.classList.toggle('is-active', !hamburger.classList.contains('is-active'));
    });
    
    // 6) LISTENER FOR HOVERING.
    const items = document.querySelectorAll('.nav-primary-item');
    items.forEach(item => {
        [ n1, n2, number ] = item.id.split('-');
        const submenu = document.getElementById(`nav-children-${number}`);
        if (submenu) {
            item.addEventListener('mouseenter', function() { submenu.classList.remove('hide') });
            item.addEventListener('mouseleave', function() { submenu.classList.add('hide') });
        }
    });

    // 5) LISTENER FOR SEARCH GLASS.
    const searchExpand = document.getElementById('search-expand');
    const searchGlass  = document.getElementById('search-glass');
    searchGlass.addEventListener('click', function (e) {
        searchExpand.classList.toggle('search-open', searchExpand.classList.contains('search-open'));
        searchExpand.classList.toggle('search-closed', searchExpand.classList.contains('search-close'));
    });
});