/*******************************************************************************************************
   Copyright 2021 - Calise Partners LLC d/b/a Imaginuity. All rights are reserved. 
   Reproduction or transmission in whole or in part, in any form or by any means, electronic, mechanical 
   or otherwise, is prohibited without the prior written consent of Calise Partners LLC d/b/a Imaginuity.
********************************************************************************************************
   Project : 10618 Maverick Platform Development
   Created By : Joe Bannon
   Creation Date : August 25, 2021
   Description : Universal helper functions for Pylot platform available via pylot object.
   Documentation : Please read the readme.md file for more information.
********************************************************************************************************
    UPDATES:
********************************************************************************************************/

var domain = window.location.hostname.toLowerCase();

this.module = function(names, fn) {
    var name, space; 
    names = (typeof names === 'string') ? names.split('.') : names;
    space = this[name = names.shift()] || (this[name] = {});
    space.module || (space.module = this.module);
    return (names.length) ? space.module(names, fn) : fn.call(space);
}

this.module('cache', function() {
    this.fonts = {}; 
    this.toggled = {};
    this.dev = domain.includes('dev') ? true : false;
});

this.module('pylot', function() {
    this.cache = {
        layouts: {}
    };
    this.attempt = {};
    this.cookies = {};

    this.module('cookie', function() {
        this.getall = function() {
            var cookies = {};
            var decoded = decodeURIComponent(document.cookie);
            var array   = decoded.split(';');
            for (var i = 0; i < array.length; i++) {  
                var [ key, value ] = array[i].split('=');
                cookies[key.trim()] = value;
                pylot.cookies[key.trim()] = value;
            }
            return cookies;
        }
    });

    this.module('design', function() {
        this.coloring = function(id, location) {
            switch (id) {
                case 'background_color':
                    return { [location]: cache.settings.design?.colors?.background_color };
                case 'primary_color':
                    return { [location]: cache.settings.design?.colors?.primary_color };
                case 'secondary_color':
                    return { [location]: cache.settings.design?.colors?.secondary_color };
                case 'tertiary_color':
                    return { [location]: cache.settings.design?.colors?.tertiary_color };
                case 'body_copy_color':
                    return { [location]: cache.settings.design?.colors?.body_copy_color };
                case 'white':
                    return { [location]: '#fff' };
                default:
                    return {};
            }
        };
        this.bgColorClass = function(id) {
            switch (id) {
                case 'background_color':
                    return 'background-bg';
                case 'primary_color':
                    return 'primary-bg';
                case 'secondary_color':
                    return 'secondary-bg';
                case 'tertiary_color':
                    return 'tertiary-bg';
                case 'body_copy_color':
                    return 'body-copy-bg';
                case 'none':
                    return '';
                default:
                    return {};
            }
        };
        this.parallax = function(url) {
            return { backgroundImage:`url(${url})`, backgroundRepeat:'no-repeat', backgroundPosition:'center', backgroundSize:'cover', backgroundAttachment:'fixed' };
        };
        this.background = function(url) {
            return { backgroundImage:`url(${url})`, backgroundRepeat:'no-repeat', backgroundPosition:'center', backgroundSize:'cover' };
        };
        this.hexToRGB = function(hex, opacity = 1) {
            var h = hex?.replace('#', '');
            h =  h.match(new RegExp('(.{'+h.length/3+'})', 'g'));
            for (var i=0; i<h.length; i++) {
                h[i] = parseInt(h[i].length==1? h[i]+h[i]:h[i], 16);
            }
            if (typeof opacity != 'undefined') h.push(opacity);
            return 'rgba('+h.join(',')+')';
        };
    });

    this.module('styling', function() {
        this.inject = function(selector, declaration) {
            var StyleSheet = document.createElement('style');
            var StylesString = ` ${selector} { ${declaration}; } `;
            StyleSheet.innerHTML = StylesString;
            document.getElementsByTagName('head')[0].appendChild(StyleSheet);
        };
    });

    this.module('interface', function() {
        this.toggle = function(id, classes) {
            var element = document.getElementById(id);
        }
    });

    this.resolve = function(path, obj) {
        return path.split('.').reduce(function(prev, curr) {
            return prev ? prev[curr] : null;
        }, obj || self);
    };

    this.module('tools', function() {
        this.module('domain', function() {
            this.add = function(path = '') {
                var domain = window.location.hostname;
                // ALLOWS .DEV TO VIEW .PREFLIGHT IMAGES WHILE WORKING LOCALLY.
                domain = domain.replace('.dev', '.preflight');
                return (path.includes('http')) ? path : `https://${domain}${path}`;
            };
        });
    });

    this.module('dates', function() {
        this.day = function(num) {
            return ['Sunday','Monday','Tuesday','Wednesday','Thursday','Friday','Saturday'][num];
        };
        this.month = function(num) {
            return ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'][num];
        };
        this.getOrdinialNum = function(num) {
            const ordinialIndicators = ['th', 'st', 'nd', 'rd', '']

            if (num <= 0) {
                selector = 4;
            } else if ((num > 3 && num < 21) || num % 10 > 3) {
                selector = 0;
            } else {
                selector = num % 10;
            }

            return num + ordinialIndicators[selector];
        }
    });

    this.module('socialShareUrls', function() {
        this.googleCalendar = function({ // should this go into a /helpers folder instead?
            startDate, // full new Date(obj)
            endDate, // full new Date(obj)
            text,
            details
        }) {
            const formatDates = (date) => `${date.toISOString().replace(new RegExp('\\b(.|:|-)\\b', 'gi'), '').slice(0, -4)}`;

            let googleCalenderStr = 'http://www.google.com/calendar/event?action=TEMPLATE';
            googleCalenderStr += `&text=${encodeURI(text)}`;
            googleCalenderStr +=  `&dates=${formatDates(startDate)}/${formatDates(endDate)}`;
            googleCalenderStr += `&details=${encodeURI(details)}`;
        
            return googleCalenderStr;    
        };
        this.facebook = function() {
            return `https://www.facebook.com/sharer/sharer.php?u=${encodeURI(location.href)}&t=${encodeURI(document.title)}`;
        };
        this.twitter = function() {
            return `http://twitter.com/share?text=${encodeURI(document.title)}&url=${location.href}`
        };
        this.reddit = function() {
            return `http://www.reddit.com/submit?url=${encodeURI(location.href)}&title=${encodeURI(document.title)}`
        };
        this.tumblr = function() {
            return `http://tumblr.com/share?s=&v=3&t=${encodeURI(document.title)}&u=${encodeURI(location.href)}`
        };
        this.email = function() {
            return `mailto:?subject=${document.title}&amp;body=Check out this site ${location.href}.`
        }
    });

    this.module('validate', function() {
        this.emailaddress = function(emailaddress) {
            var filter = /^([+\w-\.]+)@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.)|(([\w-]+\.)+))([a-zA-Z]{2,6}|[0-9]{1,3})(\]?)$/;
            return (filter.test(emailaddress)) ? true : false;
        }
    });

    this.module('cleanse', function() {
        this.emailaddress = function(emailaddress) {
            return emailaddress.replace(/^([\w-\.]+)@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.)|(([\w-]+\.)+))([a-zA-Z]{2,6}|[0-9]{1,3})(\]?)$/, '');
        }
    });
});