import React, { useContext, useEffect } from 'react';
import { ThemeProvider } from "styled-components";
var AmazonCognitoIdentity = require('amazon-cognito-identity-js');

// CONFIG.
import config from 'src/config';

// CONTEXT.
import { PrincipalContext } from "src/context";
import { CognitoProvider, CognitoContext } from "src/cognito/context";

// PARTIALS.
import SignInForm from 'src/cognito/signin';
import SignUpForm from 'src/cognito/signup';
import PasswordForm from 'src/cognito/password';
import ResetForm from 'src/cognito/reset';
import ConfirmForm from 'src/cognito/confirm';
import SignOutForm from 'src/cognito/signout';
import ChangeForm from 'src/cognito/change';

// HELPERS.
const Imaging = React.lazy(() => import('src/helpers/imaging'));

// SCSS.
import 'src/cognito/index.scss';
import Styles from "src/styles";

const Start = ({ settings }) => {
    return (
        <CognitoProvider settings={settings}>
            <ThemeProvider theme={Styles({
                settings: settings
            })}>
                <LoadCognito settings={settings} />
            </ThemeProvider>
        </CognitoProvider>
    );
};

const LoadCognito = ({ settings }) => {
    const [ cognito, setCognito ] = useContext(CognitoContext);

    useEffect(() => {
        if (!cognito.loading) {
            var userPool = new AmazonCognitoIdentity.CognitoUserPool(cognito.poolData);
            setCognito({ ...cognito, ...{ userPool:userPool }});
        }
    }, [cognito.loading]);

    if (cognito.loading) {
        return (<div />);
    } else {
        return (
            <div id="cognito">
                <div class='form-container'>
                    <div class='logo-container'>
                        <Imaging data={settings.branding?.main_logo} />
                    </div>
                    <Ascertain settings={settings} />
                </div>
            </div>
        );
    }
};

const Ascertain = ({ settings }) => {
    const [ cognito, setCognito ] = useContext(CognitoContext);

    useEffect(() => {
        setCognito({ ...cognito, ...{ errors:false }});
    }, [cognito.page]);

    switch (cognito.page) {
        case 'signup':
            return (<SignUpForm settings={settings} />);
        case 'signout':
            return (<SignOutForm settings={settings} />);
        case 'confirm':
            return (<ConfirmForm settings={settings} />);
        case 'password':
            return (<PasswordForm settings={settings} />);
        case 'reset':
            return (<ResetForm settings={settings} />);
        case 'change':
            return (<ChangeForm settings={settings} />);
        case 'confirmed':
        case 'signin':
        default:
            return (<SignInForm settings={settings} />);
    }
};

export default Start;
