import React, { useContext } from 'react';
var AmazonCognitoIdentity = require('amazon-cognito-identity-js');

import { CognitoProvider, CognitoContext } from "src/cognito/context";

const Start = ({ settings }) => {
    const [cognito, setCognito]   = useContext(CognitoContext);

    function handleSubmit(e) {
        e.preventDefault();
        var userPool = new AmazonCognitoIdentity.CognitoUserPool(cognito.poolData);
        var cognitoUser = userPool.getCurrentUser();

        var attributeList = [];
        var attribute = new AmazonCognitoIdentity.CognitoUserAttribute({ Name: 'custom:last_site_visited', Value: window.location.hostname });
        attributeList.push(attribute);
        cognitoUser.updateAttributes(attributeList, (err, result) => console.log('COGNITO RESULTS:', err, result));

        cognitoUser.signOut();
        setCognito({ ...cognito, ...{ page:'signin' }});
    }

    var submitButtonStyles = {
        color: '#fff',
        backgroundColor: settings.design?.colors?.primary_color,
    }

    return (
        <form id="form-cognito" onSubmit={handleSubmit} role="form" aria-label="sign out">
            <div class="form-buttons">
                <button style={submitButtonStyles} type="submit" form="form-cognito">CONFIRM SIGN OUT</button>
            </div>
        </form>
    );
};

export default Start;