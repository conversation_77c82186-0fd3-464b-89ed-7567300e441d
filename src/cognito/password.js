import React, { useState, useContext } from 'react';
var AmazonCognitoIdentity = require('amazon-cognito-identity-js');

import { CognitoProvider, CognitoContext } from "src/cognito/context";

const Clicker = React.lazy(() => import('src/helpers/clicker'));

const Start = ({ settings }) => {
    const [cognito, setCognito]           = useContext(CognitoContext);
    const [username, setUsername]         = useState('');

    function handleUsername(e) {
        setUsername(e.target.value);
    }

    function handleSubmit(e) {
        e.preventDefault();

        var userPool = new AmazonCognitoIdentity.CognitoUserPool(cognito.poolData);
        var cognitoUser = new AmazonCognitoIdentity.CognitoUser({ Username: username, Pool: userPool });

        cognitoUser.forgotPassword({
            onSuccess: function(result) {
                var attributeList = [];
                var attribute = new AmazonCognitoIdentity.CognitoUserAttribute({ Name: 'custom:last_site_visited', Value: window.location.hostname });
                attributeList.push(attribute);
                cognitoUser.updateAttributes(attributeList, (err, result) => console.log('COGNITO RESULTS:', err, result));
                setCognito({ ...cognito, ...{ page:'reset' }});
            },
            onFailure: function(err) {
                console.log(err);
                setCognito({ ...cognito, ...{ errors:err }});
            },
        });
    }

    var submitButtonStyles = {
        color: '#fff',
        backgroundColor: settings.design?.colors?.primary_color,
    }

    return (
        <form id="form-cognito" onSubmit={handleSubmit} role="form" aria-label="reset password request">
            <div class='form-subtitle' dangerouslySetInnerHTML={{ __html:cognito.lost_password_message }} />

            {cognito.errors && <div class='form-error'>{cognito.errors.message}</div>}

            <div class='field-group'>
                <div class='field-name'>
                    <label htmlFor='username'>{cognito.email_field_label} <span class="required">*</span></label>
                </div>
                <div class='field-input'>
                    <input id="username" type='text' name='username' placeholder='Email Address' onChange={handleUsername} />
                </div>
            </div>

            <div class="form-required">* Required Fields.</div>
            <div class="form-buttons">
                <button style={submitButtonStyles} type="submit" form="form-cognito">REQUEST RESET</button>
            </div>
        </form>
    );
};

export default Start;
