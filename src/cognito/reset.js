import React, { useState, useContext } from 'react';
var AmazonCognitoIdentity = require('amazon-cognito-identity-js');

import { decode } from 'html-entities';


import { CognitoProvider, CognitoContext } from "src/cognito/context";

const Start = ({ settings }) => {
    const [cognito, setCognito]   = useContext(CognitoContext);
    const [username, setUsername] = useState('');
    const [password, setPassword] = useState('');
    const [code, setCode]         = useState('');

    function handleUsername(e) {
        setUsername(e.target.value);
    }
    
    function handlePassword(e) {
        setPassword(e.target.value);
    }

    function handleCode(e) {
        setCode(e.target.value);
    }

    function handleSubmit(e) {
        e.preventDefault();

        // IS DOMAIN ALLOWED?
        if (cognito.allowed_domains.length > 0) {
            var [ handle, domain ] = username.split('@');
            var allowed = cognito.allowed_domains.includes(domain);
            if (!allowed) {
                setCognito({ ...cognito, ...{ errors: { message:'The email address used is not allowed.' }}});
                return;
            }
        }

        var userPool = new AmazonCognitoIdentity.CognitoUserPool(cognito.poolData);
        var cognitoUser = new AmazonCognitoIdentity.CognitoUser({ Username: username, Pool: userPool });

        cognitoUser.confirmPassword(code, password, {
			onSuccess(result) {
                var attributeList = [];
                var attribute = new AmazonCognitoIdentity.CognitoUserAttribute({ Name: 'custom:last_site_visited', Value: window.location.hostname });
                attributeList.push(attribute);
                cognitoUser.updateAttributes(attributeList, (err, result) => console.log('COGNITO RESULTS:', err, result));
				setCognito({ ...cognito, ...{ page:'signin' }});
			},
			onFailure(err) {
                setCognito({ ...cognito, ...{ errors:err }});
			}
        });
    }

    var submitButtonStyles = {
        color: '#fff',
        backgroundColor: settings.design?.colors?.primary_color,
    }

    return (
        <form id="form-cognito" onSubmit={handleSubmit} role="form" aria-label="reset password">
            <div class='form-subtitle' dangerouslySetInnerHTML={{ __html:cognito.reset_confirm_message }} />

            {cognito.errors && <div class='form-error'>{cognito.errors.message}</div>}

            <div class='field-group'>
                <div class='field-name'>
                    <label htmlFor='username'>{cognito.email_field_label} <span class="required">*</span></label>
                </div>
                <div class='field-input'>
                    <input id="username" type='text' name='username' placeholder='Enter Email Address Here.' required onChange={handleUsername} />
                </div>
            </div>

            <div class='field-group'>
                <div class='field-name'>
                    <label htmlFor='code'>Confirmation Code <span class="required">*</span></label></div>
                <div class='field-input'>
                    <input id="code" type='text' name='code' placeholder='Enter Confirmation Code Here.' minlength='1' required onChange={handleCode} />
                </div>
            </div>

            <div class='field-group'>
                <div class='field-name'>
                    <label htmlFor='password'>New Password <span class="required">*</span></label>
                </div>
                <div class='field-input'>
                    <input id="password" type='password' name='password' placeholder='Enter New Password Here.' minlength='6' required onChange={handlePassword} />
                </div>
            </div>

            <div class="form-required">* Required Fields.</div>
            <div class="form-buttons">
                <button style={submitButtonStyles} type="submit" form="form-cognito">RESET PASSWORD</button>
            </div>
        </form>
    );
};

export default Start;