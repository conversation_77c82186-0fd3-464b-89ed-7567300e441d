#cognito {
    // margin: 200px auto;
    // // padding: 30px;
    // max-width: 350px;
    // width: 100%;
    // border: 1px solid rgba(0,0,0,0.1);
    // box-shadow: 2px 2px 0px rgba(0,0,0,0.3);

    margin: 15px;


    .form-container {
        margin: 100px auto;
        padding: 15px;
        max-width: 350px;
        box-shadow: 0 3px 9px rgba(0, 0, 0, 0.4);
        border: 1px solid rgba(0,0,0,0.1);

        .logo-container {
            text-align: center;
            margin: 30px 0px;
    
            img {
                max-width: 100%;
                height: auto;
            }
        }

        .form-title {
            padding: 10px 0px;
            font-size: 2.25rem;
            line-height: 1.1;
        }
    
        .form-subtitle {
            margin-bottom: 15px;
            padding: 10px 0px;
            font-size: 1rem;
            text-align: center;
        }
    }


    #form-cognito {
        margin: 0 auto;

        .field-group {
            margin-bottom: 15px;

            .field-name {
                margin-bottom: 3px;
                font-size: 0.938rem;
                font-weight: 400;
                span {
                    font-weight: 900;
                    color:red;
                }
            }
        
            .field-input {
                margin-bottom: 3px;
        
                input, select {
                    width: 100%;
                    padding: 10px;
                    border: 0px;
                    border-radius: 4px;
                    background-color: #f4f6f8;
                }
            }
        
            .field-options {
                text-align: left;
                color: #337ab7;
                font-size: 0.875rem;
                .option {
                    cursor: pointer;
                }
            }
        }
   



        .form-error {
            margin-bottom: 20px;
            font-size: 1rem;
            text-align: center;
            color: #B60000;
        }
    
        .form-required {
            margin: 0px auto 20px;
            padding-top: 10px;
            font-size: 0.813rem;
            color:#B60000;
            text-align: center;
        }

        .form-buttons {
            margin-bottom: 20px;
    
            button {
                // padding: 18px 50px;;
                font-size: 0.875rem;
                letter-spacing: 1.1px;
                font-weight: 600;
                outline: none;
                cursor: pointer;
                vertical-align: middle;
                width: 100%;
                height: 40px;
                border-radius: 4px;
                border:0px;
            }
        }
    
        .form-options {
            text-align: center;
            font-size: 0.875rem;
            line-height: 24px;
    
            .switch-forms {
                font-size: 0.875rem;
                text-decoration: none;
                margin-left: 4px;
                color: #337ab7;
    
                &:hover {
                    cursor: pointer;
                }
            }
        }
    }
}