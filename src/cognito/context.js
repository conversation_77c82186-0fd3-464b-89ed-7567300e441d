import React, { useState, useEffect, createContext } from 'react';
import { useParams } from "react-router-dom";

export const CognitoContext = createContext();
export const CognitoProvider = (props) => {
    const [ cognito, setCognito ] = useState({ loading:true });
    const params = useParams();

    useEffect(() => {
        if (props?.settings?.cognito) {
            var data = props.settings.cognito;
            data.loading = false;
            data.referer = window.location.pathname;
            data.page = (params.page) ? params.page : 'signin';
            data.poolData = {
                UserPoolId: props.settings.cognito.user_pool_id,
                ClientId: props.settings.cognito.app_client_id
            };
            setCognito(data);
        } else {
            setCognito(false);
        }
    }, []);

    return (
        <CognitoContext.Provider value={[cognito, setCognito]}>
            {props.children}
        </CognitoContext.Provider>
    );
}

export const FormContext = createContext();
export const FormProvider = (props) => {
    const [ form, setForm ] = useState({});
    return (
        <FormContext.Provider value={[form, setForm]}>
            {props.children}
        </FormContext.Provider>
    );
}