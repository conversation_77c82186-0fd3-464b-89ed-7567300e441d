import React, { useState, useContext } from 'react';
var AmazonCognitoIdentity = require('amazon-cognito-identity-js');

import { CognitoProvider, CognitoContext } from "src/cognito/context";

const Start = ({ settings }) => {
    const [cognito, setCognito]     = useContext(CognitoContext);
    const [name, setName]           = useState();
    const [phone, setPhone]         = useState();
    const [password1, setPassword1] = useState('');
    const [password2, setPassword2] = useState('');
    const [confirmed, setConfirmed] = useState(false);

    function handleName(e) {
        setName(e.target.value);
    }

    function handlePhone(e) {
        setPhone(e.target.value);
    }

    function handlePassword1(e) {
        setPassword1(e.target.value);
    }

    function handlePassword2(e) {
        setPassword2(e.target.value);
    }

    function handleSubmit(e) {
        e.preventDefault();

        if (password1 == password2) {
            cognito.sessionUserAttributes.name = name;
            // cognito.sessionUserAttributes.phone_number = `+${phone}`;
            cognito.cognitoUser.completeNewPasswordChallenge(password1, cognito.sessionUserAttributes, {
                onSuccess: (result) => {
                    var attributeList = [];
                    var attribute = new AmazonCognitoIdentity.CognitoUserAttribute({ Name: 'custom:last_site_visited', Value: window.location.hostname });
                    attributeList.push(attribute);
                    cognitoUser.updateAttributes(attributeList, (err, result) => console.log('COGNITO RESULTS:', err, result));
                    // setCognito({ ...cognito, ...{ page:'success' }});
                    window.location.href = `//${window.location.hostname}`;
                },
                newPasswordRequired: (userAttributes, requiredAttributes) => {
                },
                onFailure: (err) => {
                    setCognito({ ...cognito, ...{ errors:err }});
                    throw err;
                }
            });
        } else {
            setCognito({ ...cognito, ...{ error: { message:'Passwords didn\'t match.' }}});
        }
    }

    var submitButtonStyles = {
        color: '#fff',
        backgroundColor: settings.design?.colors?.primary_color,
    }

    return (
        <form id="form-cognito" onSubmit={handleSubmit} role="form" aria-label="change password">
            <div class='form-subtitle'>Please replace your temporary password below.</div>

            {cognito.errors && <div class='form-error'>{cognito.errors.message}</div>}

            <div class='field-group'>
                <div class='field-name'>
                    <label htmlFor='name'>Name <span class="required">*</span></label>
                </div>
                <div class='field-input'>
                    <input id="name" type='text' name='name' placeholder='Enter your name here.' onChange={handleName} />
                </div>
            </div>

            <div class='field-group'>
                <div class='field-name'>
                    <label htmlFor='name'>Phone Number <span class="required">*</span></label>
                </div>
                <div class='field-input'>
                    <input id="phone" type='text' name='phone' placeholder='Enter your phone here.' onChange={handlePhone} />
                </div>
            </div>

            <div class='field-group'>
                <div class='field-name'>
                    <label htmlFor='password1'>New Password <span class="required">*</span></label>
                </div>
                <div class='field-input'>
                    <input id="password1" type='password' name='password1' placeholder='Enter new password here.' onChange={handlePassword1} />
                </div>
            </div>

            <div class='field-group'>
                <div class='field-name'>
                    <label htmlFor='password2'>New Password, Again <span class="required">*</span></label>
                </div>
                <div class='field-input'>
                    <input id="password2" type='password' name='password2' placeholder='Enter new password again here' onChange={handlePassword2} />
                </div>
            </div>

            <div class="form-required">* Required Fields.</div>
            <div class="form-buttons">
                <button style={submitButtonStyles} type="submit" form="form-cognito">UPDATE PASSWORD</button>
            </div>
        </form>
    );
};



export default Start;
