import React, { useState, useContext } from 'react';
var AmazonCognitoIdentity = require('amazon-cognito-identity-js');

// CONTEXT.
import { FormProvider, FormContext, CognitoContext } from "src/cognito/context";

// HELPERS.
const Clicker = React.lazy(() => import('src/helpers/clicker'));

const Start = ({ settings }) => {
    return (
        <FormProvider>
            <Container settings={settings} />
        </FormProvider>
    );
};

const Container = ({ settings }) => {
    const [cognito, setCognito]   = useContext(CognitoContext);
    const [form, setForm]         = useContext(FormContext);
    const [username, setUsername] = useState('');
    const [password, setPassword] = useState('');

    function handleUsername(e) {
        setUsername(e.target.value);
    } 
    
    function handlePassword(e) {
        setPassword(e.target.value);
    }

    function handleSubmit(e) {
        e.preventDefault();

        // IS DOMAIN ALLOWED?
        if (cognito.allowed_domains.length > 0) {
            var [ handle, domain ] = username.split('@');
            var allowed = cognito.allowed_domains.includes(domain);
            if (!allowed) {
                setCognito({ ...cognito, ...{ errors: { message:'The email address used is not allowed.' }}});
                return;
            }
        }

        // IS EMAIL ADDRESS CORRECT FORMAT?
        var valid = pylot.validate.emailaddress(username);
        if (!valid) {
            setCognito({ ...cognito, ...{ errors: { message:'The email address isn\'t valid.' }}});
        }

        if (valid && allowed != false) {
            var userPool = new AmazonCognitoIdentity.CognitoUserPool(cognito.poolData);
            var attributeList = [];

            var attributeSiteID = new AmazonCognitoIdentity.CognitoUserAttribute({ Name: 'custom:site_id', Value: String(settings.site_id) });
            attributeList.push(attributeSiteID);
            var attributeHostName = new AmazonCognitoIdentity.CognitoUserAttribute({ Name: 'custom:sign_up_via', Value: window.location.hostname });
            attributeList.push(attributeHostName);
            var attributeHostName = new AmazonCognitoIdentity.CognitoUserAttribute({ Name: 'custom:last_site_visited', Value: window.location.hostname });
            attributeList.push(attributeHostName);

            Object.keys(form).map((key) => {
                var value = (key == 'phone_number') ? `+${form[key]}` : form[key];
                var attribute = new AmazonCognitoIdentity.CognitoUserAttribute({ Name: key, Value: value });
                attributeList.push(attribute);
            });

            userPool.signUp(username, password, attributeList, null, function(err, result) {
                if (err) {
                    setCognito({ ...cognito, ...{ errors:err }});
                    return;
                } else {
                    setCognito({ ...cognito, ...{ page:'confirm' }});
                }
            });
        }
    }

    return (
        <form id="form-cognito" onSubmit={handleSubmit} role="form" aria-label="sign up">
            <div class='form-subtitle' dangerouslySetInnerHTML={{ __html:cognito.sign_up_message }} />

            {cognito.errors && <div class='form-error'>{cognito.errors.message}</div>}
            {cognito.custom_signup_fields && cognito.custom_signup_fields.map((field, index) => <CustomField field={field} index={index} />)}

            <div class='field-group'>
                <div class='field-name'>
                    <label htmlFor='username'>{cognito.email_field_label} <span class="required">*</span></label>
                </div>
                <div class='field-input'>
                    <input id="usrename" type='text' name='username' placeholder='Email Address' required value={username} onChange={handleUsername} />
                </div>
            </div>

            <div class='field-group'>
                <div class='field-name'>
                    <label htmlFor='password'>Password <span class="required">*</span></label>
                </div>
                <div class='field-input'>
                    <input id="password" type='password' name='password' placeholder='Password' minlength="8" required onChange={handlePassword} />
                </div>
            </div>
            {cognito.enable_property &&
                <div class='field-group'>
                    <div class='field-name'>
                        <label htmlFor='property'>Property</label>
                    </div>
                    <div class='field-input'>
                        <select id="property" type='password' name='property' onChange={(e) => setForm({ ...form, ...{ ["custom:property"]: e.target.value } })}>
                            <option value="">Select Property</option>
                            {cognito.property && cognito.property.map((p) => <option value={p}>{p}</option>)}
                        </select>
                    </div>
                </div>
            }
            <div class="form-required">* Required Fields.</div>
            <FormSubmit settings={settings} />
            <FormOptions settings={settings} />
        </form>
    );
};

const CustomField = ({ field }) => {
    const [form, setForm]   = useContext(FormContext);
    const [value, setValue] = useState('');

    function handleChange(e) {
        var key = String(field.value)
        var input = e.target.value;
        input = (field.value == 'phone_number') ? input.replace(/[^\d]/g, "") : input;
        setValue(input);
        setForm({ ...form, ...{ [key]:input }});
    }

    var placeholder = (field.value == 'phone_number') ? '15552227171' : field.label;
    var reqLength   = (field.value == 'phone_number') ? 11 : null;

    return (
        <div class='field-group'>
            <div class='field-name'>
                <label htmlFor={field.value}>{field.label} <span class="required">*</span></label>
            </div>
            <div class='field-input'>
                <input id={field.value} type='text' name={field.value} placeholder={placeholder} required value={value} onChange={handleChange} maxlength={reqLength} minlength={reqLength} />
            </div>
        </div>     
    );
};

const FormSubmit = ({ settings }) => {

    var submitButtonStyles = {
        color: '#fff',
        backgroundColor: settings.design?.colors?.primary_color,
    };

    return (
        <div class="form-buttons">
            <button style={submitButtonStyles} type="submit" form="form-cognito">SIGN UP</button>
        </div>
    );
};

const FormOptions = ({ settings }) => {
    const [cognito, setCognito] = useContext(CognitoContext);

    function switchToSignIn(e) {
        setCognito({ ...cognito, ...{ page:'signin' }});
    }

    return (
        <div class="form-options">
            <div>Already have an account?</div>
            <Clicker class="switch-forms" process={switchToSignIn}>Sign In</Clicker>
        </div>
    );
};

export default Start;

function normalize(phone) {
    phone = phone.replace(/[^\d]/g, "");
    if (phone.length == 10) {
        return phone.replace(/(\d{3})(\d{3})(\d{4})/, "($1) $2-$3");
    }
    return null;
}