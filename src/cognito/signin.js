import React, { useState, useContext } from 'react';
var AmazonCognitoIdentity = require('amazon-cognito-identity-js');

import { decode } from 'html-entities';


import { CognitoContext } from "src/cognito/context";

// HELPERS.
const Clicker = React.lazy(() => import('src/helpers/clicker'));

const Start = ({ settings }) => {
    const [cognito, setCognito]   = useContext(CognitoContext);
    const [username, setUsername] = useState('');
    const [password, setPassword] = useState('');

    function handleUsername(e) {
        setUsername(e.target.value);
    }
    
    function handlePassword(e) {
        setPassword(e.target.value);
    }

    function handleSubmit(e) {
        e.preventDefault();

        var authenticationDetails = new AmazonCognitoIdentity.AuthenticationDetails({ Username: username, Password: password });
        var cognitoUser = new AmazonCognitoIdentity.CognitoUser({ Username: username, Pool: cognito.userPool });

        cognitoUser.authenticateUser(authenticationDetails, {
            onSuccess: function(result) {
                var attributeList = [];
                var attribute = new AmazonCognitoIdentity.CognitoUserAttribute({ Name: 'custom:last_site_visited', Value: window.location.hostname });
                attributeList.push(attribute);
                cognitoUser.updateAttributes(attributeList, (err, result) => console.log('COGNITO RESULTS:', err, result));

                setCognito({ ...cognito, ...{ page:'success', authenticationDetails:authenticationDetails, cognitoUser:cognitoUser }});
                location.reload();
            },
            onFailure: function(err) {
                if (err.code =='UserNotConfirmedException') {
                    setCognito({ ...cognito, ...{ page:'confirm', authenticationDetails:authenticationDetails, cognitoUser:cognitoUser }});
                } else {
                    setCognito({ ...cognito, ...{ errors:err }});
                }
            },
            newPasswordRequired: function(userAttributes, requiredAttributes) {
                // delete userAttributes.name;
                delete userAttributes.email_verified;
                delete userAttributes.email;
                delete userAttributes.phone_number;
                delete userAttributes.phone_number_verified

                var sessionUserAttributes = userAttributes;
                setCognito({ ...cognito, ...{ page:'change', authenticationDetails:authenticationDetails, cognitoUser:cognitoUser, sessionUserAttributes:sessionUserAttributes }});
            }
        });
    }

    function switchToLostPassword(e) {
        setCognito({ ...cognito, ...{ page:'password' }});
    }

    var submitButtonStyles = {
        color: '#fff',
        backgroundColor: settings.design?.colors?.primary_color,
    }

    return (
        <form id="form-cognito" onSubmit={handleSubmit} role="form" aria-label="sign in">
            {cognito.page == 'confirmed' && <div class='form-subtitle'><p>Your Email Address Has Been Confirmed</p></div>}
            <div class='form-subtitle' dangerouslySetInnerHTML={{ __html:cognito.sign_in_message }} />

            {cognito.errors && <div class='form-error'>{cognito.errors.message}</div>}

            <div class='field-group'>
                <div class='field-name'>
                    <label htmlFor='username'>{cognito.email_field_label} <span class="required">*</span></label>
                </div>
                <div class='field-input'>
                    <input id="username" type='text' name='username' placeholder='Email Address' required value={username} onChange={handleUsername} />
                </div>
            </div>

            <div class='field-group'>
                <div class='field-name'>
                    <label htmlFor='password'>Password <span class="required">*</span></label>
                </div>
                <div class='field-input'>
                    <input id="password" type='password' name='password' placeholder='Password' minlength="8" required onChange={handlePassword} />
                </div>
                <div class="field-options">
                    <Clicker class="option" process={switchToLostPassword}>LOST PASSWORD?</Clicker>
                </div>
            </div>

            <div class="form-required">* Required Fields.</div>
            <div class="form-buttons">
                <button style={submitButtonStyles} type="submit" form="form-cognito">SIGN IN</button>
            </div>
            {cognito.sign_up_enabled && <SignUp />}
        </form>
    );
};

const SignUp = (props) => {
    const [cognito, setCognito] = useContext(CognitoContext);

    function switchToSignUp(e) {
        setCognito({ ...cognito, ...{ page:'signup' }});
    }

    return (
        <div class="form-options">
            <div>Need an account?</div>
            <Clicker class="switch-forms" process={switchToSignUp}>Sign Up</Clicker>
        </div>
    );
}

export default Start;