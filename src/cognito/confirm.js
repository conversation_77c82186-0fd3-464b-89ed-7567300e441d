import React, { useState, useContext } from 'react';
var AmazonCognitoIdentity = require('amazon-cognito-identity-js');

import { CognitoProvider, CognitoContext } from "src/cognito/context";

const Clicker = React.lazy(() => import('src/helpers/clicker'));

const Start = ({ settings }) => {
    const [cognito, setCognito]   = useContext(CognitoContext);
    const [username, setUsername] = useState('');
    const [code, setCode]         = useState('');

    function handleUsername(e) {
        setUsername(e.target.value);
    }
    
    function handleCode(e) {
        setCode(e.target.value);
    }

    function handleSubmit(e) {
        e.preventDefault();

        var userPool = new AmazonCognitoIdentity.CognitoUserPool(cognito.poolData);
        var cognitoUser = new AmazonCognitoIdentity.CognitoUser({ Username: username, Pool: userPool });

        cognitoUser.confirmRegistration(code, true, function(err, result) {
            if (err) {
                setCognito({ ...cognito, ...{ errors:err }});
            } else {
                var attributeList = [];
                var attribute = new AmazonCognitoIdentity.CognitoUserAttribute({ Name: 'custom:last_site_visited', Value: window.location.hostname });
                attributeList.push(attribute);
                cognitoUser.updateAttributes(attributeList, (err, result) => console.log('COGNITO RESULTS:', err, result));
                setCognito({ ...cognito, ...{ page:'confirmed' }});
            }
        });
    }

    function switchToSignUp(e) {
        setCognito({ ...cognito, ...{ page:'signup' }});
    }

    function switchToLostPassword(e) {
        setCognito({ ...cognito, ...{ page:'password' }});
    }

    var submitButtonStyles = {
        color: '#fff',
        backgroundColor: settings.design?.colors?.primary_color,
    }

    return (
        <form id="form-cognito" onSubmit={handleSubmit} role="form" aria-label="confirm account">
            <div class='form-subtitle'>Confirm your account with this form.</div>

            {cognito.errors && <div class='form-error'>{cognito.errors.message}</div>}


            <div class='field-group'>
                <div class='field-name'>
                    <label htmlFor='username'>{cognito.email_field_label} <span class="required">*</span></label>
                </div>
                <div class='field-input'>
                    <input id="username" type='text' name='username' placeholder='Email Address' onChange={handleUsername} />
                </div>
            </div>

            <div class='field-group'>
                <div class='field-name'>
                    <label htmlFor='code'>Confirmation Code <span class="required">*</span></label>
                </div>
                <div class='field-input'>
                    <input id="code" type='text' name='code' placeholder='Confirmation Code' onChange={handleCode} />
                </div>
            </div>

            <div class="form-required">* Required Fields.</div>
            <div class="form-buttons">
                <button style={submitButtonStyles} type="submit" form="form-cognito">Confirm Account</button>
            </div>
            <div class="form-options">
                <Clicker class="switch-forms" process={switchToSignUp}>SIGN UP PAGE</Clicker>
            </div>
        </form>
    );
};

export default Start;