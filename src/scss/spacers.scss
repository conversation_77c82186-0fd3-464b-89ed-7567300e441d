@import "src/scss/variables.scss";

// Global Spacer classes
// => these should be used to determine module / container spacing

#app {
    #modules-container {
        .container-spacer { // class still needs adjustment
            padding-left: 20px;
            padding-right: 20px;
        
            @media (min-width: get-breakpoint(sm)) {
                padding-left: 44px;
                padding-right: 44px;
            }
        }
        
        .module-spacer {
            padding-top: 50px;
            padding-bottom: 50px;
        
            @media (min-width: get-breakpoint(md)) {
                padding-top: 60px;
                padding-bottom: 60px;
            }
        
            @media (min-width: get-breakpoint(lg)) {
                padding-top: 75px;
                padding-bottom: 75px;
            }
        }
        
    }
}
