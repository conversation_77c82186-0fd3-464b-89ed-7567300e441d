@import 'src/scss/variables.scss';

.hidden { visibility: hidden; }
.hide { display: none !important; }

@media screen and (max-width: $break-mobile) {
    .hide-mobile {
        display: none;
    }
}




// OPACITY.
.o9 { opacity: 0.9; }
.o8 { opacity: 0.8; }
.o7 { opacity: 0.7; }
.o6 { opacity: 0.6; }
.o5 { opacity: 0.5; }
.o4 { opacity: 0.4; }
.o3 { opacity: 0.3; }
.o2 { opacity: 0.2; }
.o1 { opacity: 0.1; }





/* DESKTOP */

@media screen and (max-width:699px) {
    .desktop {
        visibility: hidden !important;
        display: none !important;
    }
}

/* MOBILE */

@media screen and (min-width:699px) {
    .mobile {
        visibility: hidden !important;
        display: none !important;
    }
}

@media screen and (max-width:599px),
screen and (min-width:699px) {
    .sbt600-700 {
        visibility: hidden !important;
        display: none !important;
    }
}

/* HIDE LESS EQUAL THAN */

@media screen and (max-width:1100px) {
    .hlet1100 {
        visibility: hidden !important;
        display: none !important;
    }
}

@media screen and (max-width:1000px) {
    .hlet1000 {
        visibility: hidden !important;
        display: none !important;
    }
}

@media screen and (max-width:900px) {
    .hlet900 {
        visibility: hidden !important;
        display: none !important;
    }
}

@media screen and (max-width:800px) {
    .hlet800 {
        visibility: hidden !important;
        display: none !important;
    }
}

@media screen and (max-width:700px) {
    .hlet700 {
        visibility: hidden !important;
        display: none !important;
    }
}

@media screen and (max-width:600px) {
    .hlet600 {
        visibility: hidden !important;
        display: none !important;
    }
}

@media screen and (max-width:500px) {
    .hlet500 {
        visibility: hidden !important;
        display: none !important;
    }
}

@media screen and (max-width:400px) {
    .hlet400 {
        visibility: hidden !important;
        display: none !important;
    }
}

/* HIDE LESS THAN */

@media screen and (max-width:1099px) {
    .hlt1100 {
        visibility: hidden !important;
        display: none !important;
    }
}

@media screen and (max-width:999px) {
    .hlt1000 {
        visibility: hidden !important;
        display: none !important;
    }
}

@media screen and (max-width:899px) {
    .hlt900 {
        visibility: hidden !important;
        display: none !important;
    }
}

@media screen and (max-width:799px) {
    .hlt800 {
        visibility: hidden !important;
        display: none !important;
    }
}

@media screen and (max-width:699px) {
    .hlt700 {
        visibility: hidden !important;
        display: none !important;
    }
}

@media screen and (max-width:599px) {
    .hlt600 {
        visibility: hidden !important;
        display: none !important;
    }
}

@media screen and (max-width:499px) {
    .hlt500 {
        visibility: hidden !important;
        display: none !important;
    }
}

@media screen and (max-width:399px) {
    .hlt400 {
        visibility: hidden !important;
        display: none !important;
    }
}


/* HIDE GREATER EQUAL THAN */

@media screen and (min-width:1100px) {
    .hget1100 {
        visibility: hidden !important;
        display: none !important;
    }
}

@media screen and (min-width:1000px) {
    .hget1000 {
        visibility: hidden !important;
        display: none !important;
    }
}

@media screen and (min-width:900px) {
    .hget900 {
        visibility: hidden !important;
        display: none !important;
    }
}

@media screen and (min-width:800px) {
    .hget800 {
        visibility: hidden !important;
        display: none !important;
    }
}

@media screen and (min-width:700px) {
    .hget700 {
        visibility: hidden !important;
        display: none !important;
    }
}

@media screen and (min-width:600px) {
    .hget600 {
        visibility: hidden !important;
        display: none !important;
    }
}

@media screen and (min-width:500px) {
    .hget500 {
        visibility: hidden !important;
        display: none !important;
    }
}

@media screen and (min-width:400px) {
    .hget400 {
        visibility: hidden !important;
        display: none !important;
    }
}


/* HIDE GREATER THAN */

@media screen and (min-width:1101px) {
    .hgt1100 {
        visibility: hidden !important;
        display: none !important;
    }
}

@media screen and (min-width:1001px) {
    .hgt1000 {
        visibility: hidden !important;
        display: none !important;
    }
}

@media screen and (min-width:901px) {
    .hgt900 {
        visibility: hidden !important;
        display: none !important;
    }
}

@media screen and (min-width:801px) {
    .hgt800 {
        visibility: hidden !important;
        display: none !important;
    }
}

@media screen and (min-width:701px) {
    .hgt700 {
        visibility: hidden !important;
        display: none !important;
    }
}

@media screen and (min-width:601px) {
    .hgt600 {
        visibility: hidden !important;
        display: none !important;
    }
}

@media screen and (min-width:501px) {
    .hgt500 {
        visibility: hidden !important;
        display: none !important;
    }
}

@media screen and (min-width:401px) {
    .hgt400 {
        visibility: hidden !important;
        display: none !important;
    }
}

.slick-arrow.slick-disabled {
    display: none !important;
}