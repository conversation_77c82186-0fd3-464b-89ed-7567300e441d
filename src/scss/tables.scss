.table-container {
    overflow-y: auto;
    table {
        border: none;
        margin: auto;
        thead {
            th, td {
                padding: 1rem;
                font-size: 1.25rem;
                font-weight: bold;
                text-align: left;
                height: auto !important;
                h3 {
                    font-size: 1.25rem;
                    font-weight: bold;
                    line-height: 1.25;
                }
                h4 {
                    font-size: .875rem;
                    font-weight: bold;
                }
                small {
                    font-size: .75rem;
                }
            }
            td {
                // text-align: center;
            }
            tr:nth-child(-n+2) > th:first-of-type {
                font-size: 0;
                border: none;
            }
            .blue {
                background-color: #6b89a5;
            }
            .gold {
                background-color: #daaf2f;
            }
            .platinum {
                background-color: #bebebe;
            }
        }
        tbody {
            tr {
                th, td {
                    padding: 1rem;
                    font-size: .875rem;
                    font-weight: normal;
                    text-align: left;
                    min-width: 150px;
                    &.check {
                        &:after {
                            content: '\2713';
                            font-size: 1.5rem;
                            color: inherit;
                            display: flex;
                            justify-content: center;
                        }
                        &.blue {
                            color:#6b89a5;
                        }
                        &.gold {
                            color: #daaf2f;
                        }
                        &.platinum {
                            color: #bebebe;
                        }
                    }
                    p {
                        font-size: inherit !important;
                        font-weight: inherit !important;
                    }
                }
                td {
                    text-align: center;
                }
                &.section {
                    background-color: #f0f4f7;
                    th {
                        font-weight: bold;
                        font-size: 1rem;
                    }
                }
            }
        
        }
    
    }
}