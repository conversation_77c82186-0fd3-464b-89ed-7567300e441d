$max-width: 1200px;




@function get-breakpoint($key) {
    @if map-has-key($py-breakpoints, $key) {
        @return map-get($py-breakpoints, $key);
    }

    @warn "Unknown `#{$key}` in $breakpoints.";
    @return null;
}

// @function strip-unit($number) { 
//     @if type-of($number) == 'number' and not unitless($number) {
//         @return calc($number / ($number * 0 + 1));
//     }

//     @return $number;
// }

// New global breakpoints (Ex usage: breakpoint(sm)
// => gets imported into "src/styles.js" component & set in styled-components theme as well
$py-breakpoints: (
    xs: 320,
    sm: 640px,
    md: 1024px,
    lg: 1250px,
    xl: 1500px,
 
    // specific / legacy
    mobile: 688px,
    medium-dev: 768px,
    tablet: 992px,
);

$break-xsmall: get-breakpoint(xs);         // 320
$break-small: get-breakpoint(sm);          // 640
$break-mobile: get-breakpoint(mobile);     // 688
$break-medium: get-breakpoint(medium-dev); // 768
$break-tablet: get-breakpoint(tablet);     // 992
$break-large: get-breakpoint(md);          // 1024
$break-desktop: get-breakpoint(lg);        // 1250
$break-xlarge: get-breakpoint(xl);         // 1500
// :export { // exporting breakpoints to be used in Javascript
//     breakpointXs: strip-unit(get-breakpoint(xs));
//     breakpointSm: strip-unit(get-breakpoint(sm));
//     breakpointMd: strip-unit(get-breakpoint(md));
//     breakpointLg: strip-unit(get-breakpoint(lg));
//     breakpointXl: strip-unit(get-breakpoint(xl));
// };



// BREAK POINTS.
// TRUE BREAK POINTS FROM THE SWISHER SWEETS PROJECT RESEARCH. HANDS OFF!!!!
 $breakpoint-xsmall: 360px;
  $breakpoint-small: 390px;
 $breakpoint-mobile: 428px;
 $breakpoint-medium: 640px;
 $breakpoint-tablet: 768px;
  $breakpoint-large: 960px;
 $breakpoint-larger: 1024px;
$breakpoint-largest: 1280px;
$breakpoint-desktop: 1280px;
$breakpoint-maximum: 1280px;
$breakpoint-padding: 1300px;
 $breakpoint-padded: 1300px;
