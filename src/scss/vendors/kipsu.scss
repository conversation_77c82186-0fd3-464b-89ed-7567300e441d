@import "src/scss/variables.scss";

body {
    .kipsu-chat {
        position: fixed;
        // top: 60%;
        // transform: translateY(-50%);
        bottom: 36%;
        right: 0;
        width: 50px;
        height: 50px;
        z-index: 1000;
        background: #D95020;

        .kipsu-chat__link {
            // display: block;
            // width: 50px;
            // height: 50px;
            display: flex;
            justify-content: center;
            align-items: center;
            width: 100%;
            height: 100%;
        }

        .kipsu-chat__icon::before {
            display: block;
            width: 27px;
            height: auto;
            // margin: 0 auto;
            // transform: translateY(12px);
        }

        .icon-common-chat::before {
            // content: "\6e";
        }
    }
}

.kipsu-chat .kipsu-chat__link .kipsu-chat__icon {
    // font-family: "Font Awesome 6 Pro";
    // font-weight: 900;
    // display: inline-block;
    // font-size: 2.5rem;
}

.kipsu-chat .kipsu-chat__link .kipsu-chat__icon:before {
    // content: "\f4b6";
    // content: "\1F5E8";
    // content: url("../../../public/favicon-32x32.png");
    content: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 512 512'%3E%3C!--! Font Awesome Pro 6.1.1 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2022 Fonticons, Inc. --%3E%3Cpath fill='%23fff' d='M447.1 0h-384c-35.25 0-64 28.75-64 63.1v287.1c0 35.25 28.75 63.1 64 63.1h96v83.98c0 9.836 11.02 15.55 19.12 9.7l124.9-93.68h144c35.25 0 64-28.75 64-63.1V63.1C511.1 28.75 483.2 0 447.1 0zM464 352c0 8.75-7.25 16-16 16h-160l-80 60v-60H64c-8.75 0-16-7.25-16-16V64c0-8.75 7.25-16 16-16h384c8.75 0 16 7.25 16 16V352z'/%3E%3C/svg%3E");
}