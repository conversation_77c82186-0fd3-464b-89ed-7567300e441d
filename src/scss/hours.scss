@import "src/scss/variables.scss";

.hours-module {
    width: 100%;
    padding: 2rem 0;
    h2 {
        text-align: center;
        line-height: 1.5;
        margin-bottom: 0.5rem;
    }
    .special-instructions {
        text-align: center;
    }
    .custom-hours-message {
        text-align: center;
    }
    .current-week {
        text-align: center;
        margin-bottom: 1.5rem;
    }
    .hours-row {
        display: inline-flex;
        justify-content: space-between;
        // text-transform: capitalize; // can't because french days are lowercase
        width: 100%;
        .open-label {
            display: none;
        }
        span {
            text-transform: lowercase;
            text-align: right;
            &.custom {
                text-transform: none;
            }
        }
    }
    .hours-list {
        p.current-day {
            font-weight: bold;
        }
        .list-row {
            // text-transform: capitalize; // can't because french days are lowercase
            margin-bottom: 0.25rem;
            span {
                text-transform: lowercase;
            }
        }
    }

    .reduced {
        padding: 1rem 0;
        text-align: center;
        p {
            margin-bottom: 0.25rem;
        }
    }
    .carousel {
        overflow: hidden;
        .slick-slide {
            height: auto;
        }
        .slick-arrow {
            &.slick-prev {
                left: -1rem;
            }
            &.slick-next {
                right: -1rem;
            }
        }

        &.table {
            margin: 30px auto;
            padding: 20px 40px;
        }
        &.card {
            .slick-list {
                .slick-track {
                    padding: 1rem 0;
                    .slick-slide {
                        margin-top: 10px;
                        margin-bottom: 10px;
                        padding-top: 10px;
                        padding-bottom: 10px;
                        opacity: 0.5;
                        &.slick-center {
                            opacity: 1;
                            -webkit-box-shadow: 0px 5px 20px -1px rgba(0, 0, 0, 0.75);
                            -moz-box-shadow: 0px 5px 20px -1px rgba(0, 0, 0, 0.75);
                            box-shadow: 0px 5px 20px -1px rgba(0, 0, 0, 0.75);
                        }
                        .hours-set {
                            width: 80%;
                            margin: auto;
                            padding: 15px 10px;
                            text-align: center;
                            .hours-row {
                                border-bottom: 1px solid;
                            }
                        }
                    }
                }
            }
            .slick-arrow {
                &.slick-prev {
                    left: 1rem;
                }
                &.slick-next {
                    right: 1rem;
                }
            }
        }
    }
    .reduced-widget {
        .title {
            margin-bottom: 1rem;
        }
    }
    @media (min-width: $break-medium) {
        .hours-container {
            position: relative;
            column-count: 2;
            column-gap: 2rem;
            &:after {
                content: "";
                position: absolute;
                border-right: 1px solid;
                height: 100%;
                top: 0;
                left: 50%;
                transform: translateX(-50%);
            }
        }
    }
    @media (min-width: $break-tablet) {
        .hours-container {
            column-count: 4;
            column-gap: 3rem;
            &:before {
                content: "";
                position: absolute;
                height: 100%;
                border-right: 1px solid;
                left: 50%;
                transform: translateX(-50%);
            }
            &:after {
                border-left: 1px solid;
                width: 52%;
            }
        }
    }
}
.open-label {
    display: none;
}

// store hours style: special
.hours-special {
    width: 100%;
    h2 { margin-bottom: 1rem; }

    .hours-container {
        .current-week {
            font-weight: bolder;
            margin-bottom: 1.5rem;
        }
        .hours-row {
            // text-transform: capitalize;
            margin-bottom: 1.5rem;
            display: grid;
            grid-auto-flow: row;
            grid-template-columns: 40% 60%;
            .custom {
                text-transform: initial;
            }
            span {
                text-transform: lowercase;
                span.open-label {
                    display: inline;
                    text-transform: capitalize;
                }
            }
        }
    }
    @media (min-width: 640px) {
        h2 {
            margin-bottom: 2rem;
        }
        .hours-container {
            column-count: 2;
            @media (min-width: 992px) {
                max-width: 85%;
                column-gap: 8%;
            }
        }
    }
}

.hours-daily {
    .hours-container {
        text-align: center;
        .hours-block {
            margin: 1rem 1rem 2rem;
        }
        @media (min-width: 640px) {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            .hours-block {
                margin: 1rem 2rem 2rem;
                @media (min-width: 1366px) {
                    margin: 2rem;
                }
            }
        }
    }
}
