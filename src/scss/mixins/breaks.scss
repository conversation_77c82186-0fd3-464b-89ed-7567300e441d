@import 'src/scss/variables.scss';

$breakpoints: (
     'xsmall':(max-width: $breakpoint-xsmall),
      'small':(max-width: $breakpoint-small),
     'mobile':(max-width: $breakpoint-mobile),
     'medium':(max-width: $breakpoint-medium),
     'tablet':(max-width: $breakpoint-tablet),
      'large':(max-width: $breakpoint-large),
     'larger':(max-width: $breakpoint-larger),
    'largest':(max-width: $breakpoint-largest),
    'desktop':(max-width: $breakpoint-desktop),
    'maximum':(max-width: $breakpoint-maximum),
     'padded':(max-width: $breakpoint-padded)
) !default;

@mixin breakpoint($breakpoint) {
    @if map-has-key($breakpoints, $breakpoint) {
        @media #{inspect(map-get($breakpoints, $breakpoint))} {
            @content;
        }
    }
    @else {
        @warn "Breakpoint: no value could be retrieved from `#{$breakpoint}`. Available breakpoints are: #{map-keys($breakpoints)}.";
    }
}

$breakless: (
     'xsmall':(max-width: $breakpoint-xsmall),
      'small':(max-width: $breakpoint-small),
     'mobile':(max-width: $breakpoint-mobile),
     'medium':(max-width: $breakpoint-medium),
     'tablet':(max-width: $breakpoint-tablet),
      'large':(max-width: $breakpoint-large),
     'larger':(max-width: $breakpoint-larger),
    'largest':(max-width: $breakpoint-largest),
    'desktop':(max-width: $breakpoint-desktop),
    'maximum':(max-width: $breakpoint-maximum),
     'padded':(max-width: $breakpoint-padded),
) !default;

@mixin breakless($breakpoint) {
    @if map-has-key($breakless, $breakpoint) {
        @media #{inspect(map-get($breakless, $breakpoint))} {
            @content;
        }
    }
    @else {
        @warn "Breakless: no value could be retrieved from `#{$breakpoint}`. Available breakpoints are: #{map-keys($breakless)}.";
    }
}

$breakmore: (
     'xsmall':(min-width: $breakpoint-xsmall),
      'small':(min-width: $breakpoint-small),
     'mobile':(min-width: $breakpoint-mobile),
     'medium':(min-width: $breakpoint-medium),
     'tablet':(min-width: $breakpoint-tablet),
      'large':(min-width: $breakpoint-large),
     'larger':(min-width: $breakpoint-larger),
    'largest':(min-width: $breakpoint-largest),
    'desktop':(min-width: $breakpoint-desktop),
    'maximum':(min-width: $breakpoint-maximum),
     'padded':(min-width: $breakpoint-padded)
) !default;

@mixin breakmore($breakpoint) {
    @if map-has-key($breakmore, $breakpoint) {
        @media #{inspect(map-get($breakmore, $breakpoint))} {
            @content;
        }
    }
    @else {
        @warn "Breakmore: no value could be retrieved from `#{$breakpoint}`. Available breakpoints are: #{map-keys($breakmore)}.";
    }
}