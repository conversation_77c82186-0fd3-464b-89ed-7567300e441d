@import 'src/scss/variables.scss';

div.flexbox,
div.fbox {
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;

    // GAPS
    &.gap  { gap: 1rem; }
    &.gap1 { gap: 1rem; }
    &.gap2 { gap: 2rem; }
    &.gap3 { gap: 3rem; }
    &.gap4 { gap: 4rem; }
    &.gap5 { gap: 5rem; }
    &.gap-tight { gap: 0.4rem; }

    &.gap01 { gap: 0.1rem; }
    &.gap11 { gap: 1.1rem; }
    &.gap02 { gap: 0.2rem; }
    &.gap12 { gap: 1.2rem; }
    &.gap03 { gap: 0.3rem; }
    &.gap13 { gap: 1.3rem; }
    &.gap04 { gap: 0.4rem; }
    &.gap14 { gap: 1.4rem; }
    &.gap05 { gap: 0.5rem; }
    &.gap15 { gap: 1.5rem; }
    &.gap06 { gap: 0.6rem; }
    &.gap16 { gap: 1.6rem; }
    &.gap07 { gap: 0.7rem; }
    &.gap17 { gap: 1.7rem; }
    &.gap08 { gap: 0.8rem; }
    &.gap18 { gap: 1.8rem; }
    &.gap09 { gap: 0.9rem; }
    &.gap19 { gap: 1.9rem; }

    &.row,
    &.columns,
    &.fcols {
        flex-direction: row;
    }

    &.reverse {
        flex-direction: column-reverse;
    }

    &.column,
    &.fcol {
        flex-direction: column;
        flex-flow: column;
        -webkit-flex-flow: column;
        &.reverse {
            flex-direction: column-reverse;
            flex-flow: column-reverse;
            -webkit-flex-flow: column-reverse;
        }
    }
    
    @media screen and (max-width: $breakpoint-mobile) {
        &.column-mobile {
            flex-direction: column;
            flex-flow: column;
            -webkit-flex-flow: column;
        }
        &.reverse-mobile {
            flex-direction: column-reverse;
        }
    }

    &.wrap {
        flex-wrap: wrap;
        -webkit-flex-wrap: wrap;
    }

    &.nowrap {
        flex-wrap: nowrap;
        -webkit-flex-wrap: nowrap;
    }

    &.align {
        align-items: center;
    }

    &.top {
        align-items: flex-start;
    }

    &.justify {
        justify-content: center;
    }

    &.end {
        justify-content: flex-end;
    }

    &.between {
        justify-content: space-between;
    }

    &.full {
        width: 100%;
    }

    .flex {
        flex: 0 1 auto;
        -webkit-flex: 0 1 auto;

        &.push {
            flex: 1 0 auto;
            -webkit-flex: 1 0 auto;
        }

        &.pull {
            flex: 0 1 !important;
            -webkit-flex: 0 1 !important;
        }

        &.full {
            flex: 0 0 100%;
        }

        &.grey {
            background-color: rgba(0, 0, 0, 0.05);
        }

        &.nowrap {
            white-space: nowrap !important;
        }

        &.basis {
            flex-basis: auto !important;
        }

        &.even {
            flex-grow: 1 !important;
        }

    }

    .flex1 {
        flex: 1;
        -webkit-flex: 1;
    }

    .flex2 {
        flex: 2;
        -webkit-flex: 2;
    }

    .flex3 {
        flex: 3;
        -webkit-flex: 3;
    }

    .flex4 {
        flex: 4;
        -webkit-flex: 4;
    }

    .flex5 {
        flex: 5;
        -webkit-flex: 5;
    }

    .flex6 {
        flex: 6;
        -webkit-flex: 6;
    }

    .flex7 {
        flex: 7;
        -webkit-flex: 7;
    }

    .flex8 {
        flex: 8;
        -webkit-flex: 8;
    }

    .flex9 {
        flex: 9;
        -webkit-flex: 9;
    }
}