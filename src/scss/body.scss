@import "~foundation-sites/scss/foundation.scss";
@include foundation-xy-grid-classes;
@include foundation-float-classes;
@include foundation-flex-classes;
@include foundation-visibility-classes;
@include foundation-prototype-classes;

html {
    position: relative;
    margin: 0px;
    padding: 0px;
}

body {
    position: relative;
    margin: 0px;
    padding: 0px;
    font-size: 1rem;
    &.modal-open {
        overflow: hidden;
    }
    &:has(#bottomMobileBar #location-selector-menu.open) {
        overflow: hidden;
    }
}

img {
    max-width: 100%;
    height: auto;
}

.p-10 {
    padding: 10px;
}

.p-20 {
    padding: 20px;
}

.pv-10 {
    padding-top: 10px;
    padding-bottom: 10px;
}

.ph-20 {
    padding-left: 20px;
    padding-right: 20px;
}

.ph-25 {
    padding-left: 25px;
    padding-right: 25px;
}

.ph-30 {
    padding-left: 30px;
    padding-right: 30px;
}

.br-5 {
    border-radius: 5px;
}

.br-10 {
    border-radius: 10px;
}
