h1,
h2,
h3,
h4,
h5,
h6 {
    margin: 0px;
    padding: 0px;
    font-size: inherit;
    letter-spacing: 0.5px;
    line-height: 1.25;
    margin-bottom: 1rem;
}

h1 {
    font-size: 3.5rem;

    @media screen and (max-width: 767px) {
        font-size: 2.1875rem;
    }
}

h2 {
    font-size: 2.5rem;

    @media screen and (max-width: 767px) {
        font-size: 1.3125rem;
    }
}

h3 {
    font-size: 1.75rem;

    @media screen and (max-width: 767px) {
        font-size: 1.2rem;
    }
}

h4 {
    font-size: 1.125rem;

    @media screen and (max-width: 767px) {
        font-size: 0.875rem;
    }
}

p {
    margin-block-start: 0px;
    font-size: 0.875rem;
    line-height: 1.25;
    @media screen and (min-width: 768px) {
        font-size: 1rem;
    }
}

ul, ol {
    font-size: 0.875rem;
    line-height: 1.25;
    @media screen and (min-width: 768px) {
        font-size: 1rem;
    }
}

.bold {
    font-weight: 800;
}

a,
a.link,
a:link,
a:visited,
.link {
    font-family: inherit;
    font-size: inherit;
    text-decoration: none;
}

.link:hover {
    cursor: pointer;
}

a:hover,
a:active {
    // text-decoration: none;
    cursor: pointer;
}
a:focus {
    text-decoration: underline;
}
#header a:focus {
    text-decoration: none;
}
.redirect {
    cursor: pointer;
}

.underline, a.underline {
    text-decoration: underline;
}

.capitalize,
.cap {
    text-transform: capitalize;
}

.uppercase,
.upper {
    text-transform: uppercase;
}

.hover:hover,
[redirect] {
    cursor: pointer;
}

.white {
    color: white !important;
}

.black {
    color: black !important;
}

.crimson {
    color: crimson !important;
}

.left {
    text-align: left !important;
}

.right {
    text-align: right !important;
}

.center {
    text-align: center;
}

.wrap {
    word-wrap: break-word;
}

.nowrap {
    white-space: nowrap;
}

.strong,
.bold {
    font-weight: bold;
}

.stronger {
    font-weight: bolder;
}

.locked {
    margin-left: .5rem;
}
.color-splash-hr {
    max-width: 100%;
}