@import 'src/scss/variables.scss';

input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus
input:-webkit-autofill,
textarea:-webkit-autofill,
textarea:-webkit-autofill:hover
textarea:-webkit-autofill:focus,
select:-webkit-autofill,
select:-webkit-autofill:hover,
select:-webkit-autofill:focus {
    -webkit-box-shadow:0px 0px 0px 1000px white inset !important;
    -webkit-text-fill-color:black !important;
}

::placeholder               { color:rgba(0,0,0,0.3); }
::-webkit-input-placeholder { color:rgba(0,0,0,0.3); }
::-moz-placeholder          { color:rgba(0,0,0,0.3); }
:-moz-placeholder           { color:rgba(0,0,0,0.3); }
:-ms-input-placeholder      { color:rgba(0,0,0,0.3); }

.placecenter::placeholder               { text-align:center; }
.placecenter::-webkit-input-placeholder { text-align:center }
.placecenter::-moz-placeholder          { text-align:center }
.placecenter:-moz-placeholder           { text-align:center }
.placecenter:-ms-input-placeholder      { text-align:center }

input,
select {
    font-family: 'Open Sans', sans-serif;
    outline: 0px !important;
    // border-radius: 5px !important;
    box-sizing: border-box;
}

input[type="text"],
input[type="email"],
input[type="password"],
select {
    width: 100%;
    font-size: 0.813rem;
    padding: 6px;
    background-color: #f4f6f8;
    -webkit-box-shadow: none;
    box-shadow: none;
    outline: none;
    // border: 2px solid rgba(0, 0, 0, 0.1);
}

input.blend {
    background: transparent;
    -webkit-box-shadow: none;
    box-shadow: none;
    outline: none;
    border: none;
}

select {
    padding: 6px;
    height: 35px;
}

textarea {
    font-family: 'Open Sans', sans-serif;
    border-radius: 0px 0px 0px 0px;
    resize: vertical;
    overflow: hidden;
    box-sizing: border-box;
    padding: 6px;
    font-size: 0.813rem;
    border: 2px solid rgba(0, 0, 0, 0.1);
    width: 100%;
    background-color: #f4f6f8;
    -webkit-box-shadow: none;
    -webkit-appearance: none;
    box-shadow: none;
    outline: none;
}

input.white {
    color: black !important;
    background-color: white !important;
}

input.clean {
    border: none !important;
}

input.search {
    margin: 0px;
    border-top: 0px;
    border-left: 0px;
    border-right: 0px;
    border-bottom: 2px solid rgba(0,0,0,0.1);
    background-color: transparent;
}


input.fault,
select.fault,
textarea.fault {
    border-color: hotpink;
}

input.success,
select.success,
textarea.success {
    border-color: lime;
}

// this is to prevent automatically zooming in to the react select fields on mobile
@media (max-width: $break-medium) {
    .react-select__control, .react-select__control.react-select__control--is-focused {
        font-size: 1rem !important;
    }
}