@import "variables.scss";

body {
    position: relative;
    margin: 0px;
    padding: 0px;
    height: 100%;

    #app {
        position: relative;
        height: 100%;
        // overflow-x: hidden;
        &.no-margin {
            margin-top: 0px !important;
        }
        &.fullpage {
            min-height: 100%;
        }
        @media (max-width: 1199px) {
            &:has(.floating-hamburger) {
                margin-top: 0px !important;
            }
        }
        .trunk,
        .container {
            margin: 0px auto;
            // width: 100%;
            max-width: 1250px;

            &.spacer {
                // @media screen and (max-width: $break-desktop) {
                //     margin-left: 20px;
                //     margin-right: 20px;
                // }
                @media screen and (min-width: 768px) {
                    // margin-left: 20px;
                    // margin-right: 20px;
                    padding-left: 20px;
                    padding-right: 20px;
                }
            }
        }

        #search-results {
            padding-top: 2rem;
            padding-bottom: 8rem;
            h1 {
                margin-bottom: 1rem;
            }
            .cell {
                &.medium-8 {
                    margin-bottom: 2rem;
                }
                @media (min-width: 640px) {
                    &.medium-4 {
                        margin-bottom: 2rem;
                    }
                }
                @media (min-width: 1024px) {
                    &.medium-4 {
                        margin-bottom: 3rem;
                    }
                    &.medium-8 {
                        margin-bottom: 4rem;
                    }
                }
            }
            .search-title {
                margin-bottom: 2rem;
                @media (min-width: 1024px) {
                    margin-bottom: 4rem;
                }
            }

            .content-wrapper {
                .title {
                    margin-bottom: 0.5rem;
                }
                .link {
                    display: block;
                    margin: 1.5rem 0;
                }
            }
        }
        @media (min-width: 1200px) {
            #header.absolute + #search-results {
                padding: 270px 40px 200px;
            }
        }
        .post-submission-link {
            margin: 20px 0;
            display: flex;
            flex-wrap: wrap;
            align-items: center;
            justify-content: flex-end;
            .submission-description {
                margin: 1rem 1rem 1rem 0;
            }
        }
        .mvk-responsive-video {
            & > .overlay {
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                z-index: 1;
                cursor: pointer;
                &:after {
                    content: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><!--!Font Awesome Free 6.6.0 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free Copyright 2024 Fonticons, Inc.--><path fill="white" d="M464 256A208 208 0 1 0 48 256a208 208 0 1 0 416 0zM0 256a256 256 0 1 1 512 0A256 256 0 1 1 0 256zM188.3 147.1c7.6-4.2 16.8-4.1 24.3 .5l144 88c7.1 4.4 11.5 12.1 11.5 20.5s-4.4 16.1-11.5 20.5l-144 88c-7.4 4.5-16.7 4.7-24.3 .5s-12.3-12.2-12.3-20.9l0-176c0-8.7 4.7-16.7 12.3-20.9z"/></svg>');
                    position: absolute;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    width: 120px;
                    opacity: 0.5;
                    transition: .3s;
                    pointer-events: none;
                }
                &:hover:after {
                    opacity: 1;
                }
            }
        }
        .store-iframe-container {
            max-width: 1400px;
            margin: auto;
            padding-bottom: 2rem;
            iframe {
                padding: 0 .625rem;
                box-sizing: border-box;
                border: none;
            }
        }
    }
}
