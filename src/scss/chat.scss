.chat-button-content {
    margin-right: 1rem !important;
    box-shadow: none !important;
    & > div {
        display: flex !important;
        align-items: center;
        flex-direction: row-reverse;
        justify-content: flex-end; 
        border: 2px solid #fff !important;
        background: #D5A619 !important;
        
        #chatWindowOption3 {
            line-height: 2.5;
            color: #1B2635 !important;
            font-size: 0.813rem !important;
            font-weight: bold !important;
            padding-left: 1rem !important;
            text-transform: capitalize;
        }
        img {
            border: none !important;
            position: absolute;
            bottom: -22px;
            right: -95px;
            background: none !important;
            width: auto !important;
            height: auto !important;
            border-radius: 0 0 32px 0 !important;
        }
        .hideButton {
            top: -30px !important;
            right: 0 !important;
        }
        
    }
   
}

.operator-details img {
    width: auto !important;
}
