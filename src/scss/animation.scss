.slide-in {
    z-index: 10; /* to position it in front of the other content */
    position: relative;
    overflow: hidden; /* to prevent scrollbar appearing */

    &.from-left {
        left: 0;

        .slide-in-content {
            transform: translateX(-100%);
            -webkit-transform: translateX(-100%);
        }
    }

    &.from-right {
        right: 0;

        .slide-in-content {
            transform: translateX(100%);
            -webkit-transform: translateX(100%);
        }
    }

    .slide-in-content {
        padding: 0px;
        transition: transform 0.5s ease;
    }

    &.show {
        overflow: visible;
        .slide-in-content {
            transform: translateX(0);
            -webkit-transform: translateX(0);
        }
    }
}

// we could discuss good naming conventions for animations? (went w/ this for now)
.hover {
    &.hover\:float-content,
    .hover\:float-content {
        // may dumb this animation down l8r?
        position: relative;

        &::before {
            // animating opacity vs box-shadow for better performance
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            box-shadow: 0 4px 30px rgba(44, 44, 44, 0.5);
            opacity: 0;
            transition: 0.4s ease-in-out opacity;
        }
    }

    &:hover {
        &.hover\:float-content::before,
        .hover\:float-content::before {
            opacity: 1;
        }
    }
}

.fade-in {
    opacity: 0;
    transform: translateY(40px);
    overflow: hidden;
    &.animate {
        opacity: 1;
        // transform: translateY(0); causing problems
        transform: none;
        transition: 0.75s;
    }
}
