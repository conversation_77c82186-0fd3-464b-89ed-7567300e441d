/*******************************************************************************************************
   Copyright 2020 - Calise Partners LLC d/b/a Imaginuity. All rights are reserved.
   Reproduction or transmission in whole or in part, in any form or by any means, electronic, mechanical
or otherwise, is prohibited without the prior written consent of Calise Partners LLC d/b/a Imaginuity.
********************************************************************************************************
   Project : 10618 Maverick Platform Development
   Author : Imaginuity Developers (<PERSON>, <PERSON>)
   Description : Used for making API/GET requests to the backend. Accessed through context.js
   Creation Date : Mon Nov 23 2020
   Documentation : [documentation url - point to default wiki for project if no specific documentation for script]
 ********************************************************************************************************/

import axios from 'axios';

// CONFIG.
import config from 'src/config';

export const API = axios.create({
    baseURL: `https://${config.base}/mvk-api/v1/content/${config.domain}/`,
    withCredentials: false,
    headers: {
        'Content-Type': 'application/json'
    }
});

export const APIsearch = axios.create({
    baseURL: `https://${config.base}`,
    withCredentials: false,
    headers: {
        'Content-Type': 'application/json'
    }
});

export const APIforms = axios.create({
    baseURL: `https://${config.base}/mvk-api/v1/forms`,
    withCredentials: false,
    headers: {
        'Content-Type': 'multipart/form-data'
    }
})

// SEARCH.
export const Search = (term) => {
    return APIsearch.get('/mvk-api/v1/search/', { params: { site:config.domain, term: term } }).then(res => res.data).catch(error => console.log(error));
};

// PRINCIPAL.
export const Principal = (translation) => {
    var requests = ['settings.json',`pages.json`,`redirects.json`];
    var translationRequests = [`settings.${translation}.json`, `pages.${translation}.json`]
    var names    = ['settings','pages','redirects', 'settingsTranslated', 'pagesTranslated'];
    var results  = {};

    const isTranslationRequestNeeded = (translation && translation !== 'en');
    if(isTranslationRequestNeeded) {
        requests = [...requests, ...translationRequests]
    }

    return axios.all(requests.map((url, index) => {
        return API.get(url).then(content => {
            results[names[index]] = content.data
        }).catch(error => console.log(error));
    })).then(() => { 
        if(!isTranslationRequestNeeded) return results;
        if((!results?.pagesTranslated && !results?.settingsTranslated) || !results?.settings?.translations?.[translation]) return results;

        const formattedResults = {
            settings: {...results.settings, ...results.settingsTranslated},
            pages: {...results.pages, ...results.pagesTranslated},
            redirects: results.redirects
        };

        return formattedResults;
    });
};

// FLEET.
export const Fleet = (subsite, translation) => {
    var requests = [`sites/${subsite}/settings.json`,`sites/${subsite}/pages.json`,`sites/${subsite}/redirects.json`];
    var translationRequests = [`sites/${subsite}/settings.${translation}.json`, `sites/${subsite}/pages.${translation}.json`];
    var names    = ['settings','pages','redirects'];
    var results  = {};

    var translationRequests = [`settings.${translation}.json`, `pages.${translation}.json`];
    const isTranslationRequestNeeded = (translation && translation !== 'en');
    if(isTranslationRequestNeeded) {
        requests = [...requests, ...translationRequests]
    }

    return axios.all(requests.map((url, index) => {
        return API.get(url).then(content => {
            results[names[index]] = content.data
        }).catch(error => console.log(error));
    })).then(() => { 
        if(!isTranslationRequestNeeded) return results;
        if((!results?.pagesTranslated && !results?.settingsTranslated) || !results?.settings?.translations?.[translation]) return results;

        const formattedResults = {
            settings: {...results.settings, ...results.settingsTranslated},
            pages: {...results.pages, ...results.pagesTranslated},
            redirects: results.redirects
        };

        return formattedResults;
    });
};

export const Navigation = (subsite, translation) => {
    var reqOne = API.get((subsite) ? `sites/${subsite}/nav.main.json` : 'nav.main.json');
    var reqTwo = API.get((subsite) ? `sites/${subsite}/nav.main_2.json` : 'nav.main_2.json');
    var reqThree = API.get((subsite) ? `sites/${subsite}/nav.utility.json` : 'nav.utility.json');

    const useTranslationRequest = (translation && translation !== 'en')
    if(useTranslationRequest) {
        reqOne = API.get((subsite) ? `sites/${subsite}/nav.main.${translation}.json` : `nav.main.${translation}.json`);
        reqTwo = API.get((subsite) ? `sites/${subsite}/nav.main_2.${translation}.json` : `nav.main_2.${translation}.json`);
        reqThree = API.get((subsite) ? `sites/${subsite}/nav.utility.${translation}.json` : `nav.utility.${translation}.json`);
    }

    return axios.all([reqOne, reqTwo, reqThree]).then(axios.spread((resOne, resTwo, resThree) => {
        return { main: resOne.data, main_2: resTwo ? resTwo.data : false, utility: resThree.data };
    })).catch(error => console.log(error));
};

export const NavFooter = (subsite, translation) => {
    var reqOne = API.get((subsite) ? `sites/${subsite}/nav.footer.json` : `nav.footer.json`);
    var reqTwo = API.get((subsite) ? `sites/${subsite}/nav.footer_utility.json` : 'nav.footer_utility.json');

    const useTranslationRequest = (translation && translation !== 'en')
    if(useTranslationRequest) {
        // reqOne didn't have a translated version? Need to confirm this is correct
        reqOne = API.get((subsite) ? `sites/${subsite}/nav.footer.${translation}.json` : `nav.footer.${translation}.json`);
        reqTwo = API.get((subsite) ? `sites/${subsite}/nav.footer_utility.${translation}.json` : `nav.footer_utility.${translation}.json`);
    }

    return axios.all([reqOne, reqTwo]).then(axios.spread((resOne, resTwo) => {
        return { main: resOne.data, utility: resTwo ? resTwo.data : false };
    })).catch(error => console.log(error));
};

export const Stores = (subsite, translation) => {
    var request = (subsite) ? `sites/${subsite}/stores.json` : `stores.json`;
    var translationRequest = (subsite) ? `sites/${subsite}/stores.${translation}.json` : `stores.${translation}.json`;

    const useTranslationRequest = (translation && translation !== 'en')
    if (useTranslationRequest) {
        request = translationRequest
    }

    return API.get(request).then(res => res.data).catch(error => console.log(error));
};

export const Pages = (subsite, translation) => {
    var request = (subsite) ? `sites/${subsite}/pages.json` : `pages.json`;
    var translationRequest = (subsite) ? `sites/${subsite}/pages.${translation}.json` : `pages.${translation}.json`;

    const useTranslationRequest = (translation && translation !== 'en')
    if(!useTranslationRequest) return API.get(request).then(res => res.data).catch(error => console.log(error));
    
    return axios.all([request, translationRequest]).then(axios.spread((regularRes, translatedRes) => {
        if(regularRes?.data && !translatedRes?.data) return regularRes;
        return {...regularRes, ...translatedRes};
    })).catch(error => console.log(error));
};

export const Sales = (subsite, translation) => {
    var request = (subsite) ? `sites/${subsite}/sales.json` : `sales.json`;
    var translationRequest = (subsite) ? `sites/${subsite}/sales.${translation}.json` : `sales.${translation}.json`;

    const useTranslationRequest = (translation && translation !== 'en')
    if (useTranslationRequest) {
        request = translationRequest
    }

    return API.get(request).then(res => res.data).catch(error => console.log(error));
   
};

export const Posts = (subsite, translation) => {
    var request = (subsite) ? `sites/${subsite}/posts.json` : `posts.json`;
    var translationRequest = (subsite) ? `sites/${subsite}/posts.${translation}.json` : `posts.${translation}.json`;

    const useTranslationRequest = (translation && translation !== 'en')
    if (useTranslationRequest) {
        request = translationRequest
    }

    return API.get(request).then(res => res.data).catch(error => console.log(error));
};

// export const NavMain = (subsite, translation) => {
//     var request = (subsite) ? `sites/${subsite}/nav.main.json` : `nav.main.json`;
//     var translationRequest = (subsite) ? `sites/${subsite}/nav.main.${translation}.json` : `nav.main.${translation}.json`;

//     const useTranslationRequest = (translation && translation !== 'en')
//     if(useTranslationRequest) return API.get(translationRequest).then(res => res.data).catch(error => console.log(error));
    
//     return API.get(request).then(res => res.data).catch(error => console.log(error));
// };

export const ContentTypes = (subsite) => {
    var request = (subsite) ? `sites/${subsite}/content_types.json` : `content_typss.json`;
    return API.get(request).then(res => res.data).catch(error => console.log(error));
};

export const Events = (subsite, translation) => {
    var request = (subsite) ? `sites/${subsite}/mec-events.json` : `mec-events.json`;
    var translationRequest = (subsite) ? `sites/${subsite}/mec-events.${translation}.json` : `mec-events.${translation}.json`;

    const useTranslationRequest = (translation && translation !== 'en')
    if (useTranslationRequest) {
        request = translationRequest
    }

    return API.get(request).then(res => res.data).catch(error => console.log(error));
};

export const Jobs = (subsite, translation) => {
    var request = (subsite) ? `sites/${subsite}/jobs.json` : `jobs.json`;
    var translationRequest = (subsite) ? `sites/${subsite}/jobs.${translation}.json` : `jobs.${translation}.json`;

    const useTranslationRequest = (translation && translation !== 'en')
    if (useTranslationRequest) {
        request = translationRequest
    }

    return API.get(request).then(res => res.data).catch(error => console.log(error));
};

export const Properties = (subsite, translation) => {
    var request = (subsite) ? `sites/${subsite}/properties.json` : `properties.json`;
    var translationRequest = (subsite) ? `sites/${subsite}/properties.${translation}.json` : `properties.${translation}.json`;

    const useTranslationRequest = (translation && translation !== 'en')
    if (useTranslationRequest) {
        request = translationRequest
    }

    return API.get(request).then(res => res.data).catch(error => console.log(error));
};

export const Spaces = (subsite, translation) => {
    var request = (subsite) ? `sites/${subsite}/spaces.json` : `spaces.json`;
    var translationRequest = (subsite) ? `sites/${subsite}/spaces.${translation}.json` : `spaces.${translation}.json`;

    const useTranslationRequest = (translation && translation !== 'en')
    if (useTranslationRequest) {
        request = translationRequest
    }

    return API.get(request).then(res => res.data).catch(error => console.log(error));
};

export const Team = (subsite, translation) => {
    var request = (subsite) ? `sites/${subsite}/team.json` : `team.json`;
    var translationRequest = (subsite) ? `sites/${subsite}/team.${translation}.json` : `team.${translation}.json`;

    const useTranslationRequest = (translation && translation !== 'en')
    if (useTranslationRequest) {
        request = translationRequest
    }

    return API.get(request).then(res => res.data).catch(error => console.log(error));
};

export const Testimonials = (subsite, translation) => {
    var request = (subsite) ? `sites/${subsite}/testimonials.json` : `testimonials.json`;
    var translationRequest = (subsite) ? `sites/${subsite}/testimonials.${translation}.json` : `testimonials.${translation}.json`;

    const useTranslationRequest = (translation && translation !== 'en')
    if (useTranslationRequest) {
        request = translationRequest;
    }
    
    return API.get(request).then(res => res.data).catch(error => console.log(error));
};

// Form Submission
export const FormSubmit = (url, fields) => {
    return APIforms.post(url, fields).then(res => res.data).catch(error => console.log(error));
};