import React, { Suspense } from 'react';
import { createRoot } from 'react-dom/client';

const Routing = React.lazy(() => import("src/routing"));

const Start = (props) => {
   
    return (
        <Suspense fallback={<div />}>
            <Routing />
        </Suspense>
    );
};

export default Start;

document.addEventListener("DOMContentLoaded", (e) => {
    const container = document.getElementById('body');
    const root = createRoot(container);
    root.render(
        <React.StrictMode>
            <Start />
        </React.StrictMode>
    );
});