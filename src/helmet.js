import React, { useContext, useEffect, useState, useRef } from "react";
import { datadogRum } from '@datadog/browser-rum';
import { Helmet, HelmetProvider } from 'react-helmet-async';
import { decode } from 'html-entities';
import { useLocation, useParams } from 'react-router-dom';

import config from 'src/config';

// HELPERS.
import { SettingsContext } from "src/context";
// HOOKS.
import { useQuery } from 'src/hooks/query';
import { scrollIntoViewWithOffset } from 'src/helpers/scrollIntoViewWithOffset';

const Start = ({ page = false }) => {
    if (!page) {
        return (<LoadBasic />);
    } else {
        return (<LoadDynamic page={page} />);
    }
};

const LoadBasic = ({ page }) => {
    const [settings, setSettings] = useContext(SettingsContext);

    useEffect(() => {
        // GTM
        window.dataLayer = window.dataLayer || [];
        let siteGuid = settings?.site_guid ? settings?.site_guid : '';

        window?.dataLayer?.push({
            'event': 'meta_internal_id_event',
            'meta_internal_id': siteGuid
        });

        if (window.location.hash) {
            setTimeout(function () { scrollIntoViewWithOffset(window.location.hash, 40); }, 700, 'smooth');
        }
    }, []);

    return (
        <HelmetProvider>
            <Helmet>
                <title>{decode(settings?.branding?.site_title.replace(/['"]+/g, ''))}</title>
                {window?.location?.href?.indexOf('/search') > -1 && <meta name="robots" content="noindex, follow" />}
            </Helmet>
        </HelmetProvider>
    );
};

const LoadDynamic = ({ page }) => {
    const [settings, setSettings] = useContext(SettingsContext);
    const [titleTag, setTitleTag] = useState(null);
    const query = useQuery();
    const params = useParams();
    const location = useLocation();
    const [siteGuid] = useState(settings?.site_guid ? settings?.site_guid : '');
    const [subCenterID] = useState(settings?.sei_settings?.sub_center_id ? settings?.sei_settings?.sub_center_id : '');
    const cookieConsent3p = settings?.cookie_consent_3p?.enabled && settings?.cookie_consent_3p?.code;
    // enable datadog real user monitoring
    if (settings.datadog?.enable_rum && !window?.DD_RUM?.getInternalContext()) {
        const conditions = ['dev', 'preflight', 'approach']
        const envVar = conditions.some(el => config.env.domain.includes(el)) ? 'preflight' : 'production';
        datadogRum.init({
            applicationId: '8a60dd81-e206-485a-8ecd-e17fa8da4ace',
            clientToken: 'pub1b651ccb0af61fa4b9692951ed4f321a',
            site: 'datadoghq.com',
            service: 'pylot-frontend',
            env: envVar, //can use preflight/production
            version: '1.12.1',  //this should be release #
            sessionSampleRate: 100,
            sessionReplaySampleRate: 20,
            trackUserInteractions: true,
            trackResources: true,
            trackLongTasks: true,
            defaultPrivacyLevel: 'mask-user-input'
        });

        datadogRum.startSessionReplayRecording();
    }

    useEffect(() => {
        window.dataLayer = window.dataLayer || [];

        if (siteGuid) {
            window?.dataLayer?.push({
                'event': 'meta_internal_id_event',
                'meta_internal_id': siteGuid
            });
        }
        if (subCenterID) {
            window?.dataLayer?.push({
                'event': 'sub_center_id_event',
                'sub_center_id': subCenterID
            });
        }
        if (window.location.hash) {
            // console.log("HASH", window.location.hash);
            setTimeout(function () { scrollIntoViewWithOffset(window.location.hash, 40); }, 700, 'smooth');
        }
        // save all query params 
        const queryParams = Object.fromEntries(query);
        if (queryParams && query.get('utm_source')) {
            sessionStorage.setItem('utm_data', JSON.stringify(queryParams));
        }

    }, [query, siteGuid, subCenterID, page]);

    var SeoSchema = (page?.mvk_item_seo && !page?.mvk_item_seo?.custom_schema) ? page?.mvk_item_seo?.seo_schema[0] : '';
    SeoSchema = SeoSchema && SeoSchema["@graph"] ? SeoSchema["@graph"]?.filter(item => item["@type"] !== "Person") : '';
    SeoSchema = SeoSchema ? JSON.stringify(SeoSchema) : '';
    var customPageSchema = (page?.mvk_item_seo?.custom_schema) ? page?.mvk_item_seo?.seo_schema : '';
    var siteSchema = settings?.seo?.site_schema ? settings?.seo?.site_schema : '';

    useEffect(() => {
        if (settings.current_location && page.mvk_meta_placeholders) {
            let slug = params?.slug;
            let locationParam = settings?.locations?.no_subdirectory ? params.first : params.second;
            let locationData = settings?.locations?.template_slugs[slug] ? settings?.locations?.template_slugs[slug] : settings?.locations?.template_slugs[locationParam] ? settings?.locations?.template_slugs[locationParam] : false;
            setTitleTag(page.mvk_meta_placeholders[locationData['slug']]?.title);
        } else {
            let decodeTitle = decode(page?.mvk_item_seo?.seo_title);
            setTitleTag(decodeTitle.replace(/['"]+/g, ''));
            Object.keys(page?.mvk_item_seo?.meta_tags)?.map((key) => {
                var tagData = page.mvk_item_seo.meta_tags[key];
                if (tagData.name === 'og:title' || tagData.property === 'og:title') {
                    let decodeContent = decode(tagData.content);
                    setTitleTag(decodeContent.replace(/['"]+/g, ''));
                }
            });
        }
    }, [page, location, settings.current_location, params])

    return (
        <HelmetProvider>
            <Helmet defer={false} title={titleTag}>
                {cookieConsent3p && <script data-cfasync="false" src={settings?.cookie_consent_3p?.code}></script>}
                {siteSchema && siteSchema.map((jsonLdString, index) => (
                    <script key={index} type="application/ld+json">
                        {jsonLdString}
                    </script>
                ))}
                {customPageSchema && customPageSchema.map((jsonLdString, index) => (
                    <script key={index} type="application/ld+json">
                        {jsonLdString}
                    </script>
                ))}
                {SeoSchema && <script type="application/ld+json">{SeoSchema}</script>}
                {settings?.seo && <meta name="google-site-verification" content={settings?.seo?.google_verification} />}
                {settings?.seo && <meta name="msvalidate.01" content={settings?.seo?.bing_verification} />}
                {settings?.seo && <meta name='facebook-domain-verification' content={settings?.seo?.facebook_verification} />}
                {config?.base && <link rel='preconnect' href={config?.base} />}
            </Helmet>
        </HelmetProvider>
    );
};


export default Start;
