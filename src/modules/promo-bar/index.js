import React from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faAngleRight } from "@fortawesome/free-solid-svg-icons";
import { useInView } from 'react-intersection-observer';

import "./index.scss";

const Imaging = React.lazy(() => import('src/helpers/imaging'));
import Clicker from "src/helpers/clicker";

const Start = ({ data, settings }) => {
    const { ref, inView } = useInView({
        triggerOnce: true,
        fallbackInView: true
    });

    var promoFont = document.createElement("link");
    promoFont.setAttribute("href", data.google_font_link);
    promoFont.setAttribute("rel", "stylesheet");
    document.getElementsByTagName("head")[0].appendChild(promoFont);

    if (data.google_font_properties?.length >= 1) {
        var fontProperties = data.google_font_properties;
    } else {
        var fontProperties = 'sans-serif';
    }

    return (
        <div
            ref={ref}
            class="promo-bar__module"
            style={{
                backgroundColor: data.background_color,
                fontFamily: `"${data.google_font_name}", ${fontProperties}`,
                fontWeight: data.google_font_weight,
            }}
        >
            {inView ?
                <div class="inner-container grid-container grid-x">
                    {data?.icon_links &&
                        data.icon_links.map((link, index) => (
                            <div key={index} class="promo-link cell shrink">
                                <Imaging data={link.icon_image} />
                                <Clicker
                                    type="anchor"
                                    url={link.icon_link.url}
                                    target={link.icon_link.target}
                                    title={link.icon_link.title}
                                >
                                    {link.icon_link.title}
                                </Clicker>
                                <FontAwesomeIcon
                                    icon={faAngleRight}
                                    class="icon-angle-right"
                                />
                            </div>
                        ))}
                </div>
                : null}
        </div>
    );
};

export default Start;
