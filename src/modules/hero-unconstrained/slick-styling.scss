#modules-container {
    .hero-unconstrained {
        .slider-container {
            .slick-slider {
                position: relative;

                .slick-arrow {
                    @media (min-width: 768px) {
                        top: unset;
                        bottom: 11px;
                        transform: none;
                    }

                    &.slick-next {
                        right: 0;

                        @media (min-width: 768px) {
                            right: 30%;
                        }
                    }
                    &.slick-prev {
                        left: 0;

                        @media (min-width: 768px) {
                            left: 30%;
                        }
                    }
                }

                .slick-list {
                    overflow: hidden;
                    width: 100%;

                    .slick-track {
                        display: -webkit-box;
                        display: -moz-box;
                        display: -ms-flexbox;
                        display: -webkit-flex;
                        display: flex;
                        flex-direction: row;
                        justify-items: center;

                        .slick-slide {
                            display: inline-block;
                            text-align: center;
                            img:not(.icon) {
                                min-width: 100%;
                                display: block;
                                vertical-align: top;
                                // max-width: none;
                                // object-fit: cover;
                            }
                        }
                    }
                }

                div.slick-dots {
                    position: absolute;
                    z-index: +1;
                    bottom: 20px;
                    margin: 0px auto;

                    left: 50%;
                    -webkit-transform: translate(-50%, 0);
                    -ms-transform: translate(-50%, 0);
                    transform: translate(-50%, 0);

                    ul.dots-list {
                        margin: 0px auto;
                        padding: 7px 0px 7px;
                        text-align: center;
                        height: 15px;

                        // display: block;
                        flex: 1 0 100%;
                        list-style-type: disc;
                        margin-block-start: 0px;
                        margin-block-end: 0px;
                        margin-inline-start: 0px;
                        margin-inline-end: 0px;
                        padding-inline-start: 0px;

                        li {
                            position: relative;
                            display: inline-block;
                            width: 15px;
                            height: 15px;
                            margin: 0px 10px;
                            padding: 0px;
                            cursor: pointer;
                            opacity: 0.5;
                            font-family: unset;
                            filter: drop-shadow(1px 1px 2px rgba(0, 0, 0, 0.4));
                            border-radius: 50%;
                            &::before {
                                content: none;
                            }

                            &.slick-active {
                                opacity: 1;
                            }

                            svg {
                                position: relative;
                                height: 15px;
                                width: 15px;
                                vertical-align: top;
                                font-family: unset;
                                box-sizing: border-box;
                            }
                        }
                    }
                }
            }
        }
    }
}
