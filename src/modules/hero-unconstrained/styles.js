import styled from 'styled-components';

export const Overlay = styled.div`
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    background: ${props => props.overlayColor};
    opacity: ${props => props.overlayOpacity};
    &.top-bottom {
        background: linear-gradient(${props => props.overlayColor} 45%, transparent);
    }
    &.bottom-top {
        background: linear-gradient(transparent, ${props => props.overlayColor} 45%);
    }
    &.left-right {
        background: linear-gradient(to right, ${props => props.overlayColor} 45%, transparent);
    }
     &.right-left {
        background: linear-gradient(to left, ${props => props.overlayColor} 45%, transparent);
    }
`

export const TitleBlurbContainer = styled.div`
    .button-wrapper {
        margin: 1rem 0 0.5rem;
    }
    &.custom-positioning {
        position: absolute;
        top: ${props => props.contentPositioning?.mobile_vertical}%;
        left: ${props => props.contentPositioning?.mobile_horizontal}%;
        transform: translate(-${props => props.contentPositioning?.mobile_horizontal}%, -${props => props.contentPositioning?.mobile_vertical}%);
        padding: 1rem;
        @media (min-width: 1024px) {
            top: ${props => props.contentPositioning?.desktop_vertical}%;
            left: ${props => props.contentPositioning?.desktop_horizontal}%;
            transform: translate(-${props => props.contentPositioning?.desktop_horizontal}%, -${props => props.contentPositioning?.desktop_vertical}%);
            width: 100%;
            max-width: 700px;
            box-sizing: border-box;
        }
    }
`