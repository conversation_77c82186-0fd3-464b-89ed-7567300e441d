/*******************************************************************************************************
   Copyright 2020 - Calise Partners LLC d/b/a Imaginuity. All rights are reserved. 
   Reproduction or transmission in whole or in part, in any form or by any means, electronic, mechanical 
or otherwise, is prohibited without the prior written consent of Calise Partners LLC d/b/a Imaginuity.
********************************************************************************************************
   Project : 10618 Maverick Platform Development
   Author : Imaginuity Developers (<PERSON>, <PERSON>)
   Description : Styles for content with featured image.
   Creation Date : Mon Dec 14 2020
   Documentation : [documentation url - point to default wiki for project if no specific documentation for script]
 ********************************************************************************************************/

@import "src/scss/variables.scss";

#modules-container {
    .content-with-featured-image {
        margin: 0px auto;
        position: relative;

        @media screen and (min-width: $break-desktop) {
            width: 100%;
        }

        .cwfi-box {
            position: relative;
        }

        .cwfi-cb-content-title,
        .cwfi-default-content-title,
        .cwfi-cbc-content-title,
        .cwfi-box-content-title {
            font-size: 2.188rem;
        }

        .cwfi-cb-content-text,
        .cwfi-default-content-text,
        .cwfi-cbc-content-text {
            margin-bottom: 30px;
            p,
            ul li {
                font-size: 0.875rem;
                @media (min-width: 768px) {
                    font-size: 1rem;
                }
                // line-height: 30px;
            }
        }

        .buttons .button {
            margin-bottom: 1rem;
        }

        // styles for vertical alignment of the content
        .content-top {
            align-self: flex-start;
        }

        .content-middle {
            align-self: center;
        }

        .content-bottom {
            align-self: flex-end;
        }

        .position-top {
            top: 0;
        }

        .position-bottom {
            bottom: 0;
        }

        .contained-top {
            align-items: flex-start !important;
            background-position: center top !important;
        }

        .contained-bottom {
            align-items: flex-end !important;
            background-position: center bottom !important;
        }

        .color-top, .color-middle, .color-bottom {
            .cwfi-cbc-content-text {
                @media (min-width: 640px) {
                    margin-bottom: unset;
                }
            }
        }

        .color-top {
            align-items: flex-start !important;

            .cwfi-cbc-content-wrapper {
                @media (min-width: 640px) {
                    padding-top: 10px !important;
                    padding-bottom: 70px !important;
                }
            }
        }

        .color-bottom {
            align-items: flex-end !important;
            
            .cwfi-cbc-content-wrapper {
                @media (min-width: 640px) {
                    padding-top: 70px !important;
                    padding-bottom: 10px !important;
                }
            }
        }
    }
}
