/*******************************************************************************************************
   Copyright 2020 - Calise Partners LLC d/b/a Imaginuity. All rights are reserved. 
   Reproduction or transmission in whole or in part, in any form or by any means, electronic, mechanical 
or otherwise, is prohibited without the prior written consent of Calise Partners LLC d/b/a Imaginuity.
********************************************************************************************************
   Project : 10618 Maverick Platform Development
   Author : Imaginuity Developers (<PERSON>, <PERSON>)
   Description : styles for regular color block module
   Creation Date : Mon Dec 14 2020
   Documentation : [documentation url - point to default wiki for project if no specific documentation for script]
 ********************************************************************************************************/

@import 'src/scss/variables.scss';

#modules-container {
    .content-with-featured-image.color-block {
        @media screen and (min-width: $break-medium) {
            padding: 50px 0px;
        }

        .background {
            position: absolute;
            z-index: 50;
            top: 0px;
            bottom: 0px;
            width: 100%;
            @media (min-width: 768px) {
                width: 65%;
            }
            &.image-left {
                right: 0px;
                background-position: right center;
                @media screen and (max-width: $break-small) {
                    top: 70px !important;
                }
            }
            &.image-right {
                left: 0px;
                background-position: left center;
                @media screen and (max-width: $break-small) {
                    bottom: 70px !important;
                }
            }
        }

        .cwfi-box {
            position: relative;
            z-index: 100;
            margin: 0 auto;
            max-width: 1170px;

            .cwfi-box-image {
                @media screen and (min-width: 501px) {
                    max-width: 50%;
                }

                @media screen and (max-width: 500px) {
                    width: 100%;
                }

                img {
                    max-width: 100%;

                    @media screen and (max-width: 500px) {
                        width: 100%;
                    }
                }

                &.image-right img {
                    float: right;
                }

                img {
                    width: 100%;
                }
            }

            .cwfi-box-content {
                padding: 20px 0px;
                max-width: 600px;
                text-align: left;

                @media screen and (min-width: $break-medium) {
                    padding: unset;
                }

                @media screen and (max-width: 500px) {
                    #btn-primary {
                        width: 100%;
                        justify-content: center;
                    }
                }

                @media screen and (min-width: 992px) {
                    #btn-primary {
                        margin-top: 70px;
                    }
                }
            }
        }

    }
}
