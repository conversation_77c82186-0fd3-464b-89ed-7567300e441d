@import 'src/scss/variables.scss';

#modules-container {
    .content-with-featured-image.content-foreground-background {
        padding: 30px 0px;

        @media screen and (max-width: $break-mobile) {
            padding: 0px;
        }

        // FOUNDATION ONLY TWEAKS.
        .grid-container {
            @media screen and (max-width: $break-mobile) {
                padding: 0px;
            }
            .cell {
                margin: 0px;
                @media screen and (max-width: $break-mobile) {
                    width: 100%;
                }
            }
        }

        .cwfi-box.content-foreground-background {
            max-width: 1250px;
            margin: 0 auto;
            display: flex;
            flex-flow: column nowrap;
            justify-content: center;

            &.image-right {
                align-items: flex-end;
            }

            .cwfi-box-image {
                position: relative;
                img {
                    width: 100%;
                }
            }

            .cwfi-box-content {
                position: absolute;
                right: 0px;
                z-index: +1;
                background-color: white;

                &.image-right {
                    left: 0px;
                }

                @media screen and (max-width: $break-mobile) {
                    position: relative;
                    margin: 30px;
                    width: calc(100% - 60px);
                }

                .cwfi-box-content-spacer {
                    padding: 20px;

                    .buttons {
                        padding-bottom:20px;
                    }
                }
            }
        }
    }
}
