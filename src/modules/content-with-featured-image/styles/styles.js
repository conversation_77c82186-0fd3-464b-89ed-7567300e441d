import styled from 'styled-components'

export const ContainedBackground = styled.div`
    color: ${props => props.textColor};
    margin: 2rem auto;
    &.color {
        .module-box {
            background-color: ${props => props.bgColor};
        }
    }
    &.image {
        .module-box {
            background-image: url(${props => props.bgImage});
            background-position: center center;
            background-size: cover;
            background-repeat: no-repeat;
        }
    }
    .module-box {
        border-radius: ${props => props.borderRadius};
        overflow: hidden;
        display: flex;
        flex-flow: column;
        justify-content: space-between;
        align-items: center;
        .content-container {
            padding: 2rem;
            .buttons {
                .button {
                    margin: 1rem auto;
                }
            }
        }
        .image-container img {
            width: 100%;
            display: block;
        }
        @media (min-width: 1024px) {
            flex-flow: row;
            .content-container {
                width: 50%;
            }
            .image-container {
                width: 50%;
                align-self: flex-end;
            }
        }
    }
`