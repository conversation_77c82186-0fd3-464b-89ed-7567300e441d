import React, { useState, useEffect } from 'react';
import { useInView } from 'react-intersection-observer';
import { decode } from 'html-entities';

// HELPERS.
import Imaging from 'src/helpers/imaging';
import HtmlParser from 'src/helpers/html-parser';
import { Coloring } from 'src/helpers';

// PARTIALS.
import Button from 'src/partials/button';
// Styles
import * as S from './styles';

const Start = ({ data, settings, placeholders }) => {
    const title = (placeholders && data.title_selection === 'dynamic') ? placeholders.single_line[data.title] : data.title
    const image = (placeholders && data.desktop_image_selection === 'dynamic') ? placeholders.image[data?.image] : data?.image;
    const mobileImage = (placeholders && data.mobile_image_selection === 'dynamic') ? placeholders.image[data?.mobile_image] : data?.mobile_image;
    const bgColor = (data?.background_type === 'color' && data.background_color && !data.background_color.startsWith('#')) ? Coloring(data.background_color, settings) : data.background_color;

    return (
        <S.ContainedBackground
            className={`cwfi-contained-background ${data.background_type}`}
            bgColor={bgColor}
            bgImage={(data.background_type == 'image' && data.background_image) ? data.background_image?.url : ''}
            textColor={data.background_value === 'dark' ? '#fff' : Coloring('body_copy_color', settings)}
            borderRadius={data.background_border_radius ? `${data.background_border_radius}px` : ''}
        >
            <div className='grid-container'>
                <div className='module-box'>
                    <div className='content-container'>
                        {title && <h3>{decode(title)}</h3>}
                        {data.content && <HtmlParser html={data.content} placeholders={placeholders} />}
                        {data.buttons &&
                            <div class='buttons'>
                                {data.buttons.map((item) => {
                                    const buttonLink = placeholders ? placeholders.button_link[item?.button] : item?.button;
                                    if (buttonLink) {
                                        return (
                                            <div class='button'><Button type={item.button_style} title={buttonLink.title} url={buttonLink.url} target={buttonLink.target} tone={data.background_value} /></div>
                                        )
                                    }
                                })}
                            </div>
                        }
                    </div>
                    <div className='image-container'>
                        {mobileImage && <Imaging className="hide-for-medium" data={mobileImage} />}
                        {image && <Imaging className={mobileImage ? `show-for-medium` : ''} data={image} />}
                    </div>
                </div>
            </div>
        </S.ContainedBackground>
    )
}

export default Start;