#modules-container {
    .content-with-featured-image.default {
        padding: 30px 0px;

        .cwfi-box.default {
            max-width: 1250px;
            margin: 0 auto;
            display: flex;
            // flex-flow: column nowrap;
            justify-content: center;

            @media screen and (max-width: 767px) {
                align-items: center;
            }

            @media screen and (min-width: 768px) and (max-width: 1250px) {
                padding: 0 20px;
            }

            // &.image-left {
            //     @media screen and (min-width: 768px) {
            //         flex-flow: row nowrap;
            //     }
            // }

            // &.image-right {
            //     @media screen and (min-width: 768px) {
            //         flex-flow: row-reverse nowrap;
            //     }
            // }

            .cwfi-default-image {
                img {
                    width: 100%;
                    display: block;
                }
            }
        }
    }
}
