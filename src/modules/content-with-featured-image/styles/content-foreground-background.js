import React, { useEffect, useState } from "react";
import { decode } from 'html-entities';

// HELPERS.
const Imaging = React.lazy(() => import('src/helpers/imaging'));
const Clicker = React.lazy(() => import('src/helpers/clicker'));
import HtmlParser from 'src/helpers/html-parser'
import { Coloring } from 'src/helpers';

// PARTIALS.
const Button = React.lazy(() => import('src/partials/button'));

// MODULE'S COMPOUND COMPONENTS.
import Layout from 'src/modules/content-with-featured-image/layout';

// SCSS.
import 'src/modules/content-with-featured-image/styles/content-foreground-background.scss';

const Default = ({ data, settings, placeholders }) => {
    return (
        <Layout data={data} settings={settings} placeholders={placeholders}>
            <Layout.Container data={data}>
                {data.type == 'image-left' &&
                    <Layout.Container.Box data={data}>
                        <ImageContainer data={data} placeholders={placeholders} />
                        <ContentContainer data={data} settings={settings} placeholders={placeholders} />
                    </Layout.Container.Box>
                }
                {data.type == 'image-right' &&
                    <Layout.Container.Box data={data}>
                        <ContentContainer data={data} placeholders={placeholders} />
                        <ImageContainer data={data} settings={settings} placeholders={placeholders} />
                    </Layout.Container.Box>
                }
            </Layout.Container>
        </Layout>
    );
};

const ImageContainer = ({ data, placeholders }) => {
    const [number] = useState(Math.floor(Math.random() * 10000));
    const image = (placeholders && data.desktop_image_selection === 'dynamic') ? placeholders.image[data?.image] : data?.image;
  
    return (
        <div class={`cwfi-box-image cell small-12 medium-7 large-7 ${data.type}`}>
            <div id={`left-${number}`}>
                <div>
                    <Imaging data={image} />
                </div>
            </div>
        </div>
    );
};

const ContentContainer = ({ data, settings, placeholders }) => {
    const [number] = useState(Math.floor(Math.random() * 10000));
    const title = (placeholders && data.title_selection === 'dynamic') ? placeholders.single_line[data.title] : data.title;
    const bgImage = (placeholders && data.content_background_image_selection === 'dynamic') ? placeholders.image[data?.content_background_image_dynamic] : data?.content_background_image;
    const bgColor = (data?.background_type === 'color' && data.content_background_color && !data.content_background_color.startsWith('#')) ? Coloring(data.content_background_color, settings) : data.content_background_color;

    const styles = {
        color: data?.module_background_value === 'dark' ? '#fff' : settings?.design?.colors?.body_copy_color,
        backgroundImage: data?.enable_content_background && bgImage ? `url(${bgImage?.url})` : 'unset',
        backgroundColor: data?.enable_content_background ? bgColor : 'transparent',
    };

    return (
        <div class={`cwfi-box-content cell small-12 medium-7 large-7 ${data.type} position-${data?.vertical_content_alignment}`} style={styles}>
            <div id={`right-${number}`} class="cwfi-box-content-spacer">
                <div class={data.content_background_value == 'dark' ? 'white' : ''}>
                    {title &&
                        <div>
                            <h3 class={`cwfi-box-content-title`}>{decode(title)}</h3>
                        </div>
                    }
                    <div class="cwfi-box-content-text">
                        {data.content && <HtmlParser html={data.content} placeholders={placeholders} />}
                    </div>
                    {data.buttons &&
                        <div class={`buttons ${data?.button_alignment}`}>
                            {data.buttons.map((item) => {
                                const buttonLink = placeholders ? placeholders.button_link[item?.button] : item?.button;
                                if (buttonLink) {
                                    return (
                                        <div class='button'><Button type={item.button_style} title={buttonLink.title} url={buttonLink.url} target={buttonLink.target} tone={data.background_value} /></div>
                                    )
                                }
                            })}
                        </div>
                    }
                </div>
            </div>
        </div>
    );
};

export default Default;