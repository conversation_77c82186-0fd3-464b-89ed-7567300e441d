/*******************************************************************************************************
Copyright 2020 - Calise Partners LLC d/b/a Imaginuity. All rights are reserved. 
Reproduction or transmission in whole or in part, in any form or by any means, electronic, mechanical 
or otherwise, is prohibited without the prior written consent of Calise Partners LLC d/b/a Imaginuity.
********************************************************************************************************
Project : 10618 Maverick Platform Development
Author : Imaginuity Developers (<PERSON>, <PERSON>)
Description : style properties for color block contained module
Creation Date : Mon Dec 14 2020
Documentation : [documentation url - point to default wiki for project if no specific documentation for script]
********************************************************************************************************/

@import 'src/scss/variables.scss';
@import "~foundation-sites/scss/util/_util.scss";

#modules-container {
    .content-with-featured-image.color-block-contained {
        margin: 0;
        
        .cwfi-box.color-block-contained {
            &.image-left {
                .cwfi-cbc-content {
                    .cwfi-cbc-content-box {
                        @media screen and (min-width: $break-medium) {
                            margin: 0px auto 0px 0px;
                        }
                    }
                }
            }
            
            &.image-right {
                .cwfi-cbc-content {
                    .cwfi-cbc-content-box {
                        @media screen and (min-width: $break-medium) {
                            margin: 0px 0px 0px auto;
                        }
                    }
                }
            }

            .cwfi-cbc-image {
                // width: 50%;
                margin-left: 0px;
                margin-right: 0px;

                .cwfi-cbc-image-box {
                    width: 100%;
                    height: 100%;
                    object-fit: cover;

                    .cwfi-cbc-image-wrapper {
                        width: 100%;
                        height: 100%;
                        display: flex;
                        overflow: hidden;
                        object-fit: cover;

                        img {
                            width: 100%;
                            height: 100%;
                            object-fit: cover;
                            object-position: center center;

                            @media screen and (min-width: $break-medium) {
                                // max-height: 400px;
                            }
                        }
                    }
                }
            }

            .cwfi-cbc-content {
                // width: 50%;
                margin-left: 0px;
                margin-right: 0px;

                text-align: left;
                // height: fit-content;
                display: flex;
                align-items: center;

                .cwfi-cbc-content-box {
                    // max-width: 600px;

                    .cwfi-cbc-content-wrapper {
                        padding: 70px 65px;
                        // margin: 0 40px;

                        @include breakpoint(small down) {
                            padding: 40px 35px;
                        }

                        button {
                            border-radius: 32px;
                            border-color: transparent;
                        }
                    }
                }
            }
        }
        .buttons .button {
            margin-bottom: 1rem;
        }
    }
}
