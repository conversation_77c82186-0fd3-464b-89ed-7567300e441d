import React, { useState, useEffect } from "react";
import { useInView } from 'react-intersection-observer';
import { decode } from 'html-entities';
// HELPERS.
import Imaging from 'src/helpers/imaging';
import HtmlParser from 'src/helpers/html-parser'

// PARTIALS.
const Button = React.lazy(() => import('src/partials/button'));

// MODULE'S COMPOUND COMPONENTS.
import Layout from 'src/modules/content-with-featured-image/layout';

// SCSS.
import 'src/modules/content-with-featured-image/styles/default.scss';

const Default = ({ data, settings, placeholders }) => {
    return (
        <Layout data={data} settings={settings} placeholders={placeholders}>
            <Layout.Container data={data}>
                {data.type == 'image-left' &&
                    <Layout.Container.Box data={data}>
                        <ImageContainer data={data} settings={settings} placeholders={placeholders} />
                        <ContentContainer data={data} settings={settings} placeholders={placeholders} />
                    </Layout.Container.Box>
                }
                {data.type == 'image-right' &&
                    <Layout.Container.Box data={data}>
                        <ContentContainer data={data} settings={settings} placeholders={placeholders} />
                        <ImageContainer data={data} settings={settings} placeholders={placeholders} />
                    </Layout.Container.Box>
                }
            </Layout.Container>
        </Layout>
    );
};

const ImageContainer = ({ data, settings, placeholders }) => {
    const [animate, setAnimate] = useState(settings?.mvk_theme_config?.other?.enable_animations);
    const { ref, inView } = useInView({
        triggerOnce: true,
        fallbackInView: true,
        threshold: 0.75
    });
    const image = (placeholders && data.desktop_image_selection === 'dynamic') ? placeholders.image[data?.image] : data?.image;
    const mobileImage = (placeholders && data.mobile_image_selection === 'dynamic') ? placeholders.image[data?.mobile_image] : data?.mobile_image;
   
    return (
        <div ref={ref} class={`cwfi-default-image cell small-12 medium-6 large-6 ${data.type}`}>
            <div class={`${animate ? 'slide-in from-left' : ''}${inView ? ' show' : ''}`}>
                <div class="slide-in-content">
                    <Imaging className={mobileImage ? 'show-for-medium' : ''} data={image} />
                    {mobileImage && <Imaging className='hide-for-medium' data={mobileImage} />}
                </div>
            </div>
        </div>
    );
};

const ContentContainer = ({ data, settings, placeholders }) => {
    const [animate, setAnimate] = useState(settings?.mvk_theme_config?.other?.enable_animations);
    const { ref, inView } = useInView({
        triggerOnce: true,
        fallbackInView: true,
        threshold: 0.75
    });
    const title = (placeholders && data.title_selection === 'dynamic') ? placeholders.single_line[data.title] : data.title

    return (
        <div ref={ref} class={`cwfi-default-content cell small-12 medium-6 large-6 ${data.type} content-${data?.vertical_content_alignment}`}>
            <div class={`${animate ? 'slide-in from-right' : ''}${inView ? ' show' : ''}`}>
                <div class={`slide-in-content ${data.background_value == 'dark' ? 'white' : ''}`}>
                    {title &&
                        <div>
                            <h3 class={`cwfi-default-content-title`}>{decode(title)}</h3>
                        </div>
                    }
                    <div class="cwfi-default-content-text">
                        {data.content && <HtmlParser html={data.content} placeholders={placeholders} />}
                    </div>
                    {data.buttons &&
                        <div class='buttons'>
                            {data.buttons.map((item) => {
                                const buttonLink = placeholders ? placeholders.button_link[item?.button] : item?.button;
                                if (buttonLink?.url) {
                                    return (
                                        <div class='button'><Button type={item.button_style} title={buttonLink.title} url={buttonLink.url} target={buttonLink.target} tone={data.background_value} /></div>
                                    )
                                }
                            })}
                        </div>
                    }
                </div>
            </div>
        </div>
    );
};

export default Default;
