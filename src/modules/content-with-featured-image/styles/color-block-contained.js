/*******************************************************************************************************
   Copyright 2020 - Calise Partners LLC d/b/a Imaginuity. All rights are reserved.
   Reproduction or transmission in whole or in part, in any form or by any means, electronic, mechanical
or otherwise, is prohibited without the prior written consent of Calise Partners LLC d/b/a Imaginuity.
********************************************************************************************************
   Project : 10618 Maverick Platform Development
   Author : Imaginuity Developers (<PERSON>, <PERSON>)
   Description : module that displays a color block with content next to an image
   Creation Date : Mon Dec 14 2020
   Documentation : [documentation url - point to default wiki for project if no specific documentation for script]
 ********************************************************************************************************/

import React, { useState, useEffect } from 'react';
import { useInView } from 'react-intersection-observer';
import { decode } from 'html-entities';

// HELPERS.
import Imaging from 'src/helpers/imaging';
import HtmlParser from 'src/helpers/html-parser';

// PARTIALS.
const Button = React.lazy(() => import('src/partials/button'));

// MODULE'S COMPOUND COMPONENTS.
import Layout from 'src/modules/content-with-featured-image/layout';

// SCSS.
import 'src/modules/content-with-featured-image/styles/color-block-contained.scss';

const Start = ({ data, settings, placeholders }) => {
    return (
        <Layout data={data} settings={settings} placeholders={placeholders}>
            <Layout.Container data={data}>
                {data.type == 'image-left' &&
                    <Layout.Container.Box data={data} placeholders={placeholders}>
                        <ImageContainer data={data} settings={settings} placeholders={placeholders} />
                        <ContentContainer data={data} settings={settings} placeholders={placeholders} />
                    </Layout.Container.Box>
                }
                {data.type == 'image-right' &&
                    <Layout.Container.Box data={data}>
                        <ContentContainer data={data} settings={settings} placeholders={placeholders} />
                        <ImageContainer data={data} settings={settings} placeholders={placeholders} />
                    </Layout.Container.Box>
                }
            </Layout.Container>
        </Layout>
    );
};

const ImageContainer = ({ data, settings, placeholders }) => {
    const [animate, setAnimate] = useState(settings?.mvk_theme_config?.other?.enable_animations);
    const { ref, inView } = useInView({
        triggerOnce: true,
        fallbackInView: true,
        threshold: 0.75
    });
    const image = (placeholders && data.desktop_image_selection === 'dynamic') ? placeholders.image[data?.image] : data?.image;
    const mobileImage = (placeholders && data.mobile_image_selection === 'dynamic') ? placeholders.image[data?.mobile_image] : data?.mobile_image;

    return (
        <div ref={ref} class="cwfi-cbc-image cell small-12 medium-6 large-6">
            <div class={`cwfi-cbc-image-box${animate ? ' slide-in from-right' : ''}${inView ? ' show' : ''}`}>
                <div class="cwfi-cbc-image-wrapper slide-in-content">
                    <Imaging className={mobileImage ? 'show-for-medium' : ''} data={image} />
                    {mobileImage && <Imaging className='hide-for-medium' data={mobileImage} />}
                </div>
            </div>
        </div>
    );
}


const ContentContainer = ({ data, settings, placeholders }) => {
    const [animate, setAnimate] = useState(settings?.mvk_theme_config?.other?.enable_animations);
    const { ref, inView } = useInView({
        triggerOnce: true,
        fallbackInView: true,
        threshold: 0.75
    });
    const title = (placeholders && data.title_selection === 'dynamic') ? placeholders.single_line[data.title] : data.title
    const bgImage = (placeholders && data.background_image_selection === 'dynamic') ? placeholders.image[data?.background_image_dynamic] : data?.background_image

    let headingStyles = {}, contentStyles = {};
    if (data.background_value === "light") {
        headingStyles = { color: settings.design?.colors?.primary_color };
        contentStyles = { color: settings.design?.colors?.body_copy_color };
    } else if (data.background_value === "dark") {
        headingStyles = { color: "#fff" };
        contentStyles = { color: "#fff" };
    }

    let bgStyles = {
        backgroundImage: data?.style === 'color-block-contained' && data?.background_type === 'image' && bgImage ? `url(${bgImage?.url})` : 'unset',
        backgroundPosition: 'center center',
        backgroundRepeat: 'no-repeat'
    };

    return (
        <div ref={ref} class={`cwfi-cbc-content cell small-12 medium-6 large-6 ${data?.background_type === 'image' ? `contained-${data?.vertical_content_alignment}` : `color-${data?.vertical_content_alignment}`}`} style={bgStyles}>
            <div class={`cwfi-cbc-content-box${animate ? ' slide-in from-left' : ''}${inView ? ' show' : ''}`}>
                <div class="cwfi-cbc-content-wrapper slide-in-content" style={contentStyles}>
                    {title && <h2 class="cwfi-cbc-content-title" style={headingStyles}>{decode(title)}</h2>}
                    <div class="cwfi-cbc-content-text" style={contentStyles}>
                        {data.content && <HtmlParser html={data.content} placeholders={placeholders} />}
                    </div>
                    {data.buttons &&
                        <div class='buttons'>
                            {data.buttons.map((item) => {
                                const buttonLink = placeholders ? placeholders.button_link[item?.button] : item?.button;
                                if (buttonLink) {
                                    return (
                                        <div class='button'><Button type={item.button_style} title={buttonLink.title} url={buttonLink.url} target={buttonLink.target} tone={data.background_value} /></div>
                                    )
                                }
                            })}
                        </div>
                    }
                </div>
            </div>
        </div>
    );
};

export default Start;
