/*******************************************************************************************************
   Copyright 2020 - Calise Partners LLC d/b/a Imaginuity. All rights are reserved.
   Reproduction or transmission in whole or in part, in any form or by any means, electronic, mechanical
or otherwise, is prohibited without the prior written consent of Calise Partners LLC d/b/a Imaginuity.
********************************************************************************************************
   Project : 10618 Maverick Platform Development
   Author : Imaginuity Developers (<PERSON>, <PERSON>)
   Description : responsible for deciding which style of content with featured image module to display
   Creation Date : Mon Dec 14 2020
   Documentation : [documentation url - point to default wiki for project if no specific documentation for script]
 ********************************************************************************************************/

import React, { Suspense, useEffect } from 'react';

// STYES, SUB PARTIALS.
const ColorBlock          = React.lazy(() => import('src/modules/content-with-featured-image/styles/color-block'));
const ColorBlockContained = React.lazy(() => import('src/modules/content-with-featured-image/styles/color-block-contained'));
const ContentFGBG         = React.lazy(() => import('src/modules/content-with-featured-image/styles/content-foreground-background'));
const Default             = React.lazy(() => import('src/modules/content-with-featured-image/styles/default'));
const ContainedBackground = React.lazy(() => import('src/modules/content-with-featured-image/styles/contained-background'));

// SCSS.
import 'src/modules/content-with-featured-image/index.scss';

const Start = ({ data, settings, placeholders }) => {
    data.type = (data.type == 'image-left') ? 'image-left' : 'image-right';
    
    return (
        <Suspense fallback={<div />}>
            {data.style === 'color-block' && <ColorBlock data={data} settings={settings} placeholders={placeholders} />}
            {data.style === 'color-block-contained' && <ColorBlockContained data={data} settings={settings} placeholders={placeholders} />}
            {data.style === 'content-foreground-background' && <ContentFGBG data={data} settings={settings} placeholders={placeholders} />}
            {data.style === 'default' && <Default data={data} settings={settings} placeholders={placeholders} />}
            {data.style === 'contained-background' && <ContainedBackground data={data} settings={settings} placeholders={placeholders} />}
        </Suspense>
    );
};

export default Start;
