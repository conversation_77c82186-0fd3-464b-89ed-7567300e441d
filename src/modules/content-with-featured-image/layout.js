import React, { useState, useEffect } from 'react';
import { useInView } from 'react-intersection-observer';
import { Coloring } from 'src/helpers';

export default function Start({ data, settings, placeholders, children }) {
    const { ref, inView } = useInView({
        triggerOnce: true,
        fallbackInView: true
    });
    const bgImage = (placeholders && data.background_image_selection === 'dynamic') ? placeholders.image[data?.background_image_dynamic] : data?.background_image
    const bgColor = (data?.background_type === 'color' && data.background_color && !data.background_color.startsWith('#')) ? Coloring(data.background_color, settings) : data.background_color;

    const styles = {
        color: data?.background_value === 'dark' ? '#fff' : settings?.design?.colors?.body_copy_color,
        backgroundImage: data?.style !== 'color-block' && data?.style !== 'color-block-contained' && data?.background_type === 'image' && bgImage ? `url(${bgImage?.url})` : 'unset',
        backgroundColor: (data.style != 'color-block') ? bgColor : 'transparent',
        backgroundSize: 'cover',
        backgroundPosition: 'center'
    };

    return (
        <div ref={ref} class={`content-with-featured-image ${data.style} ${data.type}`} style={styles}>
            {inView ? children : null}
        </div>
    );
};
Start.displayName = 'modules, content-with-featured-image, layout: Start';

Start.Background = ({ data, placeholders, settings }) => {
    const bgImage = (placeholders && data.background_image_selection === 'dynamic') ? placeholders.image[data?.background_image_dynamic] : data?.background_image
    const bgColor = (data?.background_type === 'color' && data.background_color && !data.background_color.startsWith('#')) ? Coloring(data.background_color, settings) : data.background_color;
    const styles = {
        backgroundImage: data?.background_type === 'image' && bgImage ? `url(${bgImage?.url})` : 'unset',
        backgroundColor: data?.background_type === 'color' ? bgColor : 'transparent',
        backgroundSize: 'cover',
        backgroundPosition: 'center'
    };

    return (<div class={`background ${data.style} ${data.type}`} style={styles} />);
};
Start.Background.displayName = 'modules, content-with-featured-image, layout: Start.Background';

Start.Container = ({ data, children }) => {
    function classes() {
        if (data.style == 'color-block-contained') return 'full';
        else return '';
    };

    return (
        <div class={`grid-container ${data.style} ${data.type} ${classes()}`}>
            {children}
        </div>
    );
};
Start.Container.displayName = 'modules, content-with-featured-image, layout: Start.Container';

Start.Container.Box = ({ data, children }) => {
    // INITIAL CLASSES FOR DIV.
    var classes = ['cwfi-box', 'grid-x', 'grid-margin-x', data.style, data.type];
    // ADD or REMOVE CLASSES AS NEEDED.
    if (data.style == 'color-block-contained') delete classes[2];

    return (
        <div class={classes.join(' ')}>
            {children}
        </div>
    );
};
Start.Container.Box.displayName = 'modules, content-with-featured-image, layout: Start.Container.Box';