import React, { useState, useEffect } from 'react';
import { decode } from 'html-entities';
import { useInView } from 'react-intersection-observer';
//Helpers
import { Coloring } from 'src/helpers';
import HtmlParser from 'src/helpers/html-parser';
//Styles
import * as S from './styles';
const InfoCards = React.lazy(() => import('./styles/info-cards'));

const Start = ({ data, settings, placeholders }) => {
    const { ref, inView } = useInView({
        triggerOnce: true,
        fallbackInView: true
    });
    const title = (placeholders && data.title_selection === 'dynamic') ? placeholders.single_line[data.title] : data.title;
    const bgImage = (placeholders && data.background_image_selection === 'dynamic') ? placeholders.image[data?.background_image_dynamic] : data?.background_image

    return (
        <S.PostTypeListLocations
            ref={ref}
            className={`post-type-list-locations ${data.background_type}`}
            bgColor={Coloring(data.background_color, settings)}
            bgImage={bgImage ? bgImage.url : ''}
            textColor={data.background_value === 'dark' ? '#fff' : Coloring('body_copy_color', settings)}
        >
            <div className='grid-container'>
                <div className='grid-x grid-margin-x'>
                    {(title || data.blurb) &&
                        <div className={`cell title-container ${data.title_alignment}`}>
                            {title && <h2>{decode(title)}</h2>}
                            {data.blurb && <HtmlParser html={data.blurb} placeholders={placeholders} />}
                        </div>
                    }
                </div>
                <Styles data={data} settings={settings} />
            </div>
        </S.PostTypeListLocations>
    )

}

const Styles = ({ data, settings }) => {
    switch (data.style) {
        case 'info-cards':
        default:
            return <InfoCards data={data} settings={settings} />;
    }
}

export default Start;