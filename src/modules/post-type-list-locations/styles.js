import styled from 'styled-components';

export const PostTypeListLocations = styled.div`
    color: ${props => props.textColor};
    &.color {
        background-color: ${props => props.bgColor};
    }
    &.image {
        background-image: url(${props => props.bgImage});
        background-position: center center;
        background-size: cover;
        background-repeat: no-repeat;
    }
    padding: 3rem 0;
    .title-container {
        margin-bottom: 1rem;
    }
`

export const InfoCards = styled.div`
    @media (min-width: 640px) {
        display: grid;
        grid-auto-flow: row;
        grid-template-columns: 1fr 1fr;
        row-gap: 3rem;
        column-gap: 1rem;
    }
    @media (min-width: 1024px) {
        &.three-column {
            grid-template-columns: 1fr 1fr 1fr;
        }
        &.four-column {
            grid-template-columns: 1fr 1fr 1fr 1fr;
        }
    }
`

export const Location = styled.div`
    margin-bottom: 3rem;
    &:last-child {
        margin-bottom: 0;
    }
    @media (min-width: 640px) {
        margin-bottom: 0;
    }
    .featured-image {
        display: block;
        position: relative;
        min-height: 300px;
        margin-bottom: 1.5rem;
        overflow: hidden;
        img {
            position: absolute;
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: .3s;
        }
        .flag {
            position: absolute;
            top: 1.5rem;
            left: 0;
            z-index: 1;
            padding: .5rem 1rem;
            background-color: ${props => props.flagBgColor};
            color: ${props => props.flagTextColor};
            h6 {
                margin: 0;
            }
        }
        @media (min-width: 640px) {
            min-height: 280px;
        }
        @media (min-width: 1024px) {
            min-height: 220px;
        }
    }
    &:has(.post-link):hover .featured-image {
        img {
            transform: scale(1.1);
        }
    }
    .content-wrapper {
        h5 {
            margin-bottom: .5rem;
            font-weight: bold;
        }
        .address {
            display: block;
            margin-bottom: .5rem;
            text-decoration: underline;
            p {
                margin-bottom: .5rem; 
            }
        }
        .post-link {
            display: block;
            margin-top: 1rem;
            font-weight: bold !important;
        }
    }
`