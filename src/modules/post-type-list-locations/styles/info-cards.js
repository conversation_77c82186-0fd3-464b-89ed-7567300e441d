import React, { useState, useEffect, useContext } from 'react';
import { decode } from 'html-entities';
import { LocationsContext } from 'src/context';
//Helpers
import { Coloring } from 'src/helpers';
import HtmlParser from 'src/helpers/html-parser';
import Imaging from 'src/helpers/imaging';
import Clicker from 'src/helpers/clicker';
//Styles
import * as S from '../styles';

const Start = ({ data, settings }) => {
    const [locationsContext] = useContext(LocationsContext);

    return (
        <S.InfoCards className={`info-cards ${data.layout_options}`}>{data.locations && data.locations.map((location) => (location.slug && locationsContext[location.slug]) && <Location location={location} locationData={locationsContext[location.slug]} data={data} settings={settings} />)}</S.InfoCards>
    )
}

const Location = ({ location, locationData, data, settings }) => {
    const [showLocation, setShowLocation] = useState(true);
    const customFields = locationData ? locationData?.mvk_item_content?.custom_fields : false;
    const linkToLocation = customFields?.link_to_location;
    const showAddress = customFields?.show_address;
    const address = showAddress ? `${customFields?.address_info?.address_line_1 ? `${customFields?.address_info?.address_line_1},` : ''}${customFields?.address_info?.address_line_2 ? ` ${customFields?.address_info?.address_line_2},` : ''} ${customFields?.address_info?.city}, ${customFields?.address_info?.state_province}` : '';
    const directionsLink = showAddress ? `https://www.google.com/maps/place/${customFields?.address_info?.address_line_1.replace(/ /g, '+')}+${customFields?.address_info?.city.replace(/ /g, '+')}+${customFields?.address_info?.state_province.replace(/ /g, '+')}+${customFields?.address_info?.postal_code_zip.replace(/ /g, '+')}` : '';
    const flag = customFields?.flag ? customFields.flag_text : false;

    useEffect(() => {
        if (settings.current_location && settings.current_location === location.slug) {
            setShowLocation(false);
        } else {
            setShowLocation(true);
        }
    }, [settings.current_location]);

    if (showLocation) {
        return (
            <S.Location
                className={`location`}
                flagBgColor={Coloring(settings?.locations?.flag_background_color, settings)}
                flagTextColor={settings?.locations?.flag_background_value === 'dark' ? '#fff' : Coloring('body_copy_color', settings)}
            >
                {location.featured_image &&
                    <Clicker type={linkToLocation ? 'anchor' : ''} url={linkToLocation ? location.url : null} className='featured-image' ariaLabel={`link to ${decode(location.title)} homepage`}>
                        {flag && <div className='flag'><h6>{decode(flag)}</h6></div>}
                        <Imaging data={location.featured_image} />
                    </Clicker>
                }
                <div className='content-wrapper'>
                    <h5>{decode(location.title)}</h5>
                    {showAddress && <a class='address body-copy-txt' href={directionsLink} target='_blank' aria-label={`directions to ${decode(location.name)}`}><p>{address}</p></a>}
                    {locationData?.todays_hours && <p className='todays-hours'>{locationData?.todays_hours?.closed ? locationData?.todays_hours?.closed : `Open today: ${locationData?.todays_hours.start_time} - ${locationData?.todays_hours.end_time}`}</p>}
                    {(linkToLocation && data.post_link_text) && <Clicker type='anchor' url={location.url} className='post-link body-copy-txt' ariaLabel={`link to ${decode(location.title)} homepage`}>{decode(data.post_link_text)}</Clicker>}
                </div>
            </S.Location>
        )
    } else {
        null;
    }
}

export default Start;