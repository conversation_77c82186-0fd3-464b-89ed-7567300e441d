.tabbed-link-blocks {
    padding: 2rem 0 3rem;
    > .grid-container {
        @media (max-width: 1023px) {
            padding: 0;
        }
    }
    .title {
        margin-bottom: 1rem;
    }
    .blurb {
        padding: 0 0.625rem;
        @media (min-width: 1024px) {
            padding-left: 0;
        }
    }
    @media (min-width: 1024px) {
        padding: 5rem 0;
        &.vertical_right .block-nav {
            order: 2;
            text-align: right;
        }
    }
    .mobile-carousel,
    .tabbed-content {
        @media (max-width: 1023px) {
            margin-bottom: 1rem;
        }
        .tab {
            .inner-wrapper {
                position: relative;
                display: flex;
                align-items: flex-end;
                margin: 0 0.5rem;
                min-height: 400px;
                overflow: hidden;
                img {
                    position: absolute;
                    width: 100%;
                    height: 100%;
                    object-fit: cover;
                }
                .content-wrapper {
                    z-index: 1;
                    padding: 1rem;
                    &.dark {
                        color: #fff;
                    }
                }
            }
            @media (min-width: 1024px) {
                display: none;
                &.active {
                    display: block;
                }
                .inner-wrapper {
                    min-height: 500px;
                    margin: 0;
                    .content-wrapper {
                        padding: 2rem 2.5rem;
                        .tab-content {
                            max-width: 85%;
                        }
                    }
                }
            }
            @media (min-width: 1200px) {
                .inner-wrapper {
                    min-height: 675px;
                }
            }
        }
        .slick-dots {
            bottom: -50px;
            li {
                display: inline-flex;
                justify-content: center;
                button {
                    border: 2px solid currentColor;
                    border-radius: 100%;
                    margin: 0;
                    width: 10px;
                    height: 10px;
                    &:before {
                        color: transparent;
                        line-height: 18px;
                        transform: translate(-50%, -50%);
                        top: 44%;
                        left: 52%;
                    }
                }
                &.slick-active {
                    button:before {
                        opacity: 1;
                        font-size: 1.5rem;
                        color: inherit;
                    }
                }
            }
        }
    }
    .block-nav {
        @media (min-width: 1024px) {
            text-transform: uppercase;
            .module-title .title {
                text-transform: none;
            }
            .tab-title {
                margin-bottom: 1.5rem;
                padding-bottom: 0.25rem;
                font-size: 1.35rem;
                cursor: pointer;
                border-bottom: 1px solid transparent;
                &.active {
                    border-color: currentColor;
                }
            }
        }
    }
    .mobile-accordion {
        .image-wrapper {
            min-height: 400px;
            width: 100%;
            margin-bottom: 1.5rem;
        }
        .accordion-item {
            padding: 0.5rem 1rem;
            margin-bottom: 0.5rem;
            @media (min-width: 1024px) {
                padding: 0.5rem;
            }
            .heading {
                display: flex;
                justify-content: space-between;
                align-items: center;
                font-size: 1rem;
                cursor: pointer;
                .acc-title {
                    margin: 0;
                    pointer-events: none;
                }
                .icon {
                    position: relative;
                    height: 15px;
                    width: 15px;
                    min-width: 15px;
                    margin-left: 1rem;
                    display: flex;
                    flex-direction: column;
                    justify-content: center;
                    pointer-events: none;
                    .one,
                    .two {
                        position: absolute;
                        content: "";
                        width: 100%;
                        height: 3px;
                        border-radius: 10px;
                        transition: 0.2s;
                    }
                    .one {
                        transform: rotate(90deg);
                    }
                }
                &.open .icon .one {
                    transform: rotate(90deg) scale(0);
                }
            }
            .content {
                max-height: 0;
                overflow: hidden;
                transition: all 0.3s;
            }
            &.open {
                .content {
                    max-height: 100vh;
                    padding: 1rem 1rem 0 0;
                }
            }
        }
    }
    &.accordion {
        .block-nav {
            text-transform: none;
            .mobile-accordion {
                .accordion-item {
                    .heading {
                        font-weight: bolder;
                        .acc-title {
                            margin: 0;
                        }
                    }
                }
            }
            @media (min-width: 1024px) {
                .module-title .title,
                h1 {
                    font-size: 2rem;
                }
                .mobile-accordion {
                    .accordion-item {
                        .heading {
                            flex-direction: row-reverse;
                            justify-content: flex-end;
                            pointer-events: all;
                            .acc-title {
                                pointer-events: none;
                            }
                            .icon {
                                margin: 0 1rem 0 0;
                            }
                        }
                    }
                }
            }
        }
        @media (min-width: 1024px) {
            &.vertical_left .tabbed-content {
                box-sizing: border-box;
                padding-left: 20px;
            }
            &.vertical_right .tabbed-content {
                box-sizing: border-box;
                padding-right: 20px;
            }
        }
    }
    &.underline {
        text-decoration: none;
    }
    &.animated {
        .animated-first-col {
            text-align: center;
            @media (min-width: 1024px) {
                text-align: left;
            }
            .first-col-button {
                margin: 2rem 0;
            }
        }

        .mobile-carousel {
            .tab {
                .inner-wrapper {
                    display: block;
                    img {
                        position: relative;
                        height: 430px;
                    }
                }
                .content-wrapper {
                    text-align: center;
                    padding: 0;
                    margin: 4rem 0 2rem;
                    h3 {
                        margin-bottom: 0.5rem;
                    }
                }
            }
            .slick-dots {
                top: 450px;
                bottom: unset;
                .slick-active {
                    button {
                        background-color: currentColor;
                        &::before {
                            display: none;
                        }
                    }
                }
            }
        }

        @media (min-width: 1024px) {
            .block-nav {
                text-transform: none;
                .tab-title {
                    color: initial !important;
                    margin-bottom: 0.5rem;
                    display: inline-block;
                    padding: 0;
                    font-weight: 700;
                    border: none;
                }
                .content-wrapper {
                    margin: 0 2rem 2.5rem 0;
                }
            }
            .tabbed-content {
                .tab {
                    .inner-wrapper {
                        min-height: unset;
                        img {
                            position: relative;
                            transition: 1s;
                        }
                    }
                    &.active {
                        .inner-wrapper {
                            img {
                                animation: fadeIn 8s;
                            }
                        }
                    }
                }
            }
        }
    }
}
@keyframes fadeIn {
    0% {opacity: 0;}
    10% {opacity: 1;}
    90% {opacity: 1;}
    100% {opacity: 0;}
}