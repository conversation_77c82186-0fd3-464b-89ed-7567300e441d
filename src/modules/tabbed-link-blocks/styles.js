import styled from 'styled-components';

export const TabbedLinkBlocks = styled.div`
    background-color: ${props => props.backgroundColor};
    .content a {
        color: ${props => props.linkColor};
        text-decoration: underline;
        &[role='button'] {
            text-decoration: none;
        }
    }
    &.dark {
        .hide-for-large, .block-nav {
            .title, .blurb {
                color: #fff;
            }
        }
        .mobile-accordion {
            color: #fff;
        }
    }
`;
export const TabTitle = styled.div`
    position: relative;
    &:after {
        content: '';
        position: absolute;
        display: block;
        height: 4px;
        background-color: ${props => props.timerColor};
        transition: width ${props => props.time}s linear;
        animation-duration: ${props => props.time}s;
        // animation-iteration-count: infinite;
        animation-fill-mode: forwards;
        animation-delay: .5s;
    }
    &.active {
        &:after {
            animation-name: timerWidth;
            animation-play-state: ${props => props.playState};
        }
    }
    @keyframes timerWidth {
        0% {
            width: 0;
        }
        100% {
            width: 100%;
        }
    }
`