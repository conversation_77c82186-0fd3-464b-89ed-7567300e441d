import React, { useState, useEffect } from "react";
import Slider from "react-slick";
import { decode } from 'html-entities';
import { useInView } from 'react-intersection-observer';
// HELPERS
import { BackgroundColor } from 'src/helpers/theme';
import { Coloring } from 'src/helpers';
const Imaging = React.lazy(() => import('src/helpers/imaging'));
//PARTIALS
const Button = React.lazy(() => import('src/partials/button'));
//STYLES
import { TabbedLinkBlocks, TabTitle } from './styles';
import './index.scss';

import "slick-carousel/slick/slick.scss";
import "slick-carousel/slick/slick-theme.scss";

const Start = ({ data, settings }) => {
    const bgColor = data.background_color ? Coloring(data.background_color, settings) : 'none';
    const { ref, inView } = useInView({
        triggerOnce: true,
        fallbackInView: true
    });

    let firstColClasses = '';
    if (data.title_alignment === 'tabbed' && data.style != 'animated') {
        firstColClasses = 'center hide-for-large'
    } else if (data.style == 'animated') {
        firstColClasses = 'animated-first-col large-4'
    }

    return (
        <TabbedLinkBlocks
            ref={ref}
            className={`tabbed-link-blocks ${data.type} ${data.style} ${data.background_value}`}
            linkColor={Coloring(data.tab_color_selected, settings)}
            backgroundColor={bgColor}
            textColor={data.background_value === 'dark' ? '#fff' : Coloring('body_copy_color', settings)}
        >
            {inView ?
                <div className="grid-container">
                    <div className="grid-x">
                        {(data.title || data.blurb) &&
                            <div className={`cell ${data.style != 'animated' ? data.title_alignment : ''} ${firstColClasses}`}>
                                {(data.style != 'animated' && data.title) &&
                                    <h2 className='title padding-horizontal-1 primary-txt'>{decode(data.title)}</h2>
                                }
                                <div className='blurb' dangerouslySetInnerHTML={{ __html: data.blurb }} />
                                {(data.style == 'animated' && data.button) &&
                                    <Button className='first-col-button' title={data.button.title} url={data.button.url} target={data.button.target} type={data.button_style} tone={data.background_value} />
                                }
                            </div>
                        }
                        <TabbedContent data={data} settings={settings} />
                        {(data.mobile_style === 'carousel' && data.style !== 'accordion' || data.style == 'animated') &&
                            <MobileCarousel data={data} settings={settings} />
                        }
                        {(data.mobile_style === 'accordion' || data.style === 'accordion') && data.style != 'animated' &&
                            <MobileAccordion data={data} settings={settings} hideLarge={true} />
                        }
                    </div>
                </div>
                : null}
        </TabbedLinkBlocks>
    );
}

const TabbedContent = ({ data, settings }) => {
    const [active, setActive] = useState(false);
    const [activeIndex, setActiveIndex] = useState(0);
    const [timerId, setTimerId] = useState(null);
    const totalTabs = data.tabs?.length;
    let initial = 1;
    let time = 8000;
    let timer;

    useEffect(() => {
        setActive({ 'item-0': true })
    }, []);

    // interval for style = animated
    const interval = (e) => {
        let indexNumber = e?.target?.dataset?.index?.split('-')[1];
        // if clicked and a timer exists clearInterval and set to clicked index
        if (indexNumber && timerId) {
            clearInterval(timerId)
            timer = null;
            setTimerId(null)
            initial = parseInt(indexNumber);
            setActiveIndex(initial);
            initial++;
        }
        if (!indexNumber || timerId) {
            timer = setInterval(() => {
                setTimerId(timer);
                if (initial >= totalTabs) {
                    initial = 0;
                }
                setActiveIndex(initial);
                initial++;
            }, time);
        }
    }

    const setActiveTab = (e) => {
        setActive({ [e.target.dataset.index]: true });
    }

    useEffect(() => {
        // if style = animated start the interval
        if (data.style == 'animated') {
            interval();
            return () => clearTimeout(timerId);
        }
    }, [totalTabs]);

    useEffect(() => {
        // if style = animated set active tab on activeIndex change
        if (data.style == 'animated') {
            setActive({ [`item-${activeIndex}`]: true })
        }
    }, [activeIndex]);

    return (
        <>
            <BlockNav data={data} settings={settings} checkActive={data.style == 'animated' ? interval : setActiveTab} active={active} />
            <div className={`cell ${data.style === 'animated' ? 'large-4' : 'large-9'} tabbed-content show-for-large`}>
                {data.tabs && data.tabs.map((tab, index) => <Tab data={data} tab={tab} settings={settings} index={index} active={active} />)}
            </div>
        </>
    );

}
const BlockNav = ({ data, settings, checkActive, active }) => {

    const setState = (e) => {
        checkActive(e);
    }

    const defaultColor = {
        color: data.background_value === 'dark' ? '#fff' : BackgroundColor(data.tab_color_default)
    }

    const selectedColor = {
        color: data.background_value === 'dark' ? '#fff' : BackgroundColor(data.tab_color_selected)
    }

    return (
        <div className={`cell ${data.style === 'animated' ? 'large-4' : 'large-3'} block-nav show-for-large`}>
            {(data.title || data.blurb) && data.title_alignment === 'tabbed' && data.style !== 'animated' &&
                <div className='module-title'>
                    {data.title &&
                        <h2 className={`title primary-txt`}>{decode(data.title)}</h2>
                    }
                    <div className='blurb' dangerouslySetInnerHTML={{ __html: data.blurb }} />
                </div>
            }
            {data.style !== 'accordion' && data.tabs && data.tabs.map((tab, index) => (
                <>
                    {data.style === 'animated' &&
                        <TabTitle timerColor={settings.design?.colors?.primary_color} time={8} playState={'running'} className={`tab-title ${active[`item-${index}`] ? 'active' : ''} ${data.bold_selected_tab && active[`item-${index}`] ? 'strong' : ''}`} style={active[`item-${index}`] ? selectedColor : defaultColor} onClick={setState} data-index={`item-${index}`}>{tab.tab_title}</TabTitle>
                    }
                    {data.style !== 'animated' &&
                        <div className={`tab-title ${active[`item-${index}`] ? 'active' : ''} ${data.bold_selected_tab && active[`item-${index}`] ? 'strong' : ''}`} style={active[`item-${index}`] ? selectedColor : defaultColor} onClick={setState} data-index={`item-${index}`}>{tab.tab_title}</div>
                    }
                    {data.style === 'animated' &&
                        <div className={`content-wrapper`}>
                            <div className='tab-content' dangerouslySetInnerHTML={{ __html: tab.tab_content }} />
                            {tab.button &&
                                <Button title={tab.button.title} url={tab.button.url} target={tab.button.target} type={tab.button_style} tone={tab.background_value} />
                            }
                        </div>
                    }
                </>
            ))}
            {data.style === 'accordion' &&
                <MobileAccordion data={data} settings={settings} checkActive={setState} />
            }
        </div>
    );

}

const MobileCarousel = ({ data, settings }) => {
    let sliderSettings = {
        infinite: false,
        slidesToShow: 2,
        slidesToScroll: 1,
        arrows: false,
        dots: true,
        centerMode: data.style != 'animated' ? true : false,
        centerPadding: data.style != 'animated' ? '30px' : 0,
        appendDots: dots => <ul>{dots}</ul>,
        customPaging: i => (
            <button style={{ color: data.style == 'animated' ? settings.design?.colors?.body_copy_color : settings.design?.colors?.tertiary_color }} />
        ),
        responsive: [
            {
                breakpoint: 768,
                settings: {
                    slidesToShow: 1,
                    slidesToScroll: 1,
                    centerMode: true,
                }
            },
        ]
    };
    return (
        <Slider className='cell mobile-carousel hide-for-large' {...sliderSettings}>
            {data.tabs && data.tabs.map((tab, index) => <Tab data={data} tab={tab} index={index} active={false} />)}
        </Slider>
    );

}

const Tab = ({ data, tab, index, active }) => {

    const bgColor = BackgroundColor(tab.background_color);

    return (
        <div className={`tab ${active[`item-${index}`] ? 'active' : ''}`}>
            <div className="inner-wrapper" style={data.style !== 'animated' ? { backgroundColor: bgColor } : null}>
                {(tab.background_type === 'image' && tab.background_image && data.style !== 'animated') &&
                    <Imaging data={tab.background_image} />
                }
                {(data.style === 'animated' && tab.third_column_image) &&
                    <Imaging data={tab.third_column_image} />
                }
                {data.style !== 'accordion' &&
                    <div className={`content-wrapper ${data.style !== 'animated' ? tab.background_value : 'hide-for-large'}`}>
                        {(data.style == 'animated' && tab.tab_title) &&
                            <h3>{decode(tab.tab_title)}</h3>
                        }
                        {(data.style != 'animated' && tab.tab_title) &&
                            <h2>{decode(tab.tab_title)}</h2>
                        }
                        <div className='tab-content' dangerouslySetInnerHTML={{ __html: tab.tab_content }} />
                        {tab.button &&
                            <Button title={tab.button.title} url={tab.button.url} target={tab.button.target} type={tab.button_style} tone={tab.background_value} />
                        }
                    </div>
                }
            </div>
        </div>
    );
}

const MobileAccordion = ({ data, settings, checkActive, hideLarge }) => {
    const [open, setOpen] = useState(false);
    const [bgColor, setBgColor] = useState(false);
    const [imageUrl, setImageUrl] = useState();
    const initialBg = BackgroundColor(data.tabs[0].background_color);
    const initialImage = data.tabs[0].background_image ? data.tabs[0].background_image.url : false;

    useEffect(() => {
        setBgColor(initialBg);
        setImageUrl(initialImage);
    }, []);

    function toggle(e) {
        if (e.target?.classList?.contains('open')) {
            setOpen({ [e.target.dataset.index]: false });
            setBgColor(initialBg);
            setImageUrl(initialImage);
            checkActive(e);
        } else {
            setOpen({ [e.target.dataset.index]: true });
            setBgColor(e.target.dataset.bgcolor);
            setImageUrl(e.target.dataset.image);
            checkActive(e);
        }
    }
    let imageWrapperStyle = {
        backgroundColor: bgColor
    }
    if (imageUrl) {
        imageWrapperStyle.background = `url(${imageUrl}) no-repeat center center / cover`;
    }

    return (
        <div className={`cell mobile-accordion ${data.style === 'accordion' && !hideLarge ? '' : 'hide-for-large'}`}>
            {!data.hide_images_mobile &&
                <div className={`image-wrapper ${data.style === 'accordion' ? 'hide-for-large' : ''}`} style={imageWrapperStyle}></div>
            }
            <div className="accordion">
                {data?.tabs?.map((tab, index) => <Strip data={data} tab={tab} index={index} open={open} toggle={toggle} />)}
            </div>
        </div>
    );

}

const Strip = ({ data, tab, index, open, toggle }) => {
    const checkState = (e) => {
        toggle(e);
    }
    const defaultColor = {
        color: data.background_value === 'dark' ? '#fff' : BackgroundColor(data.tab_color_default)
    }

    const selectedColor = {
        color: data.background_value === 'dark' ? '#fff' : BackgroundColor(data.tab_color_selected)
    }

    const iconDefaultColor = {
        backgroundColor: data.background_value === 'dark' ? '#fff' : BackgroundColor(data.tab_color_default)
    }

    const iconSelectedColor = {
        backgroundColor: data.background_value === 'dark' ? '#fff' : BackgroundColor(data.tab_color_selected)
    }

    return (
        <div key={index} class='cell'>
            <div class={`accordion-item ${open[`item-${index}`] ? 'open' : ''}`}>
                <div
                    class={`heading ${open[`item-${index}`] ? 'open secondary-txt' : 'primary-txt'} ${data.bold_selected_tab && open[`item-${index}`] ? 'strong' : ''}`}
                    onClick={checkState}
                    data-index={`item-${index}`}
                    data-bgcolor={BackgroundColor(tab.background_color)}
                    data-image={tab.background_image?.url}
                    style={open[`item-${index}`] ? selectedColor : defaultColor}
                >
                    <h4 class='acc-title'>{decode(tab.tab_title)}</h4>
                    <span class='icon'>
                        <span class={`one ${open[`item-${index}`] ? 'open' : ''}`} style={open[`item-${index}`] ? iconSelectedColor : iconDefaultColor}></span>
                        <span class={`two ${open[`item-${index}`] ? 'open' : ''}`} style={open[`item-${index}`] ? iconSelectedColor : iconDefaultColor}></span>
                    </span>
                </div>
                <div class='content'>
                    <div dangerouslySetInnerHTML={{ __html: tab.tab_content }} />
                    {tab.button &&
                        <Button title={tab.button.title} url={tab.button.url} target={tab.button.target} type={tab.button_style} tone={data.background_value} />
                    }
                </div>
            </div>
        </div>
    );
};

export default Start;