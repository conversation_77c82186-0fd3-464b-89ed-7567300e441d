import React, { Suspense, useState, useEffect } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faSliders } from '@fortawesome/free-solid-svg-icons';
import { decode } from 'html-entities';
import { useInView } from 'react-intersection-observer';

// Helpers
import { Coloring } from "src/helpers";
const Imaging = React.lazy(() => import('src/helpers/imaging'));
// Partials
const Button = React.lazy(() => import('src/partials/button'));
// Styles
import { PostTypeListResource, Resource, FilterScreen, SearchInput, SearchSVG } from './styles';
import './index.scss';

const Start = ({ data, settings }) => {
    const [resources, setResources] = useState(data.resources);
    const [showFilters, setFilters] = useState(false);
    const [searchTerm, setFilterSearch] = useState(false);
    const { ref, inView } = useInView({
        triggerOnce: true,
        fallbackInView: true
    });

    function filterCats(selectedCat) {
        let matched = [];
        let catMatch = false;
        let searchField = searchTerm;
        searchField = searchField ? searchField.toLowerCase() : false;
        data.resources.forEach((resource) => {
            let match = selectedCat === 0 ? true : false;
            if (selectedCat !== 0 || searchField) {
                resource.mvk_item_tax?.cats.map((cat) => {
                    if (cat.term_id === parseInt(selectedCat)) {
                        match = true;
                    }
                    if (selectedCat === 0 || cat.term_id === parseInt(selectedCat)) {
                        if (cat.name.toLowerCase().includes(searchField)) {
                            catMatch = true;
                            match = true;
                        }
                    }
                });
            }
            if (searchField && !resource.title.toLowerCase().includes(searchField) && (!resource.copy || (resource.copy && !resource.copy.toLowerCase().includes(searchField))) && !catMatch) {
                match = false;
            }
            if (match) matched.push(resource);
        });


        filterResources(matched);
    }
    function applyFilter(id) {
        filterCats(id);
    }

    function filterResources(items) {
        setResources(items);
    }

    function setSearchTerm(term) {
        setFilterSearch(term);
    }

    return (
        <Suspense fallback={<div />}>
            <PostTypeListResource
                ref={ref}
                className={`post-type-list-resource ${data.background_type}`}
                bgColor={Coloring(data.background_color, settings)}
                textColor={data.background_value === 'dark' ? '#fff' : Coloring('body_copy_color', settings)}
                bgImage={data.background_type === 'image' ? data.background_image?.url : ''}
            >
                {inView ? <>
                    <div className="grid-container">
                        {(data.title || data.blurb) &&
                            <div className="grid-x">
                                {data.title &&
                                    <div className={`cell ${data.title_alignment}`}>
                                        <h2 className='module-title'>{decode(data.title)}</h2>
                                    </div>
                                }
                                {data.blurb &&
                                    <div className="cell blurb" dangerouslySetInnerHTML={{ __html: data.blurb }} />
                                }
                            </div>
                        }
                        <div className="cell">
                            <div className="filter-button" onClick={() => setFilters(true)}>
                                ALL FILTERS <FontAwesomeIcon icon={faSliders} />
                            </div>
                        </div>
                        {resources && resources.map((row, index) => <Row index={index} row={row} data={data} settings={settings} />)}
                        {resources && resources.length === 0 &&
                            <div className='cell'>
                                <h4>No matches for the search criteria.</h4>
                            </div>
                        }
                    </div>
                    <Filters data={data} settings={settings} filterID={applyFilter} showFilters={showFilters} setFilters={setFilters} setSearchTerm={setSearchTerm} searchTerm={searchTerm} />
                </> : null}
            </PostTypeListResource>
        </Suspense>
    )
}

const Row = ({ index, row, data, settings }) => {
    const [open, setOpen] = useState(index === 0);

    const toggleOpen = () => {
        setOpen(open ? false : true);
    }

    return (
        <Resource
            className={`resource cell ${open ? 'open' : ''}`}
            bgClosed={Coloring(data.closed_accordion_background_color, settings)}
            txtColorClosed={data.closed_accordion_background_value === 'dark' ? '#fff' : settings?.design?.colors?.body_copy_color}
            bgOpen={Coloring(data.open_accordion_background_color, settings)}
            txtColorOpen={data.open_accordion_background_value === 'dark' ? '#fff' : settings?.design?.colors?.body_copy_color}
        >
            <div class={`heading`} onClick={toggleOpen}>{decode(row.title)}<span class='icon'><span class='one'></span><span class='two'></span></span></div>
            <div class='content'>
                {row.feat_image &&
                    <div className="image-wrapper">
                        <Imaging data={row.feat_image} />
                    </div>
                }
                <div className='inner-wrapper'>
                    {row.copy &&
                        <div className='copy' dangerouslySetInnerHTML={{ __html: row.copy }} />
                    }
                    {row.files && row.files.map((item) => {
                        return (
                            item.resource_type === 'file' ?
                                <div className='button-wrapper'><Button url={item.file?.url} target="_blank" title={item.button_text ? item.button_text : `Download ${getFileExtension(item.file?.mime_type)}`} type={data.button_style} /></div>
                                :
                                <div className='button-wrapper'><Button url={item.resource_link?.url} target={item.resource_link?.target} title={item.resource_link?.title} type={data.button_style} /></div>
                        )
                    }
                    )}
                </div>
            </div>
        </Resource>
    )
}

const Filters = ({ data, settings, filterID, showFilters, setFilters, setSearchTerm, searchTerm }) => {
    const [selectedCat, setSelectedCat] = useState(0);
    const [parent, setParent] = useState(false);

    useEffect(() => {
        // if parent is the same and not the selected category -> remove active class
        let allCats = document.querySelectorAll(`.category[data-parent="${parent}"]:not([data-term_id="${selectedCat}"])`);
        Array.from(allCats).forEach((el) => {
            el.classList.remove('active');
        });
    }, [selectedCat, parent]);

    function selectCategory(e) {
        setSelectedCat(e.target.dataset.term_id);
        setParent(e.target.dataset.parent);
    }

    function applyFilter(e) {
        e.preventDefault();
        filterID(selectedCat);
        setFilters(false);
    }
    function resetFilters(e) {
        e.preventDefault();
        setSelectedCat(0);
        filterID(0);
        setFilters(false);
        setSearchTerm('');
        Array.from(document.querySelectorAll('.category.active')).forEach((el) => el.classList.remove('active'));
    }

    return (
        <FilterScreen
            className={showFilters ? 'show' : 'hidden'}
            bodyCopyColor={settings?.design?.colors?.body_copy_color}
        >
            <div className='inner-wrapper body-copy-txt'>
                <div className='content-container'>
                    <div className='close-button' onClick={() => setFilters(false)}></div>
                    <div classname='search-wrapper'>
                        <SearchForm settings={settings} setSearchTerm={setSearchTerm} searchTerm={searchTerm} />
                    </div>
                    <div className='category-wrapper'>
                        <h3>CATEGORIES</h3>
                        {data.resource_categories && data.resource_categories.map((category) => <Category data={category} selectCategory={selectCategory} selectedCat={selectedCat} />)}
                        <Button className='apply' title='APPLY' onClick={applyFilter} />
                        <Button className='reset' title='RESET' onClick={resetFilters} />
                    </div>
                </div>
            </div>
        </FilterScreen>
    )
}


const Category = ({ data, selectCategory, selectedCat }) => {
    const [active, setActive] = useState(false);

    function setCategory(e) {
        let parent = e.target.dataset.parent ? document.getElementById(`term-${e.target.dataset.parent}`) : e.target.dataset.parent;
        let current = document.getElementById(`term-${e.target.dataset.term_id}`);
        if (active && current.classList.contains('active') && !current.classList.contains('selected') && (parent && !parent.classList.contains('active'))) {
            setActive(false);
        } else {
            setActive(true);
        }
        selectCategory(e);
    }

    return (
        <div className={`cat-group${parseInt(selectedCat) === parseInt(data.term_id) ? ' selected' : ''}`}>
            <div
                id={`term-${data.term_id}`}
                className={`category${active ? ' active' : ''}${data.parent === 0 ? ' top-level' : ''}${parseInt(selectedCat) === parseInt(data.term_id) ? ' selected' : ''}`}
                data-term_id={data.term_id}
                data-parent={data.parent}
                onClick={setCategory}>
                {decode(data.name)}{data.children && <span class='icon'><span class='one'></span><span class='two'></span></span>}
            </div>
            {data.children &&
                <div className={'children'}>
                    {data.children.map((child) => <Category data={child} selectCategory={selectCategory} selectedCat={selectedCat} />)}
                </div>
            }
        </div>
    )
}

const SearchForm = ({ settings, setSearchTerm, searchTerm }) => {

    const SetTerm = (e) => {
        e?.preventDefault();
        var term = document?.getElementById(`resource-search`)?.value;
        setSearchTerm(term);
    };

    return (
        <div class="search-form-container flex-container flex-dir-row">
            <form id="filter-search" method="post" action="#" autocomplete="off" onSubmit={SetTerm} onChange={SetTerm} class="flex-child-grow" role="search" aria-label="resources">
                <label htmlFor={`resource-search`}>
                    <SearchInput id={`resource-search`} filterColor={settings?.design?.colors?.primary_color} type="text" className="search" value={searchTerm ? searchTerm : ''} placeholder={'Search Filter'} />
                </label>
            </form>
            <SearchSVG filterColor={settings?.design?.colors?.primary_color} onClick={SetTerm} version="1.1" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" aria-label="search icon">
                <g id="info" />
                <g id="icons">
                    <path d="M22.4,19.6l-4.8-4.8c0.9-1.4,1.4-3,1.4-4.8c0-5-4-9-9-9s-9,4-9,9s4,9,9,9c1.8,0,3.4-0.5,4.8-1.4l4.8,4.8   c0.4,0.4,0.9,0.6,1.4,0.6c1.1,0,2-0.9,2-2C23,20.4,22.8,19.9,22.4,19.6z M5,10c0-2.8,2.2-5,5-5s5,2.2,5,5s-2.2,5-5,5S5,12.8,5,10z" id="icon-search" />
                </g>
            </SearchSVG>
        </div>
    )
}
export default Start;

function getFileExtension(val) {
    if (val.includes('/'))
        val = val.split('/')[1];
    if (val.includes('.'))
        val = val.split('.')[1]
    return val.toUpperCase();
}