import styled from 'styled-components';

export const PostTypeListResource = styled.div`
    position: relative;
    color: ${props => props.textColor};
    padding: 2rem 0;
    @media (min-width: 640px) {
        min-height: 1000px;
    }
    .module-title, .blurb {
        margin-bottom: 1rem;
    }
    @media (min-width: 1200px) {
        padding: 3rem 0;
    }
    &.color {
        background-color: ${props => props.bgColor};
    }
    &.image {
        background-image: url(${props => props.bgImage});
        background-position: center center;
        background-size: cover;
        background-repeat: no-repeat;
    }
    .filter-button {
        display: inline-block;
        margin: 1rem 0 2rem;
        font-size: 1.25rem;
        font-weight: bold;
        cursor: pointer;
        svg {
            margin-left: .5rem;
        }
    }
`;

export const Resource = styled.div`
    background-color: ${props => props.bgClosed};
    color: ${props => props.txtColorClosed};
    border-radius: 10px;
    margin-bottom: .75rem;
    overflow: hidden;
    .heading {
        display: flex;
        justify-content: space-between;
        font-size: 1rem;
        font-weight: bold;
        cursor: pointer;
        padding: 1rem;
        .icon {
            position: relative;
            height: 20px;
            width: 20px;
            min-width: 20px;
            margin-left: 1rem;
            display: flex;
            flex-direction: column;
            justify-content: center;
            pointer-events: none;
            .one, .two {
                position: absolute;
                content: '';
                width: 100%;
                height: 3px;
                border-radius: 10px;
                transition: .3s;
                background-color: ${props => props.txtColorClosed};
            }
            .one {
                transform: rotate(90deg);
            }
        }
    }
    .content {
        max-height: 0;
        padding: 0 1rem;
        opacity: 0;
        transition: .3s;
        & > * {
            display: none;
        }
        .image-wrapper {
            margin-bottom: 1rem;
            img {
                width: 100%;
            }
        }
        .inner-wrapper {
            .copy {
                margin-bottom: 2rem;
            }
            a[role='button'] {
                margin-bottom: 1rem;
            }
        }
        @media (min-width: 768px) {
            display: flex;
            & > div {
                width: 50%;
            }
            .heading {
                font-size: 1.25rem;
            }
            .image-wrapper {
                margin-right: 2rem;
            }
        }
        @media (min-width: 1200px) {
            .image-wrapper {
                width: 33%;
                margin-right: 3rem;
            }
            .inner-wrapper {
                width: 66%;
            }
        }
    }
    &.open {
       background-color: ${props => props.bgOpen};
       color: ${props => props.txtColorOpen};
       .heading .icon {
           .one,.two {
                background-color: ${props => props.txtColorOpen};
           }
           .one {
                transform: rotate(90deg)scale(0);
           }
       }
       .content {
            max-height: 10000px;
            opacity: 1;
            padding: 1rem 1rem 2rem;
             & > * {
                display: block;
            }
       }
    }
   
`;

export const FilterScreen = styled.div`
    position: absolute;
    top: 0;
    background: rgba(0,0,0,.6);
    width: 100%;
    height: 100%;
    box-sizing: border-box;
    .inner-wrapper {
        background: #fff;
        width: 100%;
        box-sizing: border-box;
        padding: 2rem 1rem;
        height: 100%;
        position: absolute;
        left: -100%;
        max-width: 600px;
        transition: .3s;
        @media (min-width: 1200px) {
            padding: 2rem;
        }
        .search-form-container {
            max-width: 75%;
            margin-bottom: 3rem;
            svg {
                width: 1.5rem;
            }
        }
        .category-wrapper {
            h3 {
                font-weight: bold;
                border-bottom: 1px solid #ddd;
                padding-bottom: 1rem;
            }
            .category.top-level { 
                &.active, &.selected {
                    & ~ .children {
                        display: block;
                    } 
                    .icon {
                        .one {
                                transform: rotate(90deg)scale(0);
                        }
                    }
                }
                // & ~ .cat-group {
                //     display: none;
                // }
            }
            // .cat-group.top-level:not(.selected) {
            //     display: none;
            // }
            .category {
                padding: 0.75rem 0 0.75rem 0.75rem;
                border-bottom: 1px solid #ddd;
                display: flex;
                justify-content: space-between;
                cursor: pointer;
                &:not(.top-level) {
                    &.selected, &.active {
                        & ~ .children {
                            display: block;
                        }
                        .icon {
                            .one {
                                    transform: rotate(90deg)scale(0);
                            }
                        }
                    }
                }
                &.selected {
                    font-weight: bold;
                    text-decoration: underline;
                    & ~ .children {
                        .category.active {
                            .icon .one {
                                transform: rotate(90deg);
                            }
                        }
                        .children {
                            display: none;
                        }
                    } 
                }
                .icon {
                    position: relative;
                    height: 20px;
                    width: 20px;
                    min-width: 20px;
                    margin-left: 1rem;
                    display: flex;
                    flex-direction: column;
                    justify-content: center;
                    pointer-events: none;
                    .one, .two {
                        position: absolute;
                        content: '';
                        width: 100%;
                        height: 2px;
                        border-radius: 10px;
                        transition: .3s;
                        background-color: ${props => props.bodyCopyColor};
                    }
                    .one {
                        transform: rotate(90deg);
                    }
                }
            }
            .children {
                display: none;
                margin-left: 0.75rem;
            }
            .apply {
                margin: 3rem 0 1rem;
            }
            @media (min-width: 768px) {
                .reset {
                    margin: 3rem 2rem 1rem;
                }
            }
        }
    }
    @media (max-width: 639px) {
        position: fixed;
        z-index: 99999;
    }
    &.show {
        .inner-wrapper {
            left: 0;
        }
    }
`;

// Search Form Input
export const SearchInput = styled.input`
    border-color: ${props => props?.filterColor} !important;
    color: ${props => props?.filterColor};
    border-radius: 0;
    -webkit-appearance: none;
    &::placeholder {
        color: ${props => props?.filterColor};
    }
`;

// Search Icon
export const SearchSVG = styled.svg`
    fill: ${props => props?.filterColor};
`;