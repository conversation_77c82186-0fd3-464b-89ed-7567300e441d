#modules-container {
    .fcc-container {
        margin: 50px auto;
        padding: 20px 0;
        max-width: 1250px;
        @media (min-width: 1200px) {
            padding: 0px;
        }
        .fcc-title {
            margin-top: 1rem;
            margin-bottom: 1rem;
            padding: 0px 10px;
        }
        .slick-slider {
            .slick-list {
                padding: 10px 0px;
            }
            .slick-track {
                display: -webkit-box;
                display: -moz-box;
                display: -ms-flexbox;
                display: -webkit-flex;
                display: flex;
                gap: 1rem;
                .slick-slide {
                    height: inherit;
                    .fcc-slide {
                        border: 0px;
                        max-width: 400px;
                        box-shadow: 0px 0px 5px rgba(0, 0, 0, 0.2);
                        height: 100%;

                        .spacer {
                            padding: 20px;
                        }
                        .fcc-image {
                            margin-bottom: 20px;
                            img {
                                max-width: 200px;
                            }
                        }
                        .fcc-heading {
                            margin-bottom: 10px;
                            font-size: 24px;
                        }
                        .fcc-blurb {
                            font-size: 0.875rem;
                            line-height: 24px;
                        }
                        .fcc-link {
                            font-size: 1rem;
                            margin-top: 1rem;
                            text-decoration: none;

                            @media screen and (min-width: 768px) {
                                font-size: 1.125rem;
                            }

                            a:hover {
                                text-decoration: underline;
                            }
                        }
                    }
                }
            }
            .lifestyle-button {
                box-shadow: none;
            }
        }
    }
}
