import React from "react";
import Slider from "react-slick";
import styled from 'styled-components';
import { useInView } from 'react-intersection-observer';
// Helpers
import Imaging from 'src/helpers/imaging';
import Button from 'src/partials/button';
import { PrevArrow, NextArrow } from 'src/helpers/slick';
// Styles
import "slick-carousel/slick/slick.scss";
import "slick-carousel/slick/slick-theme.scss";
import './single-scroll.scss';

const Start = ({ data, settings }) => {
    const { ref, inView } = useInView({
        triggerOnce: true,
        fallbackInView: true
    });

    const sliderSettings = {
        dots: false,
        adaptiveHeight: false,
        arrows: true,
        autoplay: true,
        autoplaySpeed: 5000,
        pauseOnHover: false,
        infinite: true,
        slidesToShow: 1,
        slidesToScroll: 1,
        speed: 800,
        nextArrow: <NextArrow />,
        prevArrow: <PrevArrow />,
        responsive: [
            {
                breakpoint: 769,
                settings: {
                    dots: true,
                    arrows: false,
                },
            },
        ]
    };

    return (
        <div ref={ref} class='fcc__single-scroll'>
            {inView ?
                <SlideWrapper className='fcc-inner-container' color={settings.design?.colors?.primary_color}>
                    <Slider {...sliderSettings}>
                        {data?.slides && data.slides.map((slide, index) => <SlideData slide={slide} settings={settings} key={index} />)}
                    </Slider>
                </SlideWrapper>
                : null}
        </div>
    );
};

const SlideData = ({ slide, settings }) => {
    return (
        <div class='slide-container'>
            <div class='img-container'>
                <Imaging data={slide.image} />
            </div>

            <div class='content-container grid-container'>
                <h3 class='fcc-heading primary-txt'>{slide.heading}</h3>
                <p class='fcc-blurb'>{slide.blurb}</p>
                {slide.button?.url && <Button title={slide.button.title} url={slide.button.url} target={slide.button.target} type={slide.button_style} />}
            </div>
        </div>
    );
};

const SlideWrapper = styled.div`
    .slick-dots {
        button:before {
            background-color: ${props => props.color}
        }
    }
`;

export default Start;