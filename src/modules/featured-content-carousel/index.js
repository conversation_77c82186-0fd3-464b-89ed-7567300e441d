import React, { Suspense } from 'react';

const Cards = React.lazy(() => import('./cards'));
const SingleScroll = React.lazy(() => import('./single-scroll'));

const Start = ({ data, settings }) => {
    return (
        <Suspense fallback={<div />}>
            {data.style === 'cards' && <Cards data={data} settings={settings} />}
            {data.style === 'single-scroll' && <SingleScroll data={data} settings={settings} />}
        </Suspense>
    );
};

export default Start;