.fcc__single-scroll {
    @media (max-width: 767px) {
        margin-bottom: 3rem;
    }
    .slick-next {
        right: 2%;
    }

    .slick-dots {
        li {
            margin: 0;
            
            &:before {
                content: "";
                display: block;
            }

            button {
                padding: 0;

                &:before {
                    content: "";
                    opacity: .5;
                    left: 0px;
                    line-height: 20px;
                    position: relative;
                    display: inline-block;
                    border-radius: 50%;
                    height: 8px;
                    width: 8px;
                }
            }

            &.slick-active {
                button:before {
                    opacity: .75;
                }
            }
        }

        @media screen and (max-width: 499px) {
            top: 320px
        }

        @media screen and (min-width: 500px) and (max-width: 649px) {
            top: 420px
        }
        
        @media screen and (min-width: 650px) and (max-width: 768px) {
            top: 520px
        }
    }

    .slide-container {
        @media screen and (min-width: 769px) {
            display: grid;
            grid-auto-flow: row;
            grid-template-columns: 1fr 1fr;
        }
    }

    .img-container {
        @media screen and (max-width: 768px) {
            margin-bottom: 3.3125rem;
        }

        @media screen and (min-width: 769px) {
            height: 375px;
        }
        
        @media screen and (min-width: 992px) {
            height: 475px;
        }
        
        @media screen and (min-width: 1200px) {
            height: 600px;
        }
        
        @media screen and (min-width: 1366px) {
            height: 655px;
        }
        
        @media screen and (min-width: 1500px) {
            height: 730px;
        }

        img {
            width: 100%;
            object-fit: cover;
            
            @media screen and (max-width: 499px) {
                height: 300px;
            }

            @media screen and (min-width: 500px) and (max-width: 649px) {
                height: 400px;
            }
            
            @media screen and (min-width: 650px) and (max-width: 768px) {
                height: 500px;
            }

            @media screen and (min-width: 769px) {
                height: 100%;
            }
        }
    }

    .content-container {
        @media screen and (max-width: 768px) {
            text-align: center;
        }

        &.grid-container {
            @media screen and (min-width: 768px) {
                margin: unset;
                max-width: unset;
            }
        }

        @media screen and (min-width: 769px) {
            display: flex;
            flex-flow: column nowrap;
            justify-content: center;
            align-items: flex-start;
            padding: 0 5rem 0 3rem;
        }

        @media screen and (min-width: 1366px) {
            padding: 0 7rem 0 5rem;
        }
    }

    .fcc-heading {
        margin-bottom: 1rem;
    }

    .fcc-blurb {
        margin-bottom: 2rem;
        line-height: 1.5;
    }
}
