import React from "react";
import Slider from "react-slick";
import { useInView } from 'react-intersection-observer';
import { decode } from 'html-entities';
// Helpers
import Imaging from 'src/helpers/imaging';
import { PrevArrow, NextArrow } from 'src/helpers/slick';
// Styles
import "slick-carousel/slick/slick.scss";
import "slick-carousel/slick/slick-theme.scss";
import './cards.scss';

const Start = ({ data, settings }) => {
    const { ref, inView } = useInView({
        triggerOnce: true,
        fallbackInView: true
    });

    var display = (data?.slides?.length < 6) ? data?.slides?.length : 5;

    const sliderSettings = {
        dots: false,
        adaptiveHeight: true,
        arrows: true,
        autoplay: true,
        autoplaySpeed: 5000,
        pauseOnHover: false,
        infinite: true,
        slidesToShow: display,
        slidesToScroll: 1,
        speed: 800,
        nextArrow: <NextArrow />,
        prevArrow: <PrevArrow />,
        responsive: [
            {
                breakpoint: 1000,
                settings: {
                    slidesToShow: 3,
                    slidesToScroll: 1,
                },
            },
            {
                breakpoint: 767,
                settings: {
                    slidesToShow: 2,
                    slidesToScroll: 1,
                },
            },
            {
                breakpoint: 500,
                settings: {
                    slidesToShow: 1,
                    slidesToScroll: 1,
                },
            },
        ]
    };

    return (
        <div ref={ref} class="fcc-container">
            {inView ?
                <>
                    {data?.heading ? <Title data={data} /> : null}
                    <Slider {...sliderSettings}>
                        {data?.slides && data.slides.map(item => <Content item={item} settings={settings} />)}
                    </Slider>
                </>
                : null}
        </div>
    );
}

const Title = ({ data }) => {
    return (
        <div class="fcc-title">
            <h2>{data.heading}</h2>
        </div>
    );
}

const Content = ({ item, settings }) => {
    return (
        <div class="fcc-slide">
            <div class="spacer">
                <div class="fcc-image">
                    <Imaging data={item.image} />
                </div>
                {item.heading &&
                    <div class="fcc-heading">
                        <h3>{item.heading}</h3>
                    </div>
                }
                <div class="fcc-blurb" dangerouslySetInnerHTML={{ __html: item.blurb }} />
                {item?.button?.title && <Linkage button={item.button} settings={settings} />}
            </div>
        </div>
    );
};

const Linkage = ({ button, settings }) => {
    const linkStyles = {
        color: `${settings.design?.colors?.primary_color}`
    }
    return (
        <div class='fcc-link'>
            {button.title && <a href={`${button.url}`} target={button.target} style={linkStyles}>{decode(button.title)}</a>}
        </div>
    );
};

export default Start;
