import React from "react";
import { useInView } from 'react-intersection-observer';

import "./index.scss";

const Start = ({ data, settings }) => {
    const { ref, inView } = useInView({
        triggerOnce: true,
        fallbackInView: true
    });

    return (
        <div ref={ref} class="iframe">
            {inView ? <DisplayOptions data={data} settings={settings} /> : null}
        </div>
    );
};

const DisplayOptions = ({ data, settings }) => {
    switch (data.display_options) {
        case 'full-width':
            return <IFrameFullWidth data={data} settings={settings} />;
        case 'full-bleed':
            return <IFrameFullBleed data={data} settings={settings} />;
        case 'custom-width':
            return <IFrameCustomWidth data={data} settings={settings} />;
    }
};

const IFrameFullWidth = ({ data, settings }) => {
    return (
        <div class="grid-container">
            <div class="grid-x grid-margin-x flex-container center">
                <div class="cell margin-bottom-3">
                    {data.title && <h2 class="margin-vertical-3">{data.title}</h2>}
                    {data.blurb && <div dangerouslySetInnerHTML={{ __html: data.blurb }} />}
                    <iframe title={data.title ? data.title : 'iframe module'} src={data.iframe_url} height={`${data.iframe_height}px`} width="100%" />
                </div>
            </div>
        </div>
    );
};

const IFrameFullBleed = ({ data, settings }) => {
    return (
        <div class="grid-container fluid">
            <div class="grid-x grid-margin-x flex-container center">
                <div class="cell margin-bottom-3">
                    {data.title && <h2 class="margin-vertical-3">{data.title}</h2>}
                    {data.blurb && <div dangerouslySetInnerHTML={{ __html: data.blurb }} />}
                    <iframe title={data.title ? data.title : 'iframe module'} src={data.iframe_url} height={`${data.iframe_height}px`} width="100%" />
                </div>
            </div>
        </div>
    );
};

const IFrameCustomWidth = ({ data, settings }) => {
    return (
        <div class="grid-container fluid">
            <div class="grid-x grid-margin-x flex-container center">
                <div class="cell margin-bottom-3">
                    {data.title && <h2 class="margin-vertical-3">{data.title}</h2>}
                    {data.blurb && <div dangerouslySetInnerHTML={{ __html: data.blurb }} />}
                    <iframe title={data.title ? data.title : 'iframe module'} src={data.iframe_url} height={`${data.iframe_height}px`} width={`${data.iframe_width}px`} />
                </div>
            </div>
        </div>
    );
};

export default Start;