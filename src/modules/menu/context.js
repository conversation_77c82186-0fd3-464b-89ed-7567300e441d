import React, { useState, createContext } from 'react';
import { useLocation } from "react-router-dom";

export const MenuFilterContext = createContext();
export const MenuFilterProvider = (props) => {
    const { hash } = useLocation();

    const [menuFilters, setMenuFiltration] = useState({
        activeMenu: props.menuToggles ? (hash ? hash.replace('#', '').replaceAll('%20', ' ').replaceAll('/', '').toLowerCase() : props.menuToggles[0]?.toLowerCase()) : false,
        activeSection: null,
        toggles: props.menuToggles ? props.menuToggles : false,
        anchorMenuOpen: false
    });

    return (
        <MenuFilterContext.Provider value={[menuFilters, setMenuFiltration]}>
            {props.children}
        </MenuFilterContext.Provider>
    );
}