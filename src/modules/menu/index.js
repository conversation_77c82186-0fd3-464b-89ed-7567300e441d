import React, { useEffect, useState, useContext } from 'react';
import { useInView } from 'react-intersection-observer';
import { decode } from 'html-entities';
import Slider from "react-slick";
import Select from 'react-select';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faArrowUpWideShort, faTimes, faDownload, faChevronLeft, faChevronRight } from '@fortawesome/free-solid-svg-icons'
// Partials
import Button from "src/partials/button";
// Helpers
import { Coloring } from "src/helpers";
import Imaging from "src/helpers/imaging";
import Clicker from 'src/helpers/clicker';
import HtmlParser from 'src/helpers/html-parser';
import { pxToRem } from 'src/helpers';
import { scrollIntoViewWithOffset } from 'src/helpers/scrollIntoViewWithOffset';
import { OppositeStyle } from 'src/helpers/theme';
import { PrevArrow, NextArrow } from 'src/helpers/slick';
// Context 
import { MenuFilterContext, MenuFilterProvider } from './context';
import { SettingsContext } from 'src/context';
import { AppContext } from 'src/contexts/app';
// Styles
import * as S from './styles';
import "slick-carousel/slick/slick.scss";
import "slick-carousel/slick/slick-theme.scss";

const Start = ({ data, settings, placeholders }) => {
    const { ref, inView } = useInView({
        triggerOnce: true,
        fallbackInView: true,
    });
    const [menuToggles, setMenuToggles] = useState((settings.current_location && data.menu_toggles && data.menu_toggles[settings.current_location]) ? data.menu_toggles[settings.current_location] : data.menu_toggles);
    const bgImage = (placeholders && data.background_image_selection === 'dynamic') ? placeholders.image[data?.background_image_dynamic] : data?.background_image

    useEffect(() => {
        setMenuToggles((settings.current_location && data.menu_toggles && data.menu_toggles[settings.current_location]) ? data.menu_toggles[settings.current_location] : data.menu_toggles)
    }, [settings.current_location])

    return (
        <MenuFilterProvider menuToggles={menuToggles}>
            <S.Menu
                ref={ref}
                className={`menu-module ${data.background_type}`}
                bgColor={Coloring(data.background_color, settings)}
                textColor={data.background_value === 'dark' ? '#fff' : Coloring('body_copy_color', settings)}
                bgImage={data.background_type === 'image' ? bgImage?.url : ''}
            >
                {(data.title || data.blurb) && <Title data={data} placeholders={placeholders} />}
                {menuToggles && <MenuToggles menuToggles={menuToggles} data={data} settings={settings} />}
                <Menus menuToggles={menuToggles} data={data} settings={settings} />
            </S.Menu>
        </MenuFilterProvider>
    )
}

const Title = ({ data, placeholders }) => {
    const title = (placeholders && data.title_selection === 'dynamic') ? placeholders.single_line[data.title] : data.title;

    return (
        <div className='grid-container'>
            <div className='grid-x'>
                <div className='cell'>
                    {title &&
                        <div className={`cell ${data.title_alignment}`}>
                            <h2 className={`module-title ${data.background_value === 'dark' ? 'white-txt' : 'primary-txt'}`}>{decode(title)}</h2>
                        </div>
                    }
                    {data.blurb &&
                        <div className="cell blurb"><HtmlParser html={data.blurb} placeholders={placeholders} /></div>
                    }
                </div>
            </div>
        </div>
    )
}

const MenuToggles = ({ menuToggles, data, settings }) => {
    const [menuFilters, setMenuFiltration] = useContext(MenuFilterContext);
    const [buttonStyle] = useState(OppositeStyle(settings?.mvk_theme_config?.other?.primary_button_style));
    const [oppositeStyle] = useState(settings?.mvk_theme_config?.other?.primary_button_style);
    const [toggleTextColor] = useState((data.menu_toggle_color === 'primary_color' || data.menu_toggle_color === 'secondary_color') ? '#fff' : Coloring('body_copy_color', settings));
    if (menuToggles && menuToggles.length > 1) {
        return (
            <S.MenuToggles
                className={'menu-toggles'}
                toggleColor={Coloring(data.menu_toggle_color, settings)}
                toggleTextColor={toggleTextColor}
            >
                <div className='grid-container'>
                    <div className='grid-x'>
                        <div className='cell hide-for-large'>
                            <SelectToggles menuToggles={menuToggles} data={data} settings={settings} />
                        </div>
                        <div className='cell show-for-large'>
                            {menuToggles && menuToggles.map((toggle, i) =>
                                <Button
                                    className={`filter-button${menuFilters.activeMenu === toggle.toLowerCase() ? ' active' : ''}`}
                                    key={`filter-${i}`}
                                    title={decode(toggle)}
                                    forceStyle={menuFilters.activeMenu === toggle.toLowerCase() ? oppositeStyle : buttonStyle}
                                    onClick={() => setMenuFiltration({ ...menuFilters, activeMenu: toggle.toLowerCase() })}
                                    buttonFunction='styled'
                                    aria-label={`show ${toggle} menu`}
                                    tabindex={0}
                                    noIcon={true}
                                    active={menuFilters.activeMenu === toggle.toLowerCase() ? 'true' : 'false'}
                                />
                            )}
                        </div>
                    </div>
                </div>
            </S.MenuToggles>
        )
    } else {
        return null;
    }
}

const SelectToggles = ({ menuToggles, data, settings }) => {
    const [menuFilters, setMenuFiltration] = useContext(MenuFilterContext);
    const [toggleTextColor] = useState((data.menu_toggle_color === 'primary_color' || data.menu_toggle_color === 'secondary_color') ? '#fff' : Coloring('body_copy_color', settings));
    const [toggleColor] = useState(Coloring(data.menu_toggle_color, settings));

    let options = [];
    menuToggles.map((toggle, i) => {
        options.push({ value: toggle, label: toggle })
    })

    const Styles = {
        control: (styles, state) => ({
            ...styles,
            backgroundColor: toggleColor,
            borderRadius: `${settings.mvk_theme_config?.other?.button_border_radius_size}px`,
            border: 'none',
            cursor: 'pointer',
            padding: '0 1rem'
        }),
        menu: (styles, state) => ({ ...styles, border: `2px solid ${toggleColor}` }),
        option: (styles, state) => ({
            ...styles,
            color: state.isSelected ? toggleTextColor : settings.design.colors.body_copy_color,
            backgroundColor: state.isSelected ? toggleColor : '#fff',
            cursor: 'pointer',
        }),
        indicatorSeparator: (styles, state) => ({ ...styles, color: 'transparent', backgroundColor: 'transparent' }),
        dropdownIndicator: (styles, state) => ({
            ...styles,
            color: toggleTextColor,
        }),
        singleValue: (styles, state) => ({
            ...styles,
            color: toggleTextColor
        })
    }
    const [selectValue, setSelectValue] = useState(options[0])

    useEffect(() => {
        setSelectValue(() => options.find((i) => {
            return i.value.toLowerCase() === menuFilters.activeMenu
        }))
    }, [menuFilters]);

    return (
        <Select
            className='menu-dropdown'
            name='select menu'
            onChange={(e) => setMenuFiltration({ ...menuFilters, activeMenu: e.value.toLowerCase() })}
            aria-label='select a menu'
            options={options}
            value={selectValue}
            styles={Styles}
            isSearchable={false}
        />
    )

}

const Menus = ({ menuToggles, data, settings }) => {
    const [menuFilters, setMenuFiltration] = useContext(MenuFilterContext);
    const [menu, setMenu] = useState(null);
    const appContext = useContext(AppContext);

    useEffect(() => {
        if (data.menus) {
            setMenu(() => data.menus.find((i) => {
                return (i.selector_text.toLowerCase() === menuFilters.activeMenu) && (!settings.current_location || i.locations?.includes(settings.current_location))
            }))
        }
    }, [settings.current_location, menuFilters]);

    if (menu) {
        return (
            <div className='menu-default'>
                {menu.pdf_menu &&
                    <div className='grid-container download-container'>
                        <div className='grid-x'>
                            <div className='cell'>
                                <Clicker className='pdf' type='anchor' url={menu.pdf_menu.url} target="_blank" ariaLabel={`open pdf menu`}>
                                    <FontAwesomeIcon className='download-icon' icon={faDownload} aria-label="download menu" />
                                    {data.download_menu_text}
                                </Clicker>
                            </div>
                        </div>
                    </div>
                }
                {menu.menu_description &&
                    <div className='grid-container menu-description'>
                        <div className='grid-x'>
                            <div className='cell'>
                                <HtmlParser html={menu.menu_description} />
                            </div>
                        </div>
                    </div>
                }
                {(menu.section_ids && (menu.section_ids.length > 1 || appContext.width < 1200)) && <Anchors menuToggles={menuToggles} data={data} menu={menu} appContext={appContext} />}
                {menu.sections && <Sections data={data} menu={menu} settings={settings} />}
            </div>
        )
    } else {
        return null;
    }
}

const Anchors = ({ menuToggles, data, menu, appContext }) => {
    const [menuFilters, setMenuFiltration] = useContext(MenuFilterContext);
    const [anchorBottom, setAnchorBottom] = useState(0);
    const [anchorArrows, setAnchorArrows] = useState(true);
    const [settings, setSettings] = useContext(SettingsContext);
    const [bottomCalc, setBottomCalc] = useState(null);
    const bottomMobileNav = settings.mvk_theme_config?.nav?.enable_bottom_mobile_navigation ? 66 : 0;
    const locationSelector = settings.locations?.enable_location_selector ? 56 : 0;
    const headerFixed = settings?.mvk_theme_config?.header?.header_type !== 'static' && settings?.mvk_theme_config?.header?.header_type !== 'absolute';
    useEffect(() => {
        if (!anchorBottom && appContext.width < 1200) {
            setAnchorBottom((bottomMobileNav + locationSelector) - 2);
        }
        if (menuFilters.activeSection) {
            const anchors = document.getElementsByClassName('anchor-link');
            for (let i = 0; i < anchors.length; i++) {
                if (anchors[i].classList.contains('active')) {
                    document.getElementsByClassName('anchor-menu')[0].scrollLeft = anchors[i].offsetLeft;
                }
            }
        }

        if (settings.header_height) {
            setBottomCalc(window.innerHeight - parseInt(headerFixed ? settings.header_height : 24));
        }
    }, [menuFilters, anchorBottom, settings.header_height]);

    setTimeout(() => {
        if (appContext.width >= 1200) {
            let contentArea = (settings?.mvk_theme_config?.layout?.site_max_width ? parseInt(settings?.mvk_theme_config?.layout?.site_max_width) : 75) * 16;
            if (window.innerWidth <= contentArea) {
                contentArea = 1200;
            }
            let anchorBar = document.getElementsByClassName('anchor-menu')[0];
            setAnchorArrows(anchorBar?.offsetWidth >= (contentArea - 82));
        }
    }, 100);

    const anchorClick = (e, sectionID) => {
        e.preventDefault();
        setMenuFiltration({ ...menuFilters, anchorMenuOpen: false })
        let el = document.getElementById(decode(sectionID));
        scrollIntoViewWithOffset(el, 70, 'auto');
    }

    const scrollAnchors = (direction) => {
        const anchorBar = document.getElementsByClassName('anchor-menu')[0];
        const scrollWidth = anchorBar.scrollWidth;
        let scrollLeft = anchorBar.scrollLeft;
        if (direction === 'right') {
            scrollLeft += 200;
            if (scrollLeft >= scrollWidth) scrollLeft = scrollWidth;
            anchorBar.scrollLeft = scrollLeft;
        }
        if (direction === 'left') {
            scrollLeft -= 200;
            if (scrollLeft <= 0) scrollLeft = 0;
            anchorBar.scrollLeft = scrollLeft;
        }
    }

    const { ref, inView } = useInView({
        triggerOnce: false,
        fallbackInView: true,
        rootMargin: (bottomCalc && headerFixed) ? `-${settings.header_height}px 0px -${bottomCalc}px 0px` : (bottomCalc) ? `0px 0px -${bottomCalc}px 0px` : '0px'
    });

    return (
        <S.Anchors
            ref={ref}
            className={`section-anchors${menuFilters.anchorMenuOpen ? ' active' : ''}${inView ? ' sticky' : ''}`}
            anchorBottom={anchorBottom}
            headerHeight={(settings.header_height && settings?.mvk_theme_config?.header.header_type === 'fixed') ? settings.header_height : '0'}
            bgColor={Coloring(data.anchor_bar_background_color, settings)}
            textColorDefault={Coloring('body_copy_color', settings)}
            textColor={data.anchor_bar_background_value === 'dark' ? '#fff' : Coloring('body_copy_color', settings)}
        >
            <div className='grid-container'>
                <div className='grid-x'>
                    <div className='cell'>
                        {!menuFilters.anchorMenuOpen ? <span className='anchor-menu-toggle'><FontAwesomeIcon className='mobile-menu-icon' onClick={() => setMenuFiltration({ ...menuFilters, anchorMenuOpen: !menuFilters.anchorMenuOpen })} icon={faArrowUpWideShort} aria-label="open anchor menu" /></span> : <FontAwesomeIcon role='button' aria-label='close anchor menu' className={'close-menu'} icon={faTimes} onClick={() => setMenuFiltration({ ...menuFilters, anchorMenuOpen: false })} />}
                        {menuFilters.anchorMenuOpen && <SelectToggles menuToggles={menuToggles} data={data} settings={settings} />}
                        {anchorArrows && <FontAwesomeIcon className='anchor-scroll left' icon={faChevronLeft} onClick={() => scrollAnchors('left')} />}
                        <ul className={`anchor-menu`}>
                            {menu.section_ids.map((section) => <li>
                                <Clicker
                                    id={`${section.id}Anchor`}
                                    className={`anchor-link ${menuFilters.activeSection === decode(section.id) ? 'active' : ''}`}
                                    type='anchor'
                                    onClick={(e) => anchorClick(e, section.id)}
                                    url={`#${decode(section.id)}`}
                                    ariaLabel={`anchor to ${decode(section.name)}`}
                                >{decode(section.name)}</Clicker>
                            </li>)}
                        </ul>
                        {anchorArrows && <FontAwesomeIcon className='anchor-scroll right' icon={faChevronRight} onClick={() => scrollAnchors('right')} />}
                    </div>
                </div>
            </div>
        </S.Anchors>
    )
}

const Sections = ({ data, menu, settings }) => {
    const descriptionSizeSmall = settings?.design?.advanced?.paragraph?.size_small ? parseInt(settings?.design?.advanced.paragraph?.size_small) * .875 : '12'
    const descriptionSizeLarge = settings?.design?.advanced?.paragraph?.size_large ? parseInt(settings?.design?.advanced.paragraph?.size_large) * .875 : '14'

    return (
        <S.Sections
            sizeSmall={pxToRem(descriptionSizeSmall)}
            sizeLarge={pxToRem(descriptionSizeLarge)}
        >
            <div className='grid-container'>
                {menu.sections.map((section, i) =>
                    <div className='grid-x grid-margin-x'>
                        <div className='cell'>
                            <Section data={data} settings={settings} section={section} i={i} />
                        </div>
                    </div>
                )}
            </div>
        </S.Sections>
    )
}

const Section = ({ data, settings, section, i }) => {
    const [menuFilters, setMenuFiltration] = useContext(MenuFilterContext);

    const { ref, inView } = useInView({
        triggerOnce: false,
        fallbackInView: true,
        rootMargin: '-20% 0% -50% 0%',
        threshold: 0
    });

    useEffect(() => {
        if (inView) {
            setMenuFiltration({ ...menuFilters, activeSection: decode(section.id) })
        }
    }, [inView]);

    return (
        <div ref={ref} id={decode(section.id)} className={`${i % 2 == 0 ? 'odd-section' : 'even-section'}`}>
            <div className='heading'><h2 className='section-name'>{decode(section.name)}</h2>
                {section.description && <div className='description'><HtmlParser html={section.description} /></div>}
            </div>
            <div className='grid-x grid-margin-x'>
                <MenuItems data={data} section={section} settings={settings} />
                <div className='cell medium-1 show-for-medium spacer'></div>
                <Images section={section} />
            </div>
            {section.note && <div className='note'><HtmlParser html={section.note} /></div>}
        </div>
    )
}

const MenuItems = ({ data, section, settings }) => {

    return (
        <div className='cell medium-5 menu-items'>
            {section.menu_items && section.menu_items.map((item) => <Item item={item} data={data} settings={settings} />)}
            {section.sub_sections && section.sub_sections.map((sub, i) => {
                return (
                    <div className='sub-section'>
                        {sub.name && <div className='sub-section-heading'><h3>{decode(sub.name)}</h3></div>}
                        {sub.menu_items && sub.menu_items.map((item) => <Item item={item} data={data} settings={settings} />)}
                    </div>
                )
            })}
        </div>
    )
}

const Item = ({ item, data, settings }) => {

    return (
        <S.MenuItem
            className={`menu-item${item.featured ? ' featured' : ''}`}
            bgColor={Coloring(data.featured_item_color, settings)}
            textColor={data.featured_item_background_value === 'dark' ? '#fff' : Coloring('body_copy_color', settings)}
        >
            <div className='item-heading'>
                <span>
                    {(item.featured && settings.restaurant.featured_icon) && <Imaging className='featured-icon' data={settings.restaurant.featured_icon} />}
                    {decode(item.name)}
                </span>
                {(data.show_price && item.price) && <span>{item.price}</span>}
            </div>
            {item.description && <div className='item-description'><HtmlParser html={item.description} /></div>}
        </S.MenuItem>
    )
}

const Images = ({ section }) => {

    let sliderSettings = {
        infinite: false,
        slidesToShow: 1,
        slidesToScroll: 1,
        arrows: true,
        dots: false,
        cssEase: 'linear',
        nextArrow: <NextArrow />,
        prevArrow: <PrevArrow />
    };

    return (
        <div className='cell medium-6 images'>
            {section.images &&
                <Slider {...sliderSettings}>
                    {section.images.map((item, i) => {
                        return (
                            <div className='slide'>
                                <Imaging data={item.image} />
                                <div className='caption'>
                                    <span>{decode(item.name)}</span>
                                    {section.images.length > 1 && <span>{i + 1}/{section.images.length}</span>}
                                </div>
                            </div>
                        )
                    })}
                </Slider>
            }
        </div>
    )
}
export default Start;