import styled from 'styled-components';

export const Menu = styled.div`
    color: ${props => props.textColor};
    padding: 2rem 0;
    &.color {
        background-color: ${props => props.bgColor};
    }
    &.image {
        background-image: url(${props => props.bgImage});
        background-position: center center;
        background-size: cover;
        background-repeat: no-repeat;
    }
    .download-container {
        margin-bottom: 3rem;
    }
    .pdf {
        color: ${props => props.textColor};
        display: inline-block;
        text-decoration: underline;
        margin-bottom: .5rem;
        .download-icon {
            margin-right: .5rem;
        }
    }
`

export const MenuToggles = styled.div`
    .menu-dropdown {
        margin-bottom: 1rem;
        @media (min-width: 640px) {
            max-width: 300px;
        }
    }
    @media (min-width: 1024px) {
        margin-bottom: 1rem;
        .filter-button {
            margin-right: 1rem;
        }
        button {
            background: transparent !important;
            border-color: ${props => props.toggleColor} !important;
            color: ${props => props.toggleColor} !important;
            &.active {
                background: ${props => props.toggleColor} !important;
                border-color: ${props => props.toggleColor} !important;
                color: ${props => props.toggleTextColor} !important;
            }
        }
    }
`

export const Anchors = styled.nav`
    position: fixed;
    bottom: ${props => props.anchorBottom}px;
    color: ${props => props.textColor};
    background: ${props => props.bgColor};
    width: 100%;
    z-index: 10;
    box-shadow: 0px 0px 5px rgba(0,0,0,.5);
    @media (min-width: 1200px) {
        position: sticky;
        bottom: unset;
        top: ${props => props.headerHeight}px;
        background: transparent;
        color: ${props => props.textColorDefault};
        box-shadow: none;
        margin-bottom: 1rem;
        &.sticky {
            background: ${props => props.bgColor};
            color: ${props => props.textColor};
            box-shadow: 2px 2px 5px rgba(0,0,0,.5);
        }
    }
    .grid-container {
        @media (max-width: 1199px) {
            padding: 0 0 0 10px !important;
        }
        .cell {
            position: relative;
            overflow-y: hidden;
            scroll-behavior: smooth;
            height: 3rem;
            display: flex;
            align-items: center;
            .mobile-menu-icon {
                padding: .5rem;
                cursor: pointer;
            }
            @media (max-width: 1199px) {
                .anchor-scroll {
                    display: none;
                }
            }
            @media (min-width: 1200px) {
                .anchor-menu-toggle {
                    display: none;
                }
                .anchor-scroll {
                    padding: .5rem;
                    cursor: pointer;
                    svg {
                        opacity: 0;
                        // transition: .2s;
                    }
                    &.show-icon {
                        svg {
                            opacity: 1;
                        }
                    }
                }
            }
            ul {
                position: relative;
                display: flex;
                flex-wrap: nowrap;
                overflow-x: auto;
                list-style: none;
                padding-left: 0;
                margin-block: 0;
                white-space: nowrap;
                scroll-behavior: smooth;
                -webkit-overflow-scrolling: touch;
                -ms-overflow-style: -ms-autohiding-scrollbar;
                scrollbar-width: none;
                a {
                    color: ${props => props.textColor};
                    display: block;
                    padding: calc(.5rem + 2px) .5rem .5rem;
                    &:focus {
                        text-decoration: none;
                    }
                    &:after {
                        content: '';
                        display: block;
                        width: 0;
                        height: 2px;
                        background: ${props => props.textColor};
                        transition: width .3s;
                    }
                    &.active:after {
                        width: 100%;
                    }
                }
            }
        }
    }
    &.active {
        bottom: 0;
        z-index: 99999;
        .grid-container {
            padding: 0 10px !important;
            .cell {
                height: 75vh;
                display: block;
                .close-menu {
                    position: absolute;
                    right: 1rem;
                    top: 1rem;
                    font-size: 2rem;
                    cursor: pointer;
                }
                .menu-dropdown {
                    margin: 4rem 0 1.5rem;
                    @media (min-width: 640px) {
                        max-width: 300px;
                    }
                }
                ul {
                    display: block;
                    overflow: auto;
                    height: 40vh;
                    scrollbar-width: initial;
                    li {
                        width: 100%;
                        a {
                            display: inline-block;
                        }
                    }
                }
            }
        }
    }
`

export const Sections = styled.div`
    .grid-x {
        overflow: hidden;
        &:last-child {
            .odd-section, .even-section {
                border-bottom: none;
            }
        }
    }
    .odd-section, .even-section {
        padding: 3rem 0 2rem;
        border-bottom: 1px solid;
        .heading {
            margin-bottom: 2rem;
        }
        .description {
            margin-bottom: 2rem;
        }
        .menu-items {
            .menu-item {
                .item-description {
                    p {
                        font-size: ${props => props.sizeSmall}rem !important;
                    }
                    @media (min-width: 768px) {
                        p {
                            font-size: ${props => props.sizeLarge}rem !important;
                        }
                    }
                }
            }
        }
        .images {
            .slick-slider {
                .slide {
                    img {
                        height: 90vw;
                        width: 100%;
                        object-fit: cover;
                        @media (min-width: 640px) {
                            height: 48vw;
                        }
                        @media (min-width: 1200px) {
                            height: 600px;
                        }
                    }
                    .caption {
                        margin: 1rem 0;
                        display: flex;
                        justify-content: space-between;
                        font-weight: bold;
                    }
                }
            }
        } 
    }
    @media (min-width: 640px) {
       
        .even-section {
            .spacer {
                order: 2;
            }
            .menu-items {
                order: 3;
            }
        }
        .note {
            margin: 2rem 0;
        }
    }
`

export const MenuItem = styled.div`
    padding: 0 0 1.5rem;
    margin: 0 0 .5rem;
    .item-heading {
        display: flex;
        justify-content: space-between;
        margin-bottom: .25rem;
        font-weight: bold;
        align-items: center;
        span {
            display: flex;
            align-items: center;
            .featured-icon {
                max-width: 2rem;
                margin-right: .5rem;
            }
        }
    }
    .item-description {
        max-width: 75%;
        p {
            margin: 0;
        }
    }
    &.featured {
        padding: .75rem 0;
        margin: .5rem 0 2rem;
        position: relative;
        background-color: ${props => props.bgColor};
        color: ${props => props.textColor}; 
        &:before, &:after {
            content: '';
            position: absolute;
            height: 100%;
            width: 100%;
            background: ${props => props.bgColor};
            top: 0;
        }
        &:before {
            right: 100%
        }
        &:after {
            left: 100%;
        }
        @media (min-width: 640px) {
            &:before, &:after {
                width: 1rem;
            }
        }
    }
`