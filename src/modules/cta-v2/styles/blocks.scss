@import 'src/scss/variables.scss';

#modules-container {
    .call-to-action-v2.blocks {
        .mvk-container.blocks {
            .grid-x.blocks {
                .cell.blocks {
                    height: inherit;

                    .module-item {
                        position: relative;
                        height: 100%;
                        padding: 20px;
                        box-sizing: border-box;

                        .cta-v2-overlay {
                            position: absolute;
                            top: 0px;
                            bottom: 0px;
                            left: 0px;
                            right: 0px;
                            z-index: +1;
                        }

                        .cta-v2-inner {
                            position: relative;
                            z-index: +1;
                            height: 100%;
                            max-width: 100%;
                            box-sizing: border-box;
                            // padding: 0px 20px;

                            display: flex;
                            flex-direction: column;
                            gap: 15px;
                            align-items: stretch;

                            &.dark  { color: white; }
                            &.light { color: black; }
                            &.cards { padding: 20px; }

                            .cta-v2-title {
                                flex-grow: 0;
                                flex-shrink: 1;
                                flex-basis: auto;
                            }
                            .cta-v2-content {
                                line-height: 1.5;
                                flex-grow: 1;
                                flex-shrink: 0;
                                flex-basis: auto;
                            }
                            .cta-v2-button {
                                // white-space: nowrap;
                                flex-grow: 0;
                                flex-shrink: 1;
                                flex-basis: auto;
                            }
                        }
                    }
                }
            }
        }
    }
}