import React, { useEffect, useState } from "react";
import { decode } from 'html-entities';
// HELPERS.
import Imaging from 'src/helpers/imaging';
import Clicker from 'src/helpers/clicker';
import HtmlParser from 'src/helpers/html-parser'

// PARTIALS.
const Button = React.lazy(() => import('src/partials/button'));

const Start = ({ column, placeholders }) => {
    const title = (placeholders && column?.title_selection === 'dynamic') ? placeholders.single_line[column?.title] : column?.title
    const button = placeholders ? placeholders.button_link[column?.button] : column?.button
    const image = (placeholders && column.image_selection === 'dynamic') ? placeholders.image[column?.image_dynamic] : column?.image
    
    var coloring = pylot?.design?.coloring(column?.background_color, 'backgroundColor');
    var style = { coloring };

    if (column?.button?.url) {
        return (
            <Clicker type="anchor" url={button?.url} class={`cta-v2-outer image-button ${column?.background_value} fully-clickable`} target={column?.button?.target} style={style} ariaLabel={`link to ${column?.title ? column?.title : column?.url}`}>
                <div class={`cta-v2-inner image-button ${column?.alignment} body-copy-txt`}>
                    <CtaImage image={image} />
                    <CtaTitle title={title} column={column} />
                    <div class="cta-v2-content">
                        <HtmlParser html={column?.content} placeholders={placeholders} />
                    </div>
                    <CtaButton button={button} column={column} buttonFunction='styled' />
                </div>
            </Clicker>
        );
    } else {
        return (
            <div class={`cta-v2-outer image-button ${column?.background_value} not-clickable`} style={style}>
                <div class={`cta-v2-inner image-button ${column?.alignment} body-copy-txt`}>
                    <CtaImage image={image} />
                    <CtaTitle title={title} column={column} />
                    <div class="cta-v2-content">
                        <HtmlParser html={column?.content} placeholders={placeholders} />
                    </div>
                    <CtaButton button={button} column={column} />
                </div>
            </div>
        );
    }
};

const CtaImage = ({ image }) => {
    if (!(image)) return null;
    return (
        <div class="cta-v2-image">
            <Imaging data={image} />
        </div>
    );
};

const CtaButton = ({ button, column, buttonFunction }) => {
    if (!(button)) return null;
    return (
        <div class={`cta-v2-button ${column?.alignment}`}>
            <Button title={button?.title} url={button?.url} tone={column?.background_value} target={button?.target} icon={column?.button_icon} type={column?.button_style} buttonFunction={buttonFunction} />
        </div>
    );
};

const CtaTitle = ({ title, column }) => {
    if (!(title)) return null;
    return (
        <div class={`cta-v2-title ${column?.alignment}`}>
            <h3>{decode(title)}</h3>
        </div>
    );
};

export default Start;