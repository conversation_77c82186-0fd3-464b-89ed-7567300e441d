import React, { useState, useEffect } from "react";
import { decode } from 'html-entities';

// HELPERS.
const Imaging = React.lazy(() => import('src/helpers/imaging'));
const Clicker = React.lazy(() => import('src/helpers/clicker'));
import { BackgroundColor } from 'src/helpers/theme';
import HtmlParser from 'src/helpers/html-parser'

const Start = ({ column, settings, placeholders }) => {
    const title = (placeholders && column?.title_selection === 'dynamic') ? placeholders.single_line[column?.title] : column?.title
    const link = placeholders ? placeholders.button_link[column?.cta_link] : column?.cta_link
    const image = (placeholders && column.image_selection === 'dynamic') ? placeholders.image[column?.card_image] : column?.card_image

    if (link) {
        return (
            <Clicker type='anchor' url={link?.url} target={link?.target} ariaLabel={`link to ${decode(title)}`}>
                <ImageTextInner title={title} image={image} column={column} settings={settings} placeholders={placeholders} />
            </Clicker>
        );
    } else {
        return (<ImageTextInner title={title} image={image} column={column} settings={settings} placeholders={placeholders} />);
    }
};

const ImageTextInner = ({ title, image, column, settings, placeholders }) => {
    let columnStyle = {
        backgroundColor: column?.card_color ? BackgroundColor(column?.card_color) : 'transparent',
        color: column?.card_background_value === 'dark' ? '#fff' : settings?.design?.colors?.body_copy_color,
        borderRadius: (settings?.mvk_theme_config?.other?.enable_border_radius) ? `${settings?.mvk_theme_config?.other?.border_radius_size}px` : "0px",
    };

    return (
        <div class='cta-v2-inner' style={columnStyle}>
            {image && <Imaging data={image} /> }
            <div class='cta-v2-content'>
                {title && <h3 class='title'>{decode(title)}</h3>}
                {column?.content && <div class="content"><HtmlParser html={column?.content} placeholders={placeholders} /></div>}
            </div>
        </div>
    );
};

export default Start;