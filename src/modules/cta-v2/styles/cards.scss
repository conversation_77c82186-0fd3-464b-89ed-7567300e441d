@import 'src/scss/variables.scss';


#modules-container {
    .call-to-action-v2.cards {
        .mvk-container.cards {
            margin: 30px auto;
            padding-top: 0px;
            padding-bottom: 0px;

            .grid-x.cards {
                margin: 0px auto;
                padding-top: 0px;
                padding-bottom: 0px;

                .cell.cards {
                    height: inherit;

                    .cta-v2-outer {
                        height: inherit;
                        .cta-v2-inner {
                            height: inherit;
                            max-width: 100%;
                            display: flex;
                            flex-direction: column;
                            gap: 15px;
                            align-items: stretch;

                            &.dark  { color: white; }
                            &.light { color: black; }
                            &.cards { padding: 20px; }

                            .cta-v2-image {
                                img {
                                    display: block;
                                }
                            }
                            .cta-v2-content {
                                line-height: 1.5;
                                flex: 1 0 auto;
                            }
                        }
                    }
                }
            }
        }
    }
}