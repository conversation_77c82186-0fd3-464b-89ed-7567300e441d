import React, { useEffect, useState } from "react";
import { decode } from 'html-entities';

// HELPERS.
const Imaging = React.lazy(() => import('src/helpers/imaging'));
const Clicker = React.lazy(() => import('src/helpers/clicker'));
import { BackgroundColor } from 'src/helpers/theme';
import HtmlParser from 'src/helpers/html-parser'

// LAYOUTS.
import Layout from 'src/modules/cta-v2/layout';

// SCSS.
import 'src/modules/cta-v2/styles/staggered.scss';

const Start = ({ column, settings, placeholders }) => {
    return (
        <div class="cell staggered">
            <CTAOuter column={column} settings={settings} placeholders={placeholders} />
        </div>
    );
};

const CTAOuter = ({ column, settings, placeholders }) => {
    const title = (placeholders && column?.title_selection === 'dynamic') ? placeholders.single_line[column?.title] : column?.title
    const label = (placeholders && column?.label_selection) ? placeholders.single_line[column?.label] : column?.label
    const link = placeholders ? placeholders.button_link[column?.cta_link] : column?.cta_link
    const bgImage = (placeholders && column.background_image_selection === 'dynamic') ? placeholders.image[column?.background_image_dynamic] : column?.background_image

    let ctaStyle = {
        backgroundColor: column.background_color ? BackgroundColor(column.background_color) : 'transparent',
        color: column.background_value === 'dark' ? '#fff' : settings.design.colors.body_copy_color,
        borderRadius: (settings?.mvk_theme_config?.other?.enable_border_radius) ? `${settings?.mvk_theme_config?.other?.border_radius_size}px` : "0px",
    };

    if (link) {
        return (
            <div class="cta-v2-outer" style={ctaStyle}>
                <Clicker type="anchor" url={link.url} target={link.target} ariaLabel={`link to ${decode(column?.title)}`}>
                    <CTAInner label={label} title={title} bgImage={bgImage} column={column} settings={settings} placeholders={placeholders} />
                </Clicker>
            </div>
        );
    } else {
        return (
            <div class="cta-v2-outer" style={ctaStyle}>
                <CTAInner label={label} title={title} bgImage={bgImage} column={column} settings={settings} placeholders={placeholders} />
            </div>
        );
    }
};

const CTAInner = ({ label, title, bgImage, column, settings, placeholders }) => {
    return (
        <div class={`cta-v2-inner ${column.background_value}`}>
            {bgImage && 
                <div class="cta-v2-image">
                    <Imaging data={bgImage} />
                </div>
            }
            <Layout.Container.Columns.Item.Overlay column={column} settings={settings} />
            <div class={`cta-v2-content ${column.background_value}`}>
                {label && <div class='label'>{label}</div>}
                {title && <h3 class='title'>{decode(title)}</h3>}
                {column.content && <div class="content"><HtmlParser html={column?.content} placeholders={placeholders} /></div>}
            </div>
        </div>
    );
};

export default Start;