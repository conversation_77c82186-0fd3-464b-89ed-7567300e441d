import React, { useState, useEffect } from "react";
import { decode } from 'html-entities';

// HELPERS.
import Imaging from 'src/helpers/imaging';
import Clicker from 'src/helpers/clicker';
import HtmlParser from 'src/helpers/html-parser'

// PARTIALS.
const Button = React.lazy(() => import('src/partials/button'));

// LAYOUTS.
import Layout from 'src/modules/cta-v2/layout';

// SCSS.
import 'src/modules/cta-v2/styles/blocks.scss';

const Start = ({ data, column, settings, placeholders }) => {
    const title = (placeholders && column?.title_selection === 'dynamic') ? placeholders.single_line[column?.title] : column?.title
    const bgImage = (placeholders && column.background_image_selection === 'dynamic') ? placeholders.image[column?.background_image_dynamic] : column?.background_image
    const button = placeholders ? placeholders.button_link[column?.button] : column?.button
    const link = placeholders ? placeholders.button_link[column?.cta_link] : column?.cta_link
    const image = (placeholders && column.image_selection === 'dynamic') ? placeholders.image[column?.image_dynamic] : column?.image

    var background = {};
    var coloring = pylot?.design?.coloring(column?.background_color, 'backgroundColor');

    if (bgImage) {
        // column?.background_image?.url = pylot?.tools?.domain?.add(column?.background_image?.url);
        background = pylot?.tools?.domain?.add(bgImage?.url);
    }

    if (data?.parallax) {
        background = pylot?.design?.parallax(bgImage?.url);
    } else {
        background = (bgImage) ? { backgroundImage: `url(${bgImage?.url})`, backgroundRepeat: 'no-repeat', backgroundPosition: 'center', backgroundSize: 'cover' } : {};
    }

    var style = { ...background, ...coloring, color: column?.background_value === 'dark' ? '#fff' : settings?.design?.colors?.body_copy_color };

    let role = bgImage ? 'img' : null;
    let bgAlt = (bgImage && bgImage?.alt) ? bgImage?.alt : null;

    return (
        <Layout.Container.Columns.Item column={column} settings={settings} role={role} aria-label={bgAlt} style={style}>

            <Layout.Container.Columns.Item.Overlay column={column} settings={settings} />

            <div class={`cta-v2-inner ${data?.display_options} ${data?.style} ${column?.alignment} ${column?.background_value}`}>

                {title &&
                    <div class={`cta-v2-title ${column?.alignment}`}>
                        <h3>{decode(title)}</h3>
                    </div>
                }
                <div class="cta-v2-content">
                    <HtmlParser html={column?.content} placeholders={placeholders} />
                </div>
                {button?.url &&
                    <div class={`cta-v2-button ${column?.alignment}`}>
                        <Button title={button?.title} url={button?.url} target={button?.target} tone={column?.background_value} type={column?.button_style} aria-label={`link to ${button?.title}`} />
                    </div>
                }

                {link &&
                    <Clicker type={'anchor'} class={`cta-v2-link ${column?.background_value === 'dark' ? 'white-txt' : 'primary-txt'}`} target={link?.target} url={link?.url} ariaLabel={`link to ${decode(link?.title)}`}>
                        {decode(link?.title)}
                    </Clicker>
                }

                {image &&
                    <div class="cta-v2-image">
                        <Imaging data={image} />
                    </div>
                }

            </div>
        </Layout.Container.Columns.Item>
    );
};

export default Start;