import React, { useEffect, useState } from "react";
import { decode } from 'html-entities';

// HELPERS.
import Imaging from 'src/helpers/imaging';
import Clicker from 'src/helpers/clicker';
import HtmlParser from 'src/helpers/html-parser'
import { Coloring } from 'src/helpers';

// PARTIALS.
const Button = React.lazy(() => import('src/partials/button'));

// SCSS.
// import 'src/modules/cta-v2/styles/cards.scss';

const Start = ({ data, column, settings, placeholders }) => {
    const title = (placeholders && column?.title_selection === 'dynamic') ? placeholders.single_line[column?.title] : column?.title
    const bgImage = (placeholders && column?.card_image_selection === 'dynamic') ? placeholders.image[column?.card_image] : column?.card_image
    const button = placeholders ? placeholders.button_link[column?.button] : column?.button
    const image = (placeholders && column.image_selection === 'dynamic') ? placeholders.image[column?.image_dynamic] : column?.image
    const [bgColor, setBgColor] = useState(Coloring(column?.card_color ? column?.card_color : column?.background_color, settings));
    useEffect(() => {
        setBgColor(Coloring(column?.card_color ? column?.card_color : column?.background_color, settings))
    }, [data, column])
    let style = column?.card_background_type === 'image' ? {
        backgroundImage: bgImage ? `url(${bgImage?.url})` : '',
        backgroundPosition: 'center center',
        backgroundSize: "cover",
        backgroundRepeat: "no-repeat",
        color: data?.card_background_value === 'dark' ? '#fff' : settings?.design?.colors?.body_copy_color
    } : { backgroundColor: bgColor, color: data?.card_background_value === 'dark' ? '#fff' : settings?.design?.colors?.body_copy_color };

    if (button?.url) {
        return (
            <Clicker type="anchor" url={button?.url} class={`cta-v2-outer ${data?.display_options} ${data?.style} ${column?.card_background_type} ${column?.background_value} fully-clickable`} style={style}>
                <div class={`cta-v2-inner ${data?.display_options} ${data?.style} ${column?.alignment} ${column?.card_background_value}`}>
                    <CtaImage image={image} />
                    <CtaTitle title={title} column={column} />
                    <div class="cta-v2-content">
                        <HtmlParser html={column?.content} placeholders={placeholders} />
                    </div>
                    <CtaButton button={button} column={column} buttonFunction='styled' />
                </div>
            </Clicker>
        );
    } else {
        return (
            <div class={`cta-v2-outer ${data?.display_options} ${data?.style} ${column?.card_background_type} ${column?.background_value}`} style={style}>
                <div class={`cta-v2-inner ${data?.display_options} ${data?.style} ${column?.alignment} ${column?.card_background_value}`}>
                    <CtaImage image={image} />
                    <CtaTitle title={title} column={column} />
                    <div class="cta-v2-content">
                        <HtmlParser html={column?.content} placeholders={placeholders} />
                    </div>
                    <CtaButton button={button} column={column} />
                </div>
            </div>
        );
    }
};

const CtaImage = ({ image }) => {
    if (!(image?.url)) return null;
    return (
        <div class="cta-v2-image">
            <Imaging data={image} />
        </div>
    );
};

const CtaTitle = ({ title, column }) => {
    if (!(title)) return null;
    return (
        <div class={`cta-v2-title ${column?.alignment}`}>
            <h3>{decode(title)}</h3>
        </div>
    );
};

const CtaButton = ({ button, column, buttonFunction }) => {
    if (!(button)) return null;
    return (
        <div class={`cta-v2-button ${column?.alignment}`}>
            <Button title={button?.title} url={button?.url} tone={column?.card_background_value} target={button?.target} type={column?.button_style} buttonFunction={buttonFunction} />
        </div>
    );
};

export default Start;