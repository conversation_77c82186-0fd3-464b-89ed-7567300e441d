@import 'src/scss/variables.scss';
@import 'src/scss/mixins/flexbox.scss';
@import 'src/scss/mixins/breaks.scss';

#modules-container {
    .call-to-action-v2.side-by-side {
        div { box-sizing: border-box; }

        .mvk-container.side-by-side {
            .module-columns.side-by-side {
                .cell.side-by-side {
                    min-height: 450px;

                    .module-item {
                        @include flexbox;
                        @include flex-direction(row);
                        @include flex-gap(0rem);
                        height: 100%;

                        @include breakless('mobile') {
                            @include flex-direction(column-reverse);
                        }

                        .item-content {
                            @include flexbox;
                            @include flex-direction(column);
                            @include flex-gap(5px);
                            @include flex(1, 0, 50%);
                            padding: 5rem;

                            @include breakless('medium') {
                                padding: 3rem;
                            }
                            @include breakless('mobile') {
                                padding: 2rem;
                            }
                            .cta-v2-content {
                                letter-spacing: 0.5px;
                            }
                        }
                        
                        .item-media {
                            @include flex(1, 0, 50%);
                            height: 100%;
                            max-width: 100%;

                            img {
                                min-height: 100%;
                                max-width: 100%;
                                object-fit: cover;

                                @include breakless('mobile') {
                                    width: 100%;
                                }
                            }
                        }

                        .item-right {
                            width: 350px; 
                            height: 100%;
                            @media screen and (max-width: $break-mobile) {
                                width: 100%;
                            }

                            .cta-v2-image {
                                height: 100%;
                                max-width: 100%;

                                img {
                                    min-height: 100%;
                                    max-width: 100%;
                                    object-fit: cover;

                                    @media screen and (max-width: $break-mobile) {
                                        width: 100%;
                                    }
                                }
                            }
                        }
                    }
                }
            }
            .grid-x.side-by-side {
                .cell.side-by-side {
                    min-height: 450px;
                    .cta-v2-outer { 
                        .cta-v2-inner.side-by-side {
                            align-items: center;
                            flex-direction: row;
                            
                            .cta-v2-left {
                                padding: 5rem;
                                @media screen and (max-width: $break-mobile) {
                                    padding: 2rem;
                                }

                                .cta-v2-content {
                                    letter-spacing: 0.5px;
                                }
                            }
                            .cta-v2-right {
                                width: 350px; 
                                height: 100%;
                                @media screen and (max-width: $break-mobile) {
                                    width: 100%;
                                }
                                .cta-v2-image {
                                    height: 100%;
                                    max-width: 100%;
                                    img {
                                        min-height: 100%;
                                        max-width: 100%;
                                        object-fit: cover;

                                        @media screen and (max-width: $break-mobile) {
                                            width: 100%;
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}

