#modules-container {
    .call-to-action-v2.staggered {
        .mvk-container.staggered {
            .grid-x.staggered {
                .cell.staggered {
                    .cta-v2-outer {

                        .cta-v2-inner {
                               position: relative;
                               overflow: hidden;
                             min-height: 294px;
                                display: flex;
                            align-items: flex-end;

                            .cta-v2-image {
                                position: absolute;
                                top:0px;
                                bottom:0px;
                                left:0px;
                                right:0px;

                                img {
                                    height: 100%;
                                    width: 100%;
                                    object-fit: cover;
                                    transition: .2s;
                                    &:hover {
                                        transform: scale(1.05);
                                    }
                                }
                            }

                            .cta-v2-overlay {
                                position: absolute;
                                     top: 0px;
                                  bottom: 0px;
                                    left: 0px;
                                   right: 0px;
                                 z-index: +1;
                            }

                            .cta-v2-content {
                                position: absolute;
                                bottom: 0px;
                                z-index: +2;
                                padding: 1.5rem;
                                box-sizing: border-box;
                                width: 100%;
                                .label {
                                    margin-bottom: 0.5rem;
                                }
                                .title {
                                    line-height: 1.2;
                                    margin-bottom: 0.5rem;
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
