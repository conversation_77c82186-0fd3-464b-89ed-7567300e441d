import React, { useState, useEffect } from "react";

// HELPERS.
const Imaging = React.lazy(() => import('src/helpers/imaging'));
const Clicker = React.lazy(() => import('src/helpers/clicker'));

const Start = ({ data, column, placeholders }) => {
    const link = placeholders ? placeholders.button_link[column?.cta_link] : column?.cta_link
    const image = (placeholders && column.image_selection === 'dynamic') ? placeholders.image[column?.image_dynamic] : column?.image

    return (
        <div class={`cta-v2-outer ${data?.display_options} ${data?.style}`}>
            <Clicker type="anchor" class={`cta-v2-inner ${data?.display_options} ${data?.style} ${link?.url ? 'fully-clickable' : 'not-clickable'}`} url={link?.url}>
                {image?.url && <Imaging data={image} />}
            </Clicker>
        </div>
    );
};

export default Start;