import React, { useEffect, useState, useMemo } from "react";
import { decode } from 'html-entities';

// HELPERS.
import Imaging from 'src/helpers/imaging';
import Clicker from 'src/helpers/clicker';
import HtmlParser from 'src/helpers/html-parser'
import { BackgroundColor } from 'src/helpers/theme';

// PARTIALS.
const Button = React.lazy(() => import('src/partials/button'));

// LAYOUTS.
import Layout from 'src/modules/cta-v2/layout';

// SCSS.
import 'src/modules/cta-v2/styles/side-by-side.scss';

const Start = ({ data, column, settings, placeholders }) => {
    const title = (placeholders && column?.title_selection === 'dynamic') ? placeholders.single_line[column?.title] : column?.title
    const bgImage = (placeholders && column.background_image_selection === 'dynamic') ? placeholders.image[column?.background_image_dynamic] : column?.background_image
    const button = placeholders ? placeholders.button_link[column?.button] : column?.button
    const link = placeholders ? placeholders.button_link[column?.cta_link] : column?.cta_link
    const image = (placeholders && column.image_selection === 'dynamic') ? placeholders.image[column?.image_dynamic] : column?.image

    const style = useMemo(() => {
        return {
            backgroundImage: column.background_type == 'image' && bgImage ? `url(${bgImage?.url})` : 'unset',
            backgroundRepeat: 'no-repeat',
            backgroundPosition: 'center',
            backgroundSize: 'cover',
            backgroundColor: column?.background_color ? BackgroundColor(column?.background_color) : 'transparent',
            color: column?.background_value === 'dark' ? '#fff' : settings?.design?.colors?.body_copy_color
        };
    }, []);

    const role = useMemo(() => bgImage ? 'img' : null, []);
    const bgAlt = useMemo(() => (bgImage && bgImage?.alt) ? bgImage?.alt : null, []);
    return (
        <Layout.Container.Columns.Item data={data} settings={settings} column={column} style={style} role={role} aria-label={bgAlt}>
            <ItemContent title={title} button={button} link={link} column={column} placeholders={placeholders} />
            <ItemMedia image={image} />
        </Layout.Container.Columns.Item>
    );
};

const ItemContent = ({ title, button, link, column, placeholders }) => {
    return (
        <div class="item-content">
            {title &&
                <div class={`cta-v2-title ${column?.alignment}`}>
                    <h3>{decode(title)}</h3>
                </div>
            }
            <div class="cta-v2-content">
                <HtmlParser html={column?.content} placeholders={placeholders} />
            </div>
            <div class={`cta-v2-button ${column?.alignment}`}>
                {button && <Button title={button?.title} url={button?.url} target={button?.target} tone={column?.background_value} type={column?.button_style} />}
            </div>
            {link &&
                <Clicker type={'anchor'} class={`cta-v2-link ${column?.background_value === 'dark' ? 'white-txt' : 'primary-txt'}`} target={link?.target} url={link?.url} ariaLabel={`link to ${decode(link?.title)}`}>
                    {decode(link?.title)}
                </Clicker>
            }
        </div>
    );
};

const ItemMedia = ({ image }) => {
    return (
        <div class="item-media">
            {image && <Imaging data={image} />}
        </div>
    );
};

export default Start;