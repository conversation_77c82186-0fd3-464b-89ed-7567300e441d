import React from "react";
import { decode } from 'html-entities';
import { useInView } from 'react-intersection-observer';
import HtmlParser from 'src/helpers/html-parser';
// HELPERS.
import Colors from 'src/helpers/colors';

const Start = ({ data, settings, placeholders, children, ...otherProps }) => {
    const { ref, inView } = useInView({
        triggerOnce: true,
        fallbackInView: true
    });
    const bgImage = (placeholders && data.module_background_image_selection === 'dynamic') ? placeholders.image[data?.module_background_image_dynamic] : data?.module_background_image
    const classList = ['call-to-action-v2', data?.display_options, data?.style, data?.module_background_value, data?.partial_fill ? 'partial-fill' : '', data?.remove_padding ? 'remove-padding' : ''].join(' ')
    let style = null;
    if (!data?.partial_fill) {
        style = {
            color: data?.module_background_value === 'dark' ? '#fff' : settings?.design?.colors?.body_copy_color,
            backgroundImage: data?.module_background_type === 'image' && bgImage ? `url(${bgImage?.url})` : 'unset',
            backgroundSize: data?.module_background_type === 'image' && bgImage ? 'cover' : 'unset',
            backgroundColor: data?.module_background_type === 'color' ? BackgroundColor(data?.module_background_color, settings, false) : 'transparent'
        }
    }
    return (<div ref={ref} children={inView ? children : null} className={classList} style={style} {...otherProps} />);
};
Start.displayName = 'modules, cta-v2, layout: start';

Start.Container = ({ data, settings, children, ...otherProps }) => {
    otherProps.class = ['mvk-container', data?.display_options, data.style].join(' ');
    return (<div children={children} {...otherProps} />);
};
Start.Container.displayName = 'modules, cta-v2, layout: start.container';

Start.Container.TitleContainer = ({ data, settings, placeholders, children, ...otherProps }) => {
    const bgImage = (placeholders && data.module_background_image_selection === 'dynamic') ? placeholders.image[data?.module_background_image_dynamic] : data?.module_background_image

    otherProps.class = 'title-container';
    let style = null;
    if (data?.partial_fill) {
        style = {
            color: data?.module_background_value === 'dark' ? '#fff' : settings?.design?.colors?.body_copy_color,
            backgroundImage: data?.module_background_type === 'image' && bgImage ? `url(${bgImage?.url})` : 'unset',
            backgroundSize: data?.module_background_type === 'image' && bgImage ? 'cover' : 'unset',
            backgroundColor: data?.module_background_type === 'color' ? BackgroundColor(data?.module_background_color, settings, false) : 'transparent'
        }

    }
    return (
        <div {...otherProps} style={style}>
            <div className="grid-container">
                {children}
            </div>
        </div>
    );
}
Start.Container.displayName = 'modules, cta-v2, layout: start.container.titlecontainer';

Start.Container.Title = ({ data, settings, placeholders, children, ...otherProps }) => {
    const title = (placeholders && data.title_selection === 'dynamic') ? placeholders.single_line[data.module_title] : data.module_title
    const classList = ['module-title', data?.display_options, data?.style, data?.title_alignment].join(' ');

    return (
        <>
            {title &&
                <div {...otherProps} className={classList}>
                    <h2 class={data?.module_background_value === 'dark' ? 'white-txt' : 'primary-txt'}>{decode(title)}</h2>
                </div>
            }
        </>
    );
};
Start.Container.Title.displayName = 'modules, cta-v2, layout: start.container.title';

Start.Container.Blurb = ({ data, settings, placeholders, children, ...otherProps }) => {
    if (!(data?.blurb)) return null;
    otherProps.class = ['module-blurb', data?.display_options, data.style].join(' ');
    return (<div {...otherProps}><HtmlParser html={data.blurb} placeholders={placeholders} /></div>);
};
Start.Container.Blurb.displayName = 'modules, cta-v2, layout: start.container.blurb';

Start.Container.Columns = ({ data, settings, children, ...otherProps }) => {
    otherProps.class = ['module-columns', 'grid-x', data?.display_options, data.style].join(' ');
    return (<div className="grid-container"><div children={children} {...otherProps} /></div>);
};
Start.Container.Columns.displayName = 'modules, cta-v2, layout: start.container.columns';

// NOT BEING USED, NEEDS TO BE IMPLIMENTED.
Start.Container.Columns.Item = ({ column, data, settings, children, ...otherProps }) => {
    otherProps.class = ['module-item', data?.display_options, data?.style, column?.background_value, column?.background_type].join(' ');
    return (<div children={children} {...otherProps} />);
};
Start.Container.Columns.Item.displayName = 'modules, cta-v2, layout: start.container.columns.item';

// IS BEING USED.
Start.Container.Columns.Item.Overlay = ({ column, data, settings, children, ...otherProps }) => {
    if (column.overlay_type == 'none') return null; // DON'T SHOW IF NONE.
    const RGB = Colors.hexToRgb(column.overlay_color);
    otherProps.class = ['cta-v2-overlay', column?.overlay_type].join(' ');
    otherProps.style = (column?.overlay_type == 'solid') ? {
        backgroundColor: `rgba(${RGB.r}, ${RGB.b}, ${RGB.g}, ${column.overlay_transparency})`
    } : {
        backgroundImage: `linear-gradient(${column?.overlay_type}, rgba(${RGB.r}, ${RGB.b}, ${RGB.g}, ${column.overlay_transparency}), rgba(${RGB.r}, ${RGB.b}, ${RGB.g}, 0))`
    };

    return (<div children={children} {...otherProps} />);
};
Start.Container.Columns.Item.Overlay.displayName = 'modules, cta-v2, layout: start.container.columns.item.overlay';

export default Start;

function BackgroundColor(value, settings, selected) {
    switch (true) {
        case value == 'background_color':
            return settings.design?.colors?.background_color;
        case value == 'primary_color':
            return settings.design?.colors?.primary_color;
        case value == 'secondary_color':
            return settings.design?.colors?.secondary_color;
        case value == 'tertiary_color':
            return settings.design?.colors?.tertiary_color;
        case value == 'body_copy_color':
            return settings.design?.colors?.body_copy_color;
        case value == 'white' || selected != false:
            return '#FFF';
        case value == 'none':
        default:
            return 'transparent';
    };
};
