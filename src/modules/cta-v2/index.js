import React, { Suspense, useEffect, useState } from "react";

// PARTIALS.
const Button = React.lazy(() => import('src/partials/button'));

// LAYOUT.
import Layout from 'src/modules/cta-v2/layout';

// PARTIALS.
const Columns = React.lazy(() => import('src/modules/cta-v2/columns'));

// SCSS.
import 'src/modules/cta-v2/index.scss';

const Start = ({ data, settings, placeholders }) => {

    return (
        <Layout data={data} settings={settings} placeholders={placeholders}>
            <Layout.Container data={data} settings={settings}>
                <Layout.Container.TitleContainer data={data} settings={settings} placeholders={placeholders}>
                    <Layout.Container.Title data={data} settings={settings} placeholders={placeholders} />
                    <Layout.Container.Blurb data={data} settings={settings} placeholders={placeholders} />
                </Layout.Container.TitleContainer>
                {(data?.row_button && data?.row_button_placement === 'above-ctas') && <Buttons data={data} placeholders={placeholders} />}
                <Suspense fallback={null}>
                    <Columns data={data} settings={settings} placeholders={placeholders} />
                </Suspense>
                {(data?.row_button && data?.row_button_placement === 'below-ctas') && <Buttons data={data} placeholders={placeholders} />}
            </Layout.Container>
        </Layout>
    );
};

const Buttons = ({ data, placeholders }) => {
    const button = placeholders ? placeholders.button_link[data?.row_button] : data?.row_button;
  
    return (
        <div className="grid-container">
            <div class='grid-x'>
                <div class={`cell button-below ${data?.row_button_alignment}`}>
                    <Button class='module-button below' title={button?.title} url={button?.url} target={button?.target} tone={data?.module_background_value} type={data?.row_button_style} />
                </div>
            </div>
        </div>
    );
};

export default Start;