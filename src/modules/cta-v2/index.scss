@import "src/scss/variables.scss";

// DISPLAY OPTIONS.
// fullwidth, two-column, three-column, four-column, five-column, staggered

// STYLES.
// blocks, image-button, cards, image-only, image-text, side-by-side

#modules-container {
    .call-to-action-v2 {
        margin: 0px auto;
        padding-top: 3rem;
        padding-bottom: 3rem;
        overflow: hidden;
        &.remove-padding {
            padding: 0;
            .mvk-container.cards {
                margin: 0;
            }
        }
        .fully-clickable:hover {
            cursor: pointer;
        }
        .not-clickable:hover {
            cursor: unset;
        }

        // FOUNDATION TWEAKS FOR MODULE.
        .mvk-container {
            display: flex;
            flex-direction: column;
            gap: 30px;

            &.fullwidth {
                max-width: $break-desktop;
                margin-left: auto;
                margin-right: auto;
            }
            &.side-by-side:not(.staggered) {
                width: 100%;
                max-width: 100%;
                padding-right: 0;
                padding-left: 0;
            }
            &.cards {
                margin: 30px auto;
                padding-top: 0px;
                padding-bottom: 0px;

                .cta-v2-outer {
                    display: block;
                }
            }
            .grid-container {
                width: 100%;
                box-sizing: border-box;
            }
            // GAPS & CELL WIDTHS.
            .grid-x {
                // &.fullwidth:not(.side-by-side) {
                //     gap:0rem;
                //     .cell { width: 100%; }
                // }
                &.two-column:not(.side-by-side) {
                    gap: 1rem;
                    .cell {
                        margin-bottom: 2rem;
                        @media screen and (min-width: $break-mobile) {
                            margin-bottom: 0;
                            width: calc(50% - 0.5rem);
                        }
                    }
                }
                &.three-column:not(.side-by-side) {
                    gap: 1rem;
                    .cell {
                        margin-bottom: 2rem;
                        @media screen and (min-width: $break-mobile) {
                            margin-bottom: 0;
                            width: calc(33.3% - 0.66rem);
                        }
                    }
                }
                &.four-column:not(.side-by-side) {
                    gap: 1rem;
                    .cell {
                        margin-bottom: 2rem;
                        @media screen and (min-width: $break-mobile) {
                            margin-bottom: 0;
                            width: calc(25% - 0.75rem);
                        }
                    }
                }
                &.five-column:not(.side-by-side) {
                    gap: 1rem;
                    .cell {
                        margin: 0px auto 2rem;
                        @media screen and (min-width: $break-medium) {
                            margin: 0px auto;
                            width: calc(33% - 0.8rem);
                        }
                        @media screen and (min-width: $break-tablet) {
                            width: calc(20% - 0.8rem);
                        }
                    }
                }

                &.staggered {
                    gap: 1rem;
                    .cell {
                        &:nth-child(3n-2) {
                            @media screen and (min-width: $break-mobile) {
                                width: calc(25% - 0.667rem);
                            }
                            // @media screen and (max-width: $break-mobile) {
                            //    height: 300px;
                            // }
                        }
                        &:nth-child(3n-1) {
                            @media screen and (min-width: $break-mobile) {
                                width: calc(50% - 0.667rem);
                            }
                            // @media screen and (max-width: $break-mobile) {
                            //     height: 150px;
                            //  }
                        }
                        &:nth-child(3n-0) {
                            @media screen and (min-width: $break-mobile) {
                                width: calc(25% - 0.667rem);
                            }
                            // @media screen and (max-width: $break-mobile) {
                            //     height: 300px;
                            //  }
                        }
                    }
                }
            }

            .grid-x {
                &.cards {
                    margin: 0px auto;
                    padding-top: 0px;
                    padding-bottom: 0px;

                    .image {
                        padding: 3rem 0;
                    }
                }
                &.blocks {
                    padding: 0;
                }

                .cell {
                    height: inherit;

                    &.side-by-side {
                        min-height: 450px;
                    }

                    .cta-v2-outer {
                        height: 100%;
                        box-sizing: border-box;
                        overflow: hidden;

                        .cta-v2-inner {
                            height: 100%;
                            max-width: 100%;
                            // padding: 0px 20px;
                            box-sizing: border-box;

                            display: flex;
                            flex-direction: column;
                            gap: 15px;
                            align-items: stretch;

                            &.dark {
                                color: white;
                            }
                            &.light {
                                color: black;
                            }
                            &.cards {
                                padding: 20px;
                            }

                            .cta-v2-title {
                                flex: 0 1 auto;
                            }

                            .cta-v2-content {
                                line-height: 1.5;
                                flex: 1 0 auto;
                            }
                            .cta-v2-button {
                                flex: 0 1 auto;
                            }
                        }
                    }
                }
            }
        }
        &.partial-fill {
            padding-top: 0;
            .title-container {
                padding-top: 3rem;
                padding-bottom: 200px;
            }
            .module-columns {
                margin-top: -180px !important;
            }
        }
    }
}
