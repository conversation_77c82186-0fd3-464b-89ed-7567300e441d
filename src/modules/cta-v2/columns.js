import React, { useMemo } from "react";

// SUB PARTIALS STYLES.
const Card        = React.lazy(() => import('src/modules/cta-v2/styles/card'));
const Blocks      = React.lazy(() => import('src/modules/cta-v2/styles/blocks'));
const ImageButton = React.lazy(() => import('src/modules/cta-v2/styles/image-button'));
const ImageOnly   = React.lazy(() => import('src/modules/cta-v2/styles/image-only'));
const ImageText   = React.lazy(() => import('src/modules/cta-v2/styles/image-text'));
const SideBySide  = React.lazy(() => import('src/modules/cta-v2/styles/side-by-side'));
const Staggered   = React.lazy(() => import('src/modules/cta-v2/styles/staggered'));

// LAYOUT.
import Layout from 'src/modules/cta-v2/layout';

const Start = ({ data, settings, placeholders }) => {
    return (
        <Layout.Container.Columns data={data} settings={settings}>
            {data?.display_options != 'staggered' && data?.ctas &&
                Object?.keys(data?.ctas)?.map(key => <ColumnWrapper data={data} column={data?.ctas[key]} settings={settings} placeholders={placeholders} />)
            }
            {data?.display_options == 'staggered' && data?.cta_grid &&
                Object?.keys(data?.cta_grid)?.map(key => <Staggered column={data?.cta_grid[key]} settings={settings} placeholders={placeholders} />)
            }
        </Layout.Container.Columns>
    );
};

const ColumnWrapper = ({ data, column, settings, placeholders }) => {
    const classes = useMemo(() => [ 'cell', ClassGenerator(data?.display_options, data?.style), data?.display_options, data?.style ].join(' '), []);
    return (
        <div class={classes}>
             <ColumnContent data={data} column={column} settings={settings} classes={classes} placeholders={placeholders} />
        </div>
    );
};

const ColumnContent = ({ data, column, settings, classes, placeholders }) => {
    switch (data?.style) {
        case 'cards':
            return (<Card data={data} column={column} settings={settings} placeholders={placeholders} />);
        case 'image-text':
            return (<ImageText column={column} settings={settings} placeholders={placeholders} />);
        case 'image-button':
            return (<ImageButton column={column} placeholders={placeholders} />);
        case 'image-only':
            return (<ImageOnly data={data} column={column} placeholders={placeholders} />);
        case 'side-by-side':
            return (<SideBySide data={data} column={column} settings={settings} classes={classes} placeholders={placeholders} />);
        case 'blocks':
        default:
            return (<Blocks data={data} column={column} settings={settings} placeholders={placeholders} />); 
    };
};

export default Start;

export function ClassGenerator (displayOption, style) {
    switch (true) {
        case style == 'side-by-side':
            return 'large-6 medium-12 small-12';
        case displayOption == 'fullwidth':
            return 'large-12 medium-12 small-12';
        case displayOption == 'two-column':
            return 'large-6 medium-6 small-12';
        case displayOption == 'three-column':
            return 'large-4 medium-4 small-12';
        case displayOption == 'four-column':
            return 'large-3 medium-3 small-12';
        case displayOption == 'five-column':
            return 'col5-unit';
        case displayOption == 'staggered':
            return 'staggered';
        default:
            return 'large-12 medium-12 small-12';
    };
};