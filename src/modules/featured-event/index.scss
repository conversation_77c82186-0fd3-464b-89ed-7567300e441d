/*******************************************************************************************************
   Copyright 2020 - Calise Partners LLC d/b/a Imaginuity. All rights are reserved.
   Reproduction or transmission in whole or in part, in any form or by any means, electronic, mechanical
or otherwise, is prohibited without the prior written consent of Calise Partners LLC d/b/a Imaginuity.
********************************************************************************************************
   Project : 10618 Maverick Platform Development
   Author : Imaginuity Developers (<PERSON>, <PERSON>)
   Description : Styles for featured event module.
   Creation Date : Wed, Jan 20, 2021
   Documentation : [documentation url - point to default wiki for project if no specific documentation for script]
 ********************************************************************************************************/

$sm_breakpoint: 768px;
$md_breakpoint: 1200px;

$image_mobile_height: calc(20vw + 120px); // 200px: can switch back to px depending on browser support
$image_tablet_height: 400px;
$image_desktop_height: 400px;

$image_mobile_offset_negative: calc((20vw + 120px) / -2.4); // -80px: can switch back to px depending on browser support

$module_spacing: 50px;
$module_padding_bottom_mobile_offset: calc(
    (20vw + 120px + #{$module_spacing}) / 2.4
); // 80px: can switch back to px depending on browser support

#modules-container .featured-event__module {
    display: block;
    padding: 50px 0;
    padding-top: $module_spacing;
    padding-bottom: #{$module_padding_bottom_mobile_offset}; // accounting for the bottom offset on the mobile-image
    // pointer-events: all;
    //     > * {
    //         pointer-events: none;
    //     }
    @media (min-width: $sm_breakpoint) {
        padding-bottom: $module_spacing;
    }

    .grid-container {
        // max-width: 1100px;
        padding: 0px 30px;

        @media (min-width: 768px) {
            // matching to nav / footer container
            padding: 00px 40px;
        }

        @media (min-width: 992px) {
            // matching to nav / footer container
            padding: 00px 55px;
        }

        .flexbox {
            @media (max-width: $sm_breakpoint) {
                flex-direction: column;
            }
            .featured-event-title {
                h2 {
                    margin: 0;
                }
            }
            &.remove-constraints {
                .featured-event-image {
                    img {
                        height: auto;
                    }
                }
                @media (min-width: $sm_breakpoint) {
                    align-items: center;
                    .featured-event-image {
                        &::before {
                            display: none;
                        }
                        img {
                            width: unset;
                        }
                    }
                }
            }
        }
    }

    .featured-event-content {
        padding: 30px 30px 30px 0px;
    }

    .featured-event-flag-txt {
        h5 {
            font-weight: bold !important;
            margin: 0;
        }

        @media (min-width: $sm_breakpoint) {
            margin-bottom: 0;
        }
    }

    .featured-event-date {
        h5 {
            font-weight: bold !important;
        }
    }

    .featured-event-image {
        margin-bottom: #{$image_mobile_offset_negative};

        @media (min-width: $sm_breakpoint) {
            margin-top: -34px;
            margin-bottom: -34px;
        }

        img {
            height: $image_mobile_height;
            width: 100%;
            max-width: 100%;
            object-fit: cover;
            display: block;
            @media (min-width: $sm_breakpoint) {
                height: $image_tablet_height;
            }

            @media (min-width: $md_breakpoint) {
                height: $image_desktop_height;
            }
        }
    }
}
