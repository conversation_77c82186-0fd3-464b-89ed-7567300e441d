/*******************************************************************************************************
   Copyright 2020 - Calise Partners LLC d/b/a Imaginuity. All rights are reserved.
   Reproduction or transmission in whole or in part, in any form or by any means, electronic, mechanical
or otherwise, is prohibited without the prior written consent of Calise Partners LLC d/b/a Imaginuity.
********************************************************************************************************
   Project : 10618 Maverick Platform Development
   Author : Imaginuity Developers (<PERSON>, <PERSON>)
   Description : outputs a text area with varying column options
   Creation Date : Mon Dec 14 2020
   Documentation : [documentation url - point to default wiki for project if no specific documentation for script]
 ********************************************************************************************************/

import React, { useContext } from "react";
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faRepeat } from "@fortawesome/free-solid-svg-icons";
import { useInView } from 'react-intersection-observer';

import Clicker from 'src/helpers/clicker';
import { Coloring } from "src/helpers";
import { FormattedDate } from 'src/helpers/date';
import { convertToFrench } from 'src/helpers/hours';
import { PrincipalContext } from "src/context";

import './index.scss';
import { decode } from "html-entities";

const Start = ({ data, settings }) => {
    const { ref, inView } = useInView({
        triggerOnce: true,
        fallbackInView: true,
        threshold: 0
    });

    const bgStyles = {
        color: data.background_value === 'dark' ? '#fff' : Coloring('body_copy_color', settings),
        backgroundColor: Coloring(data.background_color, settings),
    }

    if (data.events) {
        var event = data.events[0];
        return (
            <Clicker id="pylink-featured-event" url={event.url} target={event.external_link} class={`featured-event__module hover`} ariaLabel={`link to ${event.title}`}>
                <div ref={ref}>
                    {inView ?
                        <div class="featured-event-box" style={bgStyles}>
                            <div class="grid-container">
                                <div class={`flexbox ${data.remove_image_constraints ? 'remove-constraints' : ''}`}>
                                    <div class="flex1">
                                        <Content data={data} event={event} settings={settings} />
                                    </div>
                                    {event.feat_image &&
                                        <div class="flex1">
                                            <Image event={event} />
                                        </div>
                                    }
                                </div>
                            </div>
                        </div>
                        : null}
                </div>
            </Clicker>
        );
    } else {
        return (<div />);
    }
};

const Content = ({ data, event, settings }) => {
    const [principal, setPrincipal] = useContext(PrincipalContext);
    const dateString = FormattedDate((event?.next_occurrence && event.next_occurrence.start) ? event.next_occurrence.start.event_date : event.start?.event_date, 'day-month-date');

    if (event.start.minutes == "0") { event.start.minutes = "00"; }
    if (event.end.minutes == "0") { event.end.minutes = "00"; }
    // TIMES
    let startTime = `${event.start.hour}:${event.start.minutes}${event.start.ampm}`;
    let endTime = `${event.end.hour}:${event.end.minutes}${event.end.ampm}`;
    if (principal.activeTranslation === 'fr') {
        startTime = convertToFrench(`${event.start.hour}:${event.start.minutes}`);
        endTime = convertToFrench(`${event.end.hour}:${event.end.minutes}`, true);
    }
    if (event.meta?.hide_time != "1" && event.meta?.allday != "1") {
        var times = ` | ${startTime}`;
        if (event?.meta?.mec_hide_end_time != "1") {
            times += ` - ${endTime}`
        }
    } else {
        var times = ``;
    }

    return (
        <div class="featured-event-content">
            <div class={`featured-event-flag-txt ${data.background_value === 'dark' ? 'tertiary-txt' : 'secondary-txt'}`}><h5>{settings?.mvk_theme_config?.labels?.save_the_date ? settings?.mvk_theme_config?.labels?.save_the_date : 'SAVE THE DATE'}</h5></div>
            <div class={`featured-event-title ${data.background_value === 'dark' ? 'white-txt' : 'primary-txt'}`}><h2>{decode(event.title)}</h2></div>
            <div class={`featured-event-date ${data.background_value === 'dark' ? 'tertiary-txt' : 'secondary-txt'}`}><h5>{dateString} {times} {event.repeats && <FontAwesomeIcon icon={faRepeat} className="icon-repeat" aria-label="recurring event icon" />}</h5></div>
            <p class="featured-event-exerpt" dangerouslySetInnerHTML={{ __html: event.exerpt }} />
        </div>
    );
};

const Image = ({ event }) => {
    return (
        <div class="featured-event-image hover:float-content">
            <div class="image" dangerouslySetInnerHTML={{ __html: event.feat_image }} />
        </div>
    );
};

export default Start;
