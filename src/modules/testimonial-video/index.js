import React from "react";
import { decode } from 'html-entities';
import { useInView } from 'react-intersection-observer';
// Helpers
import { Coloring } from "src/helpers";

//Styles 
import { TestimonialVideo } from './styles';

const Start = ({ data, settings }) => {
    // color options
    const bgColor = Coloring(data.background_color, settings);
    const { ref, inView } = useInView({
        triggerOnce: true,
        fallbackInView: true
    });

    return (
        <TestimonialVideo
            ref={ref}
            className={`testimonial-video ${data.background_type} ${data.background_value} ${data.layout}`}
            backgroundColor={bgColor}
            backgroundImage={data.background_type === 'image' ? data.background_image?.url : ''}
            textColor={data.background_value === 'dark' ? '#fff' : Coloring('body_copy_color', settings)}
        >
            {inView ?
                <div className="grid-container">
                    <div className="grid-x grid-margin-x">
                        <div className="cell title-container">
                            {data.title &&
                                <h2 className='title'>{decode(data.title)}</h2>
                            }
                        </div>
                        <ContentContainer data={data} />
                        <VideoContainer data={data} />
                    </div>
                </div>
                : null}
        </TestimonialVideo>
    );
}

const ContentContainer = ({ data }) => {

    return (
        <div className="cell medium-6 content-container">
            <div className="content">
                {data.blurb &&
                    <div className='blurb' dangerouslySetInnerHTML={{ __html: data.blurb }} />
                }
                {data.testimonial_data?.quote &&
                    <div className={`quote ${data.background_value === 'dark' ? 'white-txt' : 'primary-txt'}`} dangerouslySetInnerHTML={{ __html: data.testimonial_data?.quote }} />
                }
                <div className="name">
                    {data.testimonial_data?.full_name &&
                        <span>{data.testimonial_data?.full_name}</span>
                    }
                    {data.testimonial_data?.first_name &&
                        <span>{data.testimonial_data?.first_name}</span>
                    }
                    {data.testimonial_data?.last_name &&
                        <span>{data.testimonial_data?.last_name}</span>
                    }
                    {data.testimonial_data?.last_initial &&
                        <span>{data.testimonial_data?.last_initial}</span>
                    }
                </div>
                {data.testimonial_data?.job_title &&
                    <p className='job-title'>{data.testimonial_data?.job_title}</p>
                }
                {data.testimonial_data?.professional_relationship &&
                    <p className='professional-relationship'>{data.testimonial_data?.professional_relationship}</p>
                }
            </div>
        </div>

    );
}

const VideoContainer = ({ data }) => {
    return (
        <div className="cell medium-6 video-container">
            <div className='video' dangerouslySetInnerHTML={{ __html: data.testimonial_data?.video }} />
        </div>

    );
}

export default Start;