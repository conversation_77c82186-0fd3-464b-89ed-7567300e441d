import styled from 'styled-components';

export const TestimonialVideo = styled.div`
    padding: 2rem 0;
    @media (min-width: 1024px) {
        padding: 3rem 0;
    }
    color: ${props => props.textColor};
    &.color {
        background-color: ${props => props.backgroundColor};
    }
    &.image {
        background-image: url(${props => props.backgroundImage});
        background-position: center center;
        background-size: cover;
        background-repeat: no-repeat;
    }
    .title-container {
        .title {
            margin-bottom: 1rem;
        }
    }
    .content-container {
        .quote, .blurb {
            margin-bottom: 2rem;
        }
        .name {
            font-weight: bold;
            @media (min-width: 1024px) {
                font-size: 1.125rem;
            }
            span {
                margin-right: .25rem;
            }
        }
        .job-title {
            font-weight: bold;
        }
        @media (max-width: 639px) {
            text-align: center;
        }
    }
    .video-container {
        .video {
            border-radius: 7px;
            overflow: hidden;
            @media (max-width: 639px) {
                margin: 2rem 0;
            }
        }
    }
    &.video-left-content-right {
        .content-container {
            order: 2;
            @media (max-width: 639px) {
                margin-top: 1rem;
            }
        }
    }
    @media (min-width: 1024px) {
        .grid-x {
            align-items: center;
        }
    }
`;