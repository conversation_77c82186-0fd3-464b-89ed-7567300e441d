import styled from 'styled-components'

export const PTLServices = styled.div`
    padding: 3rem 0;
    color: ${props => props.textColor};
    &.color {
        background-color: ${props => props.bgColor};
    }
    &.image {
        background-image: url(${props => props.bgImage});
        background-position: center center;
        background-size: cover;
        background-repeat: no-repeat;
    }
    .blurb {
        margin-bottom: 1rem;
    }
    .module-button {
        margin-top: 1.5rem;
    }
    @media (min-width: 1200px) {
        padding: 5rem 0;
        .module-button {
            margin-top: 2rem;
        }
    }
`
export const FeaturedSection = styled.div`
    border: 2px solid ${props => props.outline};
    border-radius: ${props => props.borderRadius};
    padding: .5rem 1rem;
    margin-bottom: .75rem;
    @media (min-width: 1024px) {
        padding: 1.5rem 1rem;
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 100%;
    }
    .heading-wrapper {
        display: flex;
        justify-content: space-between;
        align-items: center;
        height: 50px;
        @media (min-width: 1024px) {
            height: auto;
            flex-flow: column;
            align-items: flex-start;
            width: 78%;
            padding-right: 2rem;
        }
        .heading {
            margin: 0;
            @media (min-width: 1024px) {
                margin-bottom: .5rem;
            }
        }
        .carat {
            width: 20px;
            transition: .3s;
            margin-right: 1rem;
            @media (min-width: 1024px) {
                display: none;
            }
        }
    }
    .content-wrapper {
        max-height: 0;
        overflow: hidden;
        .copy {
            margin-right: 2rem;
            padding-bottom: 1rem;
        }
        img {
            display: flex;
            justify-self: center;
        }
        @media (min-width: 1024px) {
            padding: .5rem 0;
            width: 22%;
            max-height: unset;
            margin-left: 1rem;
        }
    }
    @media (max-width: 1023px) {
        &.open {
            .heading-wrapper {
                .carat {
                    transform: scale(-1);
                }
            }
            .content-wrapper {
                max-height: 10000px; // bigger than it will ever be
                padding: .5rem 0;
                transition: height 0.3s;
            }
        }
    }
`

export const Cards = styled.div`
    display: grid;
    gap: .75rem;
    @media (min-width: 1024px) {
        grid-template-columns: 1fr 1fr 1fr;
    }
    @media (min-width: 1200px) {
        grid-template-columns: 1fr 1fr 1fr 1fr;
    }
    .service-card {
        padding: .5rem 1rem;
        border: 2px solid ${props => props.outline};
        border-radius: ${props => props.borderRadius};
        .heading-wrapper {
            display: flex;
            justify-content: space-between;
            align-items: center;
            height: 50px;
            @media (min-width: 1024px) {
                height: auto;
            }
            .heading-inner {
                display: flex;
                align-items: center;
                @media (min-width: 1024px) {
                    display: block;
                }
            }
            .service-icon {
                width: 50px;
                height: 50px;
                position: relative;
                margin-right: 1rem;
                & > * img {
                    position: absolute;
                    width: 100%;
                    height: 100%;
                    object-fit: contain;
                    transition: .3s;
                }
                .hover-icon img {
                    opacity: 0;
                } 
            }
            .heading {
                margin: 0;
                @media (min-width: 1024px) {
                    margin: .25rem 0;
                }
            }
            .carat {
                width: 20px;
                transition: .3s;
                margin-right: 1rem;
            }
        }
        .content {
            max-height: 0;
            overflow: hidden;
            margin-right: 2rem;
            .service-link {
                font-weight: bold !important;
            }
        }
        @media (max-width: 1023px) {
            &.open {
                .heading-wrapper {
                    .service-icon {
                        .default-icon img {
                            opacity: 0;
                        } 
                        .hover-icon img {
                            opacity: 1;
                        } 
                    }
                    .carat {
                        transform: scale(-1);
                    }
                }
                .content {
                    max-height: 10000px; // bigger than it will ever be
                    padding: .5rem 0;
                    transition: height 0.3s;
                }
            
            }
        }
        @media (min-width: 1024px) {
            transition: .3s;
            position: relative;
            padding-bottom: 3rem;
            &:not(:has(.service-icon)) {
                padding-top: 1rem;
            }
            .heading-wrapper {
                .carat {
                    display: none;
                }
            }
            &:hover {
                box-shadow: 0px 0px 6px rgba(0,0,0,.3);
                .heading-wrapper {
                    .service-icon {
                        .default-icon img {
                            opacity: 0;
                        } 
                        .hover-icon img {
                            opacity: 1;
                        } 
                    }
                }
            }
            .content {
                max-height: unset;
                padding: .5rem 0;
                & > p {
                    margin-bottom: 0;
                }
                .service-link {
                    position: absolute;
                    bottom: 1rem;
                }
            }
        }
    }
`