import React, { useState } from 'react'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faChevronDown } from '@fortawesome/free-solid-svg-icons';
import { decode } from 'html-entities'
// Helpers
import HtmlParser from 'src/helpers/html-parser'
import { Coloring } from 'src/helpers';
import Imaging from 'src/helpers/imaging';
// Partials
import Button from 'src/partials/button';
// service styles
const Cards = React.lazy(() => import('./layouts/cards'));
import * as S from './styles';

const Start = ({ data, settings, placeholders }) => {
    const title = (placeholders && data.title_selection === 'dynamic') ? placeholders.single_line[data.title_dynamic] : data.title
    const bgImage = (placeholders && data.background_image_selection === 'dynamic') ? placeholders.image[data?.background_image_dynamic] : data?.background_image

    return (
        <S.PTLServices
            className={`ptl-services ${data.background_type}`}
            bgColor={Coloring(data.background_color, settings)}
            bgImage={(data.background_type == 'image' && bgImage) ? bgImage?.url : ''}
            textColor={data.background_value === 'dark' ? '#fff' : Coloring('body_copy_color', settings)}
        >
            <div className={`grid-container`}>
                <div class='grid-x'>
                    {title &&
                        <div class={`title cell align-${data.title_alignment}`}>
                            <h2>{decode(title)}</h2>
                        </div>
                    }
                    {data.blurb &&
                        <div className={`blurb cell`}>
                            <HtmlParser html={data.blurb} />
                        </div>
                    }
                    {data.add_featured_section && <FeaturedSection data={data} settings={settings} />}
                    {data.services_style == 'cards' && <Cards data={data} settings={settings} />}
                    {data.row_button &&
                        <div class={`cell ${data?.row_button_alignment}`}>
                            <Button class='module-button' title={data?.row_button?.title} url={data?.row_button?.url} target={data?.row_button?.target} tone={data.background_value} type={data?.row_button_style} />
                        </div>
                    }
                </div>
            </div>
        </S.PTLServices>
    )
}

const FeaturedSection = ({ data, settings }) => {
    const [open, setOpen] = useState(true)

    return (
        <S.FeaturedSection
            className={`feature-card ${open ? 'open' : ''}`}
            outline={Coloring(data.featured_section_outline_color, settings)}
            borderRadius={(settings.mvk_theme_config.other?.enable_border_radius) ? `${settings.mvk_theme_config.other?.border_radius_size}px` : "0px"}
        >
            <div className='heading-wrapper' onClick={() => setOpen(!open)}>
                {data.featured_section_title && <h4 className={`heading ${data.heading_color}`}>{decode(data.featured_section_title)}</h4>}
                <FontAwesomeIcon icon={faChevronDown} class='carat' />
                <div className='copy show-for-large'>
                    {data.featured_section_copy && <HtmlParser html={data.featured_section_copy} />}
                </div>
            </div>
            <div className='content-wrapper'>
                <div className='copy hide-for-large'>
                    {data.featured_section_copy && <HtmlParser html={data.featured_section_copy} />}
                </div>
                {data.featured_section_image && <Imaging data={data.featured_section_image} />}
            </div>
        </S.FeaturedSection>
    )
}
export default Start;