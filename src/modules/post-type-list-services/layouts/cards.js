import React, { useState, useEffect } from 'react'
import { decode } from 'html-entities'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faChevronDown } from '@fortawesome/free-solid-svg-icons';
// Helpers
import HtmlParser from 'src/helpers/html-parser'
import { Coloring } from 'src/helpers';
import Imaging from 'src/helpers/imaging';
import Clicker from 'src/helpers/clicker';
// Styles
import * as S from '../styles';

const Start = ({ data, settings }) => {
    const [services, setServices] = useState(data.services ? data.services : false);
    useEffect(() => {
        if (settings.current_location) {
            setServices(() => services?.filter((i) => {
                return i.locations?.includes(settings.current_location);
            }))
        } else {
            setServices(data.services ? data.services : false);
        }
    }, [settings.current_location, data]);

    if (!services || services?.length === 0) return null;
    return (
        <S.Cards
            className='cards-container cell'
            outline={Coloring(data.card_outline_color, settings)}
            borderRadius={(settings.mvk_theme_config.other?.enable_border_radius) ? `${settings.mvk_theme_config.other?.border_radius_size}px` : "0px"}
        >
            {services && services.map((service, i) => <Card data={data} service={service} i={i} />)}
        </S.Cards>
    )
}

const Card = ({ data, service, i }) => {
    const [open, setOpen] = useState(i === 0)

    return (
        <div key={`service-${i}`} className={`service-card ${open ? 'open' : ''}`}>
            <div className='heading-wrapper' onClick={() => setOpen(!open)}>
                <div className='heading-inner'>
                    {(service.default_icon && service.hover_icon) &&
                        <div className='service-icon'>
                            <div className='default-icon'><Imaging data={service.default_icon} /></div>
                            <div className='hover-icon'><Imaging data={service.hover_icon} /></div>
                        </div>
                    }
                    {service.title && <h4 className={`heading ${data.heading_color}`}>{decode(service.title)}</h4>}
                </div>
                <FontAwesomeIcon icon={faChevronDown} class='carat' />
            </div>
            <div className='content'>
                {service.excerpt && <HtmlParser html={service.excerpt} />}
                <Clicker type='anchor' className={`service-link ${data.link_color}`} url={service.url} aria-label={`link to ${service.title} service`}>{decode(service.link_text)}</Clicker>
            </div>
        </div>
    )
}

export default Start;