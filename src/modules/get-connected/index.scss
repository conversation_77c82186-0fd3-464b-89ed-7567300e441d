/*******************************************************************************************************
   Copyright 2021 - Calise Partners LLC d/b/a Imaginuity. All rights are reserved. 
   Reproduction or transmission in whole or in part, in any form or by any means, electronic, mechanical 
or otherwise, is prohibited without the prior written consent of Calise Partners LLC d/b/a Imaginuity.
********************************************************************************************************
   Project : 10618 Maverick Platform Development
   Author : Imaginuity Developers (<PERSON>, <PERSON>)
   Description : styles for get-connected module
   Creation Date : Mon Apr 19 2021
   Documentation : [documentation url - point to default wiki for project if no specific documentation for script]
 ********************************************************************************************************/

.get-connected__module {
    padding-top: 3rem;
    padding-bottom: 2rem;
    
    .inner-wrapper {
        display: flex;
        flex-flow: column nowrap;
        align-items: center;
        text-align: center;
    }

    .title-container {
        margin-bottom: 1rem;
    }

    .social-wrapper {
        display: flex;
        flex-flow: row wrap;
        justify-content: center;
    }

    .subtitle {
        margin-bottom: 1rem;
    }

    .button-container {
        display: flex;
        flex-flow: row wrap;
        justify-content: center;

        a {
            margin: 0 1rem 1rem;
        }
    }
}
