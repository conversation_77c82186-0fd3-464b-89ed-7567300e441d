/*******************************************************************************************************
   Copyright 2021 - Calise Partners LLC d/b/a Imaginuity. All rights are reserved. 
   Reproduction or transmission in whole or in part, in any form or by any means, electronic, mechanical 
or otherwise, is prohibited without the prior written consent of Calise Partners LLC d/b/a Imaginuity.
********************************************************************************************************
   Project : 10618 Maverick Platform Development
   Author : Imaginuity Developers (<PERSON>, <PERSON>)
   Description : module that displays social media links and a link to sign up and stay connected
   Creation Date : Mon Apr 19 2021
   Documentation : [documentation url - point to default wiki for project if no specific documentation for script]
 ********************************************************************************************************/

import React, { useState, useEffect, useContext } from 'react';
const Button = React.lazy(() => import('src/partials/button'));
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faFacebookF, faInstagram, faLinkedinIn, faYoutube } from '@fortawesome/fontawesome-free-brands';
import { faTiktok, faXTwitter, faThreads } from "@fortawesome/free-brands-svg-icons";
import { useInView } from 'react-intersection-observer';

import { SocialIcon } from './styles';
import { Coloring } from 'src/helpers';
import './index.scss';
import HtmlParser from '../../helpers/html-parser';
// CONTEXT.
import { LocationsContext } from "src/context";

const Start = ({ data, settings, placeholders }) => {
    const [locations] = settings.current_location ? useContext(LocationsContext) : [];
    const [social, setSocial] = useState((settings.current_location && locations[settings.current_location].mvk_item_content?.custom_fields?.social_media_channels) ? locations[settings.current_location].mvk_item_content?.custom_fields?.social_media_channels : !settings.current_location ? settings.social : false)

    useEffect(() => {
        setSocial((settings.current_location && locations[settings.current_location].mvk_item_content?.custom_fields?.social_media_channels) ? locations[settings.current_location].mvk_item_content?.custom_fields?.social_media_channels : !settings.current_location ? settings.social : false)
    }, [settings.current_location])

    if (social) {
        return (
            <Container data={data} settings={settings} placeholders={placeholders} social={social} />
        );
    } else {
        return null;
    }

};

const Container = ({ data, settings, placeholders, social }) => {
    const subTitle = (placeholders && data.sub_title_selection === 'dynamic') ? placeholders.single_line[data.sub_title] : data.sub_title
    const bgImage = (placeholders && data.background_image_selection === 'dynamic') ? placeholders.image[data?.background_image_dynamic] : data?.background_image

    const { ref, inView } = useInView({
        triggerOnce: true,
        fallbackInView: true,
        threshold: 0.01
    });

    if (data.background_value === 'light') {
        var colors = {
            font_color: 'primary-txt',
            social_buttons_font_color: 'white-txt',
            inline_social_buttons_font_color: '#ffffff',
            social_buttons_bg_color: 'primary-bg',
            inline_social_buttons_bg_color: settings.design?.colors?.primary_color,
        };
    } else {
        var colors = {
            font_color: 'white-txt',
            social_buttons_font_color: 'body-copy-txt',
            inline_social_buttons_font_color: settings.design?.colors?.body_copy_color,
            social_buttons_bg_color: 'tertiary-bg',
            inline_social_buttons_bg_color: settings.design?.colors?.tertiary_color,
        };
    }

    var bgStyles = bgImage ?
        {
            backgroundImage: `url(${bgImage.url})`,
            backgroundRepeat: 'no-repeat',
            backgroundPosition: 'center',
            backgroundSize: 'cover'
        } : {};

    let role = bgImage ? 'img' : null;
    let bgAlt = (bgImage && bgImage.alt) ? bgImage.alt : null;

    var coloring = pylot?.design?.bgColorClass(data.background_color);

    return (
        <div ref={ref} class={`get-connected__module ${coloring}`} role={role} aria-label={bgAlt} style={bgStyles}>
            {inView ?
                <div class='get-connected-container grid-container'>
                    <div class={`inner-wrapper ${colors.font_color}`}>

                        {data.title && <Title data={data} placeholders={placeholders} />}

                        {social && <SocialItems social={social} data={data} settings={settings} colors={colors} />}

                        {subTitle && <SubTitle data={data} subTitle={subTitle} />}

                        {data.buttons &&
                            <div class='buttons'>
                                {data.buttons.map((item) => {
                                    const buttonLink = (placeholders) ? placeholders.button_link[item?.button] : item?.button;
                                    if (buttonLink) {
                                        return (
                                            <div class='button'><Button type={item.button_style} title={buttonLink.title} url={buttonLink.url} target={buttonLink.target} tone={data.background_value} /></div>
                                        )
                                    } else return null;
                                }
                                )}
                            </div>
                        }
                    </div>
                </div>
                : null}
        </div>
    );
};

const Title = ({ data, placeholders }) => {
    return (
        <div class='title-container'>
            <HtmlParser html={data.title} placeholders={placeholders} />
        </div>
    );
};

const SocialItems = ({ social, data, settings, colors }) => {
    let socialData = social.social_media_channels ? social.social_media_channels : social;

    return (
        <div class="social-wrapper">
            {Array.isArray(socialData) && socialData?.map((channel, index) => {
                let socialType = channel.channel.value;
                if (socialType === 'facebook') {
                    var icon = faFacebookF;
                } else if (socialType === 'twitter') {
                    var icon = faXTwitter;
                } else if (socialType === 'instagram') {
                    var icon = faInstagram;
                } else if (socialType === 'linkedin') {
                    var icon = faLinkedinIn;
                } else if (socialType === 'youtube') {
                    var icon = faYoutube;
                } else if (socialType === 'tiktok') {
                    var icon = faTiktok;
                } else if (socialType === 'threads') {
                    var icon = faThreads
                }

                return (
                    <SocialIcon
                        key={index}
                        href={channel.url}
                        rel="noopener"
                        target="_blank"
                        title={channel.channel.label}
                        alt={channel.channel.screenreader_alt_text}
                        aria-label={`link to ${channel?.channel?.label}`}
                        iconColor={Coloring(data.icon_color, settings)}
                        textColor={data.background_value === 'dark' ? '#fff' : settings.design?.colors?.body_copy_color}
                        backgroundColor={Coloring(data.icon_background_color, settings)}
                        moduleBackgroundColor={Coloring(data.background_color, settings)}
                        borderRadius={data.icon_border_radius ? data.icon_border_radius : '50'}
                        className={`social-icon ${!data.icon_style || data.icon_style === 'default' ? colors.social_buttons_font_color : data.icon_style} ${!data.icon_style || data.icon_style === 'default' ? colors.social_buttons_bg_color : ''}`}
                    >
                        <FontAwesomeIcon icon={icon} aria-label={`${channel?.channel?.label} logo`} />
                    </SocialIcon>
                );
            })}
        </div>
    );
};

const SubTitle = ({ subTitle }) => {
    return (
        <p class='subtitle'>{subTitle}</p>
    );
};


export default Start;
