import styled from 'styled-components';

export const SocialIcon = styled.a`
    display: flex;
    align-items: center;
    justify-content: center;
    height: 2.75rem;
    width: 2.75rem;
    border-radius: ${props => props.borderRadius}%;
    margin: 0 1rem 1rem;
    transition: .2s;
    svg {
        height: 1.75rem;
        width: 1.75rem;
    }
     &.icon-only {
        color: ${props => props.iconColor};
        &:hover {
            color: ${props => props.textColor};
        }
    }
    &.icon-outline {
        color: ${props => props.iconColor};
        border: 2px solid ${props => props.iconColor};
        &:hover {
            background-color: ${props => props.iconColor};
            color: ${props => props.moduleBackgroundColor || '#fff'};
        }
    }
    &.icon-background {
        color: ${props => props.iconColor};
        background-color: ${props => props.backgroundColor};
        &:hover {
            background-color: ${props => props.iconColor};
            color: ${props => props.backgroundColor};
        }
    }
`;