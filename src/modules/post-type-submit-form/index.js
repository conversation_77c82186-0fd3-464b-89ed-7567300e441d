import React, { useState, useEffect, useContext } from "react";
import axios from 'axios';
import { useInView } from 'react-intersection-observer';

// Partials
import Loading from "src/partials/loading";
const Button = React.lazy(() => import('src/partials/button'));

// Helpers
import { Coloring } from 'src/helpers';
// Context
import { PrincipalContext } from "src/context";

import "./index.scss";


const Start = ({ data, settings }) => {
    const { ref, inView } = useInView({
        triggerOnce: true,
        fallbackInView: true
    });
    // Styles
    const styles = {
        backgroundColor: Coloring(data.background_color, settings),
        color: settings?.design?.colors?.primary_color
    }
    if (data.background_value === "dark") {
        styles.color = "#fff";
    }

    return (
        <div ref={ref} class='post-type-submit-form' style={styles}>
            {inView ?
                <div class='grid-container'>
                    <div class='grid-x'>
                        <div class='cell text-center'>
                            {data.title && <h2 class='title'>{data.title}</h2>}
                            <p class='blurb' dangerouslySetInnerHTML={{ __html: data.blurb }} />
                        </div>
                        <Form data={data} settings={settings} />
                    </div>
                </div>
                : null}
        </div>
    );
}

const Form = ({ data, settings }) => {
    const [principal, setPrincipal] = useContext(PrincipalContext);
    const [originalHTML, setOriginalHTML] = useState(data.post_type_form);
    const [formHTML, setHTML] = useState(data.post_type_form);
    const [myForm, setMyForm] = useState(false);
    const [message, setMessage] = useState(data.success_message);
    const [loading, setLoading] = useState(false);
    const [showHide, setShowHide] = useState(false);
    const [disabled, setDisabled] = useState(false);
    const [usedLanguages, setUsedLanguages] = useState({});
    const [possibleLanguages, setPossibleLanguages] = useState(settings?.translations ? settings?.translations : {});
    const [lang, setLang] = useState(null);

    // get form id
    const domForm = new DOMParser().parseFromString(data.post_type_form, "text/html");
    const postType = domForm.querySelector('[name="pt"]').value;
    const formId = domForm.getElementsByTagName('form')[0].id;

    useEffect(() => {
        // get form from id
        const form = document.getElementById(formId);

        setMyForm(form);
        // submit url from action 
        const submitUrl = form.getAttribute('action');

        if (principal.activeTranslation && principal.activeTranslation !== 'en') {
            setPossibleLanguages({ ...possibleLanguages, ['en']: 'English' })
            const newHTML = data.post_type_form.replaceAll('name="', `name="${principal.activeTranslation}_`);
            setHTML(newHTML);
        }
        // validation
        function validate(e) {
            e.preventDefault();
            // console.log(e.target);
            if (!e.target?.value && e.target?.hasAttribute('required')) {
                // console.log(e.target.nextElementSibling);
                e.target.nextElementSibling?.classList?.remove('hide');
            } else if (e.target?.value && e.target?.hasAttribute('required') && e.target?.nextElementSibling) {
                e.target.nextElementSibling?.classList?.add('hide');
            }

        };

        // handle submit
        const customSubmit = (e) => {
            e.preventDefault();

            validate(e);
            // set loader
            setLoading(true);
            // hide form
            form.classList.add('hide');
            // form data
            const formData = new FormData(e.target);
            let langs = [];
            if (Object.keys(usedLanguages).length > 0) {
                langs.push(principal.activeTranslation);
                Object.entries(usedLanguages).map(([key, value]) => {
                    langs.push(key);
                })
                // langs.concat(Object.keys(usedLanguages))
                formData.append('langs', langs);
                formData.set('pt', postType);
            } else if (principal.activeTranslation && principal.activeTranslation !== 'en') {
                langs.push(principal.activeTranslation);
                formData.append('langs', langs);
                formData.set('pt', postType);
            }

            if (formData.get('email_address') !== '<EMAIL>' && !formData.get('website')) {
                // post form data
                axios.post(submitUrl, formData, { 'Content-Type': 'multipart/form-data' }).then(function (response) {
                    setLoading(false);
                    if (response?.data?.success) {
                        setShowHide(true);
                    } else {
                        setMessage('Sorry we encountered an error while submitting your request.');
                        setShowHide(true);
                    }
                })
                    .catch(function (response) {
                        console.log(response);
                    });
            } else {
                setTimeout(() => {
                    setLoading(false);
                    setMessage('Thank you for your submission.')
                    setShowHide(true);
                }, 3000)
            }
        };

        // add border radius from site settings
        Array.from(form.elements).forEach((input) => {
            input.style.borderRadius = `${settings.mvk_theme_config.other?.border_radius_size}px`;
        });

        // Add Form Langs
        if (Object.keys(possibleLanguages).length > 0 && lang) {
            Object.entries(possibleLanguages).map(([key, value]) => {
                // show hide store field
                setTimeout(() => {
                    // show hide date fields
                    const allDayLang = document.getElementById(`${key}_triggerTimeFields`);
                    const startTimeLang = document.getElementById(`${key}_startTime`);
                    const endTimeLang = document.getElementById(`${key}_endTime`);
                    if (allDayLang) {
                        allDayLang.addEventListener('change', (event) => {
                            if (event.currentTarget.checked) {
                                startTimeLang.style.display = 'none';
                                endTimeLang.style.display = 'none';
                            } else {
                                startTimeLang.style.display = 'block';
                                endTimeLang.style.display = 'block';
                            }
                        })
                    }

                    const noStoreLang = document.getElementById(`${key}_triggerStoreFields`);
                    const storeFieldWrapperLang = document.getElementById(`${key}_storeFieldWrapper`);
                    const storeFieldLang = document.getElementById(`${key}_storeName`);

                    if (noStoreLang && storeFieldWrapperLang && storeFieldLang) {
                        noStoreLang.addEventListener('change', (event) => {
                            if (event.currentTarget.checked) {
                                storeFieldWrapperLang.style.display = 'none';
                                storeFieldLang.removeAttribute('required');
                            } else {
                                storeFieldWrapperLang.style.display = 'block';
                                storeFieldLang.setAttribute('required', '');
                            }
                        })
                    }
                    // email validation
                    const emailLang = document.getElementById(`${key}_email_address`);
                    if (emailLang) {
                        emailLang.addEventListener("blur", (event) => {
                            validateEmail(event);
                        });
                    }

                }, 1000)
            })
        }

        // EVENT LISTENERS
        // submit listener
        form?.addEventListener('submit', customSubmit);
        // add primary color border color on focus
        form?.addEventListener('focus', (e) => {
            e.target.style.borderColor = settings.design?.colors?.primary_color;
        }, true);
        // remove border color on blur
        form?.addEventListener('blur', (e) => {
            e.target.style.borderColor = '';
            validate(e);
        }, true);

        // apply settings styles to form button
        var button = form?.getElementsByTagName('button');
        button[0].style.cssText = 'display:none;';

        // show hide date fields
        const allDay = document.getElementById('triggerTimeFields');
        const startTime = document.getElementById('startTime');
        const endTime = document.getElementById('endTime');

        if (allDay) {
            allDay.addEventListener('change', (event) => {
                if (event.currentTarget.checked) {
                    startTime.style.display = 'none';
                    endTime.style.display = 'none';
                } else {
                    startTime.style.display = 'block';
                    endTime.style.display = 'block';
                }
            })
        }
        // show hide store field
        const noStore = document.getElementById('triggerStoreFields');
        const storeFieldWrapper = document.getElementById('storeFieldWrapper');
        const storeField = document.getElementById('storeName');

        if (noStore && storeFieldWrapper && storeField) {
            noStore.addEventListener('change', (event) => {
                if (event.currentTarget.checked) {
                    storeFieldWrapper.style.display = 'none';
                    storeField.removeAttribute('required');
                } else {
                    storeFieldWrapper.style.display = 'block';
                    storeField.setAttribute('required', '');
                }
            })
        }

        // email validation
        const email = document.getElementById('email_address');
        if (email) {
            email.addEventListener("blur", (event) => {
                validateEmail(event);
            });
        }

        function validateEmail(event) {
            let emailPattern = /[a-z0-9!#$%&'*+/=?^_`{|}~-]+(?:\.[a-z0-9!#$%&'*+/=?^_`{|}~-]+)*@(?:[a-z0-9](?:[a-z0-9-]*[a-z0-9])?\.)+[a-z0-9](?:[a-z0-9-]*[a-z0-9])?/;
            if (event.currentTarget.value && !emailPattern.test(event.currentTarget.value)) {
                event.target.nextElementSibling?.classList?.remove('hide');
                setDisabled(true);
            } else {
                event.target.nextElementSibling?.classList?.add('hide');
                setDisabled(false);
            }
        }

        return () => form.removeEventListener('submit', customSubmit);

    }), [formId];

    useEffect(() => {
        if (myForm) {
            if (principal.activeTranslation === 'en' && possibleLanguages.hasOwnProperty('fr')) {
                let selectElement = document.getElementById('additionalLangs');
                selectElement.value = 'fr';
                handleAdditionalLang(false, 'fr');
            }
        }
    }, [myForm])

    const handleAdditionalLang = (e, lang) => {
        const selectedLang = lang || e.target.value;
        setLang(selectedLang);
        if (selectedLang) {
            setUsedLanguages({ ...usedLanguages, [selectedLang]: true });
            let newDiv = document.createElement('div');
            const originalForm = new DOMParser().parseFromString(originalHTML, "text/html");
            let formFields = originalForm.getElementsByClassName('repeatable-fields')[0];
            if (selectedLang !== 'en') {
                formFields = formFields.innerHTML.replaceAll('name="', `name="${selectedLang}_`).replaceAll('id="', `id="${selectedLang}_`).replaceAll('for="', `for="${selectedLang}_`);
            } else {
                formFields = formFields.innerHTML.replaceAll('id="', `id="${selectedLang}_`).replaceAll('for="', `for="${selectedLang}_`)
            }
            newDiv.innerHTML = `<h2 class='additional-lang-title'>${possibleLanguages[selectedLang]}</h2>`
            newDiv.innerHTML += formFields
            myForm.appendChild(newDiv);
        }
    }

    return (
        <div class='cell style-default'>
            <div class='form-container form-group' dangerouslySetInnerHTML={{ __html: formHTML }} />
            {showHide &&
                <p id='successMessage' class='success'>{message}</p>
            }
            {(!showHide && !loading && Object.keys(possibleLanguages).length > 0) &&
                <div className="lang-select">
                    <p className="label">{settings.mvk_theme_config?.labels?.additional_language_select || 'Additional Languages:'}</p>
                    <select id="additionalLangs" onChange={handleAdditionalLang}>
                        <option value="">{settings.mvk_theme_config?.labels?.additional_language_select_placeholder || 'Select'}</option>
                        {Object.entries(possibleLanguages).map(([key, value]) => {
                            if (principal.activeTranslation !== key && !usedLanguages[key])
                                return <option value={key}>{value}</option>
                        })}
                    </select>
                </div>
            }
            {(!showHide && !loading) &&
                <Button title={settings.mvk_theme_config?.labels?.submission_form_submit || 'Submit'} form={formId} buttonFunction='submit' tone={data.background_value} disabled={disabled} />
            }
            {loading &&
                <Loading type='dot-pulse' dotColor={data.background_value === 'dark' ? '#fff' : Coloring('primary_color', settings)} />
            }
        </div>
    );
}
export default Start;