@import "src/scss/variables.scss";

$colorDark: #1b2635;
.post-type-submit-form {
    margin-bottom: 2rem;
    padding-top: 2rem;
    padding-bottom: 2rem;
    @media (max-width: $break-large) {
        & > .grid-x > .cell.form-left:first-of-type {
            order: 2;
        }
    }
    .title {
        font-size: 2.125rem;
        margin-bottom: 1.5rem;
    }
    .blurb {
        font-size: 1rem;
        line-height: 1.5;
    }
    form, .lang-select {
        label {
            font-size: 1rem;
            &.website {
                position: absolute;
                right: 100%;
            }
        }
        input,
        textarea,
        select {
            border: none;
            // background: none;
            border: 1px solid #ccc;
            box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.08);
            margin: 0.25rem auto 1rem;
            min-height: 40px;
            padding: 0.5rem;
            font-size: 1rem;
            width: 100%;
            &[type="radio"],
            &[type="checkbox"] {
                min-height: auto;
                width: auto;
            }
        }

        &.type-textarea {
            margin-top: 0;
        }
        .error {
            display: block;
            transform: translateY(-16px);
            color: #B60000;
        }
        .additional-lang-title {
            margin: 1rem 0;
        }
    }
    button[type="submit"] {
        border: none;
        padding: 10px 40px;
    }

    .lang-select {
        margin: 1rem 0 2rem;
        label {
            margin-bottom: 0;
        }
    }
}
