import React, { useContext, useEffect, useState, useCallback } from "react";
import { decode } from 'html-entities';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { fas as solidIcons } from "@fortawesome/free-solid-svg-icons";
import { useInView } from 'react-intersection-observer';
// HELPERS 
import HtmlParser from 'src/helpers/html-parser';
// Context
import { NearbyPlacesContext, NearbyPlacesProvider } from "./context";
// STYLES
import * as S from './styles';
// Parts
import PlaceMap from './map';

const Start = ({ data, settings }) => {
    const { ref, inView } = useInView({
        triggerOnce: true,
        fallbackInView: true
    });

    return (
        <NearbyPlacesProvider>
            <S.NearbyPlaces
                ref={ref}
                className={`nearby-places`}
            >
                {inView ?
                    <div className='grid-container'>
                        <div className='grid-x grid-margin-x'>

                            {data.title &&
                                <div className={`cell ${data.title_alignment}`}>
                                    <h2 className='title'>{decode(data.title)}</h2>
                                </div>
                            }
                            {data.blurb &&
                                <div className='cell'>
                                    <div className='blurb'>
                                        <HtmlParser html={data.blurb} />
                                    </div>
                                </div>
                            }
                            <Columns data={data} settings={settings} />
                        </div>
                    </div>
                    : null}
            </S.NearbyPlaces>
        </NearbyPlacesProvider>
    );
}

const Columns = ({ data, settings }) => {

    return (
        <>
            <PlaceCategories data={data} settings={settings} />
            <PlaceMap data={data} settings={settings} />
        </>
    )
}
const PlaceCategories = ({ data, settings }) => {
    const [placeCategories, setPlaceCategories] = useState(data.place_categories);

    return (
        <S.PlaceCategories
            className="place-categories large-5 cell"
        >
            <div className="grid-x">
                {placeCategories && placeCategories.map((category) => <Category data={data} category={category} settings={settings} />)}
            </div>
        </S.PlaceCategories>
    )
}

const Category = ({ data, category, settings }) => {
    const [nearbyPlaces, setNearbyPlaces] = useContext(NearbyPlacesContext);
    useEffect(() => {
        if (!nearbyPlaces.categorySelected) {
            setNearbyPlaces({ ...nearbyPlaces, ...{ placeSelected: null } })
        }
    }, [nearbyPlaces.categorySelected])
    let svgString = '';
    if (category.category_icon_type && category.category_icon_type == 'custom') {
        svgString = category.category_icon.replace(/\<\?xml.+\?\>/g, '');
    }

    return (
        <S.Category
            key={`category-${category.id}`}
            className={`place-category cell ${data.style}`}
            primaryColor={nearbyPlaces.primaryColor}
            secondaryColor={nearbyPlaces.secondaryColor}
            tertiaryColor={nearbyPlaces.tertiaryColor}
        >
            <div
                className={`category-heading`}
                id={`category-${category.id}`}
                aria-expanded={nearbyPlaces.categorySelected === category.id}
                aria-controls={`section-${category.id}`}
                onClick={() => setNearbyPlaces({ ...nearbyPlaces, ...{ categorySelected: nearbyPlaces.categorySelected === category.id ? null : category.id } })}
                tabIndex={0}
            >
                {category.category_icon_type === 'font-awesome' ?
                    <div className='icon fa'>
                        <FontAwesomeIcon icon={solidIcons[category.category_icon]} />
                    </div>
                    :
                    <div className="icon custom" dangerouslySetInnerHTML={{ __html: svgString }} />
                }
                <h2 className="category-name">{decode(category.category_name)}</h2>
                <FontAwesomeIcon className="caret" icon={solidIcons["faChevronDown"]} />
            </div>
            {nearbyPlaces.categorySelected === category.id &&
                <div
                    className={`category-places`}
                    id={`section-${category.id}`}
                    role="region"
                    aria-labelledby={`category-${category.id}`}
                >
                    {data.places && data.places.map((place) => { if (place.category_id === category.id) return <Place place={place} /> })}
                </div>
            }
        </S.Category>
    )
}

const Place = ({ place }) => {
    const [nearbyPlaces, setNearbyPlaces] = useContext(NearbyPlacesContext);

    return (
        <div
            key={`place-${place.id}`}
            className={`place primary-txt${nearbyPlaces.placeSelected === place.id ? ' selected' : ''}`} onClick={() => setNearbyPlaces({ ...nearbyPlaces, ...{ placeSelected: place.id } })}
            tabIndex={0}
        >
            {decode(place.name)}
        </div>
    )
}


export default Start;