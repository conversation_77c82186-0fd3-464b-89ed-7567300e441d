import React, { useContext, useEffect, useState, useCallback } from "react";
import Base64 from 'base-64';
import parse from 'html-react-parser';
import GoogleMapReact from 'google-map-react';
import ReactDOMServer from "react-dom/server";
import { decode } from 'html-entities';
import { useNavigate } from 'react-router-dom';
import config from 'src/config';
import { fas as solidIcons } from "@fortawesome/free-solid-svg-icons";

// HELPERS 
import { Coloring } from "src/helpers";
import Imaging from 'src/helpers/imaging';
import HtmlParser from 'src/helpers/html-parser';
// Context
import { NearbyPlacesContext } from "./context";
// STYLES
import * as S from './styles';

const Start = ({ data, settings }) => {
    const [nearbyPlaces, setNearbyPlaces] = useContext(NearbyPlacesContext);
    const [placeCategories, setPlaceCategories] = useState(data.place_categories);
    const [mapStyles, setMapStyles] = useState(false);
    const [googleMap, setGoogleMap] = useState(null);

    useEffect(() => {
        if (googleMap && !nearbyPlaces.infowindow) {
            setNearbyPlaces({ ...nearbyPlaces, ...{ infowindow: new googleMap.maps.InfoWindow({ content: '', pixelOffset: new google.maps.Size(0, 0) }) } })
        }
        if (nearbyPlaces.placeSelected) {
            handleApiLoaded(googleMap);
        }
    }, [googleMap, nearbyPlaces.placeSelected]);

    const handleApiLoaded = (google) => {
        if (!googleMap) setGoogleMap(google);

        // if (!infowindow)
        const openInfoWindow = (index, place, path, google, marker) => {
            if (nearbyPlaces.placeSelected) {
                const content = ReactDOMServer.renderToString(<InfoWindowContent index={index} place={place} url={path} />);
                nearbyPlaces.infowindow.setContent(content);
                nearbyPlaces.infowindow.open(google.map, marker);
            }
        }

        if (data.places?.length > 0) {
            const bounds = new google.maps.LatLngBounds();
            data.places.map((place, index) => {
                const lat = place?.coordinates?.lat ? parseFloat(place?.coordinates?.lat) : false;
                const lng = place?.coordinates?.lng ? parseFloat(place?.coordinates?.lng) : false;
                if (lat && lng) {
                    bounds.extend(new google.maps.LatLng(lat, lng));
                }
                const title = decode(place.title);
                const path = `https://www.google.com/maps/place/${place?.address?.street?.replace(/ /g, '+')},${place?.address?.city?.replace(/ /g, '+')},${place?.address?.state_province?.replace(/ /g, '+')},${place?.address?.zip?.replace(/ /g, '+')}`
                let svgString = '';
                if (place.category_icon_type && place.category_icon_type == 'custom') {
                    svgString = place.category_icon.replace(/\<\?xml.+\?\>/g, '');
                }
                if (place.id === nearbyPlaces.placeSelected) {
                    svgString = svgString.replaceAll(/(?<=<g\b[^<>]*)\s*\bfill=(["'](?!none)).*?\1/g, ` fill="${settings?.design?.colors?.tertiary_color}"`)
                }
                let marker = new google.maps.Marker({
                    position: { lat: lat, lng: lng },
                    map: google.map,
                    title: title,
                    id: place.id,
                    categoryId: place.category_id,
                    icon: (place.category_icon_type && place.category_icon_type == 'font-awesome') ? {
                        path: solidIcons[`${place.category_icon}`] ? solidIcons[`${place.category_icon}`].icon[4] : '',
                        fillColor: place.id === nearbyPlaces.placeSelected ? nearbyPlaces.tertiaryColor : "#fff",
                        fillOpacity: 1,
                        anchor: new google.maps.Point(
                            solidIcons[`${place.category_icon}`] ? solidIcons[`${place.category_icon}`].icon[0] / 2 : 0, // width
                            solidIcons[`${place.category_icon}`] ? solidIcons[`${place.category_icon}`].icon[1] : 0, // height
                        ),
                        strokeWeight: 1.25,
                        strokeColor: nearbyPlaces.primaryColor,
                        scale: 0.06,
                    } : {
                        anchor: place.id === 'site-location' ? new google.maps.Point(75 / 2, 75) : new google.maps.Point(35 / 2, 35),
                        url: place.id === 'site-location' ? `https://${config.domain}${data.site_location_icon.url}` : `data:image/svg+xml;utf-8,${encodeURIComponent(svgString)}`,
                        scaledSize: place.id === 'site-location' ? new google.maps.Size(75, 75) : new google.maps.Size(35, 35)
                    }
                });

                marker?.addListener('click', () => {
                    setNearbyPlaces({ ...nearbyPlaces, ...{ placeSelected: place.id, categorySelected: place.category_id, infowindow: nearbyPlaces.infowindow ? nearbyPlaces.infowindow : new google.maps.InfoWindow({ content: '', pixelOffset: new google.maps.Size(0, 0) }) } })
                });

                if (nearbyPlaces.placeSelected && nearbyPlaces.placeSelected === place.id) {
                    openInfoWindow(index, place, path, googleMap, marker);
                }
            });
            google.map.fitBounds(bounds);

            if (data.places?.length === 1) {
                google.map.setZoom(14);
            }
        }
        if (data.add_custom_style && data.style_array) {
            setMapStyles(JSON.parse(data.style_array));
        }
    };

    const mapOptions = {
        styles: mapStyles
    }

    return (
        <S.MapWrapper
            className='map-wrapper large-7 cell'
            primaryColor={nearbyPlaces.primaryColor}
        >
            <div className="map-container">
                <GoogleMapReact
                    bootstrapURLKeys={{ key: Base64.decode('QUl6YVN5QkhGMUdEQnZMeVcwaUxUbndablNiS1JjSWN4LUZNT3Rn') }}
                    onGoogleApiLoaded={({ ...google }) => handleApiLoaded(google)}
                    defaultZoom={10}
                    defaultCenter={{ lat: 0, lng: 0 }}
                    options={mapOptions}
                    yesIWantToUseGoogleMapApiInternals
                >
                </GoogleMapReact>
            </div>
        </S.MapWrapper>
    );
}


const InfoWindowContent = ({ index, place, url }) => {
    const address = place.address ? Object.values(place.address) : false;
    const linkText = "Get Directions >";

    return (
        <S.InfoWindow className='info-window'>
            <div className="content-wrapper">
                {place.name &&
                    <div className='title'>{decode(place.name)}</div>
                }
                {address &&
                    <p className='address' dangerouslySetInnerHTML={{ __html: `${address[0]}<br/>${address[1] ? `${address[1]}, ` : ''}${address[2]} ${address[3]}` }} />
                }
            </div>
            <a id={`info-window-${index}`} target={'_blank'} className='place-link white-txt primary-bg' href={url}>{linkText}</a>
        </S.InfoWindow>
    )
}

export default Start;