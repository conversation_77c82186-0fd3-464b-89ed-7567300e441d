import React, { useState, createContext, useContext } from 'react';
import { SettingsContext } from "src/context";

export const NearbyPlacesContext = createContext();
export const NearbyPlacesProvider = (props) => {
    const [settings, setSettings] = useContext(SettingsContext);

    const [nearbyPlaces, setNearbyPlaces] = useState({
        categorySelected: null,
        placeSelected: null,
        primaryColor: settings?.design?.colors?.primary_color,
        secondaryColor: settings?.design?.colors?.secondary_color,
        tertiaryColor: settings?.design?.colors?.tertiary_color,
        listClick: false,
        infowindow: null
    });

    return (
        <NearbyPlacesContext.Provider value={[nearbyPlaces, setNearbyPlaces]}>
            {props.children}
        </NearbyPlacesContext.Provider>
    );
}