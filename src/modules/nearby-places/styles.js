import styled from 'styled-components';

export const NearbyPlaces = styled.section`
    padding: 2rem 0;
    h2.title {
        margin-bottom: 1rem;
    }
    .blurb {
        margin-bottom: 2rem;
    }
`

export const PlaceCategories = styled.div`
    margin-bottom: 1rem;
`
export const Category = styled.div`
    .category-heading {
        display: flex;
        align-items: center;
        position: relative;
        margin-bottom: 1rem;
        cursor: pointer;
        .icon {
            width: 42px;
            height: 42px;
            margin-right: 1rem;
            display: flex;
            align-items: center;
            justify-content: center;
            &.fa {
                svg {
                    width: 100%;
                    height: 100%;
                    object-fit: contain;
                    path {
                        stroke: ${props => props.primaryColor};
                        stroke-width: 20;
                        fill: #fff;
                    }
                }
            }
            &.custom {
                svg {
                    width: 100%;
                    height: 100%;
                    object-fit: contain;
                }
            }
        }
        .caret {
            position: absolute;
            right: 0;
            color: ${props => props.primaryColor};
            transition: .3s;
        }
        &[aria-expanded="true"] {
            .caret {
                transform: rotate(-180deg);
            }
            .icon {
                &.fa {
                    svg {
                        path {
                            // stroke: #fff;
                            fill: ${props => props.tertiaryColor};
                        }
                    }
                }
                &.custom {
                    svg circle {
                        fill: ${props => props.tertiaryColor};
                        // stroke: ${props => props.primaryColor};
                    }
                }
            }
        }
        @media (min-width: 1024px) {
            border-top: 2px solid ${props => props.primaryColor};
            padding-top: 1rem;
        }
       
    }
    .place {
        margin-bottom: 1rem;
        cursor: pointer;
        &.selected {
            font-weight: bold;
        }
        @media (min-width: 1024px) {
            margin-left: 62px;
        }
    }
    &.icons {
        .category-heading {
            .category-name {
                color: ${props => props.primaryColor};
                margin: 0;
            }
        }
    }
    &.buttons {
        .category-heading {
            border-top: none;
            background: ${props => props.secondaryColor};
            padding: 1rem;
            display: flex;
            justify-content: center;
            transition: .3s;
            .category-name {
                color: #fff;
                text-transform: uppercase;
                font-size: 1.5rem !important;
                margin: 0;
            }
            .icon, .caret {
                display: none;
            }
            &[aria-expanded='true'], &:hover {
                background: ${props => props.primaryColor};
            }
            @media (min-width: 1024px) {
                max-width: 390px;
                box-sizing: border-box;
            }
        }
        .category-places {
            .place {
                text-align: center;
                margin: 0 0 1rem;
            }
            @media (min-width: 1024px) {
                max-width: 390px;
                .place {
                    text-align: left;
                }
            }
        }
    }
`

export const MapWrapper = styled.div`
    height: 400px;
    .map-container {
        height: 400px;
    }
    @media (min-width: 1024px) {
        padding: 0 !important;
        height: 675px;
        .map-container {
            height: 675px;
        }
    }
    /* INFO WINDOW OVERRIDES */
    .gm-style-iw-chr {
        display: none !important;
    }
    .gm-style-iw {
        padding: 0 !important;
        max-height: 130px !important;
        overflow: hidden !important;
        min-width: 212px !important;
        border-radius: 0 !important;
        .gm-style-iw-d {
            max-height: 130px !important;
            &::-webkit-scrollbar {
                display: none;
            }
        }
    }
    .gm-style-iw-tc {
        &:after {
            background: ${props => props.primaryColor};
        }
        // display: none;
    }
    button.gm-ui-hover-effect {
        display: none !important;
    }
`

export const InfoWindow = styled.div`
    .content-wrapper {
        padding: .75rem .75rem .25rem;
        .title {
            font-size: .875rem;
            font-weight: bold;
            border-bottom: 1px solid;
            padding-bottom: 0.5rem;
            margin-bottom: 0.5rem;
        }
        .address {
            font-size: 0.875rem !important;
            margin-bottom: .5rem;
        }
    }
    .place-link {
        padding: 0.5rem;
        display: block;
        text-align: center;
        line-height: 1 !important;
        text-decoration: none;
    }
`