import React, { useState, useEffect } from "react";
import { decode } from "html-entities";
import { useInView } from 'react-intersection-observer';
// Helpers
const Imaging = React.lazy(() => import('src/helpers/imaging'));
import HtmlParser from 'src/helpers/html-parser'
const Clicker = React.lazy(() => import('src/helpers/clicker'));
import { Coloring } from "src/helpers";

import * as S from './styles';

const Start = ({ data, settings, placeholders }) => {
    const [active, setActive] = useState(0);
    const [number, setNumber] = useState(Math.floor(Math.random() * 10000))
    const title = (placeholders && data.title_selection === 'dynamic') ? placeholders.single_line[data.module_title] : data.module_title
    const bgImage = (placeholders && data.background_image_selection === 'dynamic') ? placeholders.image[data?.background_image_dynamic] : data?.background_image
 
    const { ref, inView } = useInView({
        triggerOnce: true,
        fallbackInView: true
    });

    return (
        <S.ItemList
            ref={ref}
            className={`item-list-module ${data.background_type}`}
            primaryColor={data.background_value === 'dark' ? '#fff' : Coloring('primary_color', settings)}
            bgColor={Coloring(data?.background_color, settings)}
            bgImage={data.background_type === 'image' ? bgImage : ''}
            textColor={data.background_value === 'dark' ? '#fff' : Coloring('body_copy_color', settings)}
        >
            {inView ?
                <div className={`grid-container`}>
                    {(title || data.blurb) &&
                        <div className='title-container grid-x'>
                            {title &&
                                <div className={`cell ${data.title_alignment}`}>
                                    <h2>{decode(title)}</h2>
                                </div>
                            }
                            {data.blurb &&
                                <div className='cell'>
                                    <div className='blurb'>
                                        <HtmlParser html={data.blurb} />
                                    </div>
                                </div>
                            }
                        </div>
                    }
                    <div className="grid-x">
                        <div className="tab-list" role="tablist">
                            {data.item_groups && data.item_groups.map((group, i) => group.group_heading && <GroupHeading data={data} group={group} setActive={setActive} active={active} i={i} number={number} placeholders={placeholders} />)}
                        </div>
                        {data.item_groups && data.item_groups.map((group, i) => <Group moduleData={data} data={group} active={active} i={i} number={number} placeholders={placeholders} />)}
                    </div>
                </div>
                : null}
        </S.ItemList>
    );
}

const GroupHeading = ({ data, group, setActive, active, i, number, placeholders }) => {
    const groupHeading = (placeholders && group.group_heading_selection === 'dynamic')  ? placeholders.single_line[group.group_heading] : group.group_heading
    
    return (
        <h3
            id={`tab-${number}-${i}`}
            key={`heading-${i}`}
            className={`group-heading ${data.background_value === 'dark' ? 'white-txt' : 'primary-txt'}${active === i ? ' active' : ''}`}
            onClick={() => setActive(i)}
            role="tab"
            aria-selected={active === i}
            aria-controls={`tabpanel-${number}-${i}`}
            tabIndex={0}
        >{decode(groupHeading)}</h3>
    )
}

const Group = ({ moduleData, data, active, i, number, placeholders }) => {

    return (
        <>
            {active === i ?
                <S.Group
                    id={`tabpanel-${number}-${i}`}
                    role='tabpanel'
                    aria-labelledby={`tab-${number}-${i}`}
                    key={`group-${i}`}
                    className="item-group cell"
                >
                    <ul className="items">
                        {data.items && data.items.map((item, i) => <Item moduleData={moduleData} data={item} separator={data.item_separator_lines} i={i} placeholders={placeholders} />)}
                    </ul>
                    {data.footnote &&
                        <div className="footnote"><HtmlParser html={data.footnote} placeholders={placeholders} /></div>
                    }
                </S.Group>
                : null}
        </>
    )
}

const Item = ({ moduleData, data, separator, i, placeholders }) => {
    const description = (placeholders && data.description_selection === 'dynamic') ? placeholders.single_line[data.description] : data.description
    const icon = (placeholders && data.icon_selection == 'dynamic') ? placeholders.image[data.item_icon] : data?.item_icon
    const itemLink = placeholders ? placeholders.button_link[data.item_link] : data?.item_link
   
    return (
        <S.Item
            key={`item-${i}`}
            separator={separator}
            className={`item separator-${separator}`}
        >
            <Clicker className={`inner-wrapper ${moduleData.background_value === 'dark' ? 'white-txt' : 'body-copy-txt'}`} type={itemLink?.url ? 'anchor' : null} url={itemLink?.url} target={itemLink?.target} ariaLabel={`link to ${description}`}>
                {icon ?
                    <span className="icon"><Imaging data={icon} /></span>
                    : <span className="spacer"></span>
                }
                {description &&
                    <span className="description">{decode(description)}</span>
                }
            </Clicker>
        </S.Item>
    )
}

export default Start;