import styled from 'styled-components';

export const ItemList = styled.div`
    padding: 2rem 0;
    color: ${props => props.textColor};
    &.color {
        background-color: ${props => props.bgColor};
    }
    &.image {
        background-image: url(${props => props.bgImage?.url});
        background-position: center center;
        background-size: cover;
        background-repeat: no-repeat;
    }
    .title-container {
        h2 {
            margin-bottom: 1rem;
        }
    }
    .tab-list {
        display: flex;
        flex-wrap: wrap;
        margin: 2rem 0;
        .group-heading {
            margin-right: 1rem;
            border-bottom: 3px solid transparent;
            padding-bottom: 0.25rem;
            cursor: pointer;
            &.active {
                font-weight: bold;
                border-color: ${props => props.primaryColor};
            }
        }
    }
`

export const Group = styled.div`
    .footnote {
        margin-top: 2rem;
    }
    .items {
        padding: 0;
        margin: 0;
        list-style: none;
        @media (min-width: 768px) {
            display: flex;
            flex-wrap: wrap;
            justify-content: space-between;
        }
    }
`

export const Item = styled.li`
    position: relative;
    &:before {
        content: '';
        position: absolute;
        width: 100%;
        border-top: 1px ${props => props.separator};
        top: -1px;
    }
    &:after {
        content: '';
        position: absolute;
        width: 100%;
        border-bottom: 1px ${props => props.separator};
        bottom: 0;
    }
    .inner-wrapper {
        display: flex;
        align-items: center;
        padding: 1rem 0;
        .icon {
            width: 30px;
            height: 30px;
            margin-right: .75rem;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        .spacer {
            width: 0;
            height: 30px;
        }
    }
    @media (min-width: 768px) {
        width: 49%;
    }
`