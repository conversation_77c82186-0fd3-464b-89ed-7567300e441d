import styled from 'styled-components';

export const AccordionModule = styled.div`
    margin: 0;
    padding: 3rem 0;
    color: ${props => props.textColor};
    &.color {
        background-color: ${props => props.bgColor};
    }
    &.image {
        background-image: url('${props => props.bgImage?.url}');
        background-position: center center;
        background-size: cover;
    }
    @media (min-width: 768px) {
        &.three-quarters-width {
            max-width: ${props => props.maxWidth}rem;
            margin: 2rem auto;
        }
    }
    
    .blurb {
        width: 100%;
    }
    
    &.underlined {
        .accordion-item {
            padding: 0;
            .heading {
                padding: 1rem 0;
                border-bottom: 1px solid;

                .icon {
                    .one, .two {
                        background: ${props => props.textColor};
                    }
                }
            }
            .content {
                margin-bottom: 1rem;
            }
        }
    }
`;
