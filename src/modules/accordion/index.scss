@import "src/scss/variables.scss";

.accordion {
    .title {
        margin: 1rem 0;
    }
    .blurb {
        margin-bottom: 30px;
    }
    .accordion-item {
        padding: 1rem;
        margin-bottom: 0.5rem;
        border-radius: 10px;
        .heading {
            display: flex;
            justify-content: space-between;
            font-size: 1rem;
            font-weight: bold;
            transition: height 0.3s;
            cursor: pointer;

            .icon {
                position: relative;
                height: 20px;
                width: 20px;
                min-width: 20px;
                margin-left: 1rem;
                display: flex;
                flex-direction: column;
                justify-content: center;
                pointer-events: none;
                .one,
                .two {
                    position: absolute;
                    content: "";
                    width: 100%;
                    height: 4px;
                    border-radius: 10px;
                    transition: 0.3s;
                    background-color: inherit;
                }
                .one {
                    transform: rotate(90deg);
                }
            }
            &.open {
                margin-bottom: 1rem;
                .icon .one {
                    transform: rotate(90deg) scale(0);
                }
            }
        }
        .content {
            max-height: 0;
            overflow: hidden;
        }
        &.open {
            .content {
                max-height: 10000px; // bigger than it will ever be
                transition: all 0.3s;
            }
        }
    }
    @media (min-width: 768px) {
        &.three-quarters-width {
            max-width: 75%;
            margin: 2rem auto;
        }
    }
}
