import React, { useState, useEffect } from "react";
import { decode } from 'html-entities';
import HtmlParser from 'src/helpers/html-parser'
import { useInView } from 'react-intersection-observer';
import { Coloring } from 'src/helpers';
import Schema from 'src/partials/schema/faq';
// Styles
import "./index.scss";
import { AccordionModule } from './styles';

const Start = ({ data, settings, placeholders }) => {
    const title = (placeholders && data.title_selection === 'dynamic') ? placeholders.single_line[data.title] : data.title
    const bgImage = (placeholders && data.background_image_selection === 'dynamic') ? placeholders.image[data?.background_image_dynamic] : data?.background_image
    const siteMaxWidth = settings?.mvk_theme_config?.layout?.site_max_width ? settings?.mvk_theme_config?.layout?.site_max_width : '75';
    const { ref, inView } = useInView({
        triggerOnce: true,
        fallbackInView: true
    });

    return (
        <AccordionModule
            ref={ref}
            className={`accordion ${data.background_type ?? ''} ${data.display_options ? data.display_options : data.style}`}
            maxWidth={parseInt(siteMaxWidth) * .75}
            bgColor={Coloring(data.background_color, settings)}
            bgImage={bgImage}
            textColor={data.background_value === 'dark' ? '#fff' : settings.design?.colors?.body_copy_color}
        >
            {(data.add_faq_schema || data.add_faq_schema === undefined) && <Schema data={data} settings={settings} placeholders={placeholders} />}
            {inView ?
                <div className={`grid-container ${data.restrict_module_width ? 'restricted' : ''}`}>
                    <div class='grid-x'>
                        {title &&
                            <div class={`title cell flex-container align-${data.title_alignment}`}>
                                <h2>{decode(title)}</h2>
                            </div>
                        }
                        {data.blurb &&
                            <div className="blurb">
                                <HtmlParser html={data.blurb} data={data} placeholders={placeholders} />
                            </div>
                        }
                    </div>
                    <Accordion settings={settings} data={data} placeholders={placeholders} />
                </div>
                : null}
        </AccordionModule>
    );
}

const Accordion = ({ data, placeholders, settings }) => {

    switch (data.style) {
        case 'underlined':
            return (
                <div class='grid-x accordion-container'>
                    {data?.accordion_items?.map((accordion, index) => <Strip settings={settings} data={data} accordion={accordion} placeholders={placeholders} index={index} moduleStyle={'underlined'} />)}
                </div>
            )
        case 'boxed':
        default:
            return (
                <div class='grid-x accordion-container'>
                    {data?.accordion_items?.map((accordion, index) => <Strip settings={settings} data={data} accordion={accordion} placeholders={placeholders} index={index} moduleStyle={'boxed'} />)}
                </div>
            )
    }

};

const Strip = ({ data, accordion, placeholders, index, moduleStyle, settings }) => {
    const [open, setOpen] = useState(false);
    const heading = (placeholders && accordion.heading_selection === 'dynamic') ? placeholders.single_line[accordion.heading] : accordion.heading

    let accordionStyle = null;
    let iconStyle = null;

    const contentBackgroundColor = (data.content_background_color && !data.content_background_color.startsWith('#')) ? Coloring(data.content_background_color, settings) : data.content_background_color;
    const headingBackgroundColor = (data.heading_background_color && !data.heading_background_color.startsWith('#')) ? Coloring(data.heading_background_color, settings) : data.heading_background_color;
    const contentTextColor = (data.content_text_color && !data.content_text_color.startsWith('#')) ? Coloring(data.content_text_color, settings) : data.content_text_color;
    const headingTextColor = (data.heading_text_color && !data.heading_text_color.startsWith('#')) ? Coloring(data.heading_text_color, settings) : data.heading_text_color;

    if (moduleStyle === 'boxed') {
        accordionStyle = {
            backgroundColor: open ? contentBackgroundColor : headingBackgroundColor,
            color: open ? contentTextColor : headingTextColor
        }
        iconStyle = {
            backgroundColor: open ? contentTextColor : headingTextColor
        }
    }
    const toggle = () => {
        setOpen(!open);
    }

    return (
        <div key={index} class='cell'>
            <div class={`accordion-item ${open ? 'open' : ''}`} style={accordionStyle}>
                <div class={`heading ${open ? 'open' : ''}`} onClick={toggle}><HtmlParser html={heading} /><span class='icon'><span class='one' style={iconStyle}></span><span class='two' style={iconStyle}></span></span></div>
                <div class='content'>
                    <HtmlParser html={accordion.content} placeholders={placeholders} />
                </div>
            </div>
        </div>
    );
};

export default Start;
