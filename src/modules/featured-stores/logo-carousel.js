import React from "react";
import Slider from "react-slick";
import { useInView } from 'react-intersection-observer';
import { decode } from 'html-entities';
// Partials
import Button from 'src/partials/button';
// Helpers
import Imaging from 'src/helpers/imaging';
import Clicker from 'src/helpers/clicker';
import { PrevArrow, NextArrow } from 'src/helpers/slick';
// SCSS.
import 'src/modules/featured-stores/index.scss';
import 'slick-carousel/slick/slick.scss';
import 'slick-carousel/slick/slick-theme.scss';


const Start = ({ data, settings }) => {
    const { ref, inView } = useInView({
        triggerOnce: true,
        fallbackInView: true
    });

    var bgColor;
    var bgImage;
    if (data?.background_type === 'color') {
        bgColor = pylot?.design?.bgColorClass(data?.background_color);
        bgImage = {};
    } else if (data?.background_type === 'image') {
        bgImage = {
            backgroundImage: `url(${data?.background_image?.url})`, backgroundRepeat: 'no-repeat', backgroundPosition: 'center', backgroundSize: 'cover'
        };
        bgColor = '';
    } else {
        return null;
    }

    let titleColor = data?.background_value === 'light' ? 'primary-txt' : 'white-txt';
    const colorFilter = data?.color_filter ? 'no-filter' : 'color-filter';

    return (
        <div ref={ref} className={`featured-stores ${data.style} ${bgColor} ${colorFilter}`} style={bgImage}>
            {inView ?
                <div className='grid-container'>
                    <div className="grid-x">
                        {data.title &&
                            <div className='cell' style={{ textAlign: data.title_button_alignment }}>
                                <h2 className={`title ${titleColor}`}>{decode(data.title)}</h2>
                            </div>
                        }
                        {data.stores ?
                            <div className="cell">
                                <Stores data={data} />
                            </div>
                            :
                            <NoneAvailable data={data} />
                        }
                        {!!data.button &&
                            <div className='cell' style={{ textAlign: data.title_button_alignment }}>
                                <Button class='button' url={data.button.url} target={data.button.target} icon={data.button_icon} type={data.button_style} title={data.button?.title} />
                            </div>
                        }
                    </div>
                </div>
                : null}
        </div>
    );
}

const Stores = ({ data }) => {
    let storeList = data.stores;
    const type = data.type;
    let quantity = data.quantity ? parseInt(data.quantity) : 10;
    if (storeList.length < quantity) {
        quantity = storeList.length
    }

    if (type !== 'selected') {
        // shuffle if random
        if (type === 'random') {
            storeList = randomize(storeList);
        }
        // slice if array exceeds quantity
        if (storeList.length > quantity) {
            storeList = storeList.slice(0, quantity);
        }
    }
    // randomize
    function randomize(array) {
        let currentIndex = array.length, randomIndex;
        while (currentIndex != 0) {
            randomIndex = Math.floor(Math.random() * currentIndex);
            currentIndex--;
            [array[currentIndex], array[randomIndex]] = [
                array[randomIndex], array[currentIndex]];
        }

        return array;
    }
    const sliderSettings = {
        autoplay: true,
        arrows: true,
        dots: false,
        infinite: true,
        speed: 500,
        // variableWidth: true,
        // adaptiveHeight: false,
        slidesToShow: quantity < 6 ? parseInt(quantity) : 6,
        slidesToScroll: 1,
        nextArrow: <NextArrow />,
        prevArrow: <PrevArrow />,
        responsive: [
            {
                breakpoint: 1199,
                settings: {
                    slidesToShow: 4,
                    slidesToScroll: 1,
                },
            },
            {
                breakpoint: 991,
                settings: {
                    slidesToShow: 3,
                    slidesToScroll: 1,
                },
            },
            {
                breakpoint: 767,
                settings: {
                    slidesToShow: 1,
                    slidesToScroll: 1,
                    // adaptiveHeight: true,
                },
            },
        ],
    };

    return (
        <Slider className='carousel' ref={(a) => a} {...sliderSettings}>
            {storeList.map((slide, index) => <Slide slide={slide} index={index} />)}
        </Slider>
    );
}

const Slide = ({ slide, index }) => {
    return (
        <Clicker className='store-link' type='anchor' url={slide.url} key={index} ariaLabel={`link to ${decode(slide.title)}`}>
            {(slide.logo_color || slide.logo_monochrome) ?
                <Imaging data={slide.logo_color || slide.logo_monochrome} />
                :
                <div className='no-logo center'>{decode(slide.title)}</div>

            }
        </Clicker>
    );
}

const NoneAvailable = ({ data }) => {
    return (<div class='grid-container text-center'><h3>{data?.no_stores_message_override ? data?.no_stores_message_override : 'No stores were found.'}</h3></div>);
};

export default Start;