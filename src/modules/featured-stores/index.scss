.featured-stores {
    &.logo-carousel {
        padding: 1rem 0;
        @media (min-width: 1200px) {
            padding: 2rem 0;
        }
        .title {
            margin-bottom: 1rem;
        }
        .carousel {
            padding: 0 2rem;
            @media (min-width: 1200px) {
                padding: 2rem;
            }
            .slick-track {
                display: flex;
                align-items: center;
            }
            .store-link {
                display: block;
                margin: 0 2rem;
                img {
                    margin: auto;
                    filter: grayscale(100%);
                    transition: 0.3s;
                    max-height: 100px;
                    object-fit: contain;
                }
                &:hover img {
                    filter: grayscale(0);
                }
            }
        }
        .button {
            @media (max-width: 1199px) {
                margin-top: 2rem;
            }
        }
    }
    &.no-filter.logo-carousel .carousel .store-link img {
        filter: none;
    }
}
