import React from 'react';
import { useInView } from 'react-intersection-observer';

// Styles
import './index.scss';
// Helpers
const Imaging = React.lazy(() => import('src/helpers/imaging'));

const Start = (({ data }) => {
    const { ref, inView } = useInView({
        triggerOnce: true,
        fallbackInView: true
    });

    return (
        <div ref={ref} class='module__logo-grid grid-container'>
            {inView ?
                <div class='inner-wrapper'>
                    {(data?.module_title || data?.blurb) &&
                        <div class='title-blurb-container'>
                            {data?.module_title && <h2 class={`module-title ${data?.title_alignment}`}>{data?.module_title}</h2>}
                            {data?.blurb && <div class='module-blurb' dangerouslySetInnerHTML={{ __html: data?.blurb }} />}
                        </div>
                    }

                    {data?.logo_gallery?.length > 0 &&
                        <div class='gallery-container'>
                            {data?.logo_gallery.map((item, index) => (
                                <Imaging key={index} class='logo' data={item} />
                            ))}
                        </div>
                    }
                </div>
                : null}
        </div>
    );
});

export default Start;