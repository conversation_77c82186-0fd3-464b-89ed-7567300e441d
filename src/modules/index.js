/*******************************************************************************************************
   Copyright 2020 - Calise Partners LLC d/b/a Imaginuity. All rights are reserved.
   Reproduction or transmission in whole or in part, in any form or by any means, electronic, mechanical
or otherwise, is prohibited without the prior written consent of Calise Partners LLC d/b/a Imaginuity.
********************************************************************************************************
   Project : 10618 Maverick Platform Development
   Author : Imaginuity Developers (<PERSON>, <PERSON>)
   Description : Module loader for pages.
   Creation Date : Wed Nov 18 2020
   Documentation : [documentation url - point to default wiki for project if no specific documentation for script]
 ********************************************************************************************************/

import React, { useState, useEffect, useContext, Suspense, useLayoutEffect } from "react";

// PARTIALS.
import Loading from "src/partials/loading";
import Drawer from "src/partials/drawer";

// HELPERS.
import { elementInView } from "src/helpers/scroll";
import { addStylesheet } from 'src/hooks';
import { withinDateRange } from "src/helpers/date";
import VideoOverlayHandler from "src/helpers/video-overlay-handler";
// CONTEXT.
import { AppContext } from 'src/contexts/app';
import { SettingsContext, LocationsContext } from 'src/context';
import { ModulesContext, ModulesProvider } from 'src/contexts/modules';
// HOOKS
import { useQuery } from 'src/hooks/query';


// MODULES.
const Accordion = React.lazy(() => import("src/modules/accordion"));
const AircraftMarketReportListing = React.lazy(() => import("src/modules/aircraft-market-report-listing"));
const Anchors = React.lazy(() => import("src/modules/anchors"));
const CTAv2 = React.lazy(() => import("src/modules/cta-v2"));
const ContentArea = React.lazy(() => import("src/modules/content-area"));
const ContentCarousel = React.lazy(() => import("src/modules/content-carousel"));
const ContentFeatured = React.lazy(() => import("src/modules/content-with-featured-image"));
const Countdown = React.lazy(() => import("src/modules/countdown"));
const DesignAccent = React.lazy(() => import("src/modules/design-accent"));
const EventListing = React.lazy(() => import("src/modules/event-listing"));
const FeaturedContent = React.lazy(() => import("src/modules/featured-content"));
const FeaturedContentCarousel = React.lazy(() => import("src/modules/featured-content-carousel"));
const FeaturedEvent = React.lazy(() => import("src/modules/featured-event"));
const FeaturedSales = React.lazy(() => import("src/modules/featured-sales"));
const FeaturedStores = React.lazy(() => import("src/modules/featured-stores"));
const FormStack = React.lazy(() => import("src/modules/formstack"));
const Gallery = React.lazy(() => import("src/modules/gallery"));
const GetConnected = React.lazy(() => import("src/modules/get-connected"));
const GravityForms = React.lazy(() => import("src/modules/gravity-forms"));
const Hero = React.lazy(() => import("src/modules/hero"));
const HeroAnimatedLayers = React.lazy(() => import("src/modules/hero-animated-layers"));
const HeroUnconstrained = React.lazy(() => import("src/modules/hero-unconstrained")); // (The module formerly known as: Image Carousel Hero);
const HeroRelatedContent = React.lazy(() => import("src/modules/hero-related-content"));
const Hours = React.lazy(() => import("src/modules/hours"));
const HoursDirections = React.lazy(() => import("src/modules/hours-and-directions"));
const HVADual = React.lazy(() => import("src/modules/hva-dual"));
const IFrame = React.lazy(() => import("src/modules/iframe"));
const ImageCarousel = React.lazy(() => import("src/modules/image-carousel"));
const ImageReel = React.lazy(() => import("src/modules/image-reel"));
const ItemList = React.lazy(() => import("src/modules/item-list"));
const LiveEvent = React.lazy(() => import("src/modules/live-event"));
const LocationMap = React.lazy(() => import("src/modules/location-map"));
const LogoGrid = React.lazy(() => import("src/modules/logo-grid"));
const MapAcquireDigital = React.lazy(() => import("src/modules/map-acquire-digital"));
const MapplicMap = React.lazy(() => import("src/modules/mapplic-map"));
const Menu = React.lazy(() => import("src/modules/menu"));
const MulticolumnPostTypeList = React.lazy(() => import("src/modules/multicolumn-post-type-list"));
const NearbyPlaces = React.lazy(() => import("src/modules/nearby-places"));
const Padding = React.lazy(() => import("src/modules/padding"));
const ParallaxImage = React.lazy(() => import("src/modules/parallax-image"));
const PostTypeList = React.lazy(() => import("src/modules/post-type-list"));
const PostTypeListFloorplans = React.lazy(() => import("src/modules/post-type-list-floorplans"));
const PostTypeListLocations = React.lazy(() => import("src/modules/post-type-list-locations"));
const PostTypeListResource = React.lazy(() => import("src/modules/post-type-list-resource"));
const PostTypeListServices = React.lazy(() => import("src/modules/post-type-list-services"));
const PostTyleListTeam = React.lazy(() => import("src/modules/post-type-list-team"));
const PostTypeListProperty = React.lazy(() => import("src/modules/post-type-list-property"));
const PostTypeListVideo = React.lazy(() => import("src/modules/post-type-list-video"));
const PostTypeSubmitForm = React.lazy(() => import("src/modules/post-type-submit-form"));
const PromoBar = React.lazy(() => import("src/modules/promo-bar"));
const PropertyMap = React.lazy(() => import("src/modules/property-map"));
const RawHtml = React.lazy(() => import("src/modules/raw-html"));
const ReviewListing = React.lazy(() => import("src/modules/review-listing"));
const Reviews = React.lazy(() => import("src/modules/reviews"));
const SaleBanner = React.lazy(() => import("src/modules/sale-banner"));
const SEIDial = React.lazy(() => import("src/modules/sei-dial"));
const SocialFeed = React.lazy(() => import("src/modules/social-feed"));
const StoreDirectory = React.lazy(() => import("src/modules/store-directory"));
const TabbedContent = React.lazy(() => import("src/modules/tabbed-content"));
const TabbedLinkBlocks = React.lazy(() => import("src/modules/tabbed-link-blocks"));
const TestimonialHero = React.lazy(() => import("src/modules/testimonial-hero"));
const TestimonialVideo = React.lazy(() => import("src/modules/testimonial-video"));
const TestimonialList = React.lazy(() => import("src/modules/testimonial-list"));
const Video = React.lazy(() => import("src/modules/video"));
const Quiz = React.lazy(() => import("src/modules/quiz/index.jsx"));

// SCSS.
import 'src/modules/index.scss';
import 'src/scss/animation.scss';

const translator = {
    'accordion': Accordion,
    'aircraft_market_report_listing': AircraftMarketReportListing,
    'anchors': Anchors,
    'content_area': ContentArea,
    'content_carousel': ContentCarousel,
    'content_with_featured_image': ContentFeatured,
    'cta_v2': CTAv2,
    'countdown': Countdown,
    'design_accent': DesignAccent,
    'event_listing': EventListing, // SCROLL BACK.
    'featured_content': FeaturedContent,
    'featured_content_carousel': FeaturedContentCarousel,
    'featured_event': FeaturedEvent,
    'featured_sales': FeaturedSales,
    'featured_stores': FeaturedStores,
    'formstack': FormStack,
    'form': GravityForms,
    'gallery': Gallery,
    'get_connected': GetConnected,
    'hero': Hero,
    'hero_animated_layers': HeroAnimatedLayers,
    'hero_related_content': HeroRelatedContent,
    'hours': Hours,
    'hours_and_directions': HoursDirections,
    'hva_dual': HVADual,
    'iframe': IFrame,
    'image_carousel': ImageCarousel,
    'image_carousel_hero': HeroUnconstrained,
    'image_reel': ImageReel,
    'item_list': ItemList,
    'live_event': LiveEvent,
    'location_map': LocationMap,
    'logo_grid': LogoGrid,
    'map_ad': MapAcquireDigital,
    'mapplic_map': MapplicMap,
    'menu': Menu,
    'multicolumn_post_type_list': MulticolumnPostTypeList,
    'nearby_places': NearbyPlaces,
    'padding': Padding,
    'parallax_image': ParallaxImage,
    'post_type_list': PostTypeList, // SCROLL BACK.
    'post_type_list_floorplans': PostTypeListFloorplans,
    'post_type_list_locations': PostTypeListLocations,
    'post_type_list_resource': PostTypeListResource,
    'post_type_list_services': PostTypeListServices,
    'post_type_list_team': PostTyleListTeam,
    'post_type_list_property': PostTypeListProperty,
    'post_type_list_video': PostTypeListVideo,
    'post_type_submit_form': PostTypeSubmitForm,
    'promo_bar': PromoBar,
    'property_map': PropertyMap,
    'raw_html': RawHtml,
    'review_listing': ReviewListing,
    'reviews': Reviews,
    'sale_banner': SaleBanner,
    'sei_dial': SEIDial,
    'social_feed': SocialFeed,
    'store_directory': StoreDirectory, // SCROLL BACK.
    'tabbed_content': TabbedContent,
    'tabbed_link_blocks': TabbedLinkBlocks,
    'testimonial_hero': TestimonialHero,
    'testimonial_video': TestimonialVideo,
    'testimonial_list': TestimonialList,
    'video': Video,
    'quiz': Quiz
};

const Start = ({ page, layout }) => {
    return (
        <ModulesProvider page={page}>
            <GetLayout page={page} layout={layout} />
        </ModulesProvider>
    );
};

const GetLayout = ({ page, layout }) => {
    const modulesContext = useContext(ModulesContext);

    switch (true) {
        case layout?.length > 0:
            return (<LoadLayout layout={layout} page={page} />);
        case modulesContext.loading:
        case modulesContext.nothing:
            // LOADING ANIMATION HERE?
            return null;
        case modulesContext.layout != false:
            return (<LoadLayout layout={modulesContext.layout} page={page} />);
        default:
            return null;
    };
};

const LoadLayout = ({ layout, page }) => {
    const [settings, setSettings] = useContext(SettingsContext);

    return (
        <Suspense fallback={<Loading />}>
            <div id="modules-container">
                {Array.isArray(layout) && layout.map((item, index) => (typeof item.module_toggle === 'boolean' && item.module_toggle === false) ? null : <DisplayModule item={item} page={page} index={index} />)}
                {settings.drawer && <Drawer settings={settings.drawer} />}
                <VideoOverlayHandler />
            </div>
        </Suspense>
    );
};

const DisplayModule = ({ item, page, index }) => {
    const appContext = useContext(AppContext);
    const [settings, setSettings] = useContext(SettingsContext);
    const [locations] = settings.current_location ? useContext(LocationsContext) : [];
    const [placeholders, setPlaceholders] = useState((settings.current_location && locations && locations[settings.current_location].mvk_placeholders) ? locations[settings.current_location].mvk_placeholders : false)
    const [animation, setAnimation] = useState(false);
    const Module = translator[item.mvk_mod_layout];
    const [number] = useState(Math.floor(Math.random() * 10000));
    const fadeIn = settings.mvk_theme_config?.other?.enable_animations && settings.mvk_theme_config?.other?.animation_types === 'slide-in-fade-in' && !item.disable_animations;
    let moduleID = item.module_id || item.id;
    let moduleInView = false;
    const query = useQuery();
    const simulateDate = query.get('simulate-date');

    // RUN FIRST, RUN ONCE.
    useLayoutEffect(() => {
        if (['event_listing', 'store_directory', 'post_type_list'].includes(item.mvk_mod_layout)) {
            appContext.save('restore', true);
        }
    }, []);

    useEffect(() => {
        if (fadeIn && settings.mvk_theme_config.other.modules_to_animate?.includes(item.mvk_mod_layout)) {
            setAnimation(true);
        }
    }, [fadeIn]);

    useEffect(() => {
        setPlaceholders((settings.current_location && locations && locations[settings.current_location].mvk_placeholders) ? locations[settings.current_location].mvk_placeholders : false)
    }, [settings.current_location])

    if (page.mvk_template) {
        if(page.mvk_placeholders) processDynamicSelection(item, page.mvk_placeholders)
        if (settings?.global?.mvk_placeholders) processDynamicSelection(item, settings?.global?.mvk_placeholders) 
    }
    // Add calendar style sheet early so it renders properly the first time
    if (item.add_calendar) {
        addStylesheet('https://cdn.jsdelivr.net/npm/fullcalendar@5.10.1/main.min.css');

    }
    if (fadeIn) {
        // if no id and the module is supposed to animate, set one
        if (!moduleID && animation) {
            moduleID = `module-${number}`;
        }
        // set to true when module in view
        moduleInView = elementInView(moduleID, 250);
    }

    var startDate = item?.start_date?.length > 0 ? new Date(item?.start_date) : null;
    var endDate = item?.end_date?.length > 0 ? new Date(item?.end_date) : null;
    var isActive = withinDateRange(startDate, endDate, item?.enable_holiday_settings, item?.schedule_show_hide, simulateDate);
    if (!Module) {
        return null;
        // return (<div class="center p20">MODULE NOT COMPLETED // {item.mvk_mod_layout}</div>);
    } else if ((!item?.schedule_show_hide && !item?.enable_holiday_settings) || item?.module_status === 'show' && isActive || item?.module_status === 'hide' && !isActive) {
        return (
            <Suspense fallback={<Loading />}>
                <section id={moduleID} key={`module-${index}`} className={`mvk-module${item.mvk_mod_layout === 'anchors' ? ' section__anchors' : ''}${animation ? ' fade-in' : ''}${animation && moduleInView ? ' animate' : ''}`}>
                    <Module data={item} settings={settings} page={page} placeholders={placeholders} />
                </section>
            </Suspense>
        );
    } else {
        return false;
    }
};

export default Start;

function processDynamicSelection(item, placeholders) {
    for (let key in item) {
        if (item.hasOwnProperty(key)) {
            if (Array.isArray(item[key])) {
                item[key].forEach(element => {
                    if (typeof element === 'object' && element !== null) {
                        processDynamicSelection(element, placeholders);
                    }
                });
            }
            else if (typeof item[key] === 'object' && item[key] !== null) {
                processDynamicSelection(item[key], placeholders);
            }
            else {
                if (key.endsWith('_selection') && item[key] === 'dynamic') {
                    const baseKey = key.replace('_selection', '');
                    const dynamicKey = baseKey + '_dynamic';
                    if (item.hasOwnProperty(dynamicKey)) {
                        item[baseKey] = item[dynamicKey];
                    }
                }
                if (placeholders.hasOwnProperty(item[key])) {
                    item[key] = placeholders[item[key]];
                }
            }
        }
        if (key === 'select_members_dynamic' && item[key] === 'dynamic') {
            let teamMembers = [];
            item[key].forEach(fieldKey => {
                item['team'].forEach(member => {
                    if (member.mvk_id === placeholders[fieldKey]) teamMembers.push(member);
                })
            })
            item['team'] = teamMembers;
        }
        if(key === 'select_events_dynamic' && item[key] === 'dynamic') {
            let events = [];
            item[key].forEach(fieldKey => {
                item['events'].forEach(eventItem => {
                    if (eventItem.mvk_id === placeholders[fieldKey]) events.push(eventItem);
                })
            })
            item['events'] = events;
        }
    }
}
