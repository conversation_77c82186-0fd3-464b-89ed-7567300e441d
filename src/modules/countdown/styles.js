import styled from 'styled-components';

export const Countdown = styled.div`
    padding: 2rem 0;
    color: ${props => props.textColor};
    &.color {
        background-color: ${props => props.backgroundColor};
    }
    &.image {
        background-image: url(${props => props.backgroundImage});
        background-position: center center;
        background-size: cover;
        background-repeat: no-repeat;
    }
    .title, .blurb {
        margin-bottom: 1rem;
    }
    .timer {
        display: flex;
        justify-content: center;
        margin: 1rem 0;
        max-width: 500px;
        margin: 2rem auto 0;
        .section {
            position: relative;
            min-width: 25%;
            box-sizing: border-box;
            .timeleft {
                font-size: 7vw;
            }
            &:after {
                content: '';
                position: absolute;
                top: 0;
                bottom: 0;
                right: 0;
                width: 1px;
                background: ${props => props.textColor};
                opacity: .7;
            }
            &:last-child {
                &:after {
                    display: none;
                }
            }
        }
    }
    .module-button {
        margin-top: 3rem;
    }
    @media (min-width: 1024px) {
        padding: 3rem 0;
        .timer {
            max-width: 700px;
            .section {
                .timeleft {
                    font-size: 6rem;
                }
                .interval {
                    font-size: 1.25rem;
                }
            }
        }
    }
    @media (min-width: 1366px) {
        padding: 4rem 0;
        .timer {
            max-width: 900px;
            .section {
                .timeleft {
                    font-size: 8rem;
                }
                .interval {
                    font-size: 1.5rem;
                }
            }
        }
    }
   
`;