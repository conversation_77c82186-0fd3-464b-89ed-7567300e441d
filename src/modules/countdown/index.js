import React, { useEffect, useState } from "react";
import { decode } from 'html-entities';
import { useInView } from 'react-intersection-observer';
// Helpers
import { Coloring } from "src/helpers";
// Partials
const Button = React.lazy(() => import('src/partials/button'));
// Styles
import { Countdown } from './styles';

const Start = ({ data, settings }) => {
    const { ref, inView } = useInView({
        triggerOnce: true,
        fallbackInView: true
    });

    return (
        <Countdown
            ref={ref}
            className={`countdown-module center ${data.background_type}`}
            backgroundColor={data.background_color}
            backgroundImage={data.background_type === 'image' ? data.background_image?.url : ''}
            textColor={data.background_value === 'dark' ? '#fff' : Coloring('body_copy_color', settings)}
        >
            {inView ?
                <div className="grid-container">
                    <div className="grid-x">
                        {data.title &&
                            <div className='cell'>
                                <h2 className='title'>{decode(data.title)}</h2>
                            </div>
                        }
                        {data.blurb &&
                            <div className='cell'>
                                <div className='blurb' dangerouslySetInnerHTML={{ __html: data.blurb }} />
                            </div>
                        }
                        <div className='cell'>
                            <Timer data={data} settings={settings} />
                        </div>
                        {data.button &&
                            <div className='cell'>
                                <Button className='module-button' url={data.button?.url} target={data.button?.target} title={data.button?.title} type={data.button_style} tone={data.background_value} />
                            </div>
                        }
                    </div>
                </div>
                : null}
        </Countdown>
    );
}

const Timer = ({ data, settings }) => {
    // calulate time remaining to target date
    const caculateRemainingTime = (targetDate) => {
        let countdownDate = new Date(targetDate).getTime();
        let now = new Date().getTime();
        let difference = countdownDate - now;

        let timeLeft = {};

        timeLeft = {
            days: difference > 0 ? Math.floor(difference / (1000 * 60 * 60 * 24)) : 0,
            hours: difference > 0 ? Math.floor((difference % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60)) : 0,
            minutes: difference > 0 ? Math.floor((difference % (1000 * 60 * 60)) / (1000 * 60)) : 0,
            seconds: difference > 0 ? Math.floor((difference % (1000 * 60)) / 1000) : 0
        }

        return timeLeft;
    }

    const [timeLeft, setTimeLeft] = useState(caculateRemainingTime(data.countdown_to));

    useEffect(() => {
        let interval = setInterval(() => {
            setTimeLeft(caculateRemainingTime(data.countdown_to));
        }, 1000);
        return () => clearInterval(interval);
    }, [data.countdown_to]);

    const timerComponents = [];

    const translatedLabels = {
        days: data.days || 'Days',
        hours: data.hours || 'Hours',
        minutes: data.minutes || 'Minutes',
        seconds: data.seconds || 'Seconds'
    }

    Object.keys(timeLeft).forEach((interval) => {
        timerComponents.push(
            <div className='section'>
                <div className='timeleft'>
                    {timeLeft[interval] > 0 ?
                        timeLeft[interval].toLocaleString('en-US', {
                            minimumIntegerDigits: 2,
                            useGrouping: false
                        }) : 0}
                </div>
                <div className='interval'>{translatedLabels[interval]}</div>
            </div>
        );
    });

    return (
        <div className='timer'>
            {timerComponents}
        </div>
    );
}


export default Start;