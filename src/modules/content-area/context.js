import React, { useState, useEffect, createContext } from 'react';

export const ModuleContext = createContext();
export const ModuleProvider = ({ data, settings, children }) => {
    const [ module, setModule ] = useState({ loading:true });

    useEffect(() => {
        setModule({ loading:true });
        data.columns = data?.columns?.map((obj, index) => {
            var classes = figureClasses(data.display_options, index);
            return({ ...obj, classes:classes });
        })
        setModule({ data:data, settings:settings })
    }, [ data ]);

    return (
        <ModuleContext.Provider value={[ module, setModule ]}>
            {children}
        </ModuleContext.Provider>
    );
};

function figureClasses (type, index) {
    var classes = ['cell'];
    switch (type) {
        case 'fullwidth':
            classes.push('small-12 medium-12 large-12');
            break;
        case 'half-half':
            classes.push('small-12 medium-6 large-6');
            break;
        case 'three-column':
            classes.push('small-12 medium-4 large-4');
            break;
        case 'four-column':
            classes.push('small-12 medium-6 large-3');
            break;
        case 'five-column':
            classes.push('small-12 medium-6');
            break;
        case 'one-third-two-third':
            classes.push((index == '0') ? 'small-12 medium-4 large-4' : 'small-12 medium-8 large-8');
            break;
        case 'two-third-one-third':
            classes.push((index == '0') ? 'small-12 medium-8 large-8' : 'small-12 medium-4 large-4');
            break;
    }
    return classes.join(' ');
};