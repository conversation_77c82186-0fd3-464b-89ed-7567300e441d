/*******************************************************************************************************
   Copyright 2020 - Calise Partners LLC d/b/a Imaginuity. All rights are reserved.
   Reproduction or transmission in whole or in part, in any form or by any means, electronic, mechanical
or otherwise, is prohibited without the prior written consent of Calise Partners LLC d/b/a Imaginuity.
********************************************************************************************************
   Project : 10618 Maverick Platform Development
   Author : Imaginuity Developers (<PERSON>, <PERSON>)
   Description : outputs a text area with varying column options
   Creation Date : Mon Dec 14 2020
   Documentation : [documentation url - point to default wiki for project if no specific documentation for script]
 ********************************************************************************************************/

import React, { useState, useEffect, useContext } from "react";
import { useInView } from 'react-intersection-observer';
import { decode } from 'html-entities';
// PARTIALS.
import Button from "src/partials/button";

// HELPERS.
import { Coloring } from 'src/helpers';
import HtmlParser from 'src/helpers/html-parser'

// CONTEXT.
import { ModuleProvider, ModuleContext } from "src/modules/content-area/context";

// SCSS.
import 'src/modules/content-area/index.scss';
import { ImageOverlay } from './styles';


const Start = ({ data, settings, placeholders }) => {
    return (
        <ModuleProvider data={data} settings={settings}>
            <Waiting data={data} settings={settings} placeholders={placeholders} />
        </ModuleProvider>
    );
}

const Waiting = ({ data, settings, placeholders }) => {
    const [module, setModule] = useContext(ModuleContext);

    if (module.loading) {
        return (<div />);
    } else {
        return (<Container data={data} settings={settings} placeholders={placeholders} />);
    }
}

const Container = ({ data, settings, placeholders }) => {
    const [module, setModule] = useContext(ModuleContext);
    const { ref, inView } = useInView({
        triggerOnce: true,
        fallbackInView: true
    });

    const title = (placeholders && data.title_selection === 'dynamic') ? placeholders.single_line[data.title] : data.title;
    const bgImage = (placeholders && data.background_image_selection === 'dynamic') ? placeholders.image[data?.background_image_dynamic] : data?.background_image

    if (data.background_type === 'image' && bgImage) {
        var backgroundStyles = {
            backgroundImage: `url(${bgImage.url})`,
            backgroundPosition: "50% 50%",
            backgroundSize: "cover",
            backgroundRepeat: "no-repeat",
        }
    } else {
        var backgroundStyles = {
            backgroundColor: Coloring(data.background_color, settings),
        }
    }
    let textColor = data?.background_value === 'light' ? 'body-copy-txt' : 'white-txt';
    let role = bgImage ? 'img' : null;
    let bgAlt = (bgImage && bgImage.alt) ? bgImage.alt : null;

    return (
        <div ref={ref} class={`content-area${data.remove_padding ? ' remove-padding' : ''}${data.background_image_overlay ? ' with-overlay' : ''}`} role={role} aria-label={bgAlt} style={backgroundStyles}>
            {inView ? <>
                {(data.background_type === 'image' && data.background_image_overlay) &&
                    <ImageOverlay overlayColor={data.background_image_overlay} overlayOpacity={data.background_image_overlay_opacity} />
                }
                <div class="content-area-box grid-container flexbox column">
                    {title && <Title title={title} alignment={data.title_button_alignment} settings={settings} data={data} />}
                    {(data.blurb && data.display_options !== 'fullwidth') && <div className={`blurb ${textColor}`}><HtmlParser html={data.blurb} data={data} placeholders={placeholders} /></div>}
                    <div class={`content-area-items grid-x grid-margin-x ${data.display_options}`}>
                        {module?.data?.columns?.map((item, index) => <Content item={item} settings={settings} data={data} placeholders={placeholders} index={index} />)}
                    </div>
                    {data.row_button && <Buttons data={data} settings={settings} placeholders={placeholders} />}
                </div>
            </> : null}
        </div>
    )
};

const Title = ({ title, alignment, settings, data }) => {
    const animate = settings.mvk_theme_config.other?.enable_animations && !data.disable_animations;
    const { ref, inView } = useInView({
        triggerOnce: true,
        fallbackInView: true,
        threshold: 0.5
    });
   
    let titleColor = data?.background_value === 'light' ? 'primary-txt' : 'white-txt';

    return (
        <div ref={ref} class={`${animate ? 'slide-in from-left' : ''} ${inView ? 'show' : ''}`}>
            <div class="slide-in-content">
                <div class={`content-area-title ${titleColor} ${alignment}`}>
                    <h2>{decode(title)}</h2>
                </div>
            </div>
        </div>
    );
}

const Content = ({ item, settings, data, placeholders }) => {
    const [table, setTable] = useState(false);
    const animate = settings.mvk_theme_config.other?.enable_animations && !data.disable_animations;
    const { ref, inView } = useInView({
        triggerOnce: true,
        fallbackInView: true,
        threshold: 0.5
    });

    useEffect(() => {
        let tableEl = document.getElementsByTagName('table');
        if (tableEl.length > 0 && item.content.includes('<table')) {
            setTable(true)
        }
    }, [item]);

    let textColor = data?.background_value === 'light' ? 'body-copy-txt' : 'white-txt';
    const button = placeholders ? placeholders.button_link[item.button] : item.button
    // const [button, setButton] = useState(placeholders ? placeholders.button_link[item.button] : item.button);

    // useEffect(() => {
    //     setButton(placeholders ? placeholders.button_link[item.button] : item.button)
    // }, [placeholders]);

    return (
        <div ref={ref} class={`${animate ? 'slide-in from-right' : ''} ${item.classes} ${data.add_separator_lines && 'separator-lines'}  ${inView ? 'show' : ''}`}>
            <div class="slide-in-content">
                <div class={`content-area-body ${textColor}`}>
                    <div class={table ? 'table-container' : 'content-container'}>
                        <HtmlParser html={item.content} data={data} placeholders={placeholders} />
                    </div>
                    {button?.title &&
                        <div class={`content-area-button ${item.button_alignment}`}>
                            <Button title={button?.title} url={button?.url} target={button?.target} type={item.button_style} tone={data.background_value} />
                        </div>
                    }
                </div>
            </div>
        </div>
    );
}

const Buttons = ({ data, placeholders }) => {
    const button = placeholders ? placeholders.button_link[data.row_button] : data.row_button
    // const [button, setButton] = useState(placeholders ? placeholders.button_link[data.row_button] : data.row_button);

    // useEffect(() => {
    //     setButton(placeholders ? placeholders.button_link[data.row_button] : data.row_button)
    // }, [placeholders]);

    if (button) {
        return (
            <div class={`row-button ${data.title_button_alignment}`}>
                <Button title={button.title} url={button.url} type={data.row_button_style} target={data.row_button.target} tone={data.background_value} />
            </div>
        )
    } else {
        return null;
    }
};

export default Start;
