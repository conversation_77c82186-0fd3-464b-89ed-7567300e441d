/*******************************************************************************************************
   Copyright 2020 - Calise Partners LLC d/b/a Imaginuity. All rights are reserved.
   Reproduction or transmission in whole or in part, in any form or by any means, electronic, mechanical
or otherwise, is prohibited without the prior written consent of Calise Partners LLC d/b/a Imaginuity.
********************************************************************************************************
   Project : 10618 Maverick Platform Development
   Author : Imaginuity Developers (<PERSON>, <PERSON>)
   Description : styles for content area
   Creation Date : Mon Dec 14 2020
   Documentation : [documentation url - point to default wiki for project if no specific documentation for script]
 ********************************************************************************************************/

@import "src/scss/variables.scss";
@import "~foundation-sites/scss/util/_util.scss";

#modules-container {
    .content-area {
        width: 100%;
        background-color: white;
        padding-top: 2rem;
        padding-bottom: 2rem;
        iframe {
            max-width: 100%;
        }

        .flexbox {
            &.half-half {
                justify-content: center;

                .content-area-box {
                    .cell:first-child {
                        @media screen and (min-width: 768px) {
                            max-width: 50%;
                            flex: unset;
                        }
                    }
                }
            }
        }

        .content-area-box {
            margin: 30px auto 15px;
            .blurb {
                margin-bottom: 30px;
            }
            .content-area-items {
                &.three-column {
                    @media screen and (min-width: 768px) and (max-width: 1023px) {
                        h2 {
                            font-size: 1.75rem;
                        }
                        h3 {
                            font-size: 1.5rem;
                        }
                    }
                }
                &.four-column {
                    @media screen and (min-width: 640px) and (max-width: 1023px) {
                        & > .cell {
                            margin-bottom: 1rem;
                        }
                    }
                }
                &.five-column {
                    flex-direction: row;

                    @include breakpoint(small down) {
                        flex-direction: column;
                    }

                    //! throw off layout?
                    @media screen and (min-width: 640px) {
                        .separator-lines {
                            padding-right: 2rem;
                        }
                        & > .cell {
                            margin-bottom: 1rem;
                        }
                    }
                    @media screen and (min-width: 1024px) {
                        & > .cell {
                            width: 20%;
                            margin: 0;
                            padding: 0 15px;
                            box-sizing: border-box;
                        }
                    }
                }
            }

            .separator-lines {
                position: relative;
                @media screen and (min-width: 640px) {
                    .slide-in-content {
                        margin-right: 2rem;
                    }
                }

                &:last-child {
                    &:after {
                        background: unset;
                    }

                    @media screen and (min-width: 640px) {
                        .slide-in-content {
                            margin-right: unset;
                        }
                    }
                }

                //! throw off layout?
                @media screen and (max-width: 639px) {
                    margin-bottom: 1.5rem;
                }
            }

            .content-area-body {
                // font-size: 1.125rem;
                // line-height: 1.5;

                @media screen and (max-width: 800px) {
                    margin: 0px 0px;
                    font-size: 1rem;
                }

                @media screen and (max-width: 600px) {
                    margin: 0px 0px;
                    font-size: 0.875rem;
                    line-height: 24px;
                }

                h4 {
                    font-size: 1.5rem;

                    @include breakpoint(small down) {
                        font-size: 1.313rem;
                    }
                }

                // p {
                //     font-size: 1.125rem;
                //     line-height: 1.5;
                // }

                .content-area-button {
                    @include breakpoint(small down) {
                        padding-bottom: 20px;
                    }

                    button {
                        margin-top: 20px;
                    }
                    &.left {
                        text-align: left;
                    }
                    &.center {
                        text-align: center;
                    }
                    &.right {
                        text-align: right;
                    }
                }

                hr {
                    border: 0;
                    // border-top: 0.0625rem solid #eeeeee;
                    // margin: 1rem 0 0;
                }
            }
            .row-button {
                margin-top: 12px;

                &.left {
                    text-align: left;
                }
                &.center {
                    text-align: center;
                }
                &.right {
                    text-align: right;
                }
            }
        }
        &.remove-padding {
            padding: 0;
            .content-area-box {
                margin: 0 auto;
                padding: 30px 15px 15px;
            }
        }
        &.with-overlay {
            position: relative;
            .content-area-box {
                position: relative;
            }
        }
    }
}
