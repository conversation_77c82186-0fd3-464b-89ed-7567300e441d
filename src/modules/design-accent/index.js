import React, { useState, useEffect } from "react";
import { useInView } from 'react-intersection-observer';

// Helpers
import { Coloring } from "src/helpers";
const Imaging = React.lazy(() => import('src/helpers/imaging'));

// Styles
import { DesignAccent } from './styles';

const Start = ({ data, settings, placeholders }) => {
    const [id] = useState(Math.floor(Math.random() * 10000));
    const addMobileImage = data.add_mobile_image;
    const accentImageMobile = addMobileImage ? ((placeholders && data.image_selection_mobile === 'dynamic') ? placeholders.image[data.accent_image_mobile_dynamic] : data.accent_image_mobile) : false;
    const accentImage = (placeholders && data.image_selection === 'dynamic') ? placeholders.image[data.accent_image_dynamic] : data.accent_image;

    const { ref, inView } = useInView({
        triggerOnce: true,
        fallbackInView: true,
        threshold: 1
    });

    return (
        <DesignAccent
            ref={ref}
            id={`accent-${id}`}
            className={`design-accent ${data.animation_type}${inView ? ' animate' : ''}`}
            bgColor={Coloring(data.background_color, settings)}
            heightMobile={data.module_height_mobile}
            heightDesktop={data.module_height_desktop}
            animateFrom={data.animate_from}
        >
            <div className={`accent-image ${data.alignment} ${addMobileImage ? `mobile-${data.alignment_mobile}` : ''}`}>
                {accentImageMobile && <Imaging className='hide-for-large' data={accentImageMobile} />}
                {accentImage && <Imaging className={`${accentImageMobile ? 'show-for-large' : ''}`} data={accentImage} />}
            </div>
        </DesignAccent>
    );
}

export default Start;