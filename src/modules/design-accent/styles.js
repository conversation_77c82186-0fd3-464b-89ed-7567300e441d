import styled from 'styled-components';

export const DesignAccent = styled.div`
    position: relative;
    overflow: hidden;
    background-color: ${props => props.bgColor};
    height: ${props => props.heightMobile}px;
    @media (min-width: 1024px) {
        height: ${props => props.heightDesktop}px;
    }
    .accent-image {
        position: absolute;
        height: 100%;
        width: 100%;
        transition: transform 1s ease;
        img {
            height: 100%;
            width: auto;
        }
        &.fullwidth {
            img {
                width: 100%;
                object-fit: cover;
            }
        }
        @media (max-width: 1023px) {
            &.mobile-left {
                text-align: left !important;
            }
            &.mobile-center {
                text-align: center !important;
            }
            &.mobile-right {
                text-align: right !important;
            }
            &.mobile-fullwidth {
                img {
                    width: 100%;
                    object-fit: cover;
                }
            }
        }
    }
    &.fade-it {
        .accent-image {
            &.left {
                left: 0;
            }
            &.right {
                right: 0;
            }
            &.center {
                left: 50%;
                transform: translateX(-50%);
            }
            img {
                opacity: 0;
                transition: 1s;
            }
        }
        &.animate {
            .accent-image img {
                opacity: 1;
            }
        }
    }
    &.slide-it {
        .accent-image {
            ${props => props.animateFrom}: 0;
            transform: translateX(${props => props.animateFrom === 'left' ? '-100%' : '100%'});
        }
        &.animate {
            .accent-image {
                transform: translateX(0);
                &.left {
                    ${props => props.animateFrom}: unset;
                    left: 0;
                }
                &.right {
                    ${props => props.animateFrom}: unset;
                    right: 0;
                }
                &.center {
                    ${props => props.animateFrom}: unset;
                    left: 50%;
                    transform: translateX(-50%);
                }
            }
           
        }
    }
    
`