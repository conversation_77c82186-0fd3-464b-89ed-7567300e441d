@import 'src/scss/variables.scss';

.juicer-module {
    padding: 40px 0px;

    .juicer-header {
        text-align: center;
        .handle {
            margin-bottom: 1rem;
        }
        @media (min-width: $break-medium) {
            .heading, .subheading {
                margin-bottom: 1rem;
            }
        }
    }

    .social-container {
        margin-bottom: .5rem;
        
        @media (min-width: 768px) {
            &.above-feed {
                display: grid;
                grid-template-columns: 1fr 1fr 1fr;
    
                .left {
                    grid-column-start: 1;
                    text-align: left;
                    justify-content: flex-start;
                }

                &.no-match {
                    .left {
                        grid-row-start: 1;
                    }
                }
    
                .center {
                    grid-column-start: 2;
                    text-align: unset;
                    justify-content: center;
                }
    
                .right {
                    grid-column-start: 3;
                    text-align: right;
                    justify-content: flex-end;
                }
            }
        }
    }

    .juicer-feed {
        max-width: none !important;
        .j-instagram-bg {
            background-color: transparent !important;
        }
        h1.referral {
            display: none;
        }
    }
}

.j-overlay {
    z-index: 9999 !important;
}