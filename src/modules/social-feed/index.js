import React, { useEffect, useState } from "react";
import styled from 'styled-components';
import './index.scss';
import { useInView } from 'react-intersection-observer';

//Helpers
import { Coloring } from "src/helpers";

const Social = React.lazy(() => import('src/partials/social-icons/social'));

const Start = ({ data, settings }) => {
    const [uniqueID] = useState(Math.floor(Math.random() * 100 * Date.now()));
    const bgColor = Coloring(data.background_color, settings);
    const color = data.background_value === 'dark' ? '#fff' : Coloring('body_copy_color', settings);
    const custom = (settings.mvk_theme_config?.other?.enable_custom_slider_arrows && settings.mvk_theme_config?.other?.custom_slider_arrow);

    const { ref, inView } = useInView({
        triggerOnce: true,
        fallbackInView: true
    });

    // if not already there add juicer resources
    if (!document.querySelector('.juicer-io-resources-wrapper')) {
        appendResourcesWrapper();
    }

    if (data?.icons_placement === 'above-feed') {
        var matchClass = data?.icons_alignment === data?.heading_alignment ? 'match' : 'no-match';
    }

    const translateJuicer = ((buttonText) => {
        setTimeout(() => {
            let el = document.querySelector(`#juicer_${uniqueID} .j-paginate.juicer-button`);
            if (el) {
                el.addEventListener('click', translateJuicer(buttonText));
                el.textContent = buttonText;
            }
        }, 1200)
    });
    if (data?.load_more_button_text) {
        translateJuicer(data?.load_more_button_text);
    }

    return (
        <SocialFeed
            ref={ref}
            className={`juicer-module ${data.background_value} ${custom ? 'custom-arrow' : ''}`}
            bgColor={bgColor}
            textColor={color}
            customArrow={custom ? settings.mvk_theme_config?.other?.custom_slider_arrow.url : null}
        >
            {inView ?
                <div class='grid-container'>
                    <div class='juicer-header'>
                        {data.heading &&
                            <h2 class={`heading ${data.background_value === 'dark' ? 'white-txt' : 'primary-txt'} ${data?.heading_alignment}`}>{data.heading}</h2>
                        }
                        {data.subheading &&
                            <div className={`subheading ${data?.heading_alignment}`}>
                                <h3>{data.subheading}</h3>
                            </div>
                        }
                        <div class={`social-container ${data?.icons_placement} ${matchClass}`}>
                            {data.instagram_handle &&
                                <a class={`${data?.heading_alignment}`} href={`https://instagram.com/${data.instagram_handle}`} target='_blank'>
                                    <h3 class='handle'>@{data.instagram_handle}</h3>
                                </a>
                            }

                            {data?.social_icons && data?.icons_placement === 'above-feed' &&
                                <Social data={data} settings={settings} />
                            }
                        </div>
                    </div>
                    {data.juicer_feed_id &&
                        <ul
                            id={`juicer_${uniqueID}`}
                            className='juicer-feed'
                            data-feed-id={data.juicer_feed_id}
                            data-per={data.juicer_amount ? data.juicer_amount : "12"}
                            data-style={data.juicer_feed_style}
                            data-columns={data.juicer_columns ? data.juicer_columns : "4"}
                            data-after={translateJuicer}
                        />
                    }
                    {data?.social_icons && data?.icons_placement === 'below-feed' &&
                        <Social data={data} settings={settings} />
                    }
                </div>
                : null}
        </SocialFeed>
    );
}

const SocialFeed = styled.div`
    background-color: ${props => props.bgColor};
    color: ${props => props.textColor};
    &.dark {
        a {
            color: ${props => props.textColor};
        }
    }
    .slick-arrow {
        background: none !important;
        top: 50% !important;
        transform: translateY(-50%);
        margin: 0 !important;
        padding: 0 .5rem !important;
        width: 40px !important;
        &:before {
            display: none;
        }
        &:after {
            content: '';
            display: inline-block;
            border-bottom: 4px solid #fff;
            border-right: 4px solid #fff;
            height: 16px;
            width: 16px;
            filter: drop-shadow(1px 1px 2px rgba(0, 0, 0, 0.5));
        }
        &.slick-prev:after {
            transform: rotate(135deg);
        }
        &.slick-next:after {
            transform: rotate(-45deg);
        }
    }
    &.custom-arrow .slick-arrow {
        &:after {
            background-image: url('${props => props.customArrow}');
            background-size: contain;
            border: none;
            width: 30px;
            height: 30px;
            transform: none;
            background-repeat: no-repeat;
        }
        &.slick-next:after {
            transform: scaleX(-1) !important;
        }
    }
`;

const appendResourcesWrapper = (() => {
    const juicerResourcesWrapper = document.createElement('div');
    juicerResourcesWrapper.setAttribute('class', 'juicer-io-resources-wrapper');

    const script = document.createElement('script');
    script.src = 'https://assets.juicer.io/embed.js';
    script.type = 'text/javascript';

    const link = document.createElement('link');
    link.media = 'all';
    link.rel = 'stylesheet';
    link.href = 'https://assets.juicer.io/embed.css';
    link.type = 'text/css';

    juicerResourcesWrapper.appendChild(script);
    juicerResourcesWrapper.appendChild(link);

    document.body.appendChild(juicerResourcesWrapper);
});

export default Start;