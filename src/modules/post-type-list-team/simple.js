import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import './simple.scss';

// HELPERS.
import Clicker from 'src/helpers/clicker';
import { Coloring } from 'src/helpers';

const Start = ({ data, settings, team }) => {
    const [teamData, setTeamData] = useState([]);
    
    useEffect(() => {
        if (data?.team_sort === 'first-name') {
            setTeamData(team?.sort((a, b) => {
                const nameA = a?.team_data?.first_name.toUpperCase();
                const nameB = b?.team_data?.first_name.toUpperCase();
    
                if (nameA < nameB) {
                    return -1;
                }
    
                if (nameA > nameB) {
                    return 1;
                }
    
                return 0;
            }));
        } else if (data?.team_sort === 'last-name') {
            setTeamData(team?.sort((a, b) => {
                const nameA = a?.team_data?.last_name.toUpperCase();
                const nameB = b?.team_data?.last_name.toUpperCase();
    
                if (nameA < nameB) {
                    return -1;
                }
    
                if (nameA > nameB) {
                    return 1;
                }
    
                return 0;
            }));
        } else {
            setTeamData(team);
        }
    }, [team])

    return (
        <div class={`style-${data?.style}`}>
            <div class='inner-wrapper grid-container'>
                <TeamContainer className={`team-container ${data?.layout} style-${data.style}`} textColor={settings?.design?.colors?.body_copy_color}>
                    {data?.hide_link_to_individual_posts ?
                        teamData.map((item, index) => (
                            <div className={`team-card ${data.background_value} hover-${data.background_value_hover}`} key={index}>
                                {(data.style === 'hover') && <Overlay className={`overlay style-${data.hover_style}`} bgColor={Coloring(data.background_color, settings)} bgImage={item.hover_data?.hover_image?.url} />}
                                {item?.team_data?.feat_image && <div class='feat-image' dangerouslySetInnerHTML={{ __html: item?.team_data?.feat_image }} />}
                                {(data.style === 'hover' && data.hover_style === 'overlay') && <div className='overlay'></div>}
                                <TeamDetails className='details' type='team_data' item={item} />
                                {data.style === 'hover' &&
                                    <TeamDetails className='details-hover' type='hover_data' data={data} item={item} />
                                }
                            </div>
                        ))
                        :
                        teamData.map((item, index) => (
                            <Clicker className={`team-card ${data.background_value} hover-${data.background_value_hover}`} key={index} type='anchor' url={item?.team_data?.url} target={item?.team_data?.external_link}>
                                {(data.style === 'hover') && <Overlay className={`overlay style-${data.hover_style}`} bgColor={Coloring(data.background_color, settings)} bgImage={item.hover_data?.hover_image?.url} />}
                                {item?.team_data?.feat_image && <div class='feat-image' dangerouslySetInnerHTML={{ __html: item?.team_data?.feat_image }} />}
                                <TeamDetails className='details' type='team_data' item={item} />
                                {data.style === 'hover' &&
                                    <TeamDetails className='details-hover' type='hover_data' data={data} item={item} />
                                }
                            </Clicker>
                        ))
                    }
                </TeamContainer>
            </div>
        </div>
    );
};

const TeamDetails = ({ className, type, item }) => {

    return (
        <div className={className}>
            {(item?.[type]?.first_name || item?.[type]?.last_name) && <div class={`name`}>{`${item?.[type]?.first_name ? item?.[type]?.first_name : ''} ${item?.[type]?.last_name ? item?.[type]?.last_name : ''}`}</div>}
            {item?.[type]?.job_title && <p class={`job-title`}>{item?.[type]?.job_title}</p>}
            {item?.[type]?.phone_number && <p class={`phone-number`}>{item?.[type]?.phone_number}</p>}
            {item?.[type]?.email && <p class={`email`}>{item?.[type]?.email}</p>}
            {item?.[type]?.linked_in && <p class={`linked-in`}>{item?.[type]?.linked_in}</p>}
        </div>
    )
}

const TeamContainer = styled.div`
    &.style-simple {
        .team-card {
            color: ${props => props.textColor};
        }
    }
    @media (min-width: 1024px) {
        &.style-hover {
            .team-card {
                .details *, .details-hover * {
                    color: ${props => props.textColor};
                }
                &.dark {
                    .details * {
                        color: #fff;
                    }
                }
                &.hover-dark { 
                    .details-hover * {
                        color: #fff;
                    }
                }
            }
        }
    }
`

const Overlay = styled.div`
    @media (max-width: 1023px) {
        display: none;
    }
    position: absolute;
    width: 100%;
    height: 100%;
    object-fit: cover;
    opacity: 0;
    transition: .3s;
    z-index: 1;

    &.style-overlay {
        background: ${props => props.bgColor};
    }
    &.style-image-flip {
        background: url(${props => props.bgImage ? props => props.bgImage : ''});
        background-size: cover;
    }
`

export default Start;