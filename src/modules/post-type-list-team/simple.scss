.PTL-Team {
    .style-simple,
    .style-hover {
        .title-copy-container {
            margin-bottom: 1.875rem;
        }

        .simple-title,
        .hover-title {
            margin-bottom: 1rem;
        }

        .team-container {
            display: grid;
            grid-auto-flow: row;
            justify-items: center;
            column-gap: 0.5rem;
            row-gap: 2rem;

            @media (min-width: 768px) {
                column-gap: 0.75rem;
            }

            @media (min-width: 1366px) {
                column-gap: 0.65rem;
                row-gap: 4rem;
            }

            &.two_column,
            &.three_column,
            &.four_column,
            &.five_column,
            &.six_column {
                grid-template-columns: 1fr 1fr;
            }

            &.three_column {
                @media (min-width: 992px) {
                    grid-template-columns: 1fr 1fr 1fr;
                }
            }

            &.four_column {
                @media (min-width: 992px) {
                    grid-template-columns: 1fr 1fr 1fr 1fr;
                }
            }

            &.five_column {
                @media (min-width: 992px) {
                    grid-template-columns: 1fr 1fr 1fr 1fr 1fr;
                }
            }

            &.six_column {
                @media (min-width: 992px) {
                    grid-template-columns: 1fr 1fr 1fr 1fr;
                }

                @media (min-width: 1024px) {
                    grid-template-columns: 1fr 1fr 1fr 1fr 1fr 1fr;
                }
            }
            &.style-hover {
                .team-card {
                    position: relative;
                    overflow: hidden;
                    .details,
                    .details-hover {
                        > * {
                            margin-bottom: 0.5rem;
                        }
                    }
                    @media (min-width: 1024px) {
                        .feat-image {
                            height: 100%;
                        }
                        .details,
                        .details-hover {
                            position: absolute;
                            bottom: 0;
                            left: 0.5rem;
                            right: 0.5rem;
                            transition: 0.3s;
                            z-index: 2;
                        }
                        .details-hover,
                        .overlay {
                            opacity: 0;
                        }
                        &:hover {
                            .details {
                                opacity: 0;
                            }
                            .details-hover {
                                opacity: 1;
                            }
                            .overlay {
                                opacity: 0.5;
                                &.style-image-flip {
                                    opacity: 1;
                                }
                            }
                        }
                    }
                }
            }
        }

        .team-card {
            width: 100%;
        }

        .feat-image {
            margin-bottom: 0.5rem;
            img {
                width: 100%;
                height: 100%;
                object-fit: cover;
            }
            @media (min-width: 768px) {
                margin-bottom: 1rem;
            }
        }

        .name {
            margin-bottom: 0.3rem;
            max-width: 95%;
            font-size: 1.125rem;
            font-weight: bold;
        }

        .job-title {
            max-width: 95%;
        }
    }
}
