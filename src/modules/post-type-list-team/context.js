import React, { useState, createContext } from 'react';

export const TeamFilterContext = createContext();
export const TeamFilterProvider = (props) => {
    const [teamFilters, setTeamFiltration] = useState({
        selectedCategory: 0
    });

    return (
        <TeamFilterContext.Provider value={[teamFilters, setTeamFiltration]}>
            {props.children}
        </TeamFilterContext.Provider>
    );
}