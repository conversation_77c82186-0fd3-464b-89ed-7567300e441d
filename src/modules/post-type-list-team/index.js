import React, { Suspense, useContext, useState, useEffect } from "react";
import styled from 'styled-components';
import { decode } from 'html-entities';
import { useInView } from 'react-intersection-observer';
// Context
import { TeamFilterContext, TeamFilterProvider } from "./context";
// Helpers
import { OppositeStyle } from 'src/helpers/theme';
import HtmlParser from 'src/helpers/html-parser';
// Styles
const Contact = React.lazy(() => import("./contact"));
const Simple = React.lazy(() => import("./simple")); // Also Hover
const Button = React.lazy(() => import('src/partials/button'));

const Start = ({ data, settings, placeholders }) => {
    const [listings, setListings] = useState([]);

    useEffect(() => {
        if (settings.current_location) {
            setListings(() => data.team?.filter((i) => {
                return i.location === settings.current_location;
            }))
        } else {
            setListings(data.team);
        }
    }, [placeholders])

    return (
        <Suspense fallback={<div />}>
            <TeamFilterProvider>
                <Styles data={data} settings={settings} placeholders={placeholders} listings={listings} />
            </TeamFilterProvider>
        </Suspense>
    );
};

const Styles = ({ data, settings, placeholders, listings }) => {
    const [teamFilters, setTeamFiltration] = useContext(TeamFilterContext);
    const [team, setTeam] = useState(listings);

    const { ref, inView } = useInView({
        triggerOnce: true,
        fallbackInView: true
    });

    useEffect(() => {
        if (data.team_show_filters && teamFilters.selectedCategory) {
            setTeam(listings.filter((member) => {
               if (member?.category?.includes(teamFilters.selectedCategory)) {
                    return member;
                }
            }))
        } else {
            setTeam(listings);
        }
    }, [listings, teamFilters])

    const title = (placeholders && data.title_selection === 'dynamic') ? placeholders.single_line[data.title] : data.title;

    return (
        <div ref={ref} className='grid-container PTL-Team'>
            {inView ? <>
                {(title || data?.Copy) ?
                    <div class='grid-container title-copy-container'>
                        {title && <h2 class={`${data.style}-title ${data?.title_alignment}`}>{decode(title)}</h2>}
                        {data?.Copy && <div class={`${data.style}-copy`}><HtmlParser html={data.Copy} placeholders={placeholders} /></div>}
                    </div>
                    :
                    null
                }
                {data.team_show_filters && <Filters data={data} settings={settings} />}
                {data.style === "contact" && <Contact data={data} team={team} />}
                {(data.style === "simple" || data.style == "hover") && <Simple data={data} settings={settings} team={team} />}
            </> : null}
        </div>
    )

}

const Filters = ({ data, settings }) => {

    return (
        <div className="team-filters grid-container">
            <div className="grid-x">
                <FilterButtons data={data} settings={settings} />
                <FilterDropdown data={data} settings={settings} />
            </div>
        </div>
    )
}

const FilterButtons = ({ data, settings }) => {
    const [teamFilters, setTeamFiltration] = useContext(TeamFilterContext);
    const [buttonStyle, setButtonStyle] = useState((data.button_style === 'secondary') ? settings.mvk_theme_config.other?.secondary_button_style : settings.mvk_theme_config.other?.primary_button_style)
    const [oppositeStyle, setOppositeStyle] = useState(OppositeStyle(buttonStyle));

    const filterClick = (e, cat) => {
        e.preventDefault();
        setTeamFiltration({ selectedCategory: parseInt(cat) });
    }

    return (
        <Buttons className={'filter-buttons cell show-for-large'}>
            <Button
                className={`filter-button${teamFilters.selectedCategory === 0 ? ' active' : ''}`}
                title={'All'}
                type={data.button_style}
                forceStyle={teamFilters.selectedCategory === 0 ? oppositeStyle : buttonStyle}
                buttonFunction='styled'
                onClick={(e) => filterClick(e, 0)}
                aria-label='Show All Categories'
                tabindex={0}
                active={teamFilters.selectedCategory === 0 ? 'true' : 'false'}
            />
            {data.team_categories && data.team_categories.map((category, i) =>
                <Button
                    className={`filter-button${teamFilters.selectedCategory === category.id ? ' active' : ''}`}
                    key={`filter-${i}`}
                    title={decode(category.name)}
                    type={data.button_style}
                    forceStyle={teamFilters.selectedCategory === category.id ? oppositeStyle : buttonStyle}
                    onClick={(e) => filterClick(e, category.id)}
                    buttonFunction='styled'
                    aria-label={`filter by ${category.name}`}
                    tabindex={0}
                    active={teamFilters.selectedCategory === category.id ? 'true' : 'false'}
                />
            )}
        </Buttons>
    )
}

const FilterDropdown = ({ data, settings }) => {
    const [teamFilters, setTeamFiltration] = useContext(TeamFilterContext);

    const dropdownChange = (e) => {
        setTeamFiltration({ selectedCategory: parseInt(e.target.value) });
    }

    return (
        <Dropdown
            className={'filter-dropdown cell hide-for-large'}
            primaryColor={settings?.design?.colors?.primary_color}
        >
            <select onChange={dropdownChange} aria-label={'team filter dropdown'}>
                <option value="0">{'All'}</option>
                {data.team_categories && data.team_categories.map((category, i) => <option key={`option-${i}`} value={category.id} selected={teamFilters.selectedCategory === category.id}>{decode(category.name)}</option>)}
            </select>
        </Dropdown>
    )
}

const Buttons = styled.div`
    @media (min-width: 1024px) {
        margin: 1rem 0 2rem;
        .filter-button {
            margin: 0 1rem 1rem 0;
        }
    }
`

const Dropdown = styled.div`
    margin: 1rem 0 2rem;
    select {
        background: #fff;
        border: 2px solid ${props => props.primaryColor};
    }
`

export default Start;