import React, { useState, useEffect } from 'react';

import './contact.scss';

//PARTIALS
const Button = React.lazy(() => import('src/partials/button'));

const Start = ({ data, team }) => {
    const [teamData, setTeamData] = useState([]);
    
    useEffect(() => {
        if (data?.team_sort === 'first-name') {
            setTeamData(team?.sort((a, b) => {
                const nameA = a?.team_data?.first_name.toUpperCase();
                const nameB = b?.team_data?.first_name.toUpperCase();
    
                if (nameA < nameB) {
                    return -1;
                }
    
                if (nameA > nameB) {
                    return 1;
                }
    
                return 0;
            }));
        } else if (data?.team_sort === 'last-name') {
            setTeamData(team?.sort((a, b) => {
                const nameA = a?.team_data?.last_name.toUpperCase();
                const nameB = b?.team_data?.last_name.toUpperCase();
    
                if (nameA < nameB) {
                    return -1;
                }
    
                if (nameA > nameB) {
                    return 1;
                }
    
                return 0;
            }));
        } else {
            setTeamData(team);
        }
    }, [team])
    
    return (
        <div class={`${data?.style}`}>
            <div class='inner-wrapper grid-container'>
                <div class={`contact-container ${data?.layout}`}>
                    {teamData.map((item, index) => (
                        <div class={`contact-card ${data?.contact_layout}`} key={index}>
                            <div class='image-container'>
                                {item?.team_data?.feat_image && <div class='feat-image' dangerouslySetInnerHTML={{ __html: item?.team_data?.feat_image }} />}
                            </div>

                            <div class='copy-container'>
                                <div class='name body-copy-txt'>{`${item?.team_data?.first_name && item?.team_data?.first_name} ${item?.team_data?.last_name && item?.team_data?.last_name}`}</div>
                                {item?.team_data?.job_title && <p class='job-title body-copy-txt'>{item?.team_data?.job_title}</p>}
                                {item?.team_data?.phone_number && <a class='phone body-copy-txt' href={`tel: ${item?.team_data?.phone_number}`}>{item?.team_data?.phone_number}</a>}
                                {item?.team_data?.email && <a class='email body-copy-txt' href={`mailto: ${item?.team_data?.email}`}>{item?.team_data?.email}</a>}
                                {data?.show_button && <Button class='contact-button' title={item?.team_data?.link_text_override ? item?.team_data?.link_text_override : data?.button_text ? data?.button_text : 'View Bio'} url={item?.team_data?.url} type={data?.button_style} target={item?.team_data?.external_link} />}
                            </div>
                        </div>
                    ))}
                </div>
            </div>
        </div>
    );
};

export default Start;