import React from 'react';
import styled from 'styled-components';
import { BackgroundClass } from "src/helpers/theme";

const Start = ({ data }) => {
    const bgClass = BackgroundClass(data?.background_color);
    
    return (
        <Padding className={`padding-module ${bgClass}${data?.height_mobile ? ' with-mobile' : ''}`} heightMobile={data?.height_mobile} heightDesktop={data?.height}></Padding>
    );
};

const Padding = styled.div`
    padding: ${props => props.heightDesktop}rem 0;
    &.with-mobile {
        @media (max-width: 767px) {
            padding: ${props => props.heightMobile}rem 0;
        }
    }
`

export default Start;