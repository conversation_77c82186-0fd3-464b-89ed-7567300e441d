import React, { useState, useEffect, useRef } from 'react';
import { decode } from 'html-entities';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faStar } from '@fortawesome/free-solid-svg-icons';
// Helpers
import { Coloring } from 'src/helpers';
// Styles
import * as S from './styles';
// PARTIALS.
import Button from 'src/partials/button';

const Start = ({ data, settings }) => {
    const [reviews] = useState(data.reviews?.results && Array.isArray(data.reviews?.results) ? data.reviews?.results : false);
    // Pagination
    const [currentPage, setCurrentPage] = useState(0);
    const reviewsPerPage = 4;
    const totalPages = Math.ceil(reviews.length / reviewsPerPage);
    const handleNextPage = () => {
        if (currentPage < totalPages - 1) {
            setCurrentPage(currentPage + 1);
        }
    };

    const handlePrevPage = () => {
        if (currentPage > 0) {
            setCurrentPage(currentPage - 1);
        }
    };
    const getPageNumbers = () => {
        const pageNumbers = [];
        for (let i = 1; i <= totalPages; i++) {
            pageNumbers.push(i);
        }
        return pageNumbers;
    };

    const startIndex = currentPage * reviewsPerPage;
    const endIndex = startIndex + reviewsPerPage;
    const displayedReviews = Array.isArray(reviews) ? reviews.slice(startIndex, endIndex) : false;

    return (
        <S.List
            className={`review-list grid-x`}
        >
            {displayedReviews && displayedReviews?.map((review, index) =>
                <ReviewItem review={review} index={index} bgColor={(data.background_type === 'color' && data.background_color !== 'none') ? Coloring(data.background_color, settings) : '#fff'} />
            )}
            <S.Pagination
                className="pagination cell"
                buttonColor={data.background_value === 'dark' ? '#fff' : settings.design?.colors?.primary_color}
                textColor={data.background_value === 'dark' ? settings.design?.colors?.primary_color : '#fff'}
            >
                <div className='pages'>
                    {getPageNumbers().map((page) => (
                        <button
                            key={page}
                            className={(page === currentPage + 1) ? 'active' : ''}
                            onClick={() => setCurrentPage(page - 1)}
                        >
                            {page}
                        </button>
                    ))}
                </div>
                <div className='buttons'>
                    <Button onClick={handlePrevPage} disabled={currentPage === 0} title={'Previous'} tone={data.background_value} buttonFunction="styled" />
                    <Button onClick={handleNextPage} disabled={currentPage === totalPages - 1} title={'Next'} tone={data.background_value} buttonFunction="styled" />
                </div>
            </S.Pagination>
        </S.List>
    )
}

const ReviewItem = ({ review, index, bgColor }) => {
    const [overflowState, setOverflowState] = useState([]);
    const textRef = useRef(null);
    const [readMore, setReadMore] = useState(null);

    const checkOverflow = () => {
        const el = textRef.current;
        setOverflowState(el ? el.scrollHeight > el.clientHeight : false);
    };

    useEffect(() => {
        checkOverflow();
        setReadMore(false);
        window.addEventListener('resize', checkOverflow);

        return () => {
            window.removeEventListener('resize', checkOverflow);
        };
    }, [review])

    return (
        <S.Item
            key={`review-${index}`}
            className={`review-item cell ${(overflowState && !readMore) ? 'has-overflow' : ''}`}
            bgColor={bgColor}
        >
            <div className='inner-wrapper'>
                <div className='title'>
                    <h4 className='name'>{review?.author_name}</h4>
                    <small>{review?.relative_time_description}</small>
                </div>
                <S.Rating className='rating' stars={review?.rating}>
                    <FontAwesomeIcon className='star' icon={faStar} />
                    <FontAwesomeIcon className='star' icon={faStar} />
                    <FontAwesomeIcon className='star' icon={faStar} />
                    <FontAwesomeIcon className='star' icon={faStar} />
                    <FontAwesomeIcon className='star' icon={faStar} />
                </S.Rating>
                <div id={`review-content-${review?.author_name.toLowerCase().replace(/\s+/g, '-')}`} className={`content-container${readMore ? ' show' : ''}`} ref={textRef}>
                    <p>{review?.text}</p>
                </div>
                {(overflowState) && <a className='read-more' onClick={() => setReadMore(!readMore)} role="button" aria-expanded={readMore} aria-controls={`review-content-${review?.author_name.toLowerCase().replace(/\s+/g, '-')}`}>{readMore ? 'Read Less >' : 'Read More >'}</a>}
            </div>
        </S.Item>
    )
}

export default Start;