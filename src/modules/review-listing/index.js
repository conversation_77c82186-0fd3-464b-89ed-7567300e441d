import React, { useState, useRef, useEffect, useContext } from 'react';
import Slider from "react-slick";
import { decode } from 'html-entities';
import { useInView } from 'react-intersection-observer';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faLaptopHouse, faStar } from '@fortawesome/free-solid-svg-icons';
import { LocationsContext } from 'src/context';
// Helpers
import HtmlParser from 'src/helpers/html-parser'
import { Coloring } from 'src/helpers';
import { PrevArrow, NextArrow, Dots, Dot } from 'src/helpers/slick';
import Modal from 'src/helpers/modal';
// Styles
import * as S from './styles';
import "slick-carousel/slick/slick.scss";
import "slick-carousel/slick/slick-theme.scss";
import PostedOnGoogle from '../../../public/images/post-on-google.png';
// Components
const List = React.lazy(() => import('./list'));

const Start = ({ data, settings, placeholders }) => {
    const title = (placeholders && data.title_selection === 'dynamic') ? placeholders.single_line[data.title] : data.title;
    const bgImage = (placeholders && data.background_image_selection === 'dynamic') ? placeholders.image[data?.background_image_dynamic] : data?.background_image

    const { ref, inView } = useInView({
        triggerOnce: true,
        fallbackInView: true
    });

    return (
        <S.ReviewListing
            ref={ref}
            className={`review-listing ${data.background_type ?? ''} ${data.review_source}`}
            bgColor={Coloring(data.background_color, settings)}
            bgImage={bgImage ? bgImage.url : ''}
            textColor={data.background_value === 'dark' ? '#fff' : settings.design?.colors?.body_copy_color}
        >
            {inView ?
                <div className={`grid-container`}>
                    <div class='grid-x title-container'>
                        {title &&
                            <div class={`title cell align-${data.title_alignment}`}>
                                <h2>{decode(title)}</h2>
                            </div>
                        }
                        {data.blurb &&
                            <div className="blurb cell">
                                <HtmlParser html={data.blurb} placeholders={placeholders} />
                            </div>
                        }
                    </div>
                    {((data.review_source !== 'google' && data.reviews) || (data.review_source === 'google' && data.review_style === 'carousel')) && <Reviews data={data} settings={settings} />}
                    {(data.review_source === 'google' && data.review_style === 'list') && <List data={data} settings={settings} />}
                </div>

                : null}
        </S.ReviewListing>
    )
}

const Reviews = ({ data, settings }) => {
    const [locations] = settings.current_location ? useContext(LocationsContext) : [];
    const [reviews, setReviews] = useState(data.review_source === 'google' ? data.reviews?.results : data.reviews);
    const [overflowStates, setOverflowStates] = useState([]);
    const textRefs = useRef([]);
    const [showModal, setShowModal] = useState(null);

    useEffect(() => {
        if (settings.current_location) {
            if (data.review_source != 'google') {
                setReviews(() => reviews?.filter((i) => {
                    return i.locations?.includes(settings.current_location);
                }))
            } else {
                const locationReviews = locations[settings.current_location].reviews?.results;
                setReviews(locationReviews || data.reviews?.results)
            } 
        } else {
            setReviews(data.review_source === 'google' ? data.reviews?.results : data.reviews);
        }
        checkOverflow()
    }, [settings.current_location]);

    const checkOverflow = () => {
        const newOverflowStates = textRefs.current.map(el => {
            return el ? el.scrollHeight > el.clientHeight : false;
        });
        setOverflowStates(newOverflowStates);
    };

    const sliderSettings = {
        slidesToShow: data.review_source === 'google' ? 3 : 1,
        slidesToScroll: 1,
        slickGoTo: 0,
        arrows: true,
        infinite: false,
        dots: faLaptopHouse,
        adaptiveHeight: data.review_source === 'google' ? false : true,
        nextArrow: <NextArrow />,
        prevArrow: <PrevArrow />,
        beforeChange: (current, next) => {
            checkOverflow();
        },
        afterChange: (current) => {
            checkOverflow();
        },
    };
    if (data.review_source === 'google') {
        sliderSettings.responsive = [
            {
                breakpoint: 1023,
                settings: {
                    slidesToShow: 2,
                    slidesToScroll: 1,
                    adaptiveHeight: false
                }
            },
            {
                breakpoint: 639,
                settings: {
                    slidesToShow: 1,
                    slidesToScroll: 1,
                    adaptiveHeight: true
                }
            }
        ]
    }

    const openModal = (e, index) => {
        e.preventDefault()
        setShowModal(index);
    }
    const close = () => {
        setShowModal(false);
    };
    if(typeof reviews == 'string'){
        return <h2>{reviews}</h2>;
    }
    if (!reviews || reviews?.length === 0) return null;
    return (
        <S.ReviewCarousel
            className='grid-x review-carousel'
            textColor={settings.design?.colors?.body_copy_color}
        >
            <div className='cell'>
                <Slider ref={(a) => a} {...sliderSettings}>
                    {data.review_source !== 'google' && reviews?.map((review) =>
                        <div className='review'>
                            <div className='inner-wrapper'>
                                {review.quote && <div className='quote'><HtmlParser html={review.quote} /></div>}
                                {review.source_name && <p className='source-name strong'>{decode(review.source_name)}</p>}
                                {review.source_description && <p className='source-description'>{decode(review.source_description)}</p>}
                            </div>
                        </div>
                    )}
                    {data.review_source === 'google' && reviews?.map((review, index) =>
                        <S.ReviewCard
                            key={`review-${index}`}
                            className={`review-card ${overflowStates[index] ? 'has-overflow' : ''}`}
                            onClick={overflowStates[index] ? (e) => openModal(e, index) : null}
                        >
                            <InnerWrapper review={review} textRefs={textRefs} index={index} />
                        </S.ReviewCard>
                    )}
                </Slider>
            </div>
            {(showModal || showModal === 0) &&
                <Modal close={close} closeIcon={true} clickDarkness={true} active={showModal !== false || showModal === 0} class='review-listing'>
                    <InnerWrapper review={reviews[showModal]} textRefs={textRefs} index={showModal} />
                </Modal>
            }
        </S.ReviewCarousel>
    )
}

const InnerWrapper = ({ review, textRefs, index }) => {

    return (
        <div className='inner-wrapper'>
            <div className='author'>
                {review?.profile_photo_url && <img src={review?.profile_photo_url} alt={`${review?.author_name}'s profile`} />}
                <div className='info'>
                    <h4 className='name'>{review?.author_name}</h4>
                    <small>{review?.relative_time_description}</small>
                </div>
            </div>
            <S.Rating className='rating' stars={review?.rating}>
                <FontAwesomeIcon className='star' icon={faStar} />
                <FontAwesomeIcon className='star' icon={faStar} />
                <FontAwesomeIcon className='star' icon={faStar} />
                <FontAwesomeIcon className='star' icon={faStar} />
                <FontAwesomeIcon className='star' icon={faStar} />
            </S.Rating>
            <div className={`content-container`} ref={el => textRefs.current[index] = el}>
                <p>{review?.text}</p>
            </div>
            <div className='post-on-google'>
                <img src={PostedOnGoogle} alt="posted on Google" />
            </div>
        </div>
    )
}

export default Start;