import styled from 'styled-components'

export const ReviewListing = styled.div`
    padding: 3rem 0;
    color: ${props => props.textColor};
    &.color {
        background-color: ${props => props.bgColor};
    }
    &.image {
        background-image: url(${props => props.bgImage});
        background-position: center center;
        background-size: cover;
        background-repeat: no-repeat;
    }
    .title-container {
        margin-bottom: 1rem;
    }
    .review {
        text-align: center;
        .inner-wrapper {
            padding: 0 2rem;
            margin: auto;
            max-width: 900px;
            .quote {
                margin-bottom: 1rem;
            }
            .source-name {
                margin-bottom: 0.25rem;
                font-weight: bold !important;
            }
        }
    }
    &.google {
        .review-carousel {
            margin-bottom: 3rem;
            .slick-arrow {
                transform: none;
                bottom: -62px;
                top: unset;
                &.slick-prev {
                    left: calc(50% - 3rem);
                }
                &.slick-next {
                    right: calc(50% - 3rem);
                }
                &.slick-disabled {
                    display: block !important;
                    opacity: .5;
                    cursor: unset;
                }
            }
        }
    }
`
export const ReviewCarousel = styled.div`
    color: ${props => props.textColor}; 
`

export const ReviewCard = styled.div`
    .inner-wrapper {
        background: #fff;
        padding: 1.5rem;
        margin: .5rem;
        border-radius: 10px;
        border: 2px solid #d1d1d1;
        .author {
            display: flex;
            align-items: center;
            margin-bottom: 1rem; 
            img {
                width: 60px;
                margin-right: 1rem;
            }
            .name {
                margin: 0;
            }
        }
        .content-container {
            height: 120px;
            overflow: hidden;
            position: relative;
            p {
                margin: 0;
            }
        }
        .post-on-google {
            margin-top: 1.5rem;
        }
    }
    &.has-overflow { 
        .inner-wrapper {
            cursor: pointer;
            transition: .3s;
            &:hover {
                box-shadow: 2px 2px 6px rgba(0,0,0,.5); 
            }
        }
        .content-container:after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 60px;
            background: linear-gradient(to top,white, transparent);
        }
    }
`

export const Rating = styled.div`
    margin: 1.25rem 0;
    .star {
        font-size: 1.5rem;
        margin-right: .25rem;
        color: #d1d1d1;
        &:nth-child(-n+${props => props.stars}) {
            color: #f8af0b;
        }
    }

`

export const List = styled.div`

`

export const Item = styled.div`
    margin-bottom: 2.5rem;
    padding-bottom: 3.5rem;
    border-bottom: 1px solid;
    .rating {
        margin: 1.5rem 0 2rem;
    }
    .content-container {
        max-height: 100px;
        height: 100px;
        overflow: hidden;
        position: relative;
        p {
            margin: 0;
        }
        &.show {
            max-height: 10000px;
            height: auto;
            transition: all 0.3s;
        }
    }
    &.has-overflow {
        .content-container:after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 80px;
            background: linear-gradient(to top, ${props => props.bgColor}, transparent);
        }
    }
    .read-more {
        padding: .5rem 0;
        text-decoration: underline;
        width: auto;
    }
`

export const Pagination = styled.div`
    @media (min-width: 768px) {
        display: flex;
        justify-content: space-between;
    }
    .pages {
        margin-bottom: 1rem;
        button {
            padding: 0;
            background: transparent;
            border: none;
            margin: .5rem;
            padding: 1rem;
            border-radius: 50%;
            height: 25px;
            width: 25px;
            &.active {
                background-color: ${props => props.buttonColor};
                color: ${props => props.textColor};
            }
        }
    }
    .buttons {
        display: grid;
        grid-auto-flow: column;
        grid-auto-columns: 1fr 1fr;
        gap: 1rem;
        @media (min-width: 768px) {
            display: block;
            button {
                margin-left: 1rem;
            }
        }
    }

`