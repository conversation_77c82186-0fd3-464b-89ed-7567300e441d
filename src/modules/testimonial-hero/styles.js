import styled from 'styled-components';

export const TestimonialHero = styled.div`
    padding: 2rem 0 0;
    color: ${props => props.textColor};
    &.color {
        background-color: ${props => props.backgroundColor};
    }
    &.image {
        background-image: url(${props => props.backgroundImage});
        background-position: center center;
        background-size: cover;
        background-repeat: no-repeat;
    }
    @media (max-width: 639px) {
        text-align: center;
        .main-content {
            margin-bottom: 2rem;
        }
    }
    @media (min-width: 640px) {
        &.slides-left {
            text-align: right;
            .container-one {
                order: 2;
            }
            .container-two {
                order: 1;
            }
        }
        &.slides-right {
            text-align: left;
        }
    }
    @media (max-width: 1023px) {
        .desktop-images {
            opacity: 0;
            height: 0;
            pointer-events: none;
        }
    }
    @media (min-width: 1024px) {
        padding: 2rem 0;
        position: relative;
        overflow: hidden;
        min-height: 420px;
        .grid-x {
            min-height: 420px;
            align-items: center;
            justify-content: space-between;
        }
        .desktop-images {
            position: absolute;
            left: 50%;
            transform: translateX(-38%);
            bottom: 0;
            z-index: 0;
            max-height: 98%;
            max-width: 600px;
        }
        &.slides-left {
            .desktop-images {
                transform: translateX(-62%);
            }
        }
    }
    @media (min-width: 1200px) {
        .desktop-images {
            transform: translateX(-30%);
        }
         &.slides-left {
            .desktop-images {
                transform: translateX(-70%);
            }
         }
    }
     @media (min-width: 1366px) {
        .desktop-images {
            transform: translateX(-25%);
        }
         &.slides-left {
            .desktop-images {
                transform: translateX(-75%);
            }
         }
    }
    .main-content {
        h1 {
            margin-bottom: 1rem;
            font-size: 2rem;
        }
        .blurb {
            margin-bottom: 1.5rem;
        }
    }
    .testimonial-image-carousel {
        .slick-track {
            display: flex;
            align-items: flex-end;
            .slick-slide {
                display: flex;
                justify-content: center;
            }
        } 
    }
`;

export const TestimonialHeroCarousel = styled.div`
    text-align: center;
    padding: 2rem 0 1rem;
    .carousel-item {
        .custom-title {
            margin-bottom: .5rem;
        }
        .name {
            font-weight: bold;
            @media (min-width: 1024px) {
                font-size: 1.125rem;
            }
            span {
                margin-right: .25rem;
                &:last-child {
                    margin-right: 0;
                }
            }
        }
        .job-title {
            font-weight: bold;
        }
        .carousel-link {
            margin: 1rem 0;
            display: block;
            text-decoration: underline;
            font-weight: bold;
        }
    }
    @media (min-width: 1024px) {
        position: relative;
        z-index: 5;
        &.slides-left {
            text-align: left;
            .custom-slick-nav {
                justify-content: flex-start;
            }
        }
        &.slides-right {
            text-align: right;
             .custom-slick-nav {
                justify-content: flex-end;
            }
        }
    }
`;

export const CustomSlickNav = styled.div`
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 1rem 0;
    .arrow {
        line-height: 0;
        cursor: pointer;
    }
    ul {
        list-style: none;
        display: flex;
        padding: 0 1.5rem;
        margin: 0;
        li {
            .dot {
                width: 10px;
                height: 10px;
                display: flex;
                border: 2px solid;
                margin: 0 0.25rem;
                border-radius: 50%;
                background: #fff;
                cursor: pointer;
            }
            &.slick-active .dot {
                background: ${props => props.activeColor};
            }
        }
    }   
`
