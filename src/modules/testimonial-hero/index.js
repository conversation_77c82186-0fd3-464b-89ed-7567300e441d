import React, { useState, useEffect, useRef } from "react";
import Slider from "react-slick";
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faChevronLeft, faChevronRight } from '@fortawesome/free-solid-svg-icons'
import { useInView } from 'react-intersection-observer';
import { decode } from 'html-entities';
// Helpers
import { Coloring } from "src/helpers";
const Imaging = React.lazy(() => import('src/helpers/imaging'));
const Clicker = React.lazy(() => import('src/helpers/clicker'));
// PARTIALS.
const Button = React.lazy(() => import('src/partials/button'));
//Styles 
import { TestimonialHero, TestimonialHeroCarousel, CustomSlickNav } from './styles';
import "slick-carousel/slick/slick.scss";
import "slick-carousel/slick/slick-theme.scss";

const Start = ({ data, settings }) => {
    const [slideIndex, setSlideIndex] = useState(0);
    const { ref, inView } = useInView({
        triggerOnce: true,
        fallbackInView: true
    });
    // color options
    const bgColor = Coloring(data.background_color, settings);

    const afterChangeHandler = (currentSlide) => {
        setSlideIndex(currentSlide);
    }

    return (
        <div ref={ref} className="outer-wrapper">
            {inView ? <>
                <TestimonialHero
                    className={`testimonial-hero ${data.background_type} ${data.background_value} ${data.layout}`}
                    backgroundColor={bgColor}
                    backgroundImage={data.background_type === 'image' ? data.background_image?.url : ''}
                    textColor={data.background_value === 'dark' ? '#fff' : Coloring('body_copy_color', settings)}
                >
                    <div className='grid-container'>
                        <div className="grid-x">
                            <div className="cell medium-6 large-5 container-one">
                                <div className="main-content">
                                    {data.title &&
                                        <h1>{decode(data.title)}</h1>
                                    }
                                    {data.blurb &&
                                        <div className='blurb' dangerouslySetInnerHTML={{ __html: data.blurb }} />
                                    }
                                    {data.button &&
                                        <div class='button'>
                                            <Button title={data?.button?.title} url={data?.button?.url} target={data?.button?.target} tone={data?.background_value} type={data?.button_style} />
                                        </div>
                                    }
                                </div>
                            </div>
                            <div className="cell medium-6 hide-for-large container-two">
                                <ImageCarousel data={data} slideIndex={slideIndex} />
                            </div>
                            <div className="cell large-3 show-for-large container-three">
                                <TestimonialCarousel data={data} settings={settings} afterChangeHandler={afterChangeHandler} />
                            </div>
                        </div>
                    </div>
                    <div className='desktop-images'>
                        <ImageCarousel data={data} slideIndex={slideIndex} />
                    </div>
                </TestimonialHero>
                <div className='grid-container hide-for-large'>
                    <div className="grid-x">
                        <div className="cell">
                            <TestimonialCarousel data={data} settings={settings} afterChangeHandler={afterChangeHandler} />
                        </div>
                    </div>
                </div>
            </> : null}
        </div>
    );
}

const ImageCarousel = ({ data, slideIndex }) => {
    const imageSlider = useRef();

    useEffect(() => {
        imageSlider.current.slickGoTo(slideIndex, true);
    }, [slideIndex]);


    let sliderSettings = {
        infinite: true,
        slidesToShow: 1,
        slidesToScroll: 1,
        swipe: false,
        arrows: false,
        dots: false,
        fade: true,
        cssEase: 'linear'
    };


    return (
        <Slider
            className='testimonial-image-carousel'
            ref={imageSlider}
            {...sliderSettings}>
            {data.carousel_items && data.carousel_items.map((item, index) => <ImageItem item={item} index={index} />)}
        </Slider>
    )
}

const ImageItem = ({ item }) => {

    switch (item.carousel_item_type) {
        case 'custom':
            return (<Imaging data={item.carousel_image} />)
        case 'testimonial':
            if (item.testimonial_data?.hero_image) {
                return (<Imaging data={item.testimonial_data?.hero_image} />)
            } else if (item.testimonial_data?.list_image) {
                return (<Imaging data={item.testimonial_data?.list_image} />)
            } else {
                return null;
            }
        default:
            return null;
    }
}


const TestimonialCarousel = ({ data, settings, afterChangeHandler }) => {
    const mainSlider = useRef();

    const prev = () => {
        mainSlider.current.slickPrev();
    }
    const next = () => {
        mainSlider.current.slickNext();
    }

    let sliderSettings = {
        infinite: true,
        slidesToShow: 1,
        slidesToScroll: 1,
        arrows: false,
        dots: true,
        adaptiveHeight: true,
        beforeChange: (current, next) => {
            setTimeout(afterChangeHandler(next), 0)
        },
        appendDots: dots => <>
            <CustomSlickNav
                className="custom-slick-nav"
                activeColor={settings.design.colors.primary_color}
            >
                <span className="arrow" onClick={prev}><FontAwesomeIcon icon={faChevronLeft} className='primary-txt' /></span>
                <ul>{dots}</ul>
                <span className="arrow" onClick={next}><FontAwesomeIcon icon={faChevronRight} className='primary-txt' /></span>
            </CustomSlickNav></>,
        customPaging: i => (
            <span className='dot primary-txt' />
        )
    };

    return (
        <TestimonialHeroCarousel
            className={data.layout}
        >
            <Slider
                ref={mainSlider}
                {...sliderSettings}
            >
                {data.carousel_items && data.carousel_items.map((item, index) => <Item data={data} item={item} index={index} />)}
            </Slider>
        </TestimonialHeroCarousel>
    )
}

const Item = ({ data, item }) => {
    switch (item.carousel_item_type) {
        case 'custom':
            return (
                <div className='carousel-item'>
                    <div className='content'>
                        {item.carousel_title &&
                            <h3 className='custom-title'>{decode(item.carousel_title)}</h3>
                        }
                        {item.carousel_blurb &&
                            <div className='carousel-blurb' dangerouslySetInnerHTML={{ __html: item.carousel_blurb }} />
                        }
                        {item.carousel_image_link &&
                            <Clicker type='anchor' className='carousel-link primary-txt' url={item.carousel_image_link.url} target={item.carousel_image_link.target}>{item.carousel_image_link.title}</Clicker>
                        }
                    </div>
                </div>
            )
        case 'testimonial':
            return (
                <div className={`carousel-item`}>
                    <div className="content">
                        {item.testimonial_data?.quote &&
                            <div className={`quote ${data.background_value === 'dark' ? 'white-txt' : 'primary-txt'}`} dangerouslySetInnerHTML={{ __html: item.testimonial_data?.quote }} />
                        }
                        <div className="name">
                            {item.testimonial_data?.full_name &&
                                <span>{item.testimonial_data?.full_name}</span>
                            }
                            {item.testimonial_data?.first_name &&
                                <span>{item.testimonial_data?.first_name}</span>
                            }
                            {item.testimonial_data?.last_name &&
                                <span>{item.testimonial_data?.last_name}</span>
                            }
                            {item.testimonial_data?.last_initial &&
                                <span>{item.testimonial_data?.last_initial}</span>
                            }
                        </div>
                        {item.testimonial_data?.job_title &&
                            <p className='job-title'>{item.testimonial_data?.job_title}</p>
                        }
                        {item.testimonial_data?.professional_relationship &&
                            <p className='professional-relationship'>{item.testimonial_data?.professional_relationship}</p>
                        }
                        <Clicker type='anchor' className='carousel-link primary-txt' url={item.testimonial_data?.testimonial_link}>{item.testimonial_data?.testimonial_link_text}</Clicker>
                    </div>
                </div>
            )
        default:
            return null;
    }
}

export default Start;