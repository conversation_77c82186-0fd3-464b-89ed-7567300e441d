/*******************************************************************************************************
 Copyright 2020 - Calise Partners LLC d/b/a Imaginuity. All rights are reserved. 
Reproduction or transmission in whole or in part, in any form or by any means, electronic, mechanical 
or otherwise, is prohibited without the prior written consent of Calise Partners LLC d/b/a Imaginuity.
********************************************************************************************************
Project : 10618 Maverick Platform Development
Author : Imaginuity Developers (<PERSON>, <PERSON>)
Description : Renders a video component for the hero module
Creation Date : Thu Nov 05 2020
Documentation : [documentation url - point to default wiki for project if no specific documentation for script]
********************************************************************************************************/

import React, { useEffect, useState, useContext } from 'react'
import { addScript } from 'src/hooks';
import { decode } from 'html-entities';
import { ImageOverlay } from './styles';
import { useInView } from 'react-intersection-observer';
import { AppContext } from 'src/contexts/app';
import HtmlParser from 'src/helpers/html-parser';

// partials
const Button = React.lazy(() => import('src/partials/button'));
// styles
import './video.scss';

const Start = ({ data, page, placeholders }) => {
    const appContext = useContext(AppContext);
    addScript('https://player.vimeo.com/api/player.js');
    const { ref, inView } = useInView({
        triggerOnce: true,
        fallbackInView: true
    });
    const title = (placeholders && data.title_override_selection === 'dynamic') ? placeholders.single_line[data.title_override] : data.title_override
    const vimeoLink = placeholders ? placeholders?.single_line[data?.vimeo_embed_link] : data?.vimeo_embed_link
    const mobileVimeoLink = placeholders ? placeholders?.single_line[data?.mobile_vimeo_embed_link] : data?.mobile_vimeo_embed_link
    var video;
    if (mobileVimeoLink && appContext?.width < 768) {
        video = mobileVimeoLink;
    } else {
        video = vimeoLink;
    }

    return (
        <div ref={ref} className={`hero__video style-${data.style} ${data.alignment}`}>
            {inView ? <>
                <div className={`video-container bg-video ${mobileVimeoLink ? 'mobile-vid' : ''}`}>
                    <iframe 
                        class='bg-video__iframe' 
                        title={`background video for ${decode(page?.mvk_item_content?.title)}`} 
                        src={`${video}${video?.includes('?') ? '&' : '?'}background=1&controls=0&autopause=0`} 
                        frameborder="0" 
                        allow="autoplay; fullscreen; picture-in-picture" 
                        allowfullscreen
                    >
                    </iframe>
                    {data?.video_overlay &&
                        <ImageOverlay className={data?.video_overlay_style} overlayColor={data?.video_overlay} overlayOpacity={data?.video_overlay_opacity} />
                    }
                </div>
                <div className={`content-container ${data.background_value}`}>
                    <div className="inner-wrapper grid-container">
                        {!data.hide_title &&
                            <h1 dangerouslySetInnerHTML={{ __html: title ? decode(title) : decode(page?.mvk_item_content?.title) }}></h1>
                        }
                        {data.blurb?.length > 0 && <div class='text-only-blurb'><HtmlParser html={data.blurb} placeholders={placeholders} /></div>}
                        {data.buttons &&
                            <div class='button-wrapper'>
                                {data.buttons.map((button, buttonIndex) => {
                                    const buttonLink = placeholders ? placeholders.button_link[button?.button] : button?.button;
                                    if (buttonLink) {
                                        return (
                                            <div key={buttonIndex} class={`button-${data.alignment}`}>
                                                <Button class='hero-cta' title={buttonLink.title} url={buttonLink.url} target={buttonLink.target} type={button.button_style} tone={data.background_value} />
                                            </div>
                                        );
                                    } else return null;
                                })}
                            </div>
                        }
                    </div>
                </div>
            </> : null}
        </div>
    )
}

export default Start