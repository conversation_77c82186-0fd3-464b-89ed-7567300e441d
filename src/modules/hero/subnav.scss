.page-subnav {
    padding: 1rem 0;
    position: relative;
    .subnav-toggle {
        text-align: center;
        cursor: pointer;
        svg {
            margin-right: 1rem;
        }
    }
    .subnav-wrapper {
        @media (max-width: 1023px) {
            position: absolute;
            left: 50%;
            transform: translateX(-50%) scaleY(0);
            top: calc(100% - 8px);
            width: 69%;
            padding: 0;
            z-index: 11;
            &.active {
                transform: translateX(-50%) scaleY(1);
            }
        }
        .sub-page-nav {
            list-style: none;
            text-align: center;
            padding: 0;
            margin: 0.5rem 0 0;
            li {
                text-transform: uppercase;
                border-top: 1px solid rgba(255, 255, 255, 0.2);
                a {
                    color: inherit;
                    padding: 0.75rem;
                    display: block;
                }
            }
        }
    }
    @media (min-width: 1024px) {
        position: absolute;
        width: 100%;
        bottom: 0;
        &:not(.desktop-hamburger) {
            opacity: 0.8;
            .subnav-wrapper {
                .sub-page-nav {
                    display: flex;
                    text-align: unset;
                    margin-top: 0;
                    li {
                        border: none;
                        a {
                            padding: 0;
                            margin-right: 1.75rem;
                            opacity: 0.8;
                            &:hover {
                                opacity: 1;
                            }
                        }
                    }
                }
            }
        }
        .subnav-wrapper {
            &.center .sub-page-nav {
                justify-content: center;
            }
            &.right .sub-page-nav {
                justify-content: flex-end;
                li a {
                    margin-right: 0;
                    margin-left: 1.5rem;
                }
            }
        }
        &.desktop-hamburger {
            .subnav-wrapper {
                position: absolute;
                left: 50%;
                transform: translateX(-50%) scaleY(0);
                top: calc(100% - 8px);
                width: 69%;
                max-width: 400px;
                padding: 0;
                z-index: 11;
                &.active {
                    transform: translateX(-50%) scaleY(1);
                    a {
                        opacity: 0.8;
                        &:hover {
                            opacity: 1;
                        }
                    }
                }
            }
        }
    }
}
