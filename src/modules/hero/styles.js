import styled from 'styled-components';

export const ImageOverlay = styled.div`
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    background: ${props => props.overlayColor};
    opacity: ${props => props.overlayOpacity};
    &.top-bottom {
        background: linear-gradient(${props => props.overlayColor} 45%, transparent);
    }
    &.bottom-top {
        background: linear-gradient(transparent, ${props => props.overlayColor} 45%);
    }
    &.left-right {
        background: linear-gradient(to right, ${props => props.overlayColor} 45%, transparent);
    }
     &.right-left {
        background: linear-gradient(to left, ${props => props.overlayColor} 45%, transparent);
    }
`


export const ImageCarouselContent = styled.div`
    background-color: ${props => props.mobileBackground};
`;
