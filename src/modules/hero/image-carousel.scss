/*******************************************************************************************************
   Copyright 2020 - Calise Partners LLC d/b/a Imaginuity. All rights are reserved. 
   Reproduction or transmission in whole or in part, in any form or by any means, electronic, mechanical 
or otherwise, is prohibited without the prior written consent of Calise Partners LLC d/b/a Imaginuity.
********************************************************************************************************
   Project : 10618 Maverick Platform Development
   Author : Imaginuity Developers (<PERSON>, <PERSON>)
   Description : Style properties for the image carousel hero module
   Creation Date : Thu Nov 05 2020
   Documentation : [documentation url - point to default wiki for project if no specific documentation for script]
 ********************************************************************************************************/

 .hero {
    position: relative;

    //* slick slider specific styles >>>>>
    .slick-slider {
        .lifestyle-button {
            box-shadow: none;
        }
    }

    .slick-arrow {
        position: absolute;
        top: auto !important;
        bottom: 5px;
        transform: none !important;
        &.slick-prev {
            left: 20%;
        }

        &.slick-next {
            right: 20%;
        }

        &:before {
            border-color: #fff;
            opacity: 1;
        }

        @media screen and (min-width: 1200px) {
            &.slick-prev {
                left: 30%;
            }

            &.slick-next {
                right: 30%;
            }
        }
    }

    .slick-dots {
        z-index: 1;

        li {
            margin: 0 0.75rem;

            button {
                padding: 0;

                &::before {
                    content: "";
                    opacity: 1;
                    left: 0px;
                    line-height: 20px;
                    position: relative;
                    display: inline-block;
                    vertical-align: top;
                    border: 2px solid #fff;
                    border-radius: 50%;
                    background: #fff;
                    filter: drop-shadow(1px 1px 2px black);
                }
            }

            &.slick-active {
                button::before {
                    opacity: 1;
                    border: 2px solid transparent;
                }
            }

            &:before {
                content: "";
                display: block;
            }
        }
    }
    //* <<<<< slick slider specific styles

    @media screen and (max-width: 800px) {
        height: 100%;
    }

    .hero-slider {
        height: 100%;
    }

    .slide-wrapper {
        position: relative;
    }

    .slide-background {
        position: absolute;
        width: 100%;
        height: 100%;

        &.bg-desktop {
            visibility: hidden;
        }

        @media screen and (min-width: 768px) {
            &.bg-mobile {
                visibility: hidden;
            }

            &.bg-desktop {
                visibility: visible;
            }
        }
    }

    .slide {
        position: relative;
        object-fit: cover;
        object-position: 50% 100%;
        overflow: hidden;

        display: flex;
        align-items: center;

        &.slide-left {
            justify-content: flex-start;
        }

        &.slide-center {
            justify-content: center;
        }

        &.slide-right {
            justify-content: flex-end;
        }

        @media screen and (min-width: 992px) {
            &.slide-left,
            &.slide-right {
                padding-top: 2rem;
                padding-bottom: 2rem;
            }

            &.slide-center {
                padding-top: 1rem;
                padding-bottom: 1rem;
            }
        }

        .overlay {
            position: absolute;
            z-index: 50;
            left: 0;
            top: 0;
            bottom: 0;
            right: 0;
            background: radial-gradient(circle, rgba(0, 0, 0, 0.1) 0%, rgba(0, 0, 0, 0.2) 100%);
        }

        img.hero {
            position: relative;
            object-fit: cover;
            object-position: 50% 100%;
            overflow: hidden;
            max-height: 370px;
            height: 370px;
            max-width: 100%;
            min-width: 100%;
        }

        @media screen and (max-width: 700px) {
            img.hero {
                max-height: 500px;
            }
        }
    }

    .content-wrapper {
        display: flex;
        align-items: center;

        &.slide-left {
            justify-content: flex-start;
        }

        &.slide-center {
            justify-content: center;
        }

        &.slide-right {
            justify-content: flex-end;
        }

        &.left-centered-text,
        &.right-centered-text {
            text-align: center;
            width: 100%;
            @media (min-width: 768px) {
                width: 53%;
            }
            .button-wrapper > * {
                justify-content: center;
            }
        }

        @media screen and (min-width: 992px) {
            &.left-contained,
            &.right-contained {
                // width: 52%;
                width: 38.25%;
            }

            &.center-contained {
                // width: 63%;
                width: 60%;
            }
        }
    }

    .inner-wrapper {
        width: fit-content;

        z-index: 100;
        text-align: left;

        &.content-left {
            text-align: left;

            @media screen and (max-width: 767px) {
                padding: 0 2rem;
                margin-top: 0;
            }
        }

        &.content-center {
            text-align: center;

            @media screen and (max-width: 767px) {
                padding: 0 2rem;
                margin-top: 0;
            }
        }

        &.content-right {
            text-align: right;

            @media screen and (max-width: 767px) {
                padding: 0 2rem;
                margin-top: 0;
            }
        }

        &.left-centered-text,
        &.right-centered-text {
            text-align: center;
            .button-wrapper > * {
                justify-content: center;
            }
        }
    }

    .heading {
        margin: unset !important;
        line-height: unset !important;
        font-weight: 100;
        font-size: 2.25rem !important;
        color: #fff;
        text-transform: none;

        .hidden {
            visibility: hidden;
        }

        @media screen and (min-width: 768px) {
            font-size: 3.5rem !important;
        }

        @media screen and (min-width: 1200px) {
            font-size: 3.75rem !important;
        }
    }

    .blurb {
        color: #fff;
        font-size: 1.313rem;
        margin-top: 1rem;

        @media screen and (max-width: 1000px) {
            font-size: 1.125rem;
        }

        @media screen and (max-width: 600px) {
            font-size: 1rem;
        }
    }

    .button-wrapper {
        margin-top: 2rem;
        .button-left {
            display: flex;
            justify-content: flex-start;
            margin-bottom: 0.5rem;
        }

        .button-center {
            display: flex;
            justify-content: center;
            margin-bottom: 0.5rem;
        }

        .button-right {
            display: flex;
            justify-content: flex-end;
            margin-bottom: 0.5rem;
        }
    }
    &.extra-short {
        $extra-short: 250px;
        min-height: $extra-short;
        .text-below-image .bg-mobile {
            max-height: $extra-short;
        }
        .slide {
            min-height: $extra-short;
            box-sizing: border-box;
            display: flex;
            align-items: center;
            padding-top: 1rem;
            padding-bottom: 1rem;
        }

        .slick-arrow {
            bottom: 5px;

            &:before {
                border-width: 0 4px 4px 0;
                padding: 5px;
            }
        }

        .slick-dots {
            bottom: 17px;

            button {
                &:before {
                    height: 13px;
                    width: 13px;
                }
            }
        }

        @media screen and (min-width: 1200px) {
            $extra-short: 310px;
            min-height: $extra-short;

            .slide {
                min-height: $extra-short;
            }
        }
    }
    &.short {
        $short: 375px;
        min-height: $short;
        .text-below-image .bg-mobile {
            max-height: $short;
        }
        .slide {
            min-height: $short;
            box-sizing: border-box;
            display: flex;
            align-items: center;
            padding-top: 1rem;
            padding-bottom: 1rem;
        }

        .slick-arrow {
            bottom: 5px;

            &:before {
                border-width: 0 4px 4px 0;
                padding: 5px;
            }
        }

        .slick-dots {
            bottom: 17px;

            button {
                &:before {
                    height: 13px;
                    width: 13px;
                }
            }
        }

        @media screen and (min-width: 1200px) {
            $short: 400px;
            min-height: $short;

            .slide {
                min-height: $short;
            }
        }
    }
    &.medium {
        $medium: 400px;
        min-height: $medium;
        .text-below-image .bg-mobile {
            max-height: $medium;
        }
        .slide {
            min-height: $medium;
            box-sizing: border-box;
        }

        .slick-arrow {
            bottom: 10px;

            &:before {
                border-width: 0 5px 5px 0;
                padding: 5px;
            }
        }

        .slick-dots {
            bottom: 22px;

            button {
                &:before {
                    height: 15px;
                    width: 15px;
                }
            }
        }

        @media screen and (min-width: 1200px) {
            $medium: 500px;
            min-height: $medium;

            .slide {
                min-height: $medium;
            }
        }
    }
    &.tall {
        $tall: 425px;
        min-height: $tall;
        .text-below-image .bg-mobile {
            max-height: $tall;
        }
        .slide {
            min-height: $tall;
            box-sizing: border-box;
            @media screen and (min-width: 768px) {
                min-height: $tall;
            }
        }

        .slick-arrow {
            bottom: 10px;

            &:before {
                border-width: 0 5px 5px 0;
                padding: 7px;
            }
        }

        .slick-dots {
            bottom: 22px;

            button {
                &:before {
                    height: 17px;
                    width: 17px;
                }
            }
        }

        @media screen and (min-width: 768px) {
            min-height: $tall;
        }

        @media screen and (min-width: 1200px) {
            $tall: 645px;
            min-height: $tall;

            .slide {
                min-height: $tall;
                box-sizing: border-box;

            }
        }
    }

    &.with-sub-nav {
        @media (min-width: 1024px) {
            .slick-arrow {
                bottom: 50px;
            }
            .slick-dots {
                bottom: 62px;
            }
        }
    }

    &.dashes {
        .slick-dots li button,
        .slick-dots li {
            width: 45px;
            &:before {
                width: 45px;
                height: 1px;
                border-radius: 0;
            }
        }
    }

    &.arrows {
        .slick-arrow {
            display: none !important;
            bottom: 50%;
        }

        .slick-prev {
            left: 5%;
        }

        .slick-next {
            right: 5%;
        }

        &:hover {
            @media screen and (min-width: 768px) {
                .slick-arrow {
                    display: block !important;
                }
            }
        }

        &.header-is-absolute {
            .slide {
                @media screen and (min-width: 768px) {
                    &.slide-left,
                    &.slide-right {
                        padding-top: 5rem;
                        padding-bottom: 3rem;
                    }

                    &.slide-center {
                        padding-top: 5rem;
                        padding-bottom: 3rem;
                    }
                }

                @media screen and (min-width: 992px) {
                    &.slide-left,
                    &.slide-right {
                        padding-top: 8rem;
                        padding-bottom: 3rem;
                    }

                    &.slide-center {
                        padding-top: 8rem;
                        padding-bottom: 3rem;
                    }
                }
            }
        }
    }

    .mobile-only {
        position: relative;
        padding: 2rem 0;
        &.with-carousel {
            padding-bottom: 4rem;
        }
    }
    @media (max-width: 767px) {
        .desktop-only {
            display: none;
        }
    }
    @media (min-width: 768px) {
        .mobile-only {
            display: none;
        }
    }

    &.text-carousel {
        .slide-background {
            background-color: transparent !important;
        }
    }
}
