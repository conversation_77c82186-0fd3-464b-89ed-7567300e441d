/*******************************************************************************************************
   Copyright 2020 - Calise Partners LLC d/b/a Imaginuity. All rights are reserved. 
   Reproduction or transmission in whole or in part, in any form or by any means, electronic, mechanical 
or otherwise, is prohibited without the prior written consent of Calise Partners LLC d/b/a Imaginuity.
********************************************************************************************************
   Project : 10618 Maverick Platform Development
   Author : Imaginuity Developers (<PERSON>, <PERSON>)
   Description : Renders a static text only hero module
   Creation Date : Thu Nov 05 2020
   Documentation : [documentation url - point to default wiki for project if no specific documentation for script]
 ********************************************************************************************************/

import React, { useState, useEffect } from 'react';
import './text-only.scss';
import { decode } from 'html-entities';
import HtmlParser from 'src/helpers/html-parser';


const Start = ({ data, page, placeholders }) => {
    const title = (placeholders && data.title_override_selection === 'dynamic') ? placeholders.single_line[data.title_override] : data.title_override

    return (
        <div class={`hero__text-only style-${data.style} ${data.alignment} grid-container`}>
            <h1>{title ? decode(title) : decode(page?.mvk_item_content?.title)}</h1>
            {data.blurb?.length > 0 && <div class='text-only-blurb'><HtmlParser html={data.blurb} placeholders={placeholders} /></div>}
        </div>
    );
};

export default Start;