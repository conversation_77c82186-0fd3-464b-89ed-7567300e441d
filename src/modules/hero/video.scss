/*******************************************************************************************************
   Copyright 2020 - Calise Partners LLC d/b/a Imaginuity. All rights are reserved. 
   Reproduction or transmission in whole or in part, in any form or by any means, electronic, mechanical 
or otherwise, is prohibited without the prior written consent of Calise Partners LLC d/b/a Imaginuity.
********************************************************************************************************
   Project : 10618 Maverick Platform Development
   Author : Imaginuity Developers (<PERSON>, <PERSON>)
   Description : Style properties for the image carousel hero module
   Creation Date : Thu Nov 05 2020
   Documentation : [documentation url - point to default wiki for project if no specific documentation for script]
 ********************************************************************************************************/
:root {
    --video-width: 100vw;
    --video-height: 100vh;

    @media (min-aspect-ratio: 16/9) {
        --video-height: 56.25vw;
    }

    @media (max-aspect-ratio: 16/9) {
        --video-width: 177.78vh;
    }
}
.hero__video {
    position: relative;
    overflow: hidden;
    &.style-extra-short .bg-video {
        height: 250px;
        @media screen and (min-width: 1200px) {
            height: 310px;
        }
    }
    &.style-short .bg-video {
        height: 375px;
        @media screen and (min-width: 1200px) {
            height: 400px;
        }
    }
    &.style-medium .bg-video {
        height: 400px;
        @media screen and (min-width: 1200px) {
            height: 500px;
        }
    }
    &.style-tall .bg-video {
        height: 425px;
        @media screen and (min-width: 768px) {
            height: 670px;
        }
        @media screen and (min-width: 1200px) {
            height: 645px;
        }
    }

    &.style-extra-tall .bg-video {
        height: 600px;
        @media screen and (min-width: 1200px) {
            height: 720px;
        }
        @media screen and (min-width: 1550px) {
            height: 875px;
        }
    }

    &.style-fill-screen .bg-video {
        height: 100vh;
    }

    .bg-video {
        position: relative;
        overflow: hidden;
        width: 100vw;

        &.mobile-vid {
            @media (max-width: 767px) {
                max-width: 100%;

                .bg-video__iframe {
                    max-width: 100%;
                }
            }
        }


        .bg-video__iframe {
            position: absolute;
            top: 50%;
            left: 50%;
            width: var(--video-width);
            height: var(--video-height);
            transform: translate(-50%, -50%);

            // @media (max-width: 767px) {
            //     max-width: 100%;
            // }
        }
    }
    .content-container {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        width: 100%;
        &.dark {
            color: #fff;
        }
        .inner-wrapper {
            // padding: 0 1rem;
        }
        .button-wrapper {
            margin-top: 1rem;
        }
    }
}
