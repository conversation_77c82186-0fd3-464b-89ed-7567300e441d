/*******************************************************************************************************
   Copyright 2020 - Calise Partners LLC d/b/a Imaginuity. All rights are reserved. 
   Reproduction or transmission in whole or in part, in any form or by any means, electronic, mechanical 
or otherwise, is prohibited without the prior written consent of Calise Partners LLC d/b/a Imaginuity.
********************************************************************************************************
   Project : 10618 Maverick Platform Development
   Author : Imaginuity Developers (<PERSON>, <PERSON>)
   Description : Determines which type of hero module to render
   Creation Date : Thu Nov 05 2020
   Documentation : [documentation url - point to default wiki for project if no specific documentation for script]
 ********************************************************************************************************/

import React, { Suspense, useEffect } from "react";

const TextOnly = React.lazy(() => import("./text-only"));
const ImageCarousel = React.lazy(() => import("./image-carousel"));
const Video = React.lazy(() => import("./video"));
const HalfHalf = React.lazy(() => import("./half-half"));

const Start = ({ data, settings, page, placeholders }) => {
    let type = data.type;

    return (
        <Suspense fallback={<div />}>
            {type === "text-only" && <TextOnly data={data} page={page} placeholders={placeholders} />}
            {type === "image-carousel" && <ImageCarousel data={data} settings={settings} page={page} placeholders={placeholders} />}
            {type === "video" && <Video data={data} page={page} placeholders={placeholders} />}
            {type === "half-half" && <HalfHalf data={data} page={page} placeholders={placeholders} />}
        </Suspense>
    );
};

export default Start;
