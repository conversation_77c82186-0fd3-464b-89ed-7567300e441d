import React, { useState, useEffect } from "react";
import styled from 'styled-components';
import Slider from "react-slick";
import { useInView } from 'react-intersection-observer';
import { decode } from 'html-entities';
// HELPERS
import { BackgroundClass } from "src/helpers/theme";
import Imaging from 'src/helpers/imaging';
import HtmlParser from 'src/helpers/html-parser';
import { PrevArrow, NextArrow } from 'src/helpers/slick';
// PARTIALS
import Button from 'src/partials/button';
// Styles
import "slick-carousel/slick/slick.scss";
import "slick-carousel/slick/slick-theme.scss";

const Start = ({ data, page, placeholders }) => {

    const bgClass = BackgroundClass(data.background_color);
    const bgImage = data.background_type === 'image' ? data.background_image : false;
    const { ref, inView } = useInView({
        triggerOnce: true,
        fallbackInView: true
    });
    const title = (placeholders && data.title_override_selection === 'dynamic') ? placeholders.single_line[data.title_override] : data.title_override
    const featuredImage = placeholders ? placeholders.image[data.featured_image] : data.featured_image
    
    return (
        <HalfHalf ref={ref} className={`hero__half-half ${inView ? 'add-bg-image' : ''} ${data.layout} ${data.background_type} ${data.background_value} ${bgClass} ${data.add_bottom_space ? 'add-space' : ''}`} bgImage={bgImage ? bgImage.url : ''} >
            {inView ?
                <div className={`grid-container ${data.restrict_module_width ? 'restricted' : ''}`}>
                    <div className="grid-x grid-margin-x">
                        <div className="cell large-5 content-container">
                            <div className={`inner-wrapper ${data.alignment}`}>
                                {!data.hide_title &&
                                    <h1>{title ? decode(title) : decode(page?.mvk_item_content?.title)}</h1>
                                }
                                {data.blurb &&
                                    <div className='blurb'>
                                        <HtmlParser html={data.blurb} placeholders={placeholders} />
                                    </div>
                                }
                                {data.buttons &&
                                    <div class='button-wrapper'>
                                        {data.buttons.map((button, buttonIndex) => {
                                            const buttonLink = (placeholders) ? placeholders.button_link[button?.button] : button?.button;
                                            if (buttonLink) {
                                                return (
                                                    <div key={buttonIndex} class={`button-${data.alignment}`}>
                                                        <Button class='hero-cta' title={buttonLink.title} url={buttonLink.url} target={buttonLink.target} type={button.button_style} tone={data.background_value} />
                                                    </div>
                                                );
                                            } else return null;
                                        })}
                                    </div>
                                }
                            </div>
                        </div>
                        <div className="cell large-7 image-container">
                            {featuredImage?.length > 1 ?
                                <MultiImage featuredImage={featuredImage} />
                                :
                                <div className="image">
                                    <Imaging data={featuredImage ? featuredImage[0] : false} />
                                </div>
                            }
                        </div>
                    </div>
                </div>
                : null}
        </HalfHalf>
    );
}

const MultiImage = ({ featuredImage }) => {

    const sliderSettings = {
        dots: false,
        adaptiveHeight: false,
        arrows: true,
        autoplay: true,
        autoplaySpeed: 8000,
        pauseOnHover: false,
        infinite: true,
        slidesToShow: 1,
        slidesToScroll: 1,
        speed: 800,
        nextArrow: <NextArrow />,
        prevArrow: <PrevArrow />
    };

    return (
        <Slider className={'hero-slider'} ref={(a) => a} {...sliderSettings}>
            {featuredImage && featuredImage?.map(img => <div className="image"><Imaging data={img} /></div>)}
        </Slider>
    );

}

const HalfHalf = styled.div`
    padding-bottom: 2rem;
    @media (max-width: 1024px) {
        padding-left: 1.5rem;
        padding-right: 1.5rem;
        .inner-wrapper {
            padding-top: 30px;
            padding-bottom: 20px;
        }
    }

    &.dark {
        color: #fff;
    }
    &.image {
        background-size: cover;
    }
    &.add-bg-image {
        background-image: url('${props => props.bgImage}');
    }
    &.add-space {
        padding-bottom: 11rem;
        @media (min-width: 1024px) {
            padding-bottom: 15rem;
        }
    }
    .image img {
        width: 100%;
    }
    .content-container {
        h1 {
            margin-bottom: 1rem;
            line-height: 1.1;
        }
    }
    @media (min-width: 1024px) {
        padding: 2rem 0;
        &.image-left-text-right {
            .content-container {
                order: 2;
            }
        }
        .content-container {
            display: flex;
            align-items: center;
        }
    }
`;

export default Start;