import React, { useState, useEffect, useContext } from "react";
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faBars } from '@fortawesome/free-solid-svg-icons'

const Clicker = React.lazy(() => import('src/helpers/clicker'));
import { BackgroundColor } from "src/helpers/theme";
import { NavigationProvider, NavigationContext } from 'src/context';

import { decode } from 'html-entities';


import './subnav.scss';

const Start = ({ data, settings }) => {
    const [showNav, setNav] = useState(false);
    const [navigation, setNavigation] = useContext(NavigationContext);
    const mainNav = navigation?.main?.flatMap(el => [el, ...el.children]);

    let bgStyles = {
        backgroundColor: data.subnav_background_color ? BackgroundColor(data.subnav_background_color) : 'transparent',
        color: data.subnav_background_value === 'dark' ? '#fff' : settings?.design?.colors?.body_copy_color
    }

    let reorderedArray = [];
    data?.sub_page_nav?.forEach((subItem) => {
        mainNav?.forEach((mainItem) => {
            if (subItem.url.includes(mainItem.url)) {
                subItem.order = mainItem.order;
            }
        });
        reorderedArray.push(subItem);
    });

    const toggleSubnav = () => {
        if (showNav) {
            setNav(false);
        } else {
            setNav(true);
        }
    }

    return (
        <div className={`page-subnav ${data?.hamburger_in_desktop ? 'desktop-hamburger' : ''}`} style={bgStyles}>
            <div className="grid-container">
                <div class={`subnav-toggle${data?.hamburger_in_desktop ? '' : ' hide-for-large'}`} onClick={toggleSubnav}><FontAwesomeIcon icon={faBars} size="lg" />Menu</div>
            </div>
            <div class={`subnav-wrapper grid-container ${data.subnav_alignment} ${showNav ? 'active' : ''}`} style={bgStyles}>
                <ul className='sub-page-nav' style={bgStyles}>
                    {reorderedArray && Object.keys(reorderedArray).sort((a, b) => reorderedArray[a].order - reorderedArray[b].order).map(key => <li><Clicker url={reorderedArray[key].url} type="anchor">{decode(reorderedArray[key].title)}</Clicker></li>)}
                </ul>
            </div>
        </div>
    );

}

export default Start;
