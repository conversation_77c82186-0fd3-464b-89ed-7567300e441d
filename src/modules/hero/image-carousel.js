/*******************************************************************************************************
   Copyright 2020 - Calise Partners LLC d/b/a Imaginuity. All rights are reserved.
   Reproduction or transmission in whole or in part, in any form or by any means, electronic, mechanical
or otherwise, is prohibited without the prior written consent of Calise Partners LLC d/b/a Imaginuity.
********************************************************************************************************
   Project : 10618 Maverick Platform Development
   Author : Imaginuity Developers (<PERSON>, <PERSON>)
   Description : Displays an image carousel for the hero module
   Creation Date : Thu Nov 05 2020
   Documentation : [documentation url - point to default wiki for project if no specific documentation for script]
 ********************************************************************************************************/

import React, { useState, useEffect, useContext } from "react";
import Slider from "react-slick";
import { useInView } from 'react-intersection-observer';
import { decode } from 'html-entities';
// Context
import { PrincipalContext, NavigationProvider } from 'src/context';
// Helpers
import { Coloring } from "src/helpers";
import { withinDateRange } from "src/helpers/date";
import HtmlParser from 'src/helpers/html-parser';
import { PrevArrow, NextArrow } from 'src/helpers/slick';
// Partials
import Button from 'src/partials/button';
// Hooks
import { useQuery } from 'src/hooks/query';
import { AppContext } from 'src/contexts/app';
// Styles
import { ImageOverlay, ImageCarouselContent } from './styles';
import "slick-carousel/slick/slick.scss";
import "slick-carousel/slick/slick-theme.scss";
import './image-carousel.scss';

const SubNav = React.lazy(() => import("./subnav"));

const ImageCarousel = ({ data, settings, page, placeholders }) => {
    const [principal, setPrincipal] = useContext(PrincipalContext);
    const [carouselItems, setCarouselItems] = useState(null);
    const query = useQuery();
    const simulateDate = query.get('simulate-date');
    const { ref, inView } = useInView({
        triggerOnce: true,
        fallbackInView: true
    });

    useEffect(() => {
        data.carousel_items && setCarouselItems(() => data.carousel_items?.filter((slide) => {
            var startDate = slide?.start_date?.length > 0 ? new Date(slide?.start_date) : null;
            var endDate = slide?.end_date?.length > 0 ? new Date(slide?.end_date) : null;
            var isActive = withinDateRange(startDate, endDate, slide?.enable_holiday_settings, slide?.schedule_show_hide, simulateDate);

            if ((!slide?.schedule_show_hide && !slide?.enable_holiday_settings) || slide?.module_status === 'show' && isActive || slide?.module_status === 'hide' && !isActive) {
                return slide;
            }
        }))
    }, [data.carousel_items]);

    var isHeaderAbsolute = settings?.mvk_theme_config?.header?.header_type === 'absolute' ? 'header-is-absolute' : '';

    const sliderSettings = {
        dots: data?.carousel_style === 'arrows' ? false : true,
        adaptiveHeight: data?.mobile_layout === 'text-below-image' ? true : false,
        arrows: data?.carousel_style === 'dashes' ? false : true,
        autoplay: data?.carousel_items?.length > 1 ? true : false,
        autoplaySpeed: 8000,
        pauseOnHover: false,
        infinite: true,
        slidesToShow: 1,
        slidesToScroll: 1,
        speed: 800,
        nextArrow: <NextArrow />,
        prevArrow: <PrevArrow />
    };

    var moduleBgColor = pylot?.design?.coloring(data?.module_background_color, 'backgroundColor');
    const moduleBgImage = (placeholders && data.module_background_image_selection === 'dynamic') ? placeholders.image[data.module_background_image_dynamic] : data.module_background_image

    if (data?.module_background === 'image') {
        var moduleBgStyles = {
            backgroundImage: moduleBgImage ? `url(${moduleBgImage?.url})` : '',
            backgroundPosition: 'center center',
            backgroundSize: "cover",
            backgroundRepeat: "no-repeat",
            height: '100%',
            width: '100%',
        };
    } else {
        var moduleBgStyles = {
            ...moduleBgColor
        }
    }
    if (data?.alignment === 'center' || data?.alignment === 'center-contained') {
        var slideAlignClass = 'slide-center';
        var contentAlignClass = 'content-center';
        var buttonAlignClass = 'button-center';
    } else if (data?.alignment === 'right' || data?.alignment === 'right-centered-text' || data?.alignment === 'right-contained') {
        var slideAlignClass = 'slide-right';
        var contentAlignClass = 'content-right';
        var buttonAlignClass = 'button-right';
    } else {
        var slideAlignClass = 'slide-left';
        var contentAlignClass = 'content-left';
        var buttonAlignClass = 'button-left';
    }

    return (
        <div ref={ref} class={`hero ${data.style} ${data.carousel_style} ${isHeaderAbsolute} ${(data.add_sub_page_nav && data.sub_page_nav) ? 'with-sub-nav' : ''} ${data?.style === 'text-carousel' && data?.module_height}`} style={inView ? moduleBgStyles : {}}>
            {inView ?
                <>
                    {carouselItems && carouselItems?.length > 1 ?
                        <Slider className={'hero-slider'} ref={(a) => a} {...sliderSettings}>
                            {carouselItems && carouselItems.map((slide, index) => <Slide index={index} data={data} settings={settings} page={page} placeholders={placeholders} slide={slide} slideData={data} slideAlignClass={slideAlignClass} contentAlignClass={contentAlignClass} buttonAlignClass={buttonAlignClass} />)}
                        </Slider>
                        :
                        <div className={'hero-slider single-slide'}>
                            {carouselItems && carouselItems.map((slide, index) => <Slide index={index} data={data} settings={settings} page={page} placeholders={placeholders} slide={slide} slideData={data} slideAlignClass={slideAlignClass} contentAlignClass={contentAlignClass} buttonAlignClass={buttonAlignClass} />)}
                        </div>
                    }
                    {(data?.add_sub_page_nav && data?.sub_page_nav) &&
                        <NavigationProvider
                            subsite={principal?.subsite}
                            translation={principal?.activeTranslation || ''}
                        >
                            <SubNav data={data} settings={settings} />
                        </NavigationProvider>
                    }
                </> : null}
        </div>
    );
};

const Slide = ({ index, data, settings, page, placeholders, slide, slideData, slideAlignClass, contentAlignClass, buttonAlignClass }) => {
    const appContext = useContext(AppContext);
    const { ref, inView } = useInView({
        triggerOnce: true,
        fallbackInView: true
    });

    var bgColor = pylot?.design?.coloring(slide?.background_color, 'backgroundColor');
    const mobileImage = (placeholders && slide.mobile_background_image_selection === 'dynamic') ? placeholders.image[slide?.mobile_background_image_dynamic] : slide?.mobile_background_image
    const desktopImage = (placeholders && slide.background_image_selection === 'dynamic') ? placeholders.image[slide?.background_image_dynamic] : slide?.background_image

    if (slide?.background_type === 'image') {

        if (mobileImage) {
            var bgMobile = {
                backgroundImage: `url(${mobileImage?.url})`,
                backgroundPosition: 'center center',
                backgroundSize: "cover",
                backgroundRepeat: "no-repeat",
                position: 'absolute',
                height: '100%',
                width: '100%',
            };

            var bgMobileClass = 'bg-mobile';

            var bgDesktop = {
                backgroundImage: `url(${desktopImage?.url})`,
                backgroundPosition: 'center center',
                backgroundSize: "cover",
                backgroundRepeat: "no-repeat",
                position: 'absolute',
                height: '100%',
                width: '100%',
            };

            var bgDesktopClass = 'bg-desktop';
        } else if (desktopImage) {
            var bgDesktop = {
                backgroundImage: `url(${desktopImage?.url})`,
                backgroundPosition: 'center center',
                backgroundSize: "cover",
                backgroundRepeat: "no-repeat",
                position: 'absolute',
                height: '100%',
                width: '100%',
            };
        }

    } else {
        var backgroundStyles = {
            ...bgColor,
        };
    }

    if (slide?.title_background && data?.style !== 'text-carousel') {
        let titleBgColor = (!slide?.title_background.startsWith('#')) ? Coloring(slide?.title_background, settings) : slide?.title_background;

        var TitleBackgroundStyles = {
            backgroundColor: `${pylot?.design?.hexToRGB(titleBgColor, slide?.title_background_opacity)}`,
            padding: '1rem',
        };
    }
    let role = slide?.background_type === 'image' ? 'img' : null;
    let bgAlt = (slide?.background_type === 'image' && desktopImage?.alt) ? desktopImage?.alt : null;
    let bgAltMobile = (slide?.background_type === 'image' && bgMobile) ? mobileImage?.alt : null;
    let overlayColor = (slide.image_overlay && !slide.image_overlay.startsWith('#')) ? Coloring(slide.image_overlay, settings) : slide.image_overlay;

    return (
        <div ref={ref} key={index} className={`slide-wrapper ${slideData?.mobile_layout}`}>
            {bgMobile &&
                <div class={`slide-background ${bgMobileClass ? bgMobileClass : ''}`} role={role} aria-label={bgAltMobile} style={inView && bgMobile ? bgMobile : { display: 'none' }} />
            }
            <div class={`slide-background ${bgDesktopClass ? bgDesktopClass : ''}`} role={role} aria-label={bgAlt} style={inView ? { ...bgDesktop, ...backgroundStyles } : {}} />
            {slide?.image_overlay && slideData?.style !== 'text-carousel' &&
                <ImageOverlay className={slide.image_overlay_style} overlayColor={overlayColor} overlayOpacity={slide.image_overlay_opacity} />
            }
            <div class={`slide ${slideAlignClass} grid-container`}>
                {(appContext.width >= 768 || slideData?.mobile_layout !== 'text-below-image') && <ContentWrapper page={page} placeholders={placeholders} className={slideData?.mobile_layout === 'text-below-image' ? ' desktop-only' : ''} index={index} slideData={slideData} slide={slide} TitleBackgroundStyles={TitleBackgroundStyles} slideAlignClass={slideAlignClass} contentAlignClass={contentAlignClass} buttonAlignClass={buttonAlignClass} settings={settings} />}
            </div>
            {(appContext.width < 768 && slideData?.mobile_layout === 'text-below-image') &&
                <ContentWrapper data={data} page={page} placeholders={placeholders} className={`mobile-only${slideData?.carousel_items?.length > 1 ? ' with-carousel' : ''}`} index={index} slideData={slideData} slide={slide} TitleBackgroundStyles={TitleBackgroundStyles} slideAlignClass={slideAlignClass} contentAlignClass={contentAlignClass} buttonAlignClass={buttonAlignClass} settings={settings} />
            }
        </div>
    );
}

const ContentWrapper = ({ page, placeholders, className, index, slideData, slide, TitleBackgroundStyles, slideAlignClass, contentAlignClass, buttonAlignClass, settings }) => {
    const title = (placeholders && slide.title_selection === 'dynamic') ? placeholders.single_line[slide.title] : slide.title

    let mobileBackground = 'transparent';

    if (slide?.background_value === 'dark') {
        var headingStyles = {
            // fontFamily: `${settings.design?.fonts.heading.heading_google_font_name}`,
            // fontWeight: `${settings.design?.fonts.heading.heading_font_weight}`,
            color: '#fff',
        };

        var blurbStyles = {
            // fontFamily: `${settings.design?.fonts.body.body_google_font_name}`,
            // fontWeight: `${settings.design?.fonts.body.body_font_weight}`,
            color: '#fff',
        };
    } else {
        var headingStyles = {
            // fontFamily: `${settings.design?.fonts.heading.heading_google_font_name}`,
            // fontWeight: `${settings.design?.fonts.heading.heading_font_weight}`,
            color: `${settings?.design?.colors?.primary_color}`
        };

        var blurbStyles = {
            // fontFamily: `${settings.design?.fonts.body.body_google_font_name}`,
            // fontWeight: `${settings.design?.fonts.body.body_font_weight}`,
            color: `${settings?.design?.colors?.body_copy_color}`,
        };
    }

    if (className.includes('mobile-only')) {
        mobileBackground = Coloring(slideData?.mobile_text_background_color, settings);

        if (slideData?.mobile_text_background_value === 'dark') {
            headingStyles.color = '#fff';
            blurbStyles.color = '#fff';
        } else {
            headingStyles.color = settings?.design?.colors?.primary_color;
            blurbStyles.color = settings?.design?.colors?.body_copy_color;
        }
    }

    return (
        <ImageCarouselContent className={`content-wrapper ${slideAlignClass} ${slideData?.alignment} ${className}`} mobileBackground={mobileBackground}>
            <div class={`inner-wrapper ${contentAlignClass} ${slideData?.alignment}`} style={TitleBackgroundStyles}>
                {(index === 0 && !slide?.hide_title) &&
                    <h1 style={headingStyles} class={'heading'} role='heading' aria-level='1'>{decode(title ? title : page?.mvk_item_content?.title)}</h1>
                }
                {(index !== 0 && !slide?.hide_title) &&
                    <h2 style={headingStyles} class={'heading'} role='heading' aria-level='2'>{decode(title ? title : page?.mvk_item_content?.title)}</h2>
                }
                {slide?.blurb &&
                    <div style={blurbStyles} class='blurb'>
                        <HtmlParser html={slide?.blurb} placeholders={placeholders} />
                    </div>
                }
                {slide?.buttons &&
                    <div class='button-wrapper'>
                        {slide?.buttons.map((button, buttonIndex) => {
                            const buttonLink = placeholders ? placeholders.button_link[button?.button] : button?.button;
                            if (buttonLink) {
                                return (
                                    <div key={buttonIndex} class={`${buttonAlignClass}`}>
                                        <Button class='hero-cta' title={buttonLink?.title} url={buttonLink?.url} target={buttonLink?.target} type={button?.button_style} tone={className.includes('mobile-only') ? slideData?.mobile_text_background_value : slide?.background_value} />
                                    </div>
                                );
                            } else return null;
                        })}
                    </div>
                }
            </div>
        </ImageCarouselContent>
    )
}

export default ImageCarousel;
