@import "src/scss/variables.scss";

.hours-directions {
    .content-container {
        padding: 1.5rem 1rem;
        .hours-container.list {
            text-align: unset;
            max-width: 650px;
            margin: auto;
            .hours-list .inner-wrapper {
                @media (min-width: $break-small) {
                    columns: 2;
                    column-gap: 3rem;
                }
                @media (min-width: $break-medium) {
                    .list-row {
                        font-size: 1rem;
                    }
                }
                @media (min-width: $break-desktop) {
                    column-gap: 5rem;
                    min-width: 400px;
                }
                .list-row {
                    display: flex;
                    justify-content: space-between;
                    span {
                        text-transform: unset;
                        text-align: right;
                    }
                }
            }
        }
        .hours-module {
            padding: 1rem 0 0;
        }
    }
    .map-container {
        position: relative;
        .map {
            width: 100%;
            border: none;
        }
        .directions-button {
            position: absolute;
            top: 65%;
            left: 50%;
            transform: translate(-50%, -50%);
            // padding: 10px 20px;
            width: auto;
            text-align: center;
        }
    }

    .directions {
        .title {
            text-align: center;
            margin-bottom: 1rem;
        }
    }

    .address {
        text-align: center;
        span {
            margin-right: 0.5rem;
            &.phone-number {
                display: block;
            }
        }
    }
    .hours-container {
        .hours {
            margin: 0;
        }
    }
    .full-bleed {
        .content-container {
            text-align: center;
        }
        .map {
            min-height: 350px;
        }
        .map-container.google-map {
            height: 350px;
        }
        .directions {
            padding: 2rem 0;
        }
        .reduced {
            text-align: center;
            p {
                margin-bottom: 0.25rem;
            }
        }
    }

    .one-third-two-third {
        .map {
            min-height: 450px;
        }
        .map-container.google-map {
            height: 450px;
        }
        .hours-container {
            text-align: center;
        }
        @media (min-width: $break-tablet) {
            display: flex;
            .content-container {
                .address span {
                    display: block;
                }
            }
            .map-container {
                width: 66%;
                .map {
                    height: 100%;
                }
            }
            &.with-map {
                .content-container {
                    width: 34%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                }
            }
            &.no-map {
                width: 100%;
            }
        }
    }
}
