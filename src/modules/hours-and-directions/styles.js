import styled from 'styled-components';

export const ContentContainer = styled.div`
    color: ${props => props.textColor};
    &.color {
        background-color: ${props => props.backgroundColor};
    }
    &.image {
        background-image: url(${props => props.backgroundImage});
        background-position: center center;
        background-size: cover;
        background-repeat: no-repeat;
    }
    &.dark {
        form .form-group .error, .star {
            color: #fff !important;
        }
    }
`;