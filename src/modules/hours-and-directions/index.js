import React, { useContext } from "react";
import { SettingsContext } from "src/context";
import { useInView } from 'react-intersection-observer';
// Helpers
import { WeeksHours } from 'src/helpers/hours';
import { Coloring } from "src/helpers";

// Partials
const Button = React.lazy(() => import('src/partials/button'));
const Reduced = React.lazy(() => import('src/partials/hours/reduced'));
const List = React.lazy(() => import('src/partials/hours/list'));
import CondensedList from 'src/partials/hours/condensed-list';
import GoogleMap from 'src/partials/google-map';
// Styles
import './index.scss';
import { ContentContainer } from './styles.js';

const Start = ({ data, settings }) => {
    const { ref, inView } = useInView({
        triggerOnce: true,
        fallbackInView: true
    });
    const display = data.display_options;
    // bg options
    data.bgColor = data.background_type && data.background_color ? Coloring(data.background_color, settings) : Coloring('primary_color', settings);
    data.color = data.background_type && data.background_value === 'light' ? Coloring('body_copy_color', settings) : '#fff';

    return (
        <div ref={ref} class={`hours-directions`}>
            {inView ? <>
                {display === 'full-bleed' &&
                    <FullBleed data={data} settings={settings} />
                }
                {display === 'one-third-two-third' &&
                    <OneThirdTwoThird data={data} settings={settings} />
                }
            </> : null}
        </div>
    );
}

const FullBleed = ({ data, settings }) => {

    return (
        <div class='full-bleed'>
            <ContentContainer
                className={`content-container ${data.background_type ? data.background_type : 'color'} ${data.background_value}`}
                backgroundColor={data.bgColor}
                backgroundImage={data.background_type === 'image' ? data.background_image?.url : ''}
                textColor={data.color}
            >
                <Hours data={data} style={data.hours_style} />
            </ContentContainer>
            {(data.map_embed_url || data.google_map) &&
                <Map data={data} />
            }
            {
                (data.title || data.address) &&
                <div class='grid-container'>
                    <div class='grid-x'>
                        <div class='cell'>
                            <Directions data={data} settings={settings} />
                        </div>
                    </div>
                </div>
            }
        </div>
    );
}

const OneThirdTwoThird = ({ data, settings }) => {
    return (
        <div class={`one-third-two-third ${data.map_embed_url ? 'with-map' : 'no-map'}`}>
            <ContentContainer
                className={`content-container ${data.background_type ? data.background_type : 'color'} ${data.background_value}`}
                backgroundColor={data.bgColor}
                backgroundImage={data.background_type === 'image' ? data.background_image?.url : ''}
                textColor={data.color}
            >
                <div className="inner-wrapper">
                    <Directions data={data} settings={settings} />
                    <Hours data={data} style={data.hours_style} />
                </div>
            </ContentContainer>
            {data.map_embed_url &&
                <Map data={data} />
            }
        </div>
    );
}

const Directions = ({ data, settings }) => {

    return (
        <div class='directions'>
            {data.title &&
                <div class='title'>
                    {data.display_options === 'full-bleed' ? <h2>{data.title}</h2> : <h3>{data.title}</h3>}
                </div>
            }
            {data.address &&
                <p class='address'>
                    {settings.address?.address_1 &&
                        <span>{settings.address?.address_1}</span>
                    }
                    {settings.address?.address_2 &&
                        <span>{settings.address?.address_2}</span>
                    }
                    {settings.address?.city &&
                        <span>{`${settings.address?.city}, ${settings.address?.state.value} ${settings.address?.zip}`}</span>
                    }
                    {settings.contact?.phone &&
                        <span class='phone-number'>{settings.contact?.phone}</span>
                    }
                </p>
            }
        </div>
    );
}

const Hours = ({ data, style }) => {
    const [settings, setSettings] = useContext(SettingsContext);

    const hours = WeeksHours(style);

    return (
        <div class={`hours-container ${data.hours_style}`}>
            {data.blurb &&
                <div class='blurb' dangerouslySetInnerHTML={{ __html: data.blurb }} />
            }
            {settings.hours?.custom_hours_message &&
                <p class='custom-hours-message'>{settings.hours?.custom_hours_message}</p>
            }
            {!settings.hours?.custom_hours_message &&
                <HoursStyle data={hours} style={style} />
            }
        </div>
    );
}

const HoursStyle = ({ data, style }) => {

    switch (style) {
        case 'reduced':
            return (<Reduced hours={data} />);
        case 'list':
            return (<List hours={data} />);
        case 'condensed_list':
            return (<CondensedList hours={data} />);
        default:
            return (<div />);
    }
}

const Map = ({ data }) => {

    return (
        <div class={`map-container${data.google_map ? ' google-map' : ''}`}>
            {data.google_map ?
                <GoogleMap mapID={data.google_map} />
                :
                <iframe class='map' title='hours and directions map' src={data.map_embed_url} />
            }
            {data.button?.url &&
                <Button class='directions-button' url={data.button.url} target={data.button.target} icon={data.button_icon} tone={data.background_value} type={data.button_style} title={data.button?.title} />
            }
        </div>
    );

}
export default Start;