import React from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faPlayCircle } from '@fortawesome/free-solid-svg-icons'
import { useInView } from 'react-intersection-observer';

// HELPERS
const Imaging = React.lazy(() => import('src/helpers/imaging'));
const Clicker = React.lazy(() => import('src/helpers/clicker'));
//PARTIALS
const Button = React.lazy(() => import('src/partials/button'));

import './three-columns.scss';

const Start = ({ data, settings }) => {
    const { ref, inView } = useInView({
        triggerOnce: true,
        fallbackInView: true
    });
    const bgColor = pylot?.design?.coloring(data.background_color, 'backgroundColor');
    let bgStyles;
    switch (data.background_type) {
        case 'image':
            bgStyles = {
                backgroundImage: `url(${pylot?.tools?.domain.add(data.background_image?.url)})`,
                backgroundPosition: 'center center',
                backgroundSize: "cover",
                backgroundRepeat: 'no-repeat',
                borderRadius: `${settings?.mvk_theme_config?.other?.border_radius_size}px`
            };
            break;
        case 'color':
        default:
            bgStyles = {
                ...bgColor,
                borderRadius: `${settings?.mvk_theme_config?.other?.border_radius_size}px`
            };
    };

    const color = data.background_value === 'dark' ? 'white-txt' : 'body-copy-txt';

    return (
        <div ref={ref} class={`testimonial-list ${data?.layout}`}>
            {inView ?
                <div class='inner-container grid-container'>
                    <h2 class='testimonial-title'>{data?.title}</h2>

                    <div class='testimonial-card-wrapper'>
                        {data?.testimonial_data &&
                            data?.testimonial_data.map((card, index) => (
                                <div class='testimonial-card' key={index} style={bgStyles}>
                                    <div class='upper-container'>
                                        <h3 class={`title ${color}`}>{card?.title}</h3>
                                        <div class={`quote ${color}`} dangerouslySetInnerHTML={{ __html: card?.quote }} />
                                    </div>
                                    <div class='lower-container'>
                                        <Button class='testimonial-button' title={card?.learn_more_link && card?.learn_more_link['title'] ? card?.learn_more_link['title'] : 'LEARN MORE'} url={card?.learn_more_link ? card?.learn_more_link['url'] : card?.testimonial_link} target={card?.learn_more_link ? card?.learn_more_link['target'] : ''} />
                                        <Clicker class='video-link' type={'anchor'} url={card?.testimonial_link}>
                                            <FontAwesomeIcon class={`play-button ${color}`} icon={faPlayCircle} />
                                            <span class={`video-link-text ${color}`}>{card?.testimonial_link_text}</span>
                                        </Clicker>
                                        <Imaging class='list-image' data={card?.list_image} />
                                    </div>
                                </div>
                            ))
                        }
                    </div>
                </div>
                : null}
        </div>
    );
};

export default Start;