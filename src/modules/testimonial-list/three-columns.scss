.testimonial-list {
    &.three-columns {
        .testimonial-title {
            margin-bottom: 1rem;
        }

        .testimonial-card-wrapper {
            display: grid;
            grid-auto-flow: row;
            grid-template-columns: 1fr;
            row-gap: 1rem;

            @media (min-width: 768px) {
                grid-template-columns: 1fr 1fr 1fr;
                column-gap: 0.75rem;
            }
        }

        .testimonial-card {
            display: flex;
            flex-flow: column nowrap;
            align-items: center;
            justify-content: space-between;
            padding: 1.75rem 1.5rem 0 1.5rem;
        }

        .title {
            margin-bottom: 0.5rem;
            font-weight: bold;
            text-align: center;
            font-size: 1.1rem;

            @media (min-width: 768px) {
                font-size: 1.22rem;
            }

            @media (min-width: 1200px) {
                font-size: 1.4rem;
            }
        }

        .quote {
            text-align: center;
            margin-bottom: 1rem;
        }

        .lower-container {
            display: flex;
            flex-flow: column nowrap;
            align-items: center;
            justify-content: space-between;
        }

        .testimonial-button {
            margin-bottom: 2rem;
            font-size: 0.875rem;
            background-color: #ee3e33 !important;
            color: #fff !important;
            border: none !important;

            @media (min-width: 992px) {
                font-size: 1rem;
            }
        }

        .video-link {
            margin-bottom: 1rem;
            display: flex;
            flex-flow: row nowrap;
            align-items: center;
        }

        .play-button {
            display: none;

            @media (min-width: 1024px) {
                display: inline;
                height: 1.25em;
                width: 1.25em;
                margin-right: 0.5rem;
            }
        }

        .video-link-text {
            font-size: 0.75rem;
            font-weight: bold;
            text-decoration: underline;

            @media (min-width: 992px) {
                font-size: 0.875rem;
            }
        }
    }
}
