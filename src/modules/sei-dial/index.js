import React, { useEffect, useRef, useState } from 'react'
import Slider from "react-slick";
import { useInView } from 'react-intersection-observer';
import { decode } from 'html-entities';
// Helpers
import HtmlParser from 'src/helpers/html-parser'
import { Coloring } from 'src/helpers';
import Imaging from 'src/helpers/imaging';
import { PrevArrow, NextArrow, Dots, Dot } from 'src/helpers/slick';
// Partials
import Button from 'src/partials/button';
// Styles
import * as S from './styles';
import "slick-carousel/slick/slick.scss";
import "slick-carousel/slick/slick-theme.scss";

const Start = ({ data, settings }) => {
    const dialContent = settings?.sei_settings?.dial_content ? settings?.sei_settings?.dial_content : false;
    const [activeIconClass, setActiveIconClass] = useState(null);
    const prevActiveIconClass = useRef(null);
    const sliderRef = useRef(null);
    const { ref, inView } = useInView({
        fallbackInView: true,
        threshold: 0.75
    });

    useEffect(() => {
        const defaultActiveIcon = document.querySelector(".icon.active");
        if (defaultActiveIcon) {
            const activeClass = Array.from(defaultActiveIcon.classList).find((cls) =>
                cls.startsWith("sei-")
            );
            prevActiveIconClass.current = activeClass;
            setActiveIconClass(activeClass);
        }
    }, []);

    const getSlideIndexFromClass = (className) => {
        const keys = Object.keys(dialContent);
        const contentKey = className.replace('sei-', '')
        return keys.indexOf(contentKey);
    };

    const getClassFromSlideIndex = (index) => {
        const keys = Object.keys(dialContent);
        return keys[index];
    };

    const updateActiveIcon = (newActiveClass) => {
        if (prevActiveIconClass.current) {
            const prevIcon = document.querySelector(`.icon.${prevActiveIconClass.current}`);
            if (prevIcon) {
                prevIcon.classList.remove("active");
            }
        }
        const newActiveIcon = document.querySelector(`.icon.${newActiveClass}`);
        if (newActiveIcon) {
            newActiveIcon.classList.add("active");
        }
        prevActiveIconClass.current = newActiveClass;
        setActiveIconClass(newActiveClass);
    };

    const handleIconClick = (event) => {
        const clickedElement = event.target.closest(".icon");

        if (clickedElement) {
            const clickedClass = Array.from(clickedElement.classList).find((cls) =>
                cls.startsWith("sei-")
            );
            updateActiveIcon(clickedClass);

            const slideIndex = getSlideIndexFromClass(clickedClass);
            setTimeout(() => {
                if (sliderRef.current) {
                    sliderRef.current.slickGoTo(slideIndex);
                }
            }, 0);
        }
    };

    const handleBeforeChange = (oldIndex, newIndex) => {
        const newActiveClass = getClassFromSlideIndex(newIndex);
        updateActiveIcon(`sei-${newActiveClass}`);
    };

    const sliderSettings = {
        slidesToShow: 1,
        slidesToScroll: 1,
        arrows: true,
        dots: true,
        adaptiveHeight: true,
        fade: true,
        cssEase: 'linear',
        nextArrow: <NextArrow />,
        prevArrow: <PrevArrow />,
        customPaging: dot => <Dot dot={dot} data={data} settings={settings} />,
        appendDots: dots => <Dots dots={dots} data={data} settings={settings} />,
        beforeChange: handleBeforeChange
    };

    return (
        <S.SEIDial
            className={`dial-module ${data.background_type}`}
            bgColor={Coloring(data.background_color, settings)}
            bgImage={(data.background_type == 'image' && data.background_image) ? data.background_image?.url : ''}
            textColor={data.background_value === 'dark' ? '#fff' : Coloring('body_copy_color', settings)}
        >
            <div className='grid-container'>
                <div className='title-container grid-x'>
                    {data.title &&
                        <div class={`title cell ${data.title_alignment}`}>
                            <h2>{decode(data.title)}</h2>
                        </div>
                    }
                    {data.blurb &&
                        <div className="blurb cell">
                            <HtmlParser html={data.blurb} />
                        </div>
                    }
                </div>
                <div ref={ref} className='dial-container grid-x'>
                    <S.Dial className={`dial cell medium-6 ${inView && 'animate'}`} onClick={handleIconClick} dangerouslySetInnerHTML={{ __html: data.dial_svg_code }} />
                    <div className='dial-content cell medium-6'>
                        {(dialContent && activeIconClass) &&
                            <Slider ref={sliderRef} {...sliderSettings}>
                                {Object.keys(dialContent).map((key, i) =>
                                    <div key={`sei-${key}`} id={`sei-${key}`}>
                                        <div className='inner-wrapper'>
                                            <HtmlParser html={dialContent[key]} />
                                        </div>
                                    </div>
                                )}
                            </Slider>
                        }
                    </div>
                </div>
                {data.sei_dial_button &&
                    <div className='grid-x'>
                        <div class={`cell ${data?.sei_dial__button_alignment}`}>
                            <Button class='module-button' title={data?.sei_dial_button?.title} url={data?.sei_dial_button?.url} target={data?.sei_dial_button?.target} tone={data.background_value} type={data?.sei_dial__button_style} />
                        </div>
                    </div>
                }
            </div>
        </S.SEIDial>
    )
}

export default Start;