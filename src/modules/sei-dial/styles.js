import styled from 'styled-components'

export const SEIDial = styled.div`
    padding: 3rem 0 5rem;
    @media (min-width: 1200px) {
        padding: 5rem;
    }
    color: ${props => props.textColor};
    &.color {
        background-color: ${props => props.bgColor};
    }
    &.image {
        background-image: url(${props => props.bgImage});
        background-position: center center;
        background-size: cover;
        background-repeat: no-repeat;
    }
    .dial-container {
        .dial-content {
            .mvk-responsive-video {
                border-radius: 10px;
                overflow: hidden;
                .overlay {
                    background-size: cover;
                    background-position: center center;
                }
            }
        }
    }
    .slick-dots {
        bottom: -3.5rem !important;
    }
    button.slick-arrow {
        top: unset;
        transform: unset;
        bottom: -4.5rem;
        @media (min-width: 1024px) {
            &.slick-prev {
                left: 5%;
            }
            &.slick-next {
                right: 5%;
            }
        }
        @media (min-width: 1200px) {
            &.slick-prev {
                left: 15%;
            }
            &.slick-next {
                right: 15%;
            }
        }
    }
    @media (min-width: 640px) {
        .dial-container {
            align-items: center;
        }
    }
`

export const Dial = styled.div`
    &.animate {
        animation: dialMe 1s ease-out;
    }

    @keyframes dialMe {
        0% {
            transform: rotate(0deg);
        }
        25% {
            transform: rotate(10deg);
        }
        75% {
            transform: rotate(-10deg);
        }
        100% {
            transform: rotate(0deg);
        }
    }
`
