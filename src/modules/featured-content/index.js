import React, { useEffect, useState } from "react";
import styled from 'styled-components';
import Slider from "react-slick";
import { useInView } from 'react-intersection-observer';
import { decode } from 'html-entities';
// HELPERS.
import Imaging from 'src/helpers/imaging';
import HtmlParser from 'src/helpers/html-parser';
import { PrevArrow, NextArrow } from 'src/helpers/slick';
// PARTIALS.
import Button from 'src/partials/button';
// SCSS.
import 'src/modules/featured-content/index.scss';
import "slick-carousel/slick/slick.scss";
import "slick-carousel/slick/slick-theme.scss";

const Start = ({ data, settings, placeholders }) => {
    const { ref, inView } = useInView({
        triggerOnce: true,
        fallbackInView: true
    });

    return (
        <div ref={ref} class={`featured-content ${data.style}`}>
            {inView ? <Container data={data} settings={settings} placeholders={placeholders} /> : null}
        </div>
    );
};

const Container = ({ data, settings, placeholders }) => {
    const bgImage = (placeholders && data.background_image_selection === 'dynamic') ? placeholders.image[data?.background_image_dynamic] : data?.background_image
    const bgColor = pylot.design?.coloring(data.background_color, 'backgroundColor');
    let bgStyles = {};

    switch (data.background_type) {
        case 'image':
            bgStyles = {
                backgroundImage: `url(${pylot.tools?.domain.add(bgImage?.url)})`,
                backgroundPosition: 'center center',
                backgroundSize: "cover",
                backgroundRepeat: 'no-repeat'
            };
            break;
        case 'color':
        default:
            bgStyles = {
                ...bgColor
            };
    };
    let role = data.background_type === 'image' ? 'img' : null;
    let bgAlt = (data.background_type === 'image' && bgImage.alt) ? bgImage.alt : null;

    return (
        <div class={`featured-content-container ${data.style}`}>
            <div class={`background ${data.style}`} role={role} aria-label={bgAlt} style={bgStyles} />
            <DisplayOptions data={data} placeholders={placeholders} />
        </div>
    );
};

const DisplayOptions = ({ data, placeholders }) => {
    const [direction, setDirection] = useState('flex-dir-row');

    useEffect(() => {
        var which = data.columns.find(column => column.content_type == 'image');

        if (window.innerWidth <= 768 && which) {
            setDirection(which == data.columns[0] ? 'flex-dir-column' : 'flex-dir-column-reverse');
        } else {
            setDirection('flex-dir-row');
        }
    }, [window.innerWidth]);

    let column1Classes, column2Classes;
    switch (data.display_options) {
        case 'two-thirds-one-third':
            column1Classes = 'cell large-8';
            column2Classes = 'cell large-4';
            break;
        case 'one-third-two-thirds':
            column1Classes = 'cell large-4';
            column2Classes = 'cell large-8';
            break;
        case 'half-half':
        default:
            column1Classes = 'cell large-6';
            column2Classes = 'cell large-6';
    };

    var alignItems;
    for (let item of data?.columns) {
        item.content_type === 'image-carousel' ? alignItems = 'remove-center' : alignItems = 'default';
    }

    return (
        <div class={`grid-container ${data.style}`} >
            <div class={`grid-x ${data.style !== 'full' ? 'grid-margin-x' : ''} flex-container ${direction} ${alignItems}`}>
                <Column data={data} placeholders={placeholders} column={data.columns[0]} colNum={1} classes={column1Classes} />
                <Column data={data} placeholders={placeholders} column={data.columns[1]} colNum={2} classes={column2Classes} />
            </div>
        </div>
    );
};

const Column = ({ data, placeholders, column, colNum, classes }) => {
    return (
        <div class={`${classes} column-style-${column.content_type}`}>
            <ContentType data={data} placeholders={placeholders} column={column} colNum={colNum} classes={classes} />
        </div>
    );
};

const ContentType = ({ data, placeholders, column, colNum, classes }) => {
    const title = (placeholders && column.title_selection === 'dynamic') ? placeholders.single_line[column?.title_dynamic] : column?.title
    const mapUrl = placeholders ? placeholders.single_line[column?.map_embed_url] : column?.map_embed_url
    const button = (placeholders && column.button_selection === 'dynamic') ? placeholders.button_link[column?.button_dynamic] : column?.button
    const image = (placeholders && column.image_selection === 'dynamic') ? placeholders.image[column?.image_dynamic] : column?.image
    const mobileImage = (placeholders && column.mobile_image_selection === 'dynamic') ? placeholders.image[column?.mobile_image_dynamic] : column?.mobile_image
    const imageCarousel = (placeholders && column.carousel_image_selection === 'dynamic') ? placeholders.image[column?.image_carousel_dynamic] : column?.image_carousel

    const sliderSettings = {
        dots: false,
        adaptiveHeight: false,
        arrows: true,
        autoplay: false,
        autoplaySpeed: 8000,
        pauseOnHover: false,
        infinite: true,
        slidesToShow: classes.includes('large-8') ? 2 : 1,
        slidesToScroll: 1,
        speed: 800,
        nextArrow: <NextArrow />,
        prevArrow: <PrevArrow />,
        responsive: [
            {
                breakpoint: 768,
                settings: {
                    slidesToShow: 1
                }
            }
        ]
    };

    if (data?.style === 'full' && data?.add_gutter && data?.gutter_space?.length > 0) {
        var padLeft;
        var padRight;
        if (colNum === 1) {
            padLeft = 'unset';
            padRight = `${data?.gutter_space}px`;
        } else {
            padLeft = `${data?.gutter_space}px`;
            padRight = 'unset';
        }
    }

    switch (column.content_type) {
        case 'copy':
            return (
                <div class={`featured-content-copy ${data.style} ${data.background_value}`}>
                    {title && <h2 class='featured-content-title'>{decode(title)}</h2>}
                    {column.content && <div className="featured-content-copy-content"><HtmlParser html={column.content} data={data} placeholders={placeholders} /></div>}
                    {button?.url &&
                        <div class={`featured-content-copy-cta ${column.button_alignment}`}>
                            <Button title={button.title} url={button.url} target={button.target} icon={column.button_icon} type={column.button_style} tone={data.background_value} />
                        </div>
                    }
                </div>
            );
        case 'map':
            return (
                <div class='featured-content-map'>
                    <iframe class='map' title='google map' src={mapUrl} />
                    {column.button?.url &&
                        <Button className='map-button' title={column.button.title} url={column.button.url} target={column.button.target} icon={column.button_icon} type={column.button_style} tone={data.background_value} />
                    }
                    {button?.url &&
                        <Button className='map-button' title={button.title} url={button.url} target={button.target} icon={column.button_icon} type={column.button_style} tone={data.background_value} />
                    }
                </div>
            );
        case 'image-carousel':
            return (
                <Slider className={'featured-content-slider'} ref={(a) => a} {...sliderSettings}>
                    {Array.isArray(imageCarousel) && imageCarousel?.length > 0 &&
                        imageCarousel?.map((image, index) => (
                            <div key={index} class='slide'>
                                <Imaging data={image} />
                                {(column?.show_captions && image?.caption) && <p class='image-caption'>{image?.caption}</p>}
                            </div>
                        ))
                    }
                </Slider>
            );
        case 'image':
        default:
            return (
                <FeaturedContentImage
                    className={`featured-content-image ${data.style} flex-container flex-dir-column align-center-middle height-100`}
                    bgColor={data?.add_gutter ? '#fff' : 'transparent'}
                    padLeft={padLeft}
                    padRight={padRight}
                >
                    <Imaging className={mobileImage ? 'show-for-medium' : ''} data={image} />
                    {mobileImage &&
                        <Imaging className='hide-for-medium' data={mobileImage} />
                    }
                </FeaturedContentImage>
            );
    };
};

const FeaturedContentImage = styled.div`
    img {
        background-color: ${props => props.bgColor};
        
        @media (min-width: 1024px) {
            padding-left: ${props => props.padLeft};
            padding-right: ${props => props.padRight};
        }
    }

`

export default Start;