@import "~foundation-sites/scss/util/_util.scss";
@import "src/scss/variables.scss";

// USED BY ALL STYLES.
.featured-content {
    position: relative;
    // overflow: hidden;
    &.image-bleed-top-bottom {
        margin-top: -30px;
        margin-bottom: -30px;
    }
    &.image-bleed-top {
        margin-top: -30px;
        // margin-bottom: 20px;
    }
    &.image-bleed-bottom {
        // margin-top: 20px;
        margin-bottom: -30px;
    }

    &.contained {
        // margin-top: 20px;
        // margin-bottom: 20px;
        .grid-container.contained {
            // padding: 30px 15px;
            padding: 3rem 1rem;
            @media (max-width: 1023px) {
                .cell:first-of-type {
                    margin-bottom: 1rem;
                }
            }
        }
    }

    &.full {
        .cell {
            &.column-style-copy {
                display: flex;
                align-items: center;
            }
            .featured-content-copy {
                width: 100%;
                padding: 2rem 0.625rem;
                .form-module {
                    margin: 0;
                }
                @media (min-width: $break-large) {
                    margin: auto;
                    padding: 2rem;
                }
                @media (min-width: $break-xlarge) {
                    padding: 4rem;
                }
            }
            .featured-content-image img {
                width: 100%;
                height: 100%;
                object-fit: cover;
            }
        }
    }
    .column-style-map {
        align-self: stretch;
    }
    &-container {
        position: relative;

        .background {
            position: absolute;
            left: 0;
            right: 0;
            top: 0;
            bottom: 0;
            &.image-bleed-top-bottom {
                top: 30px;
                bottom: 30px;
            }
            &.image-bleed-top {
                top: 30px;
                bottom: 0px;
            }
            &.image-bleed-bottom {
                top: 0px;
                bottom: 30px;
            }
        }

        .grid-container {
            position: relative;
            &:not(.full) {
                .grid-x {
                    align-items: center;
                }
            }
        }
    }

    &-copy {
        &.image-bleed-top-bottom {
            padding-top: 50px;
            padding-bottom: 50px;
            // @include breakpoint(medium only) { padding: 60px 44px 60px 44px; }
            // @include breakpoint(small down) { padding: 10px 20px 10px 20px; }
        }
        &.image-bleed-top {
            padding-top: 50px;
            padding-bottom: 20px;
            // @include breakpoint(medium only) { padding: 60px 44px 30px 44px; }
            // @include breakpoint(small down) { padding: 10px 20px 10px 20px; }
        }
        &.image-bleed-bottom {
            padding-top: 20px;
            padding-bottom: 50px;
            // @include breakpoint(medium only) { padding: 60px 44px 30px 44px; }
            // @include breakpoint(small down) { padding: 10px 20px 10px 20px; }
        }

        &-cta {
            @include breakpoint(small down) {
                text-align: center;
            }
        }

        &.dark {
            color: white;
        }
    }

    &-image {
        position: relative;
        z-index: +1;

        &.contained {
            margin: auto;
            @media screen and (max-width: $break-large) {
                img {
                    margin-bottom: 1rem;
                }
            }
        }
    }

    &-map {
        width: 100%;
        height: 100%;
        min-height: 420px;
        position: relative;
        .map-container {
            height: 420px;
        }
        .map {
            width: 100%;
            height: 100%;
            border: none;
        }
        .map-button {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }
    }

    .featured-content-title {
        margin-bottom: 1rem;
    }

    .featured-content-copy-content {
        margin-bottom: 1rem;
    }

    .image-caption {
        margin-top: 1rem;
    }

    .remove-center {
        align-items: flex-start !important;
    }
    .slick-slider {
        .slick-list {
            margin: 0 -0.5rem;
            .slick-slide > div {
                padding: 0 0.5rem;
            }
        }
    }
    .slide {
        @media (min-width: 768px) {
            width: auto !important;
        }
        img {
            width: 100%;
            object-fit: cover;
            @media (min-width: 768px) {
                min-height: 450px;
            }
        }
    }
}
