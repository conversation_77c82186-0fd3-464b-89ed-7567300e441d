import React from 'react';
import { useInView } from 'react-intersection-observer';

// Partials
import Hours from 'src/partials/hours';

const Start = ({ data }) => {
    const { ref, inView } = useInView({
        triggerOnce: true,
        fallbackInView: true
    });

    return (
        <div ref={ref}>
            {inView ? <Hours data={data} /> : null}
        </div>
    );
};

export default Start;