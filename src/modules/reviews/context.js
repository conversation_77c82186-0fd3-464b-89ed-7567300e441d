import React, { useState, createContext, useLayoutEffect, useEffect, useTransition, useContext } from 'react';
import postscribe from 'postscribe';

export const ReviewsContext = createContext();
ReviewsContext.displayName = 'modules, reviews, context: ReviewsContext';

export const ReviewsProvider = ({ data, settings, children }) => {
    const [ isPending, startTransition ] = useTransition(); // for future use.
    const [ loading, setLoading ]        = useState(false); // for future use.
    const [ reviews, setReviews ]        = useState(data?.review_data?.reviews || false);
    const [ errors, setErrors ]          = useState(data?.review_data?.errors || false);
    const [ selected, setSelected ]      = useState(false);

    // AMOUNT TO DISPLAY (SET TO FALSE TO SHOW ALL).
    const [ amount, setAmount ]   = useState(0);

    useEffect(() => {
        // ONLY OUTPUT IF THESE ARE TRUE/EXIST.
        if (reviews && reviews.some((item) => item.firstParty)) {
            const newJSON = {
                '@context': 'https://schema.org/',
                '@type':'Review',
                'itemReviewed': {
                    '@type': 'Organization',
                    'name': settings?.site_client,
                },
                review: reviews.filter((item) => item.firstParty).map((item, index) => {
                    var datePublished = new Date(item.publisherDate);
                    return {
                        '@type':'Review',
                        'reviewRating': {
                            '@type':'Rating',
                            'ratingValue': item.rating
                        },
                        'author': {
                            '@type':'Person',
                            'name': item.authorName
                        },
                        'datePublished': datePublished.toISOString().slice(0, 10),
                        'publisher': {
                            '@type':'Organization',
                            'name':item.publisherId
                        }
                    };
                })
            };
            postscribe(document.head, '<script type="application/ld+json">' + JSON.stringify(newJSON) + '</script>');
        }
    }, []);

    return (
        <ReviewsContext.Provider value={{ loading, reviews, errors, amount, selected, setSelected }}>
            {children}
        </ReviewsContext.Provider>
    );
};
ReviewsProvider.displayName = 'modules, reviews, context: ReviewsProvider';