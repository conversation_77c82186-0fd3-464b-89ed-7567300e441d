import React, { Fragment, useContext, useLayoutEffect } from "react";
import Slider from "react-slick";

// HELPERS.
import { PrevArrow, NextArrow } from 'src/helpers/slick';

// CONTEXT.
import { ReviewsProvider, ReviewsContext } from 'src/modules/reviews/context';

// LAYOUT.
import Layout from 'src/modules/reviews/layout';

// PARTIALS.
import Lightbox from 'src/modules/reviews/lightbox';

// PARTIALS (STYLES).
import Cards from 'src/modules/reviews/styles/cards';

// SCSS.
import 'src/modules/reviews/index.scss';
import 'src/modules/reviews/slick-styling.scss';

const Start = ({ data, settings }) => {
    return null; // Doing this temporarily to quickly remove all reviews modules from sites.

    return (
        <ReviewsProvider data={data} settings={settings}>
            <Layout data={data} settings={settings}>
                <Layout.Container data={data} settings={settings}>
                    <Layout.Container.Title data={data} settings={settings} />
                    <Layout.Container.Blurb data={data} settings={settings} />
                    <Layout.Container.Button data={data} settings={settings} placement="above" />
                    <Layout.Container.Reviews data={data} settings={settings}>
                        <WithSlider data={data} settings={settings} />
                        <Lightbox data={data} settings={settings} />
                    </Layout.Container.Reviews>
                    <Layout.Container.Button data={data} settings={settings} placement="below" />
                </Layout.Container>
            </Layout>
        </ReviewsProvider>
    );
};

const WithSlider = ({ data, settings }) => {
    const reviewsContext = useContext(ReviewsContext);

    function filtration(item, index) {
        switch (true) {
            case reviewsContext.amount > 0 && index < reviewsContext.amount:
                return true;
            case reviewsContext.amount == 0:
                return true;
            default:
                return false;
        };
    };

    return (
        <Fragment>
            <Slider {...SliderSettings(reviewsContext.reviews, data, settings)}>
                {reviewsContext.reviews && reviewsContext.reviews
                    .sort((a, b) => a - b)
                    .filter((item, index) => filtration(item, index))
                    .map((item) => <Cards item={item} data={data} settings={settings} />)
                }
            </Slider>
        </Fragment>
    );
};

export default Start;

function SliderSettings(reviews, data, settings) {
    return {
        adaptiveHeight: false,
        autoplay: false,
        autoplaySpeed: 4500,
        centerMode: true,
        centerPadding: '50px',
        infinite: true,
        pauseOnFocus: true,
        pauseOnHover: true,
        mobileFirst: true,
        slidesToScroll: 1,
        slidesToShow: (reviews.length >= 3) ? 3 : reviews.length,
        variableWidth: false,
        dots: false,
        arrows: true,
        nextArrow: <NextArrow />,
        prevArrow: <PrevArrow />,
        responsive: [
            {
                breakpoint: 1100,
                settings: {
                    slidesToShow: 2
                }
            },
            {
                breakpoint: 600,
                settings: {
                    centerPadding: '0px',
                    slidesToShow: 1
                }
            }
        ],
    };
};
