@import 'src/scss/variables.scss';
@import 'src/scss/mixins/flexbox.scss';

#app {
    #modules-container {
        .reviews-module {
               padding: 35px 0px;
            box-sizing: border-box;

            div {
                box-sizing: border-box;
            }

            .module-container {
                @include flexbox;
                @include flex-direction(column);
                @include flex-gap(2rem);

                   margin: 0px auto;
                max-width: $breakpoint-maximum;
                
                .module-heading {
                    @include flex;
                    @media screen and (max-width: $breakpoint-padding) {
                        padding: 0px 15px;
                    }
                }
                .module-blurb {
                    @include flex;
                    @media screen and (max-width: $breakpoint-padding) {
                        padding: 0px 15px;
                    }

                    p:last-of-type {
                        margin-bottom: 0px;
                    }
                }
                .module-reviews {
                    @include flex;
                    @include flexbox;
                    @include flex-direction(row);
                    @include flex-gap(1rem);
                    @include justify-content(center);
                    @include flex-wrap(wrap);

                    .reviews-item {
                        @include flex;
                        @include flexbox;
                        @include flex-direction(column);
                        @include flex-gap(1rem);

                            padding: 20px;
                          max-width: 430px;
                          min-width: 300px;
                          font-size: 0.8rem;
                        line-height: 1.2rem;
                    }
                }
                .module-buttons {
                    @include flex;
                    @media screen and (max-width: $breakpoint-padding) {
                        padding: 0px 15px;
                    }
                }
            }
        }
    }
}