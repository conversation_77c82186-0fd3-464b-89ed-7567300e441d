import React, { Fragment, useContext, useState } from "react";
import { decode } from "html-entities";
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faStar } from '@fortawesome/free-solid-svg-icons'

// HELPERS.
const Clicker = React.lazy(() => import('src/helpers/clicker'));

// CONTEXT.
import { ReviewsContext } from 'src/modules/reviews/context';

// LAYOUT.
import Layout from 'src/modules/reviews/layout';

// SCSS.
import 'src/modules/reviews/styles/cards.scss';

// WHEN FULL IS SET TO TRUE, DISPLAY FULL REVIEW.

const Start = ({ item, full = false, data, settings }) => {
    const [ shortened, setShortened ] = useState(Truncate(item.content, 300));
    const reviewsContext = useContext(ReviewsContext);

    return (
        <Layout.Container.Reviews.Item data={data} settings={settings}>
            <div class="cards-title">{item.authorName}</div>
            <div class="cards-stars">
                <DisplayStars number={item.rating} />
            </div>
            <div class="cards-content">{shortened && !full ? shortened : item.content}</div>
            {shortened && !full && <Clicker class="cards-continue secondary-txt" process={(e) => reviewsContext.setSelected(item)}>Continue Reading</Clicker>}
        </Layout.Container.Reviews.Item>
    );
};

const DisplayStars = ({ number }) => {
    return (
        <Fragment>
            {new Array(number).fill(null).map(() => 
                <div>
                    <FontAwesomeIcon icon={faStar} />
                </div>
            )}
        </Fragment>
    );
};

export default Start;

function Truncate (str, limit) {
    if (str.length <= limit) return false;
    else return str.slice(0, limit) + '...';
};