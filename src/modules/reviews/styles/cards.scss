@import 'src/scss/variables.scss';
@import 'src/scss/mixins/flexbox.scss';

#app {
    #modules-container {
        .reviews-module.cards {
            .module-container.cards {

                .module-reviews.cards {
                    .reviews-item.cards {
                        height: inherit;
                        border-radius: 5px;

                        .cards-title {
                            font-size: larger;
                        }

                        .cards-stars {
                            @include flexbox;
                            @include flex-direction(row);
                            @include flex-gap(0.4rem);

                            svg {
                                fill: goldenrod;
                                color: goldenrod;
                                width: 20px;
                                height: 20px;
                            }
                        }

                        .cards-content {
                            font-size: 0.9rem;
                            line-height: 1.4rem;
                        }

                        .cards-continue {
                            &:hover {
                                cursor: pointer;
                            }
                        }
                    }

                    #reviews-lightbox {
                        .reviews-item.cards {
                            @media screen and (max-width: $break-mobile) {
                                border-radius: 0px;
                            }
                        }
                    }
                }
            }
        }
    }
}