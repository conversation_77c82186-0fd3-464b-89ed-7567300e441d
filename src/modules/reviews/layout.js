import React, { useMemo, useContext } from "react";
import { useInView } from 'react-intersection-observer';

// HELPERS.
// import Background from 'src/helpers/background';

// CONTEXT.
import { ReviewsContext } from 'src/modules/reviews/context';

// PARTIALS
const Button = React.lazy(() => import('src/partials/button'));

// SCSS.
import 'src/modules/reviews/layout.scss';

const Start = ({ data, settings, children, ...otherProps }) => {
    const { ref, inView } = useInView({
        triggerOnce: true,
        fallbackInView: true
    });

    otherProps.class = useMemo(() => [ 'reviews-module', data.style ].join(' '), []);
    otherProps.style = useMemo(() => {
        return {
            color: data?.background_value === 'dark' ? '#fff' : settings?.design?.colors?.body_copy_color,
            ...Background(data, settings)
        }
    }, []);
    return (<div ref={ref} children={inView ? children : null} {...otherProps} />);
};
Start.displayName = 'modules, reviews, layout: start';

Start.Container = ({ data, settings, children, ...otherProps }) => {
    otherProps.class = useMemo(() => [ 'module-container', data.style ].join(' '), []);
    return (<div children={children} {...otherProps} />);
};
Start.Container.displayName = 'modules, reviews, layout: start.container';

Start.Container.Title = ({ data, settings, children, ...otherProps }) => {
    if (!(data?.title)) return null;
    otherProps.class = useMemo(() => [ 'module-heading', data.style ].join(' '), []);

    const headingStyles = {
        color: data?.background_value === 'dark' ? '#fff' : settings?.design?.colors?.body_copy_color,
        // fontFamily: settings?.design?.fonts?.heading?.heading_google_font_name,
        // fontWeight: settings?.design?.fonts?.heading?.heading_font_weight,
        textAlign: data.title_alignment
    };

    return (
        <div {...otherProps}>
            <h2 style={headingStyles}>{data?.title}</h2>
        </div>
    );
};
Start.Container.Title.displayName = 'modules, reviews, layout: start.container.title';

Start.Container.Blurb = ({ data, settings, children, ...otherProps }) => {
    if (!(data.blurb)) return null;
    otherProps.class = useMemo(() => [ 'module-blurb', data.style ].join(' '), []);
    return (<div {...otherProps} dangerouslySetInnerHTML={{ __html:data.blurb }} />);
};
Start.Container.Blurb.displayName = 'modules, reviews, layout: start.container.blurb';

Start.Container.Button = ({ data, settings, placement, children, ...otherProps }) => {
    if (!(data.button?.title) || data.button_placement != placement) return null;
    otherProps.class = useMemo(() => [ 'module-buttons', data.style, data.button_alignment ].join(' '));
    return (
        <div {...otherProps}>
            <Button title={data?.button?.title} url={data?.button?.url} target={data?.button?.target} icon={data?.button_icon} type={data?.button_style} tone={data?.background_value} />
        </div>
    );
};
Start.Container.Button.displayName = 'modules, reviews, layout: start.container.button';

Start.Container.Reviews = ({ data, settings, children, ...otherProps }) => {
    otherProps.class = useMemo(() => [ 'module-reviews', data.style ].join(' '), []);
    return (<div {...otherProps} children={children} />);
};
Start.Container.Reviews.displayName = 'modules, reviews, layout: start.container.reviews';

Start.Container.Reviews.Item = ({ data, settings, children, ...otherProps }) => {
    const reviewsContext = useContext(ReviewsContext);
    otherProps.class = useMemo(() => [ 'reviews-item', data.style ].join(' '), []);
    otherProps.style = useMemo(() => {
        return {
            backgroundColor: BackgroundColor(data?.card_background_color, settings, reviewsContext.selected),
            color: data?.card_background_value === 'dark' ? '#fff' : settings?.design?.colors?.body_copy_color,
        };
    }, []);
    return (<div {...otherProps} children={children} />);
};
Start.Container.Reviews.Item.displayName = 'modules, reviews, layout: start.container.reviews.item';

export default Start;

function Background (data, settings) {
    // console.log(data.background_type, data.background_image)

    if (data.background_type == 'image' && data.background_image) {
        if (!data.background_image.url.includes('http')) {
            data.background_image.url = `https://${config.domain}${data.background_image.url}`;
        }
        return { backgroundImage:`url(${data.background_image.url})`, backgroundRepeat:'no-repeat', backgroundPosition:'center', backgroundSize:'cover' };
    }
    
    else if (data.background_type == 'color') {
        return { backgroundColor:BackgroundColor(data.background_color, settings) };
    }    
    
    else {
        return null;
    }
};

function BackgroundColor (value, settings, selected) {
    switch (true) {
        case value == 'background_color':
            return settings.design?.colors?.background_color;
        case value == 'primary_color':
            return settings.design?.colors?.primary_color;
        case value == 'secondary_color':
            return settings.design?.colors?.secondary_color;
        case value == 'tertiary_color':
            return settings.design?.colors?.tertiary_color;
        case value == 'body_copy_color':
            return settings.design?.colors?.body_copy_color;
        case value == 'white' || selected != false:
            return '#FFF';
        case value == 'none':
        default:
            return 'unset';
    };
};