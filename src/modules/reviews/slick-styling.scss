@import 'src/scss/variables.scss';
@import 'src/scss/mixins/flexbox.scss';

#app {
    #modules-container {
        .reviews-module {
            .slick-slider {
                @include flexbox;
                @include flex-direction(row);
                @include flex-gap(0rem);
                overflow: hidden;
                position: relative;
                   width: 100%;

                .slick-list {
                    overflow: hidden;
                       width: 100%;

                    .slick-track {
                        @include flexbox;
                        @include flex-direction(row);
                        @include flex-gap(0rem);
                        @include align-content(stretch);
                        @include justify-content(center);

                        .slick-slide {
                            @include flex(1);

                            padding: 0px 10px;
                            height: inherit;
                            max-width: 420px;
                            box-sizing: border-box;

                            // MAKE ALL SLIDES EQUAL HEIGHT.
                            & > div {
                                height: 100%;
                            }
                        }
                    }
                }

                .slick-arrow {
                    @include flex(1,0,30px);
                    position: absolute;
                    z-index: +2;

                    width: 80px;
                    height: 100%;

                    &.slick-prev {
                        left: 0px;
                        .arrow-svg {
                            left: -40px;
                            svg {
                                position: absolute;
                                right: 6px;
                                top: 25px;
                                bottom: 25px;
                            }
                        }
                    }
                    &.slick-next {
                        right: 0px;
                        .arrow-svg {
                            right: -40px;
                            svg {
                                position: absolute;
                                left: 6px;
                                top: 25px;
                                bottom: 25px;
                            }
                        }
                    }

                    .arrow-svg {
                        position: absolute;
                        top: 50%;
                        z-index: +5;
                        -webkit-transform: translate(0, -50%);
                        -ms-transform: translate(0, -50%);
                        transform: translate(0, -50%);

                        background-color: rgba(0,0,0,0.1);
                        border-radius: 50%;

                        height: 80px;
                        width: 80px;

                        &:hover {
                            cursor: pointer;
                        }



                        svg {
                            height: 30px;
                            width: 30px;
                            color: white;
                            fill: white;
                        }
                    }
                }
            }
        }
    }
}