import React, { useContext } from "react";
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faXmark } from '@fortawesome/free-solid-svg-icons';

// HELPERS.
const Clicker = React.lazy(() => import('src/helpers/clicker'));
import Modal from 'src/helpers/modal';

// CONTEXT.
import { ReviewsContext } from 'src/modules/reviews/context';

// PARTIALS (STYLES).
import Cards from 'src/modules/reviews/styles/cards';

// SCSS.
import 'src/modules/reviews/lightbox.scss';
import 'src/modules/reviews/slick-styling.scss';

const Start = ({ selected, data, settings, modal, close }) => {
    const reviewsContext = useContext(ReviewsContext);

    if (reviewsContext.selected == false) return null;

    return (
        <div id="modal">
            <div id="reviews-lightbox">
                <Clicker id="lightbox-close" process={() => reviewsContext.setSelected(false)}>
                    <FontAwesomeIcon icon={faXmark} />
                </Clicker>

                <div id="lightbox-container">
                    <Cards item={reviewsContext.selected} full={true} data={data} settings={settings} />
                </div>
            </div>
        </div>
    );
};

export default Start;