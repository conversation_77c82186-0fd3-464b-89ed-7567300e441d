@import 'src/scss/variables.scss';
@import 'src/scss/mixins/modal.scss';

#modal {
    @include modal;

    #reviews-lightbox {
        overflow: hidden;
        position: relative;
        margin: 0px auto 0px;
        height: 100vh;

        #lightbox-close {
            position: absolute;
            z-index: +1;
            top: 20px;
            right: 20px;

            &:hover {
                cursor: pointer;
            }

            @media screen and (max-width: $break-mobile) {
                top: 10px;
                right: 10px;
            }

            svg {
                width: 30px;
                height: 30px;
                color: white;
                fill: white;
                @media screen and (max-width: $break-mobile) {
                    color: black;
                    fill: black;
                }
            }
        }

        #lightbox-container {
            position: relative;
            height: 100vh;

            .reviews-item {
                margin: 150px auto;
                height: unset;

                @media screen and (max-width: $breakpoint-mobile) {
                    margin: unset;
                }
            }
        }
    }
}
