import React, { useState, useEffect, useContext } from 'react';
import { decode } from 'html-entities';
import HtmlParser from 'src/helpers/html-parser'
import { useInView } from 'react-intersection-observer';
import { Coloring } from 'src/helpers';
// Context
import { LocationSelectorContext, LocationSelectorProvider } from 'src/partials/location-selector/context';
// Components
const GoogleMap = React.lazy(() => import('src/partials/location-selector/sections/location-map'));
const LocationSelector = React.lazy(() => import('src/partials/location-selector'));
// Styles 
import * as S from './styles';

const Start = ({ data, settings, placeholders }) => {
    const [currentLocation, setCurrentLocation] = useState(settings.current_location ? settings.current_location : data.locations[0]?.slug);
    const [location, setLocation] = useState(settings.current_location ? settings.locations?.coordinates?.filter(item => item.slug.includes(settings.current_location)) : data.locations)
    const title = (placeholders && data.title_selection === 'dynamic') ? placeholders.single_line[data.title] : data.title;
    const bgImage = (placeholders && data.background_image_selection === 'dynamic') ? placeholders.image[data?.background_image_dynamic] : data?.background_image

    useEffect(() => {
        setCurrentLocation(settings.current_location ? settings.current_location : data.locations[0]?.slug);
        setLocation(settings.current_location ? settings.locations?.coordinates?.filter(item => item.slug.includes(settings.current_location)) : data.locations)
    }, [settings.current_location]);

    const { ref, inView } = useInView({
        triggerOnce: true,
        fallbackInView: true
    });

    return (
        <S.LocationMapModule
            ref={ref}
            className={`location-map-module ${data.background_type ?? ''} ${data.display_type}`}
            bgColor={Coloring(data.background_color, settings)}
            bgImage={bgImage ? bgImage.url : ''}
            textColor={data.background_value === 'dark' ? '#fff' : settings.design?.colors?.body_copy_color}
        >
            {inView ?
                <div className={`grid-container`}>
                    <div class='grid-x grid-margin-x'>
                        {title &&
                            <div class={`title cell align-${data.title_alignment}`}>
                                <h2>{decode(title)}</h2>
                            </div>
                        }
                        {data.copy &&
                            <div className={`cell${data.display_type !== 'all-locations' ? ' medium-6' : ''}`}>
                                <div className='content-container'>
                                    <HtmlParser html={data.copy} placeholders={placeholders} />
                                </div>
                            </div>
                        }
                        {(data.display_type !== 'all-locations' && currentLocation && location) &&
                            <LocationSelectorProvider data={data}>
                                <S.LocationMap className={`cell${data.copy ? ' medium-6' : ''}`}>
                                    <GoogleMap focused={currentLocation} locations={location} setFocused={false} />
                                </S.LocationMap>
                            </LocationSelectorProvider>
                        }
                        {(data.display_type === 'all-locations') &&
                            <LocationSelector data={data} />
                        }
                    </div>
                </div>

                : null}
        </S.LocationMapModule>
    )
}

export default Start;