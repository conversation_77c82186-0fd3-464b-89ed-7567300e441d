import styled from 'styled-components';

export const LocationMapModule = styled.div`
    color: ${props => props.textColor};
    &.color {
        background-color: ${props => props.bgColor};
    }
    &.image {
        background-position: center center;
        background-size: cover;
        background-repeat: no-repeat;
        background-image: url(${props => props.bgImage});
    }
    &.all-locations {
        padding: 3rem 0;
        .content-container {
            margin-bottom: 2rem; 
        }
        .no-locations {
            margin: .5rem;
        }
    }
    &:not(.all-locations) {
        @media (max-width: 639px) {
            padding: 3rem 0;
            .content-container {
                margin-bottom: 3rem;
            }
        }
        .title {
            margin-bottom: 1rem;
        }
        @media (min-width: 640px) {
            padding: 0 0 3rem;
            .content-container {
                margin-top: 3rem;
            }
        }
        @media (min-width: 1200px) {
            padding: 0;
            .content-container {
                margin-top: 5rem;
            }
        }
    }
`

export const LocationMap = styled.div`
    .google-map {
        height: 485px;
        @media (min-width: 640px) {
            height: 700px;
        }
        @media (min-width: 1200px) {
            height: 1000px;
        }
    }
`