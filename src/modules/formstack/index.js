/*******************************************************************************************************
   Copyright 2021 - Calise Partners LLC d/b/a Imaginuity. All rights are reserved.
   Reproduction or transmission in whole or in part, in any form or by any means, electronic, mechanical
or otherwise, is prohibited without the prior written consent of Calise Partners LLC d/b/a Imaginuity.
********************************************************************************************************
   Project : 20-COOK-13575_Cook_and_Boardman_Pylot_ABS
   Author : Imaginuity Developers (<PERSON>, <PERSON>, <PERSON>)
   Description : creates one/two column layout woth formstack form
   Creation Date : Fri Feb 5 2021
   Documentation : [documentation url - point to default wiki for project if no specific documentation for script]
 ********************************************************************************************************/

import React, { useState, useEffect } from "react";
import postscribe from "postscribe";

import "./index.scss";

var shared = {};

const Start = ({ data, settings }) => {
  const [firstContent, setFirstContent] = useState([]);
  const [number] = useState(Math.floor(Math.random() * 10000));

  shared = { colors: settings.design?.colors };

  var backgroundStyles = {
    backgroundImage: data.background_image.url
      ? `url(${data.background_image.url})`
      : null,

    backgroundPosition: data.background_image.url ? "50% 50%" : null,
    backgroundSize: data.background_image.url ? "cover" : null,
    backgroundRepeat: data.background_image.url ? "no-repeat" : null,
    backgroundColor:
      data.background_color === "primary_color"
        ? shared.colors.primary_color
        : data.background_color === "secondary_color"
          ? shared.colors.secondary_color
          : data.background_color === "tertiary_color"
            ? shared.colors.tertiary_color
            : data.background_color === "background_color"
              ? shared.colors.background_color
              : data.background_color === "body_copy_color"
                ? shared.colors.body_copy_color
                : data.background_color === "none"
                  ? "#fff"
                  : "#fff",
  };
  let role = data.background_image ? 'img' : null;
  let bgAlt = (data.background_image && data.background_image.alt) ? data.background_image.alt : null;

  if (data.background_value === "light") {
    var title = {
      color: shared.colors.secondary_color,
    };
    var bodyTextColor = {
      color: shared.colors.secondary_color,
    };
  } else if (data.background_value === "dark") {
    var title = {
      color: "#fff",
    };

    var bodyTextColor = {
      color: "#fff",
    };
  }

  var bodyCopy = bodyTextColor;
  var titleStyles = title;

  useEffect(() => {
    if (data.display_options === "one-column") {
      postscribe("#formstack-code-one-col", data.columns[0].formstack_code);
    } else if (data.display_options === "two-column") {
      postscribe("#formstack-code-two-col-0", data.columns[0].formstack_code);
      postscribe("#formstack-code-two-col-1", data.columns[1].formstack_code);
      setFirstContent(TwoColumnContent(data.columns));
    }
  }, []);

  // refactoring func a bit, bit quickest way to seperate two columns content with the form condition
  const TwoColumnContent = (contentData) => {
    var i;
    let contentObj;
    var contentOne;
    var contentTwo;
    for (i = 0; i <= contentData.length; i++) {
      contentOne = contentData[0];
      contentTwo = contentData[1];
    }
    return (contentObj = {
      firstData: contentOne.content,
      secondData: contentTwo.content,
    });
  };

  return (
    <div className="outer-module-container">
      {data.display_options === "one-column" ? (
        <div
          className={`formstack-module-container ${data.background_color}`}
          role={role}
          aria-label={bgAlt}
          style={backgroundStyles}
        >
          <div className="formstack-box">
            {
              data.title ? <div id={`title-${number}`}>
                <div className="formstack-title">
                  <h2 style={titleStyles}>{data.title}</h2>
                </div>
              </div> : null
            }
            <div className={`flexbox ${data.display_options}`}>
              <div className="flex1 ">
                <div className="formstack-body " style={bodyCopy}>
                  <div
                    dangerouslySetInnerHTML={{
                      __html: data.columns[0].content,
                    }}
                  />

                  <div id="formstack-code-one-col" />
                </div>
              </div>
            </div>
          </div>
        </div>
      ) : (
        <div
          className={`formstack-module-container ${data.background_color}`}
          style={backgroundStyles}
        >
          <div className="formstack-box">
            {
              data.title ? <div id={`title-${number}`}>
                <div className="formstack-title">
                  <h2 style={titleStyles}>{data.title}</h2>
                </div>
              </div> : null
            }
            <div className={`flexbox ${data.display_options}`}>
              <div className="flex1 ">
                <div className="formstack-body " style={bodyCopy}>
                  <div class="column-one">
                    <div
                      dangerouslySetInnerHTML={{
                        __html: firstContent.firstData,
                      }}
                    />
                    <div id="formstack-code-two-col-0" />
                  </div>
                </div>
              </div>
              <div class="flex1">
                <div className="formstack-body " style={bodyCopy}>
                  <div class="column-two">
                    <div
                      dangerouslySetInnerHTML={{
                        __html: firstContent.secondData,
                      }}
                    />
                    <div id="formstack-code-two-col-1" />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Start;
