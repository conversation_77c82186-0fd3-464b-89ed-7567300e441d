/*******************************************************************************************************
   Copyright 2021 - Calise Partners LLC d/b/a Imaginuity. All rights are reserved. 
   Reproduction or transmission in whole or in part, in any form or by any means, electronic, mechanical 
or otherwise, is prohibited without the prior written consent of Calise Partners LLC d/b/a Imaginuity.
********************************************************************************************************
   Project : 20-COOK-13575_Cook_and_Boardman_Pylot_ABS
   Author : Imaginuity Developers (<PERSON>, <PERSON>, <PERSON>)
   Description : styles for formstack module
   Creation Date : Fri Feb 5 2021
   Documentation : [documentation url - point to default wiki for project if no specific documentation for script]
 ********************************************************************************************************/

#modules-container {
  .formstack-module-container {
    margin: 0px 0px;
    width: 100%;
    z-index: 1000;
    position: relative;

    @media screen and (max-width: 767px) {
      margin-top: 4rem;
    }

    .formstack-box {
      margin: 0px auto;
      padding: 25px 40px;
      max-width: 1250px;

      .flexbox.half-half {
        @media screen and (max-width: 600px) {
          flex-direction: column;
        }
      }

      .flexbox.half-half {
        @media screen and (max-width: 600px) {
          flex-direction: column;
        }
      }

      .formstack-title {
        margin-bottom: 20px;
        font-size: 2rem;

        @media screen and (max-width: 800px) {
          padding: 0px 0px;
          font-size: 2rem;
        }

        @media screen and (max-width: 600px) {
          padding: 0px 0px;
          font-size: 1.625rem;
        }
      }

      .formstack-body {
        font-size: 1.125rem;
        line-height: 36px;
        padding-right: 20px;

        p {
          &:last-child {
            margin-block-end: 0px;
          }
        }

        @media screen and (max-width: 800px) {
          margin: 0px 0px;
          padding: 0px 0px 20px;
          font-size: 1rem;
        }

        @media screen and (max-width: 600px) {
          margin: 0px 0px;
          padding: 0px 0px 20px;
          font-size: 0.875rem;
          line-height: 24px;
        }

        #formstack-code-one-col,
        #formstack-code-two-col-0,
        #formstack-code-two-col-1 {
          .fsBody {
            padding: 0;
            margin-top: 2rem;

            .fsForm {
              margin: 0;
              padding: 0;
            }
          }
        }

        img {
          width: 100%;
          height: 100%;
        }

        @media screen and (min-width: 768px) {
          .column-two {
            margin-left: 1rem;
          }
        }
      }

      .flexbox.half-half .formstack-body {
        &:not(:last-child) {
          padding-bottom: 30px !important;
        }
        &:not(:last-child) {
          margin-right: 30px !important;
          @media screen and (max-width: 600px) {
            margin-right: 0px !important;
            &:not(:last-child) {
              border-bottom: 2px solid rgba(211, 211, 211, 0.2) !important;
            }
          }
        }
      }
      .two-column {
        @media screen and (max-width: 767px) {
          display: block;
        }
      }
    }
  }
}
