@import "src/scss/variables";

.parallax-image {
    // height: 270px;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    &:not(&.side-to-side) {
        img {
            max-width: 1250px;
            transform: translate3d(0, 0, 0);
            -webkit-transform: translate3d(0, 0, 0);
        }
        @media (min-width: $break-desktop) {
            img {
                max-width: none;
                width: 100%;
            }
        }
    }
    &.zoom {
        position: relative;
        height: 600px;
        img {
            min-height: 100%;
            min-width: 100%;
            object-fit: contain;
        }
    }
}
.react-parallax {
    img {
        max-width: none;
    }
}