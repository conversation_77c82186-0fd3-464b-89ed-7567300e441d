import React, { useState, useEffect, useRef } from 'react';
import { Parallax } from 'react-parallax';
import { useInView } from 'react-intersection-observer';
// Helpers
import { BackgroundColor } from 'src/helpers/theme';
const Imaging = React.lazy(() => import('src/helpers/imaging'));
import HtmlParser from 'src/helpers/html-parser'
import { ScrollHandler } from 'src/helpers/scroll';
// Hooks
import { elementOnScreen } from 'src/hooks/intersectionObserver';
// Styles
import { ParallaxImage } from './styles';
import './index.scss';

const Start = ({ data, settings, placeholders }) => {
    const heightMobile = data.module_height_mobile ? data.module_height_mobile : 270;
    const heightDesktop = data.module_height_desktop ? data.module_height_desktop : 270;
    const image = (placeholders && data.image_selection === 'dynamic') ? placeholders.image[data.image_dynamic] : data.image

    const { ref, inView } = useInView({
        triggerOnce: true,
        fallbackInView: true
    });

    return (
        <>
            {data.type === 'scroll' &&
                <Parallax bgImage={image?.url} bgImageAlt={image?.alt} strength={425}>
                    <ParallaxImage
                        ref={ref}
                        className={`parallax-image`}
                        heightMobile={heightMobile}
                        heightDesktop={heightDesktop}
                    >
                        {inView && data.content ?
                            <div className='grid-container'>
                                <div className='grid-x'>
                                    <div className='cell parallax-content'>
                                        <HtmlParser html={data.content} />
                                    </div>
                                </div>
                            </div>
                            : null}
                    </ParallaxImage>
                </Parallax>
            }
            {data.type === 'side-to-side' &&
                <SideToSide data={data} image={image} heightMobile={heightMobile} heightDesktop={heightDesktop} settings={settings} />
            }
            {data.type === 'zoom' &&
                <Zoom data={data} image={image} />
            }
        </>
    );
}

const SideToSide = ({ data, image, heightMobile, heightDesktop, settings }) => {
    const scroll = ScrollHandler(0);
    const bgColor = data.background_color !== 'custom' ? BackgroundColor(data.background_color) : data.custom_background;
    const gridContainer = (settings?.mvk_theme_config?.layout?.site_max_width ? settings?.mvk_theme_config?.layout?.site_max_width : 75) * 16;
    const speedMultiplier = 2;
    const { ref, inView } = useInView({
        triggerOnce: true,
        fallbackInView: true
    });

    return (
        <ParallaxImage
            ref={ref}
            className={`parallax-image side-to-side ${data.background_type} ${inView ? 'add-bg-image' : ''}`}
            backgroundColor={bgColor}
            backgroundImage={data.background_type === 'image' ? data.background_image?.url : ''}
            heightMobile={heightMobile}
            heightDesktop={heightDesktop}
        >
            {inView ?
                <Parallax
                    renderLayer={percentage => {
                        let slide = data.direction === 'left-right' ? (gridContainer * percentage) * speedMultiplier - (gridContainer * 1.25) : (gridContainer * -percentage) * speedMultiplier + (gridContainer * 1.25);
                        return (
                            <div className={`grid-container${data.background_type === 'image' ? ' full' : ''}`}>
                                {image && <Imaging className='moving-image' data={image} style={{ transform: `translate(${slide}px, -50%)`, opacity: scroll ? 1 : 0 }} />}
                            </div>
                        )
                    }}
                ></Parallax>
                : null}
        </ParallaxImage>
    )
}

const Zoom = ({ data, image }) => {
    const preScroll = useRef(null);
    const elemRef = useRef(null);
    const [scale, setScale] = useState(.2);

    useEffect(() => {
        const topPos = (element) => element.getBoundingClientRect().top;
        // set a different initial for mobile
        if (window.innerWidth < 768) {
            setScale(0.75);
        }

        const onScroll = () => {
            const divTopPos = topPos(elemRef.current);
            const scrollPos = preScroll.current > window.scrollY;
            preScroll.current = 1000 - divTopPos;
            // console.log((preScroll.current / 450), preScroll.current, divBotPos, window.innerHeight);
            if (scrollPos == null) {
                return;
            }
            let initial = preScroll.current / 1000;

            // adjust multiplier to change rate at which it will zoom
            let multiplier = 6;
            let scaleMe = initial * (initial * multiplier);
            // console.log((initial * (initial * 1.75)) - sub);
            setScale(scaleMe > .2 ? scaleMe : .2);
        }

        window.addEventListener("scroll", onScroll);
        return () => window.removeEventListener("scroll", onScroll);

    }, []);
    // background color
    let bgColor = data.background_color !== 'custom' ? BackgroundColor(data.background_color) : data.custom_background;
    bgColor = bgColor ? { 'background-color': bgColor } : null;

    return (
        <div ref={elemRef} class={`parallax-image zoom`} style={bgColor}>
            {image && <Imaging data={image} style={{ transform: `scale(${scale})` }} />}
        </div>
    );
}

export default Start;