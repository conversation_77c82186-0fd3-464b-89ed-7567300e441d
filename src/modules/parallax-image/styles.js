import styled from 'styled-components';

export const ParallaxImage = styled.div`
    min-height: ${props => props.heightMobile ?? '270'}px;
    @media (min-width: 1024px) {
        min-height: ${props => props.heightDesktop ?? '475'}px;
    }
    .parallax-content {
        padding: 1rem 0;
    }
    &.side-to-side {
        position: relative;
        background-color: ${props => props.backgroundColor};
        &.image {
            background-position: center center;
            background-size: cover;
            background-repeat: no-repeat;
            &.add-bg-image {
                background-image: url(${props => props.backgroundImage});
            }
        }
        .react-parallax {
            width: 100%;
            min-height: ${props => props.heightMobile ?? '270'}px;
            @media (min-width: 1024px) {
                min-height: ${props => props.heightDesktop ?? '475'}px;
            }
        }
        .moving-image {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-2000%, -50%);
        }
        .grid-container {
            width: 100%;
            min-height: 200px;
            position: relative;
            overflow: hidden;
            box-sizing: border-box;
            min-height: ${props => props.heightMobile ?? '270'}px;
            @media (min-width: 1024px) {
                min-height: ${props => props.heightDesktop ?? '475'}px;
            }
        }
        &.color {
            .grid-container {
                &:before {
                    content: '';
                    position: absolute;
                    height: 100%;
                    width: 100px;
                    background: linear-gradient(to right, ${props => props.backgroundColor}, transparent);
                    left: 0;
                    top: 0;
                    z-index: 1;
                }
                &:after {
                    content: '';
                    position: absolute;
                    height: 100%;
                    width: 100px;
                    background: linear-gradient(to left, ${props => props.backgroundColor}, transparent);
                    right: 0;
                    top: 0;
                }
            }
        }
    }
`