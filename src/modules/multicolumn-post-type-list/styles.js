import styled from 'styled-components';

export const MulticolumnPTL = styled.div`
    padding: 2rem 0;
    color: ${props => props.textColor};
    &.color {
        background-color: ${props => props.bgColor};
    }
    &.image {
        background-image: url(${props => props.bgImage});
        background-position: center center;
        background-size: cover;
        background-repeat: no-repeat;
    }
    .title-container {
        margin-bottom: 1rem;
        h2 {
            margin-bottom: 1rem;
        }
    }
    .post {
        > a {
            color: ${props => props.textColor};
        }
        .post-title, .post-link {
            display: inline-block;
            color: ${props => props.primaryColor};
            margin-bottom: .5rem;
            font-weight: bold;
        }
        .locked {
            margin-left: .5rem;
        }
    }
`

export const Columns = styled.div`
   
`

export const Column = styled.div`
    margin-bottom: 2rem;
    &.type-image {
        display: flex;
        &.center {
            align-items: center;
        }
        &.bottom {
            align-items: flex-end;
        }
    }
    .column-button {
        margin-top: 1rem;
    }
`

export const Posts = styled.div`
  
`

export const Post = styled.div`
    margin-bottom: 2rem;
    width: 100%;
    .excerpt {
        margin-bottom: 1rem;
    }
`

export const PostWithImage = styled.div`
    a {
        .image-wrapper {
            position: relative;
            .image-container {
                height: 200px;
                box-shadow: 1px 2px 5px rgba(0,0,0,.3);
                margin-bottom: 1rem;
                overflow: hidden;
                img {
                    width: 100%;
                    height: 100%;
                    object-fit: cover;
                    transition: .3s;
                }
            }
            .date-block {
                position: absolute;
                bottom: 0;
                padding: 5px 10px;
                font-weight: bold;
                font-size: 1.25rem;
            }
        }
        &:hover {
            .image-wrapper .image-container img {
                transform: scale(1.1);
            }
        }
    }
`

export const PostList = styled.div`

`