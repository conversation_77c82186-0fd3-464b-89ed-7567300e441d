import React, { useContext } from 'react';
import { decode } from 'html-entities';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faLock } from '@fortawesome/free-solid-svg-icons'
// Context
import { SettingsContext } from "src/context";
// Helpers
const Clicker = React.lazy(() => import('src/helpers/clicker'));
import { DateTime } from 'src/helpers/date';
// Styles
import * as S from '../styles';


const Start = ({ data, post }) => {
    const [settings, setSettings] = useContext(SettingsContext);
    const url = (data.source === 'api' && data.post_type === 'event') ? `${settings?.module_api?.base_url}/${post.url}` : post.url;
    const excerpt = (data.source === 'api' && data.post_type === 'event') ? post.location : post.exerpt;

    return (
        <S.PostList className='post'>
            {post.title && <Clicker className={`post-link`} type='anchor' url={url} target={post.external_link} ariaLabel={`link to ${post.title}`}><h4 className={`post-title`}>{decode(post.title)}{post.locked && <FontAwesomeIcon icon={faLock} className="locked" />}</h4></Clicker>}
            {!data.hide_date && <DateTime data={data} post={post} type={'full'} />}
            {excerpt && <div className='excerpt' dangerouslySetInnerHTML={{ __html: excerpt }} />}
            {post.url && <Clicker className={`post-link`} type='anchor' url={url} target={post.external_link} ariaLabel={`link to ${post.title}`}>{post.link_text_override ? decode(post.link_text_override) : decode(data.link_text)}</Clicker>}
        </S.PostList >
    )
}

export default Start;