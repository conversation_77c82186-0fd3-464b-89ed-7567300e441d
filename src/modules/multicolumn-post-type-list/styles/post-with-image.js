import React, { useContext } from 'react';
import { decode } from 'html-entities';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faLock } from '@fortawesome/free-solid-svg-icons'
// Context
import { SettingsContext } from "src/context";
// Helpers
const Imaging = React.lazy(() => import('src/helpers/imaging'));
const Clicker = React.lazy(() => import('src/helpers/clicker'));
import { DateTime } from 'src/helpers/date';
// Styles
import * as S from '../styles';

const Start = ({ data, post }) => {
    const [settings, setSettings] = useContext(SettingsContext);
    const featuredImage = post.feat_image;
    const usesImageTag = (data.post_type === 'event' && data.source === 'pylot') || data.post_type === 'post';
    const usesImageHelper = (data.post_type === 'event' && data.source === 'api');
    const url = (data.source === 'api' && data.post_type === 'event') ? `${settings?.module_api?.chamber_base_url}/${post.url}` : post.url;
    const excerpt = (data.source === 'api' && data.post_type === 'event') ? post.location : post.exerpt;

    return (
        <S.PostWithImage className='post'>
            <Clicker type='anchor' url={url} target={post.external_link} ariaLabel={`link to ${post.title}`}>
                {featuredImage &&
                    <div className='image-wrapper'>
                        {usesImageTag && <div className='image-container' dangerouslySetInnerHTML={{ __html: featuredImage }} />}
                        {usesImageHelper &&
                            <div className='image-container'>
                                <Imaging data={{ url: featuredImage, alt: `featured image for ${decode(post.title)}` }} />
                            </div>
                        }
                        {(!data.hide_date && data.post_type === 'event') && <DateTime data={data} post={post} type={'block'} />}
                    </div>
                }
                {post.title && <><h3 className={`post-title`}>{decode(post.title)}{post.locked && <FontAwesomeIcon icon={faLock} className="locked" />}</h3></>}
                {!data.hide_date && <DateTime data={data} post={post} type={'full'} />}
                {excerpt && <div className='excerpt' dangerouslySetInnerHTML={{ __html: excerpt }} />}
                <div className='post-link'>{post.link_text_override ? decode(post.link_text_override) : decode(data.link_text)}</div>
            </Clicker>
        </S.PostWithImage>
    )
}

export default Start;