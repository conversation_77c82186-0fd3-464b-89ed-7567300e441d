import React from 'react';
import { decode } from 'html-entities';
import { useInView } from 'react-intersection-observer';
// Helpers
import { Coloring } from "src/helpers";
const Imaging = React.lazy(() => import('src/helpers/imaging'));
const HtmlParser = React.lazy(() => import('src/helpers/html-parser'));
import { PTLColumns, PTLPosts } from 'src/helpers/foundation';
// Partials
const Button = React.lazy(() => import('src/partials/button'));
// Styles
import * as S from './styles';
const PostList = React.lazy(() => import('./styles/post-list'));
const PostWithImage = React.lazy(() => import('./styles/post-with-image'));

const Start = ({ data, settings }) => {
    const { ref, inView } = useInView({
        triggerOnce: true,
        fallbackInView: true
    });

    return (
        <S.MulticolumnPTL
            ref={ref}
            className={`multicolumn-post-type-list ${data.background_type}`}
            bgColor={Coloring(data.background_color, settings)}
            textColor={data.background_value === 'dark' ? '#fff' : Coloring('body_copy_color', settings)}
            bgImage={data.background_type === 'image' ? data.background_image?.url : ''}
            primaryColor={data.background_value === 'dark' ? '#fff' : Coloring('primary_color', settings)}
        >
            {inView ?
                <div className="grid-container">
                    {(data.title || data.blurb || data.button) &&
                        <div className="grid-x title-container">
                            {data.title &&
                                <div className={`cell ${data.title_alignment}`}>
                                    <h2 className={`module-title ${data.background_value === 'dark' ? 'white-txt' : 'primary-txt'}`}>{decode(data.title)}</h2>
                                </div>
                            }
                            {data.blurb &&
                                <div className="cell blurb"><HtmlParser html={data.blurb} /></div>
                            }
                            {data.button &&
                                <div className={`cell ${data.button_alignment}`}>
                                    <Button class='row-button' title={data.button?.title} url={data.button?.url} target={data.button?.target} type={data.button_style} tone={data.background_value} />
                                </div>
                            }
                        </div>
                    }
                    {data.columns && <Columns data={data} />}
                </div>
                : null}
        </S.MulticolumnPTL>
    );
}

const Columns = ({ data }) => {
    return (
        <S.Columns className={`grid-x grid-margin-x ${data.layout_options}`}>
            {data.columns && data.columns.map((column, i) => <Column data={data} column={column} index={i} />)}
        </S.Columns>
    )
}

const Column = ({ data, column, index }) => {

    const columnClass = PTLColumns(data.layout_options, index + 1);

    return (
        <S.Column
            key={`column-${index}`}
            className={`column-${index} ${columnClass} ${column.content_type === 'image' ? `type-image ${column.image_vertical_alignment}` : `type-${column.content_type}`}`}
        >
            <div className='inner-wrapper'>
                {(column.column_blurb && column.content_type !== 'image') &&
                    <div className='grid-x'>
                        <div className="cell column-blurb"><HtmlParser html={column.column_blurb} /></div>
                    </div>
                }
                {column.content_type === 'post' && <Posts data={data} column={column} index={index} />}
                {(column.content_type === 'image' && column.featured_image) && <div className={`image-wrapper`}><Imaging data={column.featured_image} /></div>}
                {column.button &&
                    <div className='grid-x'>
                        <div className={`cell ${column.button_alignment}`}>
                            <Button class='column-button' title={column.button?.title} url={column.button?.url} target={column.button?.target} type={column.button_style} tone={data.background_value} />
                        </div>
                    </div>
                }
            </div>
        </S.Column>
    )
}

const Posts = ({ data, column, index }) => {
    const postClass = column.style === 'post-with-image' ? PTLPosts(data.layout_options, index + 1) : 'cell'

    const posts = column.post_type === 'event' ? column.posts && column.posts.sort((a, b) => {
        switch (column.sorting) {
            case 'date-desc':
                var dateA = new Date((a.next_occurrence && a.next_occurrence.start) ? a.next_occurrence.start.event_date : a.start.event_date);
                var dateB = new Date((b.next_occurrence && b.next_occurrence.start) ? b.next_occurrence.start.event_date : b.start.event_date);
                return dateB - dateA;
            case 'date-asc':
            default:
                var dateA = new Date((a.next_occurrence && a.next_occurrence.start) ? a.next_occurrence.start.event_date : a.start.event_date);
                var dateB = new Date((b.next_occurrence && b.next_occurrence.start) ? b.next_occurrence.start.event_date : b.start.event_date);
                return dateA - dateB;
        }
    }) : column.posts;
    return (
        <S.Posts className={`grid-x grid-margin-x posts ${column.style}`}>
            {posts && posts.map((post, i) => <Post data={column} post={post} index={i} postClass={postClass} />)}
        </S.Posts>
    )
}

const Post = ({ data, post, index, postClass }) => {

    return (
        <S.Post
            key={`post-${index}`}
            className={postClass}
        >
            {data.style === 'post-with-image' && <PostWithImage data={data} post={post} />}
            {data.style === 'post-list' && <PostList data={data} post={post} />}
        </S.Post>
    )
}




export default Start;