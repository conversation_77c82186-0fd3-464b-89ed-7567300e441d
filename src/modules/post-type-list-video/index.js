import React, { useState } from "react";
import { decode } from 'html-entities';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faCirclePlay } from '@fortawesome/free-regular-svg-icons';
import { useInView } from 'react-intersection-observer';

// Helpers
const Imaging = React.lazy(() => import('src/helpers/imaging'));
import { Coloring } from "src/helpers";

// Partials
const Button = React.lazy(() => import('src/partials/button'));
import { Video } from 'src/partials/video';
// Styles
import { PTLVideo, FullwidthWrapper } from './styles';

const Start = ({ data, settings }) => {
    const bgColor = Coloring(data.background_color, settings);
    const { ref, inView } = useInView({
        triggerOnce: true,
        fallbackInView: true
    });
    return (
        <PTLVideo
            ref={ref}
            className={`ptl-video ${data.background_type}`}
            backgroundColor={bgColor}
            backgroundImage={data.background_type === 'image' ? data.background_image?.url : ''}
            textColor={data.background_value === 'dark' ? '#fff' : Coloring('body_copy_color', settings)}
        >
            {inView ? <>
                <div className="title-container grid-container">
                    <div className='grid-x'>
                        {data.title &&
                            <div className={`cell ${data.title_alignment}`}>
                                <h2>{decode(data.title)}</h2>
                            </div>
                        }
                        {data.copy &&
                            <div className='cell'>
                                <div className='copy' dangerouslySetInnerHTML={{ __html: data.copy }} />
                            </div>
                        }
                    </div>
                </div>
                <VideoContainer data={data} settings={settings} />
                {data.button &&
                    <div className={`button-container grid-container ${data.title_alignment}`}>
                        <Button className='module-button' url={data.button?.url} target={data.button?.target} title={data.button?.title} type={data.button_style} tone={data.background_value} />
                    </div>
                }
            </> : null}
        </PTLVideo>
    )

}

const VideoContainer = ({ data, settings }) => {
    switch (data.style) {
        case 'full_width':
            return (<Fullwidth data={data} settings={settings} />);
        case 'four_column':
            return (<FourColumn data={data} settings={settings} />);
        default:
            return null;
    }
}

export const Fullwidth = ({ data, settings, property }) => {
    const [videoLightbox, setLightBox] = useState(false);

    const toggleVideo = () => {
        setLightBox(videoLightbox ? false : true);
    }

    return (
        <FullwidthWrapper
            className='fullwidth-video'
        >
            <div className='inner-wrapper'>
                {data.video?.image_overlay &&
                    <Imaging className='overlay' data={data.video?.image_overlay} />
                }
                {settings.video?.play_button ?
                    <Imaging className='play-button' data={settings.video?.play_button} onClick={toggleVideo} />
                    :
                    <FontAwesomeIcon className='play-button' icon={faCirclePlay} onClick={toggleVideo} />
                }
            </div>
            <div className='grid-container content-wrapper'>
                {!property &&
                    <div className={`top ${data.background_value === 'dark' ? 'white-txt' : 'primary-txt'}`}>
                        <div className='label'>{data.label || 'VIDEO'}</div>
                        {data.show_post_date &&
                            <div className='date'>{data.video?.post_date}</div>
                        }
                    </div>
                }
                <div className='title'>
                    <h3>{decode(data.video?.title)}</h3>
                    {data.video?.blurb &&
                        <p>{decode(data.video?.blurb)}</p>
                    }
                </div>
            </div>
            {videoLightbox &&
                <Video data={data} closeVideo={toggleVideo} />
            }
        </FullwidthWrapper>
    )
}

const FourColumn = ({ data, settings }) => {
    return null; // Do later, whatever this mysterious style is.
}
export default Start;