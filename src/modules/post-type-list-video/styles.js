import styled from 'styled-components';

export const PTLVideo = styled.div`
    padding: 2rem 0;
    color: ${props => props.textColor};
    &.color {
        background-color: ${props => props.backgroundColor};
    }
    &.image {
        background-image: url(${props => props.backgroundImage});
        background-position: center center;
        background-size: cover;
        background-repeat: no-repeat;
    }
    .title-container {
        margin-bottom: 1rem;
        .h2 {
            margin-bottom: 1rem;
        }
    }
    @media (min-width: 768px) {
        .button-container {
            margin-top: 2rem;
        }
    }
`;

export const FullwidthWrapper = styled.div`
    position: relative;
    .inner-wrapper {
        overflow: hidden;
        min-height: 350px;
        position: relative;
        margin-bottom: 1rem;
        .overlay {
            position: absolute;
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        .play-button {
            cursor: pointer;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%,-50%);
            max-width: 150px;
            z-index: 1;
            &.fa-circle-play {
                color: #fff;
                font-size: 6rem;
            }
        }
        @media (min-width: 768px) {
            min-height: 400px;
            margin-bottom: 0;
        }
        @media (min-width: 1200px) {
            min-height: 460px;
        }
    }
    .content-wrapper {
        margin-bottom: 2rem;
        .top {
            display: flex;
            font-size: .875rem;
            margin-bottom: .5rem;
            .label {
                margin-right: .5rem;
            }
        }
        @media (min-width: 768px) {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: #fff;
            width: 100%;
            box-sizing: border-box;
            .top {
                color: #fff;
            }
            .title {
                max-width: 40%;
            }
        }
    }
`;
