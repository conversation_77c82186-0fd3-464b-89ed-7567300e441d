import styled from 'styled-components';

export const FormModule = styled.div`
    color: ${props => props.color};
    &.color {
        background-color: ${props => props.backgroundColor};
    }
    &.image {
        background-image: url(${props => props.backgroundImage});
        background-position: center center;
        background-size: cover;
        background-repeat: no-repeat;
    }
    
`;

export const FormContainer = styled.div`
    position: relative;
    &.dark {
        form .form-group .error, .star {
            color: #fff !important;
        }
    }
    form {
        .form-group, .form-group.type-radio {
            label, .label {
                span {
                    color: ${props => props.color};
                }
                input, textarea, select, .floater, .label, .radio {
                    border-color: ${props => props.color};
                }
            }
            &.type-radio {
                 .button-wrap {
                    position: relative;
                    .label {
                        color: ${props => props.color};
                    }
                    .radio {
                        color: ${props => props.color};            
                    }
                    .error {
                        bottom: -10px;
                    }
                }
                &.vertical .button-wrap {
                    display: block;
                }
            }
            &.type-checkbox, &.type-html {
                .label, .checkbox label  {
                    color: ${props => props.color};
                }
            }
            &.type-date .focused {
                input[type=date]::-webkit-datetime-edit {
                    color: ${props => props.color};
                }
            }
        }
        &.style-underlined {
            .form-group {
                label {
                    input, textarea, select {
                        color: ${props => props.color};
                    }
                }
            }
        }
        &.style-box {
            .form-group {
                label {
                    input, textarea, select {
                        background: #fff;
                        color: ${props => props.bodyCopyColor};
                        border: none;
                        &::placeholder {
                            color: ${props => props.bodyCopyColor};
                        }
                    }
                }
            }
        }
        .fields.hide-while-loading {
            opacity: 0;
            transition: .2s;
        }
    }
    #loading {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%,-50%);
    }
`;