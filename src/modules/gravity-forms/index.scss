@import "src/scss/variables.scss";

$colorDark: #1b2635;
.form-module {
    padding-top: 2rem;
    padding-bottom: 2rem;
    @media (max-width: $break-large) {
        & > .grid-x > .cell.form-left:first-of-type {
            order: 2;
        }
    }
    .star {
        color: #b60000 !important;
    }
    .title {
        margin-bottom: 1.5rem;
    }
    .blurb {
        line-height: 1.5;
    }
    form {
        .form-group {
            margin-bottom: 0.25rem;
            label {
                position: relative;
                span {
                    // color: $colorDark;
                    font-size: 1rem;

                    &.floater {
                        position: absolute;
                        bottom: 27px;
                        transition: 0.3s;
                        transform-origin: left;
                        pointer-events: none;
                    }
                }

                input,
                textarea,
                select {
                    border: none;
                    border-bottom: 1px solid;
                    margin: 0.25rem auto 1.5rem;
                    padding: 0.5rem 0 0.25rem;
                    font-size: 1rem;
                    width: 100%;
                }

                &.focused {
                    span.floater {
                        transform: scale(0.75) translateY(-30px);
                    }
                }
            }
            &.type-textarea {
                margin-top: 1rem;
                label.focused span.floater {
                    transform: scale(0.75) translateY(-64px);
                }
            }
            &.type-radio {
                .button-wrap {
                    display: flex;
                    flex-wrap: wrap;
                    .label {
                        font-size: 1rem;
                        width: 100%;
                        margin-bottom: 1rem;
                    }
                    .radio {
                        display: flex;
                        font-size: 1rem;
                        margin-right: 0.5rem;
                        input {
                            width: auto;
                            margin-right: 0.25rem;
                        }
                    }
                }
            }
            &.type-checkbox {
                .label {
                    font-size: 1rem;
                    width: 100%;
                    margin-bottom: 1rem;
                }
                .checkbox {
                    margin: 0.5rem 0;
                    label {
                        display: flex;
                        align-items: center;
                        font-size: 1rem;
                        input {
                            width: auto;
                            margin: 0 0.5rem 0 0;
                        }
                    }
                }
            }
            &.type-html {
                margin: 0 auto .25rem;
                .label {
                    font-size: 1rem;
                }
            }
            &.type-date {
                input[type="date"]::-webkit-datetime-edit-day-field,
                input[type="date"]::-webkit-datetime-edit-month-field,
                input[type="date"]::-webkit-datetime-edit-year-field,
                input[type="date"]::-webkit-datetime-edit-text,
                input[type="date"]::-webkit-datetime-edit {
                    color: transparent;
                    visibility: hidden;
                }
                .focused {
                    input[type="date"]::-webkit-datetime-edit-day-field,
                    input[type="date"]::-webkit-datetime-edit-month-field,
                    input[type="date"]::-webkit-datetime-edit-year-field,
                    input[type="date"]::-webkit-datetime-edit-text,
                    input[type="date"]::-webkit-datetime-edit {
                        color: initial;
                        visibility: visible;
                    }
                }
            }
            .error {
                font-size: 0.75rem !important;
                position: absolute;
                bottom: -5px;
                color: #b60000;
            }
            &.type-select {
                .error {
                    bottom: -35px;
                }
            }
            &.type-checkbox {
                .error {
                    position: relative;
                }
            }
            &.type-hidden {
                appearance: none;
                visibility: hidden;
                display: none !important;
            }
            &.type-fileupload {
                label {
                    display: block;
                }
                .error {
                    position: relative;
                }
            }
        }
        button[type="submit"] {
            margin: 1rem auto;
        }

        &.style-default,
        &.style-outlined,
        &.style-box {
            .form-group {
                margin-bottom: 0.25rem;
                label {
                    position: relative;
                    span {
                        // color: $colorDark;
                        font-size: 1rem;
                        &.star {
                            color: #b60000;
                        }
                    }

                    input,
                    textarea,
                    select {
                        border: none;
                        border: 1px solid #ccc;
                        box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.08);
                        margin: 0.25rem auto 1rem;
                        min-height: 40px;
                        padding: 0.5rem;
                        font-size: 1rem;
                        width: 100%;
                        &[type="radio"],
                        &[type="checkbox"] {
                            min-height: auto;
                        }
                    }
                }
                &.type-textarea {
                    margin-top: 0;
                }
                .error {
                    margin: 0;
                    bottom: 0;
                    left: 2px;
                }
                &.type-select .error {
                    bottom: -26px;
                }
                &.type-date {
                    input[type="date"]::-webkit-datetime-edit {
                        color: #ccc;
                    }
                }
            }
        }
        &.style-underlined {
            .form-group {
                label {
                    input,
                    textarea,
                    select {
                        background: none;
                        border-radius: 0px !important;
                    }
                }
            }
        }
        &.style-box {
            .form-group {
                label {
                    input,
                    textarea,
                    select {
                        border-radius: 0px !important;
                        min-height: 50px;
                        padding: 1rem 0.5rem;
                        border: none;
                    }
                }
            }
        }
        .mvk-form-control {
            position: absolute;
            right: 1000%;
        }
    }
    .response {
        font-size: 1rem;
        margin-bottom: 1rem;
        a {
            text-decoration: underline;
        }
    }
}

// center fields if form added by shortcode and wysiwyg is centered.
p[style="text-align: center;"] .form-module .fields {
    justify-content: center;
}
