import React, { useState, useEffect, useContext, useMemo, useRef } from "react";
import { useNavigate } from 'react-router-dom';
import { useInView } from 'react-intersection-observer';
// Helpers
import { Coloring } from "src/helpers";
import { scrollIntoViewWithOffset } from "src/helpers/scrollIntoViewWithOffset";
import HtmlParser from 'src/helpers/html-parser';
// APIs
import * as API from "src/api/requests";
import config from 'src/config';
// Partials
const Button = React.lazy(() => import('src/partials/button'));
import Loading from "src/partials/loading";
// CONTEXT.
import { PrincipalContext } from "src/context";
// Styles
import "./index.scss";
import { FormModule, FormContainer } from './styles.js';
// Captcha
import ReCAPTCHA from "react-google-recaptcha";

const Start = ({ data, settings }) => {
    // display options
    const options = data.display_options;
    const position = data.display_position;
    // set column classes
    const column_1 = Columns(options, 1);
    const column_2 = Columns(options, 2);
    // color options
    const bgColor = Coloring(data.background_color, settings);
    const { ref, inView } = useInView({
        triggerOnce: true,
        fallbackInView: true
    });

    return (
        <FormModule ref={ref} className={`form-module ${data.background_type} ${data.background_value}`}
            backgroundColor={bgColor}
            backgroundImage={data.background_type === 'image' ? data.background_image?.url : ''}
            color={data.background_value === 'dark' ? '#fff' : Coloring('body_copy_color', settings)}
        >
            {inView ?
                <div class={`grid-container ${data.restrict_module_width ? 'restricted' : ''}`}>
                    <div class='grid-x grid-margin-x grid-margin-y'>
                        <div class={`${column_1} form-${position}`}>
                            {position === 'right' || options === 'fullwidth' ? <Blurb data={data} /> : <Form module={data} data={data.form} settings={settings} title={data.title} />}
                        </div>
                        <div class={`${column_2} form-${position}`}>
                            {position === 'left' && options !== 'fullwidth' ? <Blurb data={data} /> : <Form module={data} data={data.form} settings={settings} title={data.title} />}
                        </div>
                    </div>
                </div>
                : null}
        </FormModule>
    );
};

const Columns = (options, number) => {
    switch (options) {
        case 'half-half':
            return 'cell small-12 medium-6 large-6';
        case 'two-third-one-third':
            return `cell small-12 ${number === 1 ? 'large-8' : 'large-4'}`;
        case 'one-third-two-third':
            return `cell small-12 ${number === 1 ? 'large-4' : 'large-8'}`;
        default:
            return 'cell small-12';
    };
};

const Blurb = ({ data }) => {
    if (data.blurb) {
        return (
            <div class='blurb-container'>
                <div class='grid-x'>
                    {data.blurb &&
                        <div class='cell blurb'>
                            <HtmlParser html={data.blurb} />
                        </div>
                    }
                </div>
            </div>
        )
    } else {
        return null;
    }
};


export const Form = ({ module, data, settings, title, footerTextColor, bgValue, styleOverride, buttonStyleOverride, buttonTextOverride }) => {
    const [principal, setPrincipal] = useContext(PrincipalContext);
    const [errors, setErrors] = useState({});
    const [response, setResponse] = useState(false);
    const [message, setMessage] = useState(false);
    const [loading, setLoading] = useState(false);
    const [visibility, setVisibility] = useState({});
    const [number] = useState(Math.floor(Math.random() * 10000));
    const [recaptchaError, setRecaptchaError] = useState('');
    const navigate = useNavigate();
    const recaptchaRef = useRef();

    const humanReadableMediumMap = {
        'social': 'Paid Social',
        'cpc': 'Paid Search',
        'display': 'Programmatic Display',
        'direct': 'Direct',
        'audio': 'Audio'
    }

    // set url path for fleet or blank if no extra path neccessarry
    // const fleet_url = settings?.fleet_home_url ? new URL(settings.fleet_home_url).pathname : '';
    // form style from settings
    const formStyle = styleOverride ? styleOverride : settings?.mvk_theme_config?.other?.form_styles;
    const backgroundValue = bgValue ? bgValue : module?.background_value;
    const bodyCopyColor = Coloring('body_copy_color', settings);


    // set errors from response
    useEffect(() => {
        // set errors from response
        if (response && !response.success) {
            setLoading(false);
            if (response.data) {
                let errorArray = [];
                const items = response.data;
                Object.entries(items).forEach(([key, value]) => {
                    errorArray[`input_${key}_${number}`] = value;
                    errorArray[`group-${key}_${number}`] = value;
                });
                setErrors(errorArray)
            }
            setResponse(false);
        }
        // scroll to success message
        if (message) {
            let messageEl = document.getElementById(`message-gform-${data.id}${data.title?.replace(/\s+/g, '')}`);
            scrollIntoViewWithOffset(messageEl, 200, 'smooth');
        }
        if (data.fields) {
            let visibilityArray = [];
            data.fields.map((field, index) => visibilityArray[`input_${field.id}_${number}`] = field.visibility !== 'hidden' && !field.conditionalLogic);
            setVisibility(visibilityArray);
        }
    }, [data, loading, response]);

    // form submit
    const formSubmit = async e => {
        e.preventDefault();
        setLoading(true);
        setRecaptchaError(''); // Clear any previous reCAPTCHA errors
        const hasInvisibleCaptcha = data?.fields?.some(field => field.type === 'imaginuity_recaptcha');

        let recaptchaToken = null;

        if (hasInvisibleCaptcha && recaptchaRef.current) {
            try {
                setRecaptchaError(''); // Clear any previous errors
                recaptchaToken = await recaptchaRef.current.executeAsync();
            } catch (error) {
                console.error('reCAPTCHA execution failed:', error);
                setRecaptchaError('reCAPTCHA verification failed. Please try again.');
                setLoading(false);
                return;
            }
        }

        // convert fields to form data
        var form_data = new FormData(e.target);
        let submitUrl = config.domain;
        let formId = data.id;
        // append query params if they exist in the hidden form fields + some funky stuff for hva
        data.fields.map((field) => {
            // loop through utm params and insert into form data if there's a match with an inputName
            if (field.defaultValue) {
                form_data.set(`input_${field.id}`, field.defaultValue);
            }
            if (sessionStorage['utm_data']) {
                const utmObj = JSON.parse(sessionStorage['utm_data']);
                for (var key in utmObj) {
                    if (field.inputName == 'mvk_click_id' && (key == 'gclid' || key == 'msclkid')) {
                        form_data.set(`input_${field.id}`, utmObj[key]);
                        form_data.append('mvk_click_id', utmObj[key]);
                    } else if (field.inputName == key) {
                        form_data.set(`input_${field.id}`, utmObj[key]);
                        form_data.append(key, utmObj[key]);
                    } else if (field.inputName == 'utm_medium_human' && key == 'utm_medium') {
                        const humanReadable = {
                            'social': 'Paid Social',
                            'cpc': 'Paid Search',
                            'display': 'Programmatic Display',
                            'direct': 'Direct',
                            'audio': 'Audio'
                        }
                        form_data.set(`input_${field.id}`, humanReadable[utmObj[key]] ? humanReadable[utmObj[key]] : 'Landing Page');
                        form_data.append(field.inputName, humanReadable[utmObj[key]] ? humanReadable[utmObj[key]] : 'Landing Page');
                    }
                }
            }
            if (sessionStorage['quiz_results'] && field.inputName === 'quiz_results') {
                form_data.set(`input_${field.id}`, JSON.parse(sessionStorage['quiz_results']));
            }
            // send url / origin if mvk_submit_url hidden field is present
            if (field.inputName == 'mvk_submit_url' && !form_data.has('mvk_submit_url')) {
                let fullUrl = window.location.toString();
                let origin = fullUrl.slice(0, fullUrl.indexOf('?'));
                form_data.set(`input_${field.id}`, origin);
                form_data.append(field.inputName, origin);
            }
            // send document.referrer if mvk_referrer hidden field is present
            if (field.inputName == 'mvk_referrer' && !form_data.has('mvk_referrer')) {
                const referrer = document.referrer || '';
                form_data.set(`input_${field.id}`, referrer);
                form_data.append(field.inputName, referrer);
            }
            // send utm_medium_human as Landing Page if not already set from the humanReadable options above
            if (field.inputName == 'utm_medium_human' && !form_data.has('utm_medium_human')) {
                form_data.set(`input_${field.id}`, 'Landing Page');
                form_data.append(field.inputName, 'Landing Page');
            }

            // if (field.type === 'fileupload') {
            //     if (document.getElementById(`input_${field.id}`).value === '') {
            //         console.log(`input_${field.id}`, form_data)
            //         var parts = [
            //             new Blob(['/public/images/nophoto.png'], { type: 'image/png' })];
            //         // delete form_data[`input_${field.id}`];
            //         // form_data.delete(`input_${field.id}`);
            //         // form_data.set(`input_${field.id}`, new File([""], "", { type: "text/plain" }));
            //         form_data.set(`input_${field.id}`, new File([], "/public/images/nophoto.png"));
            //     }
            // }
        });
        // }
        // append form_id
        form_data.append('form_id', formId);

        if (recaptchaToken) {
            form_data.append('g-recaptcha-response', recaptchaToken);
        }

        let headers = {
            'MVK-BUILDER': settings.mvk_builder ? settings.mvk_builder : 'charter'
        }

        // post form data
        API.FormSubmit(`?site=${submitUrl}&mvk_forms_debug=true`, form_data, headers).then(response => {
            setLoading(false);

            // Check for reCAPTCHA errors in the response
            if (response.debug_result?.recaptcha_error ||
                (response.debug_result?.message && response.debug_result.message.toLowerCase().includes('recaptcha'))) {
                setRecaptchaError(response.debug_result.message || 'reCAPTCHA verification failed. Please try again.');
                return; // Don't proceed with other response handling
            }

            setResponse(response);
            if (response.type) {
                if (response.type === 'page') {
                    navigate(`/${response.page}/`);
                    console.log('navigate');
                } else if (response.type === 'redirect') {
                    window.open(response.url, '_blank');
                    setMessage(response.message);
                    console.log('redirect');
                } else if (response.type === 'message') {
                    setMessage(response.message);
                    console.log('message');
                }
                setPrincipal({ ...principal, isFormSubmitted: true });
            } else if (response.message) {
                setMessage(response.message);
            }

            if (window?.dataLayer) {
                window?.dataLayer?.push({
                    'event': 'pylot_form_submit',
                    'gtm.elementId': `gform-${formId}${data.title?.replace(/\s+/g, '')}`
                });
            }
        }).catch(error => console.log(error));
    };

    // check conditions after a field change
    function handleConditions(target) {
        let targetVal = target.value;
        let visibilityArray = [];
        let checkbox = false;

        data?.fields?.map((gf) => {
            let conditionsMet = [];
            if (!targetVal && target.target?.value) {
                checkbox = true;
                targetVal = target.target.value;
            }
            if (gf.conditionalLogic) {
                gf.conditionalLogic.rules.forEach((rule) => {
                    let val = '';
                    let rulesMet = [];
                    let checkboxes = document.querySelectorAll(`#group-${rule.fieldId}_${number} input[type=checkbox]`);

                    checkboxes.forEach(item => {
                        // Append ID's so we don't cross contaminate if the conditionals match on the same values
                        let ruleMatch = `${rule.value}_${rule.fieldId}`
                        let fieldMatch = `${item.value}_${item.id?.split('_')[1]}`;
                        if (gf.conditionalLogic.logicType == 'any') {
                            rulesMet.push((item.checked && ruleMatch === fieldMatch));
                        } else {
                            if (item.checked && ruleMatch === fieldMatch) {
                                rulesMet.push(true);
                            }
                        }
                    });
                    let inputName = document.getElementsByName(`input_${rule.fieldId}`);
                    if (inputName) {
                        for (var i = 0, length = inputName.length; i < length; i++) {
                            if (inputName[i].checked && inputName[i].type === 'radio') {
                                val = inputName[i].value;
                                break;
                            }
                        }
                    }
                    if (!val) {
                        if (document.getElementById(`input_${rule.fieldId}_${number}`)) {
                            val = document.getElementById(`input_${rule.fieldId}_${number}`).value;
                        }
                    }
                    // push to conditions met array
                    conditionsMet.push(rulesMet.length ? rulesMet.includes(true) : rule.value === val)

                });
            } else if (gf.visibility === 'hidden') {
                conditionsMet.push(false);
            }
            visibilityArray[`input_${gf.id}_${number}`] = gf.conditionalLogic.logicType == 'any' ? conditionsMet.includes(true) : !conditionsMet.includes(false);

        });
        setVisibility(visibilityArray);
    };

    return (
        <FormContainer
            className={`form-container ${backgroundValue}`}
            color={footerTextColor ? footerTextColor : backgroundValue === 'dark' ? '#fff' : bodyCopyColor}
            bodyCopyColor={bodyCopyColor}
        >

            <form id={`gform-${data.id}${data.title?.replace(/\s+/g, '')}_${number}`} title={data.title} class={`gform style-${formStyle}`} onSubmit={formSubmit} role="form">

                <div class='grid-x title-container'>
                    {title &&
                        <div class='cell'>
                            <h3 class='title'>{title}</h3>
                        </div>
                    }
                </div>
                {(!message) &&
                    <div class={`fields grid-x grid-margin-x ${loading ? 'hide-while-loading' : ''}`}>
                        <label className="mvk-form-control" for={'input_99_9999'}>
                            <input
                                type={'text'}
                                id={`input_99_9999`}
                                class="form-control"
                                name={`input_99`}
                            />
                        </label>
                        {data?.fields && data.fields.map((field, index) => <Fields recaptchaError={recaptchaError} module={module} errors={errors} field={field} index={index} settings={settings} formStyle={formStyle} footerTextColor={footerTextColor} bgValue={backgroundValue} checkConditions={handleConditions} visibility={visibility} number={number} />)}

                        <div class='cell'>
                            {data.button &&
                                <Button disabled={false} id={`submit-button-${data.id}`} title={buttonTextOverride ? buttonTextOverride : data.button.text} class='submit-button' tone={backgroundValue} type={buttonStyleOverride ? buttonStyleOverride : ''} buttonFunction={'submit'} />
                            }
                        </div>
                    </div>
                }
            </form>

            {loading &&
                <Loading type='dot-pulse' dotColor={backgroundValue === 'dark' ? '#fff' : Coloring('primary_color', settings)} />
            }



            {message &&
                <div id={`message-gform-${data.id}${data.title?.replace(/\s+/g, '')}`} class='response' dangerouslySetInnerHTML={{ __html: message }} />
            }
        </FormContainer>
    );

};

const Fields = ({ module, errors, field, index, settings, formStyle, footerTextColor, bgValue, checkConditions, visibility, number, recaptchaError }) => {
    const [fieldErrors, setFieldErrors] = useState({});
    // const [errors, setErrors] = useState({});
    const [hasFocus, setFocus] = useState(false);
    const [inputStyles, setInputStyles] = useState({ borderColor: footerTextColor ? footerTextColor : bgValue === 'dark' ? '#fff' : '', borderRadius: `${settings?.mvk_theme_config?.other?.border_radius_size}px` });
    const errorRef = useRef(null);

    useEffect(() => {
        if (formStyle === 'outlined' && bgValue !== 'dark') {
            setInputStyles({ ...inputStyles, borderColor: footerTextColor ? footerTextColor : Coloring(settings?.mvk_theme_config?.other?.outline_color, settings) })
        }
    }, []);
    useEffect(() => {
        if (field.type === 'captcha') {
            // const honeypot = document.getElementById('input_99_9999');
            // if (honeypot) {
            //     honeypot.value = 'asdfsadf';
            // }
        }
    }, []);

    // Find the textarea within the reCAPTCHA iframe and set the id to the field id
    const recaptchaRef = useRef(null);
    useEffect(() => {
      if (recaptchaRef.current) {
        if(recaptchaRef.current.parentElement) {
            const input = recaptchaRef.current.parentElement.querySelector('input');
            if (input) {
                input.value = recaptchaRef.current.getValue();
                console.log(input.value.value);
            }
        }
      }
    }, [recaptchaRef]);
    // update state on input change
    const handleInputChange = e => {
        // check validation
        checkValidation(e.target);
    }

    // Check value, set focus and reset styles
    function checkValue(event) {
        checkValidation(event.target);
        setFocus(event.target.value ? true : false);
    }

    function checkFileSize(event) {
        let files = event.currentTarget.files;
        for (let x in files) {
            const filesize = ((files[x].size / 1024) / 1024).toFixed(4); // MB
            if (files[x].name != "item" && typeof files[x].name != "undefined" && filesize > event.target.maxsize) {
                setFieldErrors({ ...fieldErrors, [event.target.id]: `Max file size of ${event.target.maxsize}MB exceeded.` })
            }
        }
    }

    // set focus and styles
    function focus() {
        setFocus(true);
        if (formStyle !== 'outlined') {
            setInputStyles({ ...inputStyles, borderColor: footerTextColor ? footerTextColor : bgValue === 'dark' ? '#fff' : settings.design?.colors?.primary_color });
        }
    }

    // on blur reset styles
    function blur(event) {
        checkValue(event);
        if (formStyle !== 'outlined') {
            setInputStyles({ ...inputStyles, borderColor: footerTextColor ? footerTextColor : bgValue === 'dark' ? '#fff' : '' });
        }
    }

    // validation
    const checkValidation = (target) => {
        // check if this change has dependant conditions
        checkConditions(target);
        let errorMessage = '';
        switch (target.type) {
            case "email":
                let emailPattern = /[a-z0-9!#$%&'*+/=?^_`{|}~-]+(?:\.[a-z0-9!#$%&'*+/=?^_`{|}~-]+)*@(?:[a-z0-9](?:[a-z0-9-]*[a-z0-9])?\.)+[a-z0-9](?:[a-z0-9-]*[a-z0-9])?/;
                // set error message
                errorMessage = (target.value.length <= 0 && target.required) ? settings?.mvk_theme_config?.labels?.form_validation?.required || 'this field is required.' : (!emailPattern.test(target.value) && target.value.length > 0) ? settings?.mvk_theme_config?.labels?.form_validation?.email || 'Must be a valid email address.' : '';
                break;
            case "tel":
                let phonePattern = /^(\+0?1\s)?\(?\d{3}\)?[\s.-]\d{3}[\s.-]\d{4}$/g;
                // set error message
                errorMessage = target.value.length <= 0 && target.required ? settings?.mvk_theme_config?.labels?.form_validation?.required || 'this field is required.' : !phonePattern.test(target.value) && target.value.length > 0 ? settings?.mvk_theme_config?.labels?.form_validation?.phone || 'Must be a valid phone number.' : '';
                break;
            case "text":
            case "textarea":
            case "select-one":
            case "select":
            case "date":
                // set error message
                errorMessage = (target.value.length <= 0 || target.value === 'none-selected') && target.required ? settings?.mvk_theme_config?.labels?.form_validation?.required || 'this field is required.' : '';

                if (target.min && parseInt(target.value) < parseInt(target.min)) {
                    errorMessage = `Must be a minimum of ${target.min}`;
                } else if (target.max && parseInt(target.value) > parseInt(target.max)) {
                    errorMessage = `Must be a maximum of ${target.max}`;
                }
                break;
            default:
                break;
        }
        // if error message set error
        errorMessage ? setFieldErrors({ ...fieldErrors, [target.id]: errorMessage }) : setFieldErrors({});
        // console.log(errorMessage, fieldErrors);
    };

    if (visibility[`input_${field.id}_${number}`]) {
        switch (field.type) {
            case 'name':
            case 'address':
                return (
                    <div class={"small-12 cell form-group " + field.cssClass + (field.size === 'large' ? ' medium-6' : '') + (field.size === 'small' ? ' medium-3' : '') + (field.isRequired ? ' required' : '') + ' type-' + field.type} id={`group-${field.id}_${number}`} data-name={field.label} data-condition={field.conditionalLogic ? field.conditionalLogic.rules : null} key={index} >
                        <div class='grid-x grid-margin-x'>
                            {field?.inputs && field?.inputs?.map((input, i) => <NameAddress module={module} field={field} input={input} i={i} settings={settings} formStyle={formStyle} footerTextColor={footerTextColor} bgValue={bgValue} checkConditions={checkConditions} visibility={visibility} number={number} />)}
                        </div>
                    </div>
                );
            case 'select':
                let selectStyles = {
                    fontFamily: cache?.fonts?.body || 'sans-serif'
                }
                return (
                    <div class={"small-12 cell form-group " + field.cssClass + (field.size === 'large' ? ' medium-6' : '') + (field.size === 'small' ? ' medium-3' : '') + (field.isRequired ? ' required' : '') + ' type-' + field.type} id={`group-${field.id}_${number}`} data-name={`input_${field.id}`} key={index} >
                        <div class="grid-x">
                            <div class='small-12 cell'>
                                <label for={`input_${field.id}_${number}`}>
                                    <span class="show-for-sr">{field.label}</span>
                                    <select class="form-control" style={selectStyles} type={field.type} id={`input_${field.id}_${number}`} name={`input_${field.id}`} value={field.value} onChange={handleInputChange} required={field.isRequired} placeholder={formStyle !== 'underlined' ? field.label + (field.isRequired ? '*' : '') : ''} >
                                        <option value="" dangerouslySetInnerHTML={{ __html: field.label + (field.isRequired ? '<span class="star">*</span>' : '') }}></option>
                                        {field.choices.map(function (choice) {
                                            return (<option value={choice.value}>{choice.text}</option>);
                                        })}
                                    </select>
                                    {(fieldErrors[`input_${field.id}_${number}`] || errors[`input_${field.id}_${number}`]) &&
                                        <p class='error'>{fieldErrors[`input_${field.id}_${number}`] || errors[`input_${field.id}_${number}`]}</p>
                                    }
                                </label>
                            </div>
                        </div>
                    </div>
                );
            case 'multiselect':
                return (
                    <div class={"small-12 cell form-group " + field.cssClass + (field.size === 'large' ? ' medium-6' : '') + (field.size === 'small' ? ' medium-3' : '') + (field.isRequired ? ' required' : '') + ' type-' + field.type} id={`group-${field.id}_${number}`} data-name={`input_${field.id}`} key={index} >
                        <div class="grid-x">
                            <div class='small-12 cell'>
                                <label for={`input_${field.id}_${number}`}>
                                    <span class="show-for-sr">{field.label}</span>
                                    <select multiple class="form-control" type={field.type} id={`input_${field.id}_${number}`} name={`input_${field.id}`} value={field.value} onChange={handleInputChange} required={field.isRequired} placeholder={formStyle !== 'underlined' ? field.label + (field.isRequired ? '*' : '') : ''} >
                                        <option value="" dangerouslySetInnerHTML={{ __html: field.label + (field.isRequired ? '<span class="star">*</span>' : '') }}></option>
                                        {field.choices.map(function (choice) {
                                            return (<option value={choice.value}>{choice.text}</option>);
                                        })}
                                    </select>
                                    {(fieldErrors[`input_${field.id}_${number}`] || errors[`input_${field.id}_${number}`]) &&
                                        <p class='error'>{fieldErrors[`input_${field.id}_${number}`] || errors[`input_${field.id}_${number}`]}</p>
                                    }
                                </label>
                            </div>
                        </div>
                    </div>
                );
            case 'checkbox':
                return (
                    <div class={"small-12 cell form-group checkbox-group " + field.cssClass + (field.size === 'large' ? ' medium-6' : '') + (field.size === 'small' ? ' medium-3' : '') + (field.isRequired ? ' required' : '') + ' type-' + field.type} id={`group-${field.id}_${number}`} data-name={field.label} key={index} >
                        <div class="grid-x">
                            <div class='small-12 cell'>
                                {(field.labelPlacement !== 'hidden_label' && formStyle !== 'box') &&
                                    <span class='label' dangerouslySetInnerHTML={{ __html: field.label + (field.isRequired ? '<span class="star">*</span>' : '') }}></span>
                                }
                                {field?.inputs && field?.inputs?.map(function (choice, i) {
                                    return (
                                        <div class="checkbox" key={i}>
                                            <label for={`input_${choice.id.replace('.', '_')}_${number}`}>
                                                <input
                                                    type="checkbox"
                                                    id={`input_${choice.id.replace('.', '_')}_${number}`}
                                                    name={`input_${choice.id.replace('.', '_')}`}
                                                    value={choice.label}
                                                    onChange={checkConditions}
                                                    required={field.isRequired}
                                                />
                                                <span dangerouslySetInnerHTML={{ __html: choice.label }} />
                                            </label>
                                        </div>
                                    );
                                })}
                                {field?.description &&
                                    <p class='description' dangerouslySetInnerHTML={{ __html: field.description }} />
                                }
                                {(fieldErrors[`group-${field.id}_${number}`] || errors[`group-${field.id}_${number}`]) &&
                                    <p class='error'>{fieldErrors[`group-${field.id}_${number}`] || errors[`group-${field.id}_${number}`]}</p>
                                }
                            </div>
                        </div>
                    </div>
                );
            // case 'consent':
            //     return (
            //         <div class={"small-12 cell form-group checkbox-group " + field.cssClass + (field.size === 'large' ? ' medium-6' : '') + (field.size === 'small' ? ' medium-3' : '') + (field.isRequired ? ' required' : '') + ' type-checkbox'} id={`group-${field.id}_${number}`} data-name={field.label} key={index} >
            //             <div class="grid-x">
            //                 <div class='small-12 cell'>
            //                     {(field.labelPlacement !== 'hidden_label' && formStyle !== 'box') &&
            //                         <span class='label' dangerouslySetInnerHTML={{ __html: field.label + (field.isRequired ? '<span class="star">*</span>' : '') }}></span>
            //                     }
            //                     <div class="checkbox">
            //                         <label for={`input_${field?.id}_${number}`}>
            //                             <input
            //                                 type="checkbox"
            //                                 id={`input_${field?.id}_${number}`}
            //                                 name={`input_${field?.id}`}
            //                                 value={'checked'}
            //                                 onChange={checkConditions}
            //                                 required={field.isRequired}
            //                             />
            //                             <span dangerouslySetInnerHTML={{ __html: field.checkboxLabel }} />
            //                         </label>
            //                     </div>
            //                     {field?.description &&
            //                         <p class='description' dangerouslySetInnerHTML={{ __html: field.description }} />
            //                     }
            //                     {(fieldErrors[`group-${field.id}_${number}`] || errors[`group-${field.id}_${number}`]) &&
            //                         <p class='error'>{fieldErrors[`group-${field.id}_${number}`] || errors[`group-${field.id}_${number}`]}</p>
            //                     }
            //                 </div>
            //             </div>
            //         </div>
            //     );
            case 'radio':
                return (
                    <div
                        class={"small-12 cell form-group " + field.cssClass + (field.size === 'large' ? ' medium-6' : '') + (field.size === 'small' ? ' medium-3' : '') + (field.isRequired ? ' required' : '') + ' type-' + field.type}
                        id={`group-${field.id}_${number}`}
                        data-name={field.label}
                        key={index}
                    >
                        <div class="grid-x">
                            <div class='small-12 cell button-wrap' role="radiogroup">
                                {(field.labelPlacement !== 'hidden_label' && formStyle !== 'box') ?
                                    <span class='label'>{field.label}</span>
                                    :
                                    <label class="show-for-sr">{field.label}</label>
                                }
                                {field?.choices?.map(function (choice, index) {
                                    return (
                                        <div class="radio" key={index}>
                                            <label for={`input_${field.id}_${index}_${number}`}>
                                                <input
                                                    type="radio"
                                                    id={`input_${field.id}_${index}_${number}`}
                                                    name={`input_${field.id}`}
                                                    value={choice.value}
                                                    onChange={handleInputChange}
                                                    required={field.isRequired}
                                                />
                                                {choice.text}
                                            </label>
                                        </div>
                                    );
                                })}
                                {(fieldErrors[`input_${field.id}_${number}`] || errors[`input_${field.id}_${number}`] || errors[`group-${field.id}_${number}`]) &&
                                    <p class='error'>{fieldErrors[`input_${field.id}_${number}`] || errors[`input_${field.id}_${number}`] || errors[`group-${field.id}_${number}`]}</p>
                                }
                            </div>
                        </div>
                    </div>
                );
            case 'html':
                return (
                    <div
                        class={"small-12 cell form-group " + field.cssClass + ' type-' + field.type}
                        id={`group-${field.id}_${number}`}
                        data-name={field.label}
                        key={index}
                    >
                        <div class="grid-x">
                            <div
                                class='small-12 cell label'
                                dangerouslySetInnerHTML={{ __html: field.content }}></div>
                        </div>
                    </div>
                );
            case 'section':
                return (
                    <div
                        class={"small-12 cell form-group " + field.cssClass + ' type-' + field.type}
                        id={`group-${field.id}_${number}`}
                        data-name={field.label}
                        key={index}
                    >
                        <div class="grid-x">
                            <div class='small-12 cell label'><h4 dangerouslySetInnerHTML={{ __html: field.label }}></h4></div>
                        </div>
                    </div>
                );
            case 'time':
                // case 'date':
                return (
                    <div
                        class={"small-12 cell form-group " + field.cssClass + (field.size === 'large' ? ' medium-6' : '') + (field.size === 'small' ? ' medium-3' : '') + (field.isRequired ? ' required' : '') + ' type-' + field.type}
                        id={`group-${field.id}_${number}`}
                        data-name={field.label}
                        key={index}
                    >
                        <div class="grid-x">
                            <label for={`input_${field.id}_${number}`} class="small-12 cell">
                                {(field.labelPlacement !== 'hidden_label' && formStyle !== 'box') &&
                                    <span>{field.label + (field.isRequired ? '<span class="star">*</span>' : '')}</span>
                                }
                                <input
                                    type="text"
                                    id={`input_${field.id}_${number}`}
                                    class="form-control"
                                    name={`input_${field.id}`}
                                    value={field.value}
                                    onChange={handleInputChange}
                                    required={field.isRequired}
                                    style={inputStyles}
                                />
                                {(fieldErrors[`input_${field.id}_${number}`] || errors[`input_${field.id}_${number}`]) &&
                                    <p class='error'>{fieldErrors[`input_${field.id}_${number}`] || errors[`input_${field.id}_${number}`]}</p>
                                }
                            </label>
                        </div>
                    </div>
                );
            case 'phone':
                function formatPhone(event) {
                    let x = event.target.value.replace(/\D/g, '').match(/(\d{0,3})(\d{0,3})(\d{0,4})/);
                    event.target.value = !x[2] ? x[1] : '(' + x[1] + ') ' + x[2] + (x[3] ? '-' + x[3] : '');
                    handleInputChange(event);
                    setFocus(event.target.value ? true : false);
                }

                return (
                    <div
                        class={"small-12 cell form-group " + field.cssClass + (field.size === 'large' ? ' medium-6' : '') + (field.size === 'small' ? ' medium-3' : '') + (field.isRequired ? ' required' : '') + ' type-' + field.type}
                        id={`group-${field.id}_${number}`}
                        data-name={field.label}
                        key={index}
                    >
                        <div class="grid-x">
                            <label for={`input_${field.id}_${number}`} class={"small-12 cell" + (hasFocus ? ' focused' : '')}>
                                {(field.labelPlacement !== 'hidden_label' && formStyle !== 'box') &&
                                    <span class={formStyle === 'underlined' ? 'floater' : ''} dangerouslySetInnerHTML={{ __html: field.label + (field.isRequired ? '<span class="star">*</span>' : '') }}></span>
                                }
                                <input
                                    type="tel"
                                    id={`input_${field.id}_${number}`}
                                    class="form-control"
                                    name={`input_${field.id}`}
                                    value={field.value}
                                    onChange={field.phoneFormat === 'standard' ? formatPhone : checkValue}
                                    required={field.isRequired}
                                    placeholder={formStyle !== 'underlined' ? field.label + (field.isRequired ? '*' : '') : ''}
                                    onFocus={focus}
                                    onBlur={blur}
                                    style={inputStyles}
                                />
                                <span class="show-for-sr">{field.label}</span>
                                {(fieldErrors[`input_${field.id}_${number}`] || errors[`input_${field.id}_${number}`]) &&
                                    <p class='error'>{fieldErrors[`input_${field.id}_${number}`] || errors[`input_${field.id}_${number}`]}</p>
                                }
                            </label>
                        </div>
                    </div>
                );
            case 'fileupload':
                return (
                    <div
                        class={"small-12 cell form-group " + field.cssClass + (field.size === 'large' ? ' medium-6' : '') + (field.size === 'small' ? ' medium-3' : '') + (field.isRequired ? ' required' : '') + ' type-' + field.type}
                        id={`group-${field.id}_${number}`}
                        data-name={field.label}
                        key={index}
                    >
                        <label for={`input_${field.id}`}><span dangerouslySetInnerHTML={{ __html: field.label + (field.isRequired ? '<span class="star">*</span>' : '') }}></span></label>
                        <input
                            type="file"
                            id={`input_${field.id}_${number}`}
                            name={`input_${field.id}`}
                            onChange={field.maxFileSize ? checkFileSize : null}
                            maxSize={field.maxFileSize ? field.maxFileSize : null}
                            multiple={field.multipleFiles}
                            required
                        />
                        {(fieldErrors[`input_${field.id}_${number}`] || errors[`input_${field.id}_${number}`]) &&
                            <p class='error'>{fieldErrors[`input_${field.id}_${number}`] || errors[`input_${field.id}_${number}`]}</p>
                        }
                    </div>
                );
            case 'number':
                function formatNumber(e) {
                    const el = e.target || e
                    el.value = el.value.replace(/[^0-9.]/g, '').replace(/(\..*)\./g, '$1');
                    checkValue(e);
                }
                return (
                    <div
                        class={"small-12 cell form-group " + field.cssClass + (field.size === 'large' ? ' medium-6' : '') + (field.size === 'small' ? ' medium-3' : '') + (field.isRequired ? ' required' : '') + ' type-' + field.type}
                        id={`group-${field.id}_${number}`}
                        data-name={field.label}
                        key={index}
                    >
                        <div class="grid-x">
                            <label for={`input_${field.id}_${number}`} class={"small-12 cell" + (hasFocus ? ' focused' : '')}>
                                {((field.labelPlacement !== 'hidden_label' && formStyle !== 'box') || formStyle === 'underlined') &&
                                    <span class={formStyle === 'underlined' ? 'floater' : ''} dangerouslySetInnerHTML={{ __html: field.label + (field.isRequired ? '<span class="star">*</span>' : '') }}></span>
                                }
                                <input
                                    type={'text'}
                                    id={`input_${field.id}_${number}`}
                                    class="form-control"
                                    name={`input_${field.id}`}
                                    value={field.value}
                                    onChange={formatNumber}
                                    required={field.isRequired}
                                    placeholder={formStyle !== 'underlined' ? field.label + (field.isRequired ? '*' : '') : ''}
                                    onFocus={focus}
                                    onBlur={blur}
                                    minLength={field.rangeMin.length}
                                    maxLength={field.rangeMax.length}
                                    min={field.rangeMin}
                                    max={field.rangeMax}
                                    style={inputStyles}
                                    data-condition={field.conditionalLogic ? field.conditionalLogic : null}
                                />

                                <span class="show-for-sr">{field.label}</span>

                                {(fieldErrors[`input_${field.id}_${number}`] || errors[`input_${field.id}_${number}`]) &&
                                    <p class='error'>{fieldErrors[`input_${field.id}_${number}`] || errors[`input_${field.id}_${number}`]}</p>
                                }
                            </label>
                        </div>
                    </div>
                );
            case 'imaginuity_recaptcha':

                const handleCaptchaChange = (value) => {
                    const input = document.querySelector(`input[name="input_${field.id}"]`);
                    const inputContainer = input.parentElement.parentElement.parentElement.parentElement;
                    if (value) {
                        // enable submit button
                        const submitButton =  inputContainer.querySelector('button[type="submit"]');
                        if (submitButton) {
                            submitButton.disabled = false;
                        }
                        if (input) {
                            input.value = value;
                        }
                    }
                    else {
                        // disable submit button
                        const submitButton = inputContainer.querySelector('button[type="submit"]');
                        if (submitButton) {
                            submitButton.disabled = true;
                        }
                        if (input) {
                            input.value = '';
                        }
                    }

                }
                const siteKey = settings?.recaptcha_settings?.gravity_forms_recaptcha_site_key;

                if (!siteKey) {
                    return <div>No CAPTCHA site key found in Super Configuration settings</div>
                }

                return (
                    <div
                        class={"small-12 cell form-group " + field.cssClass  + (field.isRequired ? ' required' : '') + ' type-' + field.type}
                        id={`group-${field.id}_${number}`}
                        data-name={field.label}
                        key={index}
                    >
                        <div class="grid-x">
                            <label for={`input_${field.id}_${number}`} class={"small-12 cell" + (hasFocus ? ' focused' : '')}>
                                {((field.labelPlacement !== 'hidden_label' && formStyle !== 'box') || formStyle === 'underlined') &&
                                    <span dangerouslySetInnerHTML={{ __html: '<span class="star">RECAPTCHA*</span>' }}></span>
                                }

                                    <ReCAPTCHA
                                        sitekey={'6LdLxlMrAAAAANJo4Kpzbx7aDiXzxLh_2FeHjxdy'}
                                        onChange={handleCaptchaChange}
                                    />

                                {recaptchaError &&
                                    <p class='error' style={{
                                        position: 'relative',
                                        bottom: 0,
                                        marginTop: '8px'
                                    }}>{recaptchaError}</p>
                                }

                                <span class="show-for-sr">{field.label}</span>
                            </label>
                        </div>
                    </div>)
            default:

                return (
                    <div
                        class={"small-12 cell form-group " + field.cssClass + (field.size === 'large' ? ' medium-6' : '') + (field.size === 'small' ? ' medium-3' : '') + (field.isRequired ? ' required' : '') + ' type-' + field.type}
                        id={`group-${field.id}_${number}`}
                        data-name={field.label}
                        key={index}
                    >
                        <div class="grid-x">
                            <label for={`input_${field.id}_${number}`} class={"small-12 cell" + (hasFocus ? ' focused' : '')}>
                                {((field.labelPlacement !== 'hidden_label' && formStyle !== 'box') || formStyle === 'underlined') &&
                                    <span class={formStyle === 'underlined' ? 'floater' : ''} dangerouslySetInnerHTML={{ __html: field.label + (field.isRequired ? '<span class="star">*</span>' : '') }}></span>
                                }
                                {field.type !== 'textarea' &&
                                    <input
                                        type={field.type}
                                        id={`input_${field.id}_${number}`}
                                        class="form-control"
                                        name={`input_${field.id}`}
                                        value={field.value}
                                        onChange={checkValue}
                                        required={field.isRequired}
                                        placeholder={formStyle !== 'underlined' ? field.label + (field.isRequired ? '*' : '') : ''}
                                        onFocus={focus}
                                        onBlur={blur}
                                        style={inputStyles}
                                        data-condition={field.conditionalLogic ? field.conditionalLogic : null}
                                    />
                                }
                                {field.type === 'textarea' &&
                                    <textarea
                                        type={field.type}
                                        id={`input_${field.id}_${number}`}
                                        class="form-control"
                                        name={`input_${field.id}`}
                                        value={field.value}
                                        onChange={checkValue}
                                        required={field.isRequired}
                                        placeholder={formStyle !== 'underlined' ? field.label + (field.isRequired ? '*' : '') : ''}
                                        onFocus={focus}
                                        onBlur={blur}
                                        style={inputStyles}
                                    />
                                }
                                <span class="show-for-sr">{field.label}</span>

                                {(fieldErrors[`input_${field.id}_${number}`] || errors[`input_${field.id}_${number}`]) &&
                                    <p class='error'>{fieldErrors[`input_${field.id}_${number}`] || errors[`input_${field.id}_${number}`]}</p>
                                }
                            </label>
                        </div>
                    </div>
                );
        }
    } else {
        return null;
    }
};

const NameAddress = ({ module, field, input, i, settings, formStyle, footerTextColor, bgValue, checkConditions, visibility, number }) => {
    const [groupErrors, setGroupErrors] = useState({});
    const [hasFocus, setFocus] = useState(false);
    const [newInputStyles, setNewInputStyles] = useState({ borderRadius: `${settings.mvk_theme_config.other?.border_radius_size}px`, fontFamily: `${settings?.design?.fonts?.body?.body_google_font_name}, sans-serif` });

    useEffect(() => {
        if (formStyle === 'outlined' && bgValue !== 'dark') {
            setNewInputStyles({ ...newInputStyles, borderColor: footerTextColor ? footerTextColor : Coloring(settings?.mvk_theme_config?.other?.outline_color, settings) })
        }
    }, []);

    // Check value, set focus and reset styles
    function checkValue(event) {
        // handleInputChange(event);
        checkValidation(event.target);
        setFocus(event.target.value ? true : false);
    }

    // set focus and styles
    function focus() {
        setFocus(true);
        if (formStyle !== 'outlined') {
            setNewInputStyles({ ...newInputStyles, borderColor: footerTextColor ? footerTextColor : bgValue === 'dark' ? '#fff' : settings.design?.colors?.primary_color });
        }
    }

    // on blur reset styles
    function blur(event) {
        checkValue(event);
        if (formStyle !== 'outlined') {
            setNewInputStyles({ ...newInputStyles, borderColor: '' });
        }
    }
    // validation
    const checkValidation = (target) => {
        checkConditions(target);
        // set error message
        let defaultError = (target.value.length <= 0 || target.value === 'none-selected') && target.required ? settings?.mvk_theme_config?.labels?.form_validation?.required || 'this field is required.' : '';

        // switch through address
        switch (input.id.substring(input.id.length - 2)) {
            case '.5':
                let zipRegex = /^([0-9]{5})(?:[-\s]*([0-9]{4}))?$/;
                let postalCodeRegex = /^([A-Z][0-9][A-Z])\s*([0-9][A-Z][0-9])$/i;
                // if(field.addressType !==)
                if (!zipRegex.test(target.value) && !postalCodeRegex.test(target.value) && target.value.length > 0) {
                    defaultError = settings?.mvk_theme_config?.labels?.form_validation?.zip_postal || `Must be a valid ${input.label}`;
                }
                break;
        }

        // if error message set error, else remove from error object
        defaultError ? setGroupErrors({ ...groupErrors, [target.id]: defaultError }) : delete groupErrors[target.id];
    }


    let isRequired = false;

    if (field.isRequired) {
        if (field.type === 'address' && input.id.substring(input.id.length - 2) === '.2') {
            isRequired = false;
        } else {
            isRequired = true;
        }
    }

    let inputLabel = input.customLabel ? input.customLabel : input.label;

    if (field.type === 'address' && input.id.substring(input.id.length - 2) === '.4' && !input.isHidden && visibility[`input_${field.id}_${number}`]) {

        if (!input.customLabel && field.addressType === 'canadian') {
            inputLabel = 'Province';
        } else if (!input.customLabel && field.addressType === 'us') {
            inputLabel = 'State';
        }

        return (
            <label for={`input_${input.id.replace('.', '_')}_${number}`}
                class={"small-12 cell medium-6" + (hasFocus ? ' focused' : '')} key={i}>
                {(field.labelPlacement !== 'hidden_label' && formStyle !== 'box') &&
                    <span class={formStyle === 'underlined' ? 'floater' : ''} dangerouslySetInnerHTML={{ __html: inputLabel + (isRequired ? '<span class="star">*</span>' : '') }}></span>
                }
                <select
                    type="text"
                    id={`input_${input.id.replace('.', '_')}_${number}`}
                    class={"form-control" + (field.cssClass ? ' ' + field.cssClass : '')}
                    name={`input_${input.id.replace('.', '_')}`}
                    value={input.value}
                    onChange={checkValue}
                    required={isRequired}
                    placeholder={formStyle !== 'underlined' ? inputLabel + (isRequired ? '*' : '') : ''}
                    onFocus={focus}
                    onBlur={blur}
                    style={newInputStyles}
                >
                    <option value="">{formStyle !== 'underlined' ? `Select ${inputLabel}` : ''}</option>
                    {field.addressType !== 'canadian' &&
                        <States />
                    }
                    {field.addressType !== 'us' &&
                        <Provinces />
                    }
                </select>
                {groupErrors[`input_${input.id.replace('.', '_')}_${number}`] &&
                    <p class='error'>{groupErrors[`input_${input.id.replace('.', '_')}_${number}`]}</p>
                }
            </label>
        );
    } else if (!input.isHidden && visibility[`input_${field.id}_${number}`]) {
        return (
            <label for={`input_${input.id.replace('.', '_')}_${number}`}
                class={"small-12 cell medium-6" + (hasFocus ? ' focused' : '')} key={i}>
                {(field.labelPlacement !== 'hidden_label' && formStyle !== 'box') &&
                    <span class={formStyle === 'underlined' ? 'floater' : ''} dangerouslySetInnerHTML={{ __html: inputLabel + (isRequired ? '<span class="star">*</span>' : '') }}></span>
                }
                <input
                    type="text"
                    id={`input_${input.id.replace('.', '_')}_${number}`}
                    class={"form-control" + (field.cssClass ? ' ' + field.cssClass : '')}
                    name={`input_${input.id.replace('.', '_')}`}
                    value={input.value}
                    onChange={checkValue}
                    required={isRequired}
                    placeholder={formStyle !== 'underlined' ? input.label + (isRequired ? '*' : '') : ''}
                    onFocus={focus}
                    onBlur={blur}
                    style={newInputStyles}
                />
                {groupErrors[`input_${input.id.replace('.', '_')}_${number}`] &&
                    <p class='error'>{groupErrors[`input_${input.id.replace('.', '_')}_${number}`]}</p>
                }
            </label>

        );

    } else {
        return (null)
    }
};

const States = () => {

    return (
        <>
            <option value="AL">Alabama</option>
            <option value="AK">Alaska</option>
            <option value="AZ">Arizona</option>
            <option value="AR">Arkansas</option>
            <option value="CA">California</option>
            <option value="CO">Colorado</option>
            <option value="CT">Connecticut</option>
            <option value="DE">Delaware</option>
            <option value="DC">District of Columbia</option>
            <option value="FL">Florida</option>
            <option value="GA">Georgia</option>
            <option value="HI">Hawaii</option>
            <option value="ID">Idaho</option>
            <option value="IL">Illinois</option>
            <option value="IN">Indiana</option>
            <option value="IA">Iowa</option>
            <option value="KS">Kansas</option>
            <option value="KY">Kentucky</option>
            <option value="LA">Louisiana</option>
            <option value="ME">Maine</option>
            <option value="MD">Maryland</option>
            <option value="MA">Massachusetts</option>
            <option value="MI">Michigan</option>
            <option value="MN">Minnesota</option>
            <option value="MS">Mississippi</option>
            <option value="MO">Missouri</option>
            <option value="MT">Montana</option>
            <option value="NE">Nebraska</option>
            <option value="NV">Nevada</option>
            <option value="NH">New Hampshire</option>
            <option value="NJ">New Jersey</option>
            <option value="NM">New Mexico</option>
            <option value="NY">New York</option>
            <option value="NC">North Carolina</option>
            <option value="ND">North Dakota</option>
            <option value="OH">Ohio</option>
            <option value="OK">Oklahoma</option>
            <option value="OR">Oregon</option>
            <option value="PA">Pennsylvania</option>
            <option value="RI">Rhode Island</option>
            <option value="SC">South Carolina</option>
            <option value="SD">South Dakota</option>
            <option value="TN">Tennessee</option>
            <option value="TX">Texas</option>
            <option value="UT">Utah</option>
            <option value="VT">Vermont</option>
            <option value="VA">Virginia</option>
            <option value="WA">Washington</option>
            <option value="WV">West Virginia</option>
            <option value="WI">Wisconsin</option>
            <option value="WY">Wyoming</option>
        </>
    )
};

const Provinces = () => {
    return (
        <>
            <option value="AB">Alberta</option>
            <option value="BC">British Columbia</option>
            <option value="MB">Manitoba</option>
            <option value="NB">New Brunswick</option>
            <option value="NF">Newfoundland</option>
            <option value="NT">Northwest Territories</option>
            <option value="NS">Nova Scotia</option>
            <option value="NU">Nunavut</option>
            <option value="ON">Ontario</option>
            <option value="PE">Prince Edward Island</option>
            <option value="QC">Quebec</option>
            <option value="SK">Saskatchewan</option>
            <option value="YT">Yukon Territory</option>
        </>
    );
};

export default Start;
