import React, { useState, useEffect } from "react";
import { decode } from 'html-entities';
// Helpers
import { Coloring } from 'src/helpers';
import HtmlParser from 'src/helpers/html-parser'
// Styles
import * as S from './styles';

const Start = ({ data, settings }) => {

    return (
        <S.TabbedContent
            className={`tabbed-content`}
            bgColor={Coloring(data.content_background_color, settings)}
            textColor={data.content_background_value === 'dark' ? '#fff' : Coloring('body_copy_color', settings)}
            tabColor={Coloring(data.default_tab_color, settings)}
            selectedTabColor={Coloring(data.selected_tab_color, settings)}
        >
            <div className="grid-container">
                <S.MobileAccordion
                    className="grid-x hide-for-large"
                    bgColor={Coloring(data.content_background_color, settings)}
                    textColor={data.content_background_value === 'dark' ? '#fff' : Coloring('body_copy_color', settings)}
                >
                    {data.tabs && data.tabs.map((item, index) => <Strip item={item} index={index} />)}
                </S.MobileAccordion>
                <DesktopTabbed data={data} />
            </div>

        </S.TabbedContent>
    )
}

const DesktopTabbed = ({ data }) => {
    const [active, setActive] = useState(`tab-0`);

    useEffect(() => {
        setActive(`tab-0`);
    }, [data]);

    return (
        <div className="grid-x show-for-large">
            <div className="cell tab-titles" role="tablist">
                {data.tabs && data.tabs?.map((item, index) =>
                    <h4
                        id={`${item.tab_title.toLowerCase().replace(/\s+/g, '')}${index}`}
                        className={`tab-title${active === `tab-${index}` ? ' active' : ''}`}
                        onClick={() => setActive(`tab-${index}`)}
                        role="tab"
                        aria-controls={`content${item.tab_title.toLowerCase().replace(/\s+/g, '')}${index}`}
                        aria-selected={active === `tab-${index}`}
                    >{decode(item.tab_title)}</h4>
                )}
            </div>
            <div className="cell tab-content">
                {data.tabs && data.tabs?.map((item, index) => 
                    <div
                        id={`content${item.tab_title.toLowerCase().replace(/\s+/g, '')}${index}`}
                        className={`tab ${item.content_column_options}${active === `tab-${index}` ? ' active' : ''}`}
                        role="tabpanel"
                        aria-labelledby={`${item.tab_title.toLowerCase().replace(/\s+/g, '')}${index}`}
                    >
                        {item.columns && item.columns?.map((content) => <div className="content-column"><HtmlParser html={content?.column_content} /></div>)}
                    </div>
                )}
            </div>
        </div>
    )
}

const Strip = ({ item, index }) => {
    const [open, setOpen] = useState(index === 0);   
    
    return (
        <div key={index} class='cell'>
            <div class={`tab-item ${open ? 'open' : ''}`}>
                <div
                    id={`acc${item.tab_title.toLowerCase().replace(/\s+/g, '')}${index}`}
                    className={`heading ${open ? 'open' : ''}`}
                    onClick={() => setOpen(!open)}
                    aria-expanded={open}
                    aria-controls={`accContent${item.tab_title.toLowerCase().replace(/\s+/g, '')}${index}`}
                >
                    <h4 className="tab-title">{decode(item.tab_title)}</h4><span class='icon'><span class='one'></span><span class='two'></span></span></div>
                <div
                    id={`accContent${item.tab_title.toLowerCase().replace(/\s+/g, '')}${index}`}
                    className='content'
                    aria-labelledby={`acc${item.tab_title.toLowerCase().replace(/\s+/g, '')}${index}`}
                >
                    {item.columns && item.columns?.map((content) => <div className="content-column"><HtmlParser html={content?.column_content} /></div>)}
                </div>
            </div>
        </div>
    );
};
export default Start;