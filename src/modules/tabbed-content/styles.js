import styled from 'styled-components'

export const TabbedContent = styled.div`
    padding: 3rem 0;
    @media (min-width: 1024px) {
        .tab-titles {
            display: flex;
            flex-wrap: wrap;
            .tab-title {
                margin-right: 2rem;
                position: relative;
                border-bottom: 2px solid transparent;
                color: ${props => props.tabColor};
                cursor: pointer;
                &:not(:last-child):after {
                    content: '|';
                    position: absolute;
                    right: -1.25rem;
                    color: ${props => props.tabColor};
                }
                &.active {
                    color: ${props => props.selectedTabColor};
                    border-color: inherit;
                }
            }
        }
        .tab-content {
            background-color: ${props => props.bgColor};
            color: ${props => props.textColor};
            .tab {
                padding: ${props => (props.bgColor === 'white' || props.bgColor === 'transparent') ? '3rem 0' : '3rem'};
                display: grid;
                grid-auto-flow: column;
                gap: 1rem;
                &:not(.active) {
                    display: none;
                }
                &.one-third-two-third {
                    grid-template-columns: 1fr 2fr;
                }
                &.two-third-one-third {
                    grid-template-columns: 2fr 1fr;
                }
            }
        }
    }
`

export const MobileAccordion = styled.div`
    .tab-item {
        background-color: ${props => props.bgColor};
        color: ${props => props.textColor};
        padding: 1rem;
        margin-bottom: 0.5rem;
        .heading {
            display: flex;
            justify-content: space-between;
            font-weight: bold;
            transition: height 0.3s;
            cursor: pointer;
            .tab-title {
                margin: 0;
            }
            .icon {
                position: relative;
                height: 20px;
                width: 20px;
                min-width: 20px;
                margin-left: 1rem;
                display: flex;
                flex-direction: column;
                justify-content: center;
                pointer-events: none;
                .one,
                .two {
                    position: absolute;
                    content: "";
                    width: 100%;
                    height: 4px;
                    border-radius: 10px;
                    transition: 0.2s;
                    background-color: ${props => props.textColor};
                }
                .one {
                    transform: rotate(90deg);
                }
            }
            &.open {
                margin-bottom: 1rem;
                .icon .one {
                    transform: rotate(90deg) scale(0);
                }
            }
        }
        .content {
            max-height: 0;
            overflow: hidden;
            .content-column {
                margin-bottom: 2rem;
            }
        }
        &.open {
            .content {
                max-height: 10000px; // bigger than it will ever be
                transition: all 0.3s;
            }
        }
    }
`