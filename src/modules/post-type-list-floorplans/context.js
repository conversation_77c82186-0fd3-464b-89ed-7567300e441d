import React, { useState, createContext } from 'react';
import { decode } from 'html-entities';
// HOOKS.
import { useQuery } from 'src/hooks/query';

export const FloorplanFilterContext = createContext();
export const FloorplanFilterProvider = (props) => {
    const query = useQuery();
    const [floorplanFilters, setFloorplanFiltration] = useState({
        beds: query.get('beds')?.split(',') || [],
        baths: query.get('baths')?.split(',') || [],
        min_price: query.get('min_price') || null,
        max_price: query.get('max_price') || null,
        min_sq_ft: query.get('min_sq_ft') || null,
        max_sq_ft: query.get('max_sq_ft') || null,
        start_date: query.get('start_date') || null,
        end_date: query.get('end_date') || null,
        reset: false
    });

    return (
        <FloorplanFilterContext.Provider value={[floorplanFilters, setFloorplanFiltration]}>
            {props.children}
        </FloorplanFilterContext.Provider>
    );
}