import React, { useEffect, useState, useContext } from "react";
import { decode } from 'html-entities';
import { useInView } from 'react-intersection-observer';
// Context
import { FloorplanFilterContext, FloorplanFilterProvider } from "./context";
// Helpers
import HtmlParser from 'src/helpers/html-parser'
import Imaging from 'src/helpers/imaging';
import { checkAvailability } from "src/helpers/checkAvailability";
import { Coloring } from 'src/helpers';
const Clicker = React.lazy(() => import('src/helpers/clicker'));
// Styles
import * as S from './styles';
// Components
import Filters from './filters';

const Start = ({ data, settings }) => {
    const { ref, inView } = useInView({
        triggerOnce: true,
        fallbackInView: true
    });

    return (
        <FloorplanFilterProvider>
            <S.PTLFloorplans ref={ref}>
                {inView ?
                    <div className="grid-container">
                        <div className="grid-x">
                            {data.title &&
                                <div className={`cell ${data.title_alignment}`}>
                                    <h2>{decode(data.title)}</h2>
                                </div>
                            }
                            {data.blurb &&
                                <div className='cell'>
                                    <HtmlParser html={data.blurb} />
                                </div>
                            }
                            {data.filters && <Filters filters={data.filters} data={data} settings={settings} />}
                            {data.floorplans && <Floorplans floorplans={data.floorplans} moduleData={data} settings={settings} />}
                        </div>
                    </div>
                    : null}
            </S.PTLFloorplans>
        </FloorplanFilterProvider>
    );
}

const Floorplans = ({ floorplans, moduleData, settings }) => {
    const [floorplanFilters, setFloorplanFiltration] = useContext(FloorplanFilterContext);
    const [results, setResults] = useState(floorplans);

    useEffect(() => {
        setResults(() => floorplans.filter((i) => {
            // Dates
            let availableDate = i.availability ? i.availability[0].available_date : false;
            const availableNow = availableDate ? checkAvailability(availableDate, 'Available Now', 'month-date-year') === 'Available Now' : false;
            availableDate = availableDate ? new Date(`${availableDate.replace(/(\d{4})(\d{2})(\d{2})/g, '$1-$2-$3')}`) : false;
            const startDate = floorplanFilters.start_date ? new Date(floorplanFilters.start_date) : false;
            const endDate = floorplanFilters.end_date ? new Date(floorplanFilters.end_date) : false;

            // Include Item - check against all filters, OR within type, AND between types
            const includeItem = (!floorplanFilters.beds.length || floorplanFilters.beds.includes(i.beds)) &&
                (!floorplanFilters.baths.length || floorplanFilters.baths.includes(i.baths)) &&
                (!floorplanFilters.min_price || parseInt(i.starting_price) >= parseInt(floorplanFilters.min_price)) &&
                (!floorplanFilters.max_price || parseInt(i.starting_price) <= parseInt(floorplanFilters.max_price)) &&
                (!floorplanFilters.min_sq_ft || parseInt(i.square_footage) >= parseInt(floorplanFilters.min_sq_ft)) &&
                (!floorplanFilters.max_sq_ft || parseInt(i.square_footage) <= parseInt(floorplanFilters.max_sq_ft)) &&
                ((!startDate || availableDate >= startDate) && (!endDate || availableDate <= endDate) || availableNow)
            return includeItem;
        }))

    }, [floorplanFilters]);

    return (
        <S.Floorplans className="results-container cell">
            {results.map((card, i) => <Card data={card} moduleData={moduleData} settings={settings} index={i} />)}
            {(!results || !results.length) && <div className="no-results"><h3>No results found.</h3></div>}
        </S.Floorplans>
    );
}

const Card = ({ data, moduleData, settings, index }) => {
    const beds = data.beds ? `${data.beds} Bed / ` : '';
    const baths = data.baths ? `${data.baths} Bath / ` : '';
    const sqFt = data.square_footage ? `${data.square_footage} sq. ft.` : '';
    const price = data.starting_price ? `Starting at $${new Intl.NumberFormat().format(data.starting_price)}` : '';
    const availibleUnit = data.availability ? data.availability[0] : false;
    const bgColor = Coloring(moduleData.card_background_color, settings);
    const textColor = moduleData.card_background_value === 'dark' ? '#fff' : Coloring('body_copy_color', settings);

    return (
        <S.Card
            key={`floorplan-card-${index}`}
            bgColor={bgColor}
            textColor={textColor}
        >
            <Clicker type="anchor" url={data.url}>
                {data.floorplan_image && <div className="image-container"><Imaging data={data.floorplan_image} /></div>}
                <div className="content-container">
                    <div className="inner-wrapper">
                        <h3 className={moduleData.card_background_value === 'dark' ? '#fff' : 'primary-txt'}>{decode(data.title)}</h3>
                        <p className="details">
                            {beds && <span>{beds}</span>}
                            {baths && <span>{baths}</span>}
                            {sqFt && <span>{sqFt}</span>}
                        </p>
                        {price && <p className="price">{price}</p>}
                        {(availibleUnit && availibleUnit.available_date) &&
                            <div className="detail-row"><p className="availability">{checkAvailability(availibleUnit.available_date, 'Available Now', 'month-date-year')}</p></div>
                        }
                    </div>
                </div>
            </Clicker>
        </S.Card>
    );
}
export default Start;