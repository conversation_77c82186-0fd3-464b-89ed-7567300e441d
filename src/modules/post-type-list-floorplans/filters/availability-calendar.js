import React, { useEffect, useState, useContext, useRef } from "react";
import DatePicker from 'react-datepicker'

// HOOKS.
import { addStylesheet } from 'src/hooks';
import { useOnClickOutside } from 'src/hooks';
// Context
import { FloorplanFilterContext } from "../context";
// Styles
import * as S from '../styles';

const Start = () => {
    addStylesheet('https://cdnjs.cloudflare.com/ajax/libs/react-datepicker/2.14.1/react-datepicker.min.css');
    const [floorplanFilters, setFloorplanFiltration] = useContext(FloorplanFilterContext);
    const [dropdown, setDropdown] = useState(false);
    const [startDate, setStartDate] = useState(floorplanFilters.start_date ? new Date(floorplanFilters.start_date) : null);
    const [endDate, setEndDate] = useState(floorplanFilters.end_date ? new Date(floorplanFilters.end_date) : null);
    const [screenSize, setSize] = useState({ width: window.innerWidth });
    const ref = useRef();

    useEffect(() => {
        if (floorplanFilters.reset) {
            setDropdown(false);
            setStartDate(null);
            setEndDate(null);
            setFloorplanFiltration({ ...floorplanFilters, ...{ reset: false } });
        } else {
            const startObject = startDate ? new Date(startDate) : null;
            const formattedStart = startObject ? `${startObject.getMonth() + 1}/${startObject.getDate()}/${startObject.getFullYear()}` : null;
            const endObject = endDate ? new Date(endDate) : null;
            const formattedEnd = endObject ? `${endObject.getMonth() + 1}/${endObject.getDate()}/${endObject.getFullYear()}` : null;

            setFloorplanFiltration({ ...floorplanFilters, ...{ start_date: formattedStart, end_date: formattedEnd, reset: false } });
        }
    }, [startDate, endDate, floorplanFilters.reset])

    const handleOnChange = (range) => {
        const [start, end] = range;
        setStartDate(start);
        setEndDate(end);
    };

    useOnClickOutside(ref, () => {
        if (dropdown && screenSize.width >= 768) {
            setDropdown(false)
        }
    })

    return (
        <div className="filter-wrap" ref={ref}>
            <S.DropdownButton className="primary-border" role='button' aria-active={dropdown} aria-controls={`date-filter-menu`} aria-label={`toggle available date options`} onClick={() => setDropdown(!dropdown)} tabIndex={0}>
                <span className="title primary-txt" >Move-In Date<i className="arrow"></i></span>
            </S.DropdownButton>
            {dropdown &&
                <S.DropdownContent className="dropdown-content primary-border" id={`date-filter-menu`} aria-label={`available date options`}>
                    <div class='input-wrapper'>
                        <label for='date-filter'>Click input to add date range.</label>
                        <DatePicker
                            id='date-filter'
                            onChange={handleOnChange}
                            startDate={startDate}
                            endDate={endDate}
                            selectsRange
                            dateFormat={'M/dd/yyyy'}
                        />
                    </div>
                </S.DropdownContent>
            }
        </div>
    )
}

export default Start;