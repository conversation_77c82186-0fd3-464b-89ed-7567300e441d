import React, { useEffect, useState, useContext } from "react";
import { useNavigate } from 'react-router-dom';

// Context
import { FloorplanFilterContext } from "../context";
//PARTIALS
const Button = React.lazy(() => import('src/partials/button'));
// Helpers
import { Coloring } from 'src/helpers';
// Styles
import * as S from '../styles';
// Components
import Checkboxes from './checkboxes';
import Range from './range';
import AvailabilityCalendar from './availability-calendar';

const Start = ({ filters, data, settings }) => {
    const [floorplanFilters, setFloorplanFiltration] = useContext(FloorplanFilterContext);
    const navigate = useNavigate();
    const [reset, setReset] = useState(false);
    const bgColor = Coloring(data.card_background_color, settings);
    const textColor = data.card_background_value === 'dark' ? '#fff' : Coloring('body_copy_color', settings);

    useEffect(() => {
        if (reset) {
            navigate({ search: '' })
            setFloorplanFiltration({
                ...floorplanFilters, ...{
                    beds: [],
                    baths: [],
                    min_price: null,
                    max_price: null,
                    min_sq_ft: null,
                    max_sq_ft: null,
                    start_date: null,
                    end_date: null,
                    reset: true
                }
            })
            setReset(false);
        } else {
            let queryString = '';
            Object.keys(floorplanFilters).map((key) => {
                queryString += floorplanFilters[key] && Boolean(floorplanFilters[key].length) ? `&${key}=${floorplanFilters[key]}` : ''
            })
            navigate({ search: `${queryString.substring(1)}` })
        }
    }, [floorplanFilters, reset]);

    return (
        <S.Filters
            className="cell"
            bgColor={bgColor}
            textColor={textColor}
        >
            {filters.bed && <Checkboxes items={settings?.floorplans?.beds} label="Bedrooms" filter="beds" />}
            {filters.bath && <Checkboxes items={settings?.floorplans?.baths} label="Bathrooms" filter="baths" />}
            {filters.starting_price && <Range label="Price" filter="starting_price" minFilter="min_price" maxFilter="max_price" tone={data.card_background_value} />}
            {filters.square_footage && <Range label="Sq. Ft." filter="square_footage" minFilter="min_sq_ft" maxFilter="max_sq_ft" tone={data.card_background_value} />}
            {filters.availability_date && <AvailabilityCalendar />}
            <Button className='reset-filters' title='Reset Filters' buttonFunction={'styled'} aria-label='reset filters' onClick={() => setReset(true)} />
        </S.Filters>
    );
}

export default Start;