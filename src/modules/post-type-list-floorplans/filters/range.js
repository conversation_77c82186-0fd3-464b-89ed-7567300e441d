import React, { useEffect, useState, useContext, useRef } from "react";
// Hooks
import { useOnClickOutside } from 'src/hooks';
// Context
import { FloorplanFilterContext } from "../context";
//PARTIALS
const Button = React.lazy(() => import('src/partials/button'));
// Styles
import * as S from '../styles';

const Start = ({label, filter, minFilter, maxFilter, tone}) => {
    const [floorplanFilters, setFloorplanFiltration] = useContext(FloorplanFilterContext);
    const [dropdown, setDropdown] = useState(false);
    const [min, setMin] = useState(floorplanFilters[minFilter]);
    const [max, setMax] = useState(floorplanFilters[maxFilter]);
    const [clicked, setClicked] = useState(false);
    const [screenSize, setSize] = useState({ width: window.innerWidth });
    const ref = useRef(filter);

    useEffect(() => {
        if (clicked) {
            setFloorplanFiltration({ ...floorplanFilters, ...{ [minFilter]: min, [maxFilter]: max } });
            setClicked(false);
            setDropdown(false);
        } else if (floorplanFilters.reset) {
            setDropdown(false);
            setMin(null);
            setMax(null);
            const minEl = document?.getElementById(`${filter}-filter-min`);
            const maxEl = document?.getElementById(`${filter}-filter-max`);
            if (minEl) minEl.value = '';
            if (maxEl) maxEl.value = '';
            setFloorplanFiltration({ ...floorplanFilters, ...{ reset: false } });
        }
    }, [clicked, floorplanFilters.reset])

    useOnClickOutside(ref, () => {
        if (dropdown && screenSize.width >= 768) {
            setDropdown(false)
        }
    })

    return (
        <div className="filter-wrap" ref={ref}>
            <S.DropdownButton className="primary-border" role='button' aria-active={dropdown} aria-controls={`${filter}-filter`} aria-label={`toggle ${label} options`} onClick={() => setDropdown(!dropdown)} tabIndex={0}>
                <span className="title primary-txt">{label}<i className="arrow"></i></span>
            </S.DropdownButton>
            {dropdown &&
                <S.DropdownContent className="dropdown-content primary-border range" id={`${filter}-filter`} aria-label={`${label} options`}>
                    <div className="input-wrapper">
                        <label for={`${filter}-filter-min`}>
                            <span>Min</span>
                            <input
                                type="number"
                                id={`${filter}-filter-min`}
                                value={min}
                                name={`${filter}-filter-min`}
                                onChange={(e) => setMin(e.target.value)}
                            />
                        </label>
                        <label for={`${filter}-filter-max`}>
                            <span>Max</span>
                            <input
                                type="number"
                                id={`${filter}-filter-max`}
                                value={max}
                                name={`${filter}-filter-max`}
                                onChange={(e) => setMax(e.target.value)}
                            />
                        </label>
                    </div>
                    <Button title='Apply' buttonFunction={'styled'} aria-label={`apply ${label} filter`} tone={tone} onClick={() => setClicked(true)} />
                </S.DropdownContent>
            }
        </div>
    )
}


export default Start;