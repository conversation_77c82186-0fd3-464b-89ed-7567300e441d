import React, { useEffect, useState, useContext, useRef } from "react";
// Hooks
import { useOnClickOutside } from 'src/hooks';
// Context
import { FloorplanFilterContext } from "../context";
// Styles
import * as S from '../styles';

const Start = ({ items, label, filter }) => {
    const [floorplanFilters, setFloorplanFiltration] = useContext(FloorplanFilterContext);
    const [selected, setSelected] = useState(floorplanFilters[filter]);
    const [dropdown, setDropdown] = useState(false);
    const [screenSize, setSize] = useState({ width: window.innerWidth });
    const ref = useRef(filter);

    useEffect(() => {
        if (floorplanFilters.reset) {
            setDropdown(false);
            setSelected(floorplanFilters[filter])
        } else {
            setFloorplanFiltration({ ...floorplanFilters, ...{ [filter]: selected, reset: false } })
        }

    }, [selected, floorplanFilters.reset]);

    const updateChoices = e => {

        if (e.target.checked) {
            setSelected(selected => [...selected, e.target.value])
        } else {
            const index = selected.indexOf(e.target.value);
            if (index > -1) {
                setSelected((selected) => selected.filter((i) => i !== e.target.value))
            }
        }
        return;
    }
    useOnClickOutside(ref, () => {
        if (dropdown && screenSize.width >= 768) {
            setDropdown(false)
        }
    })
    return (
        <div className="filter-wrap" ref={ref}>
            <S.DropdownButton className="primary-border" role='button' aria-active={dropdown} aria-controls={`${filter}-filter-menu`} aria-label={`show ${label} options`} onClick={() => setDropdown(!dropdown)} tabIndex={0}>
                <span className="title primary-txt">{label}<i className="arrow"></i></span>
            </S.DropdownButton>
            {dropdown &&
                <S.DropdownContent className="dropdown-content primary-border checkboxes" id={`${filter}-filter-menu`} aria-label={`${label} options`}>
                    {items && items.map((item, i) => {
                        let cleanItem = item.replace(/\s/g, '');
                        const lastChar = cleanItem.slice(-1);
                        if (lastChar === '+') {
                            cleanItem = cleanItem.slice(0, -1);
                        }
                        return (
                            <div>
                                <label for={`${filter}-filter-${i}`}>
                                    <input
                                        type="checkbox"
                                        id={`${filter}-filter-${i}`}
                                        value={cleanItem}
                                        name={`${filter}-filter-${i}`}
                                        onClick={updateChoices}
                                        checked={floorplanFilters[filter] && floorplanFilters[filter].includes(cleanItem)}
                                    />
                                    <span>{item}</span>
                                </label>
                            </div>
                        )
                    })}
                </S.DropdownContent>
            }
        </div>
    )
}

export default Start;