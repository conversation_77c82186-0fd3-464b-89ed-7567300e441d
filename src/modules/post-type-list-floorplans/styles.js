import styled from 'styled-components';

export const PTLFloorplans = styled.div`
    padding: 2rem 0 0;
    h2 {    
        margin-bottom: 1rem;
    }
`

export const Floorplans = styled.div`
    margin: 2rem 0 4rem;
    display: grid;
    column-gap: 1rem;
    row-gap: 1rem;
    @media (min-width: 640px) {
        grid-template-columns: 1fr 1fr;
    }
    @media (min-width: 992px) {
        grid-template-columns: 1fr 1fr 1fr;
    }
`

export const Filters = styled.div`
    display: flex;
    flex-wrap: wrap;
    margin: 2rem 0;
    .filter-wrap {
        width: 100%;
        @media (min-width: 768px) {
            position: relative;
            width: auto;
            margin-right: 1rem;
        }
        .dropdown-content {
            background-color: ${props => props.bgColor};
            color: ${props => props.textColor};
        }
    }
    .reset-filters {
        margin: 1rem 0;
    }
`

export const Card = styled.div`
    border: 1px solid #d6d6d6;
    box-sizing: border-box;
    .image-container {
        height: 192px;
        padding: 1.5rem 2rem;
        background: #fff;
        img {
            height: 100%;
            width: 100%;
            object-fit: contain;
        }
    }
    .content-container {
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        padding: 1rem;
        height: 118px;
        background-color: ${props => props.bgColor};
        color: ${props => props.textColor};
        h3, .details, .price {
            margin-bottom: .5rem;
        }
        .availability {
            font-weight: bold;
            margin: 0;
        }
        & > * {
            width: 100%;
        }
    }
    a {
        box-shadow: 2px 4px 6px rgba(0,0,0,.3);
        display: block;
        @media (min-width: 992px) {
            box-shadow: none;
            transition: .3s;
            &:hover {
                box-shadow: 3px 6px 10px rgba(0,0,0,.3);
            }
        }
    }
`

export const DropdownButton = styled.button`
    min-width: 160px;
    border: 2px solid;
    background: #fff;
    margin-top: 1rem;
    padding: 10px 30px;
    span {
        margin-right: 1rem;
        position: relative;
        .arrow {
            border: solid;
            border-width: 0 2px 2px 0;
            display: inline-block;
            padding: 5px;
            position: absolute;
            right: -1.5rem;
            top: 40%;
            transform: translateY(-50%)rotate(45deg);
        }
    }
    &[aria-active='true'] {
        span .arrow {
            transform: translateY(-50%)rotate(-135deg);
            top: 50%;
        }
    }
    &[aria-controls='date-filter-menu'] {
        min-width: 224px;
    }
    &:focus {
        box-shadow: 2px 1px 3px rgba(0,0,0,.3);
    }
`
export const DropdownContent = styled.div`
    span.react-datepicker__aria-live {
        display: none;
    }
    width: 100%;
    border-top: 0;
    margin-bottom: 1rem;
    padding: 1rem 1rem 0.5rem;
    box-sizing: border-box;
    &.checkboxes {
        label {
            padding-right: 1rem;
        }
    }
    &.range {
        .input-wrapper {
            padding: 0 0 2rem;
            display: flex;
            label {
                width: 50%;
                &:first-of-type {
                    margin-right: 1rem;
                }
                input {
                    font-size: 1rem;
                    height: 35px;
                    width: 100%;
                    border: 1px solid;
                }
            }
            @media (min-width: 768px) {
                flex-wrap: wrap;
                label {
                    width: 100%;
                    &:first-of-type {
                        margin-right: 0;
                        margin-bottom: 1rem;
                    }
                }
            }
        }
        button {
            margin-bottom: .5rem;
        }
    }
    &#date-filter-menu {
        .input-wrapper {
            padding: 1rem 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
            label {
                width: 50%;
            }
            .react-datepicker-wrapper {
                width: 50%;
                input {
                    font-size: 1rem;
                    font-size: 1rem;
                    height: 35px;
                    width: 100%;
                    border: 1px solid;
                    background: #fff;
                }
            }
            @media (min-width: 768px) {
                flex-wrap: wrap;
                label, .react-datepicker-wrapper {
                    width: 100%;
                }
            }
        }
    }
    @media (min-width: 768px) {
        position: absolute;
        background: #fff;
        z-index: 1;
        box-sizing: border-box;
        padding: 1rem;
        box-shadow: 2px 4px 6px rgba(0,0,0,.3);
    }
`