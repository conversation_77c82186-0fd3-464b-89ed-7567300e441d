import React, { useContext, useState, useEffect } from 'react'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faFacebookF } from '@fortawesome/fontawesome-free-brands';
import { faXTwitter } from "@fortawesome/free-brands-svg-icons";
import { faEnvelope, faCalendarAlt, faRepeat } from "@fortawesome/free-solid-svg-icons";
import DateHighlightItem from "./date-highlight-item";

import './event-list-item.scss'

import { decode } from 'html-entities';

const Button = React.lazy(() => import('src/partials/button'));
const Clicker = React.lazy(() => import('src/helpers/clicker'));
import { FormattedDate, DateRange } from 'src/helpers/date';
import { Background, Border, Color, ColorSplash } from '../../helpers/theme';
import { convertToFrench } from 'src/helpers/hours';
import Imaging from 'src/helpers/imaging';

import { SettingsProvider, SettingsContext, PrincipalContext } from "src/context";

// style options: "all", "upcoming"
export default function EventListItem({ data, event, eventType = 'all', eventStyle }) {
    const [settings, setSettings] = useContext(SettingsContext);
    const [principal, setPrincipal] = useContext(PrincipalContext);
    const color = data.background_value === 'dark' ? 'white-txt' : 'body-copy-txt';
    const primaryColor = data.background_value === 'dark' ? 'white-txt' : 'primary-txt';

    var startDate = (event?.next_occurrence && event.next_occurrence.start) ? event.next_occurrence.start.event_date : event.start.event_date;
    var endDate = (event?.next_occurrence && event.next_occurrence.end) ? event.next_occurrence.end.event_date : event.end.event_date;

    // const dateString = (startDate !== endDate) ? DateRange(startDate, endDate) : FormattedDate(startDate, 'day-month-date');
    var dateString;
    if (data?.style === 'four-column-copy') {
        dateString = FormattedDate(startDate, 'month-day-date');
    } else if (startDate !== endDate) {
        dateString = DateRange(startDate, endDate)
    } else {
        dateString = FormattedDate(startDate, 'day-month-date');
    }

    if (event.start.minutes == "0") { event.start.minutes = "00"; }
    if (event.end.minutes == "0") { event.end.minutes = "00"; }

    // TIMES
    let startTime = `${event.start.hour}:${event.start.minutes} ${event.start.ampm}`;
    let endTime = `${event.end.hour}:${event.end.minutes} ${event.end.ampm}`;
    if (principal.activeTranslation === 'fr') {
        startTime = convertToFrench(startTime);
        endTime = convertToFrench(endTime);
    }
    if (event.meta.mec_hide_time != "1" && event.meta.mec_allday != "1") {
        var times = ` ${startTime}`;
        if (event.meta.mec_hide_end_time != "1") {
            times += ` - ${endTime}`
        }
    } else {
        var times = ``;
    }
    var listItemStyles = {
        borderRadius: (settings?.mvk_theme_config?.other?.enable_border_radius) ? `${settings?.mvk_theme_config?.other?.border_radius_size}px` : "0px",
    };
    const categoryStyles = {
        borderRadius: (settings?.mvk_theme_config?.other?.enable_border_radius) ? `0 ${settings?.mvk_theme_config?.other?.border_radius_size}px 0 0` : "0px",
    };
    const splashColor = {
        borderColor: data.background_value === 'dark' ? '#fff' : settings.mvk_theme_config.other?.enable_color_splashes ? ColorSplash(settings.mvk_theme_config.other?.cs_color) : settings.design?.colors?.body_copy_color,
        color: data.background_value === 'dark' ? '#fff' : settings.mvk_theme_config.other?.enable_color_splashes ? ColorSplash(settings.mvk_theme_config.other?.cs_color) : settings.design?.colors?.body_copy_color
    }
    const label = data.show_category && event.category ? event.category : data.show_category && data.label ? data.label : '';
    const buttonStyle = data.button_style === 'secondary' ? settings.mvk_theme_config.other?.secondary_button_style : settings.mvk_theme_config.other?.primary_button_style;

    return (
        <Clicker type={eventStyle !== 'two-column-cards' ? 'anchor' : null} url={eventStyle !== 'two-column-cards' ? event.url : null} target={event.external_link} class={`event-list-item__module ${eventStyle !== 'two-column-cards' ? 'hover' : ''} ${eventType} ${buttonStyle?.includes('outline') ? 'button-outline' : 'button-solid'}`} ariaLabel={`link to ${event.title}`}>
            <div class="event-list-item-content flexbox wrap" style={eventStyle === 'two-column-cards' ? listItemStyles : null}>
                {eventStyle === 'date-highlight' &&
                    <DateHighlightItem data={data} event={event} settings={settings} />
                }
                {(eventStyle !== 'four-column-copy' && eventStyle !== 'date-highlight' && eventStyle !== 'simple-list') &&
                    <Image event={event} hover={eventStyle !== 'two-column-cards'} />
                }
                {(eventStyle !== 'three-column' && eventStyle !== 'four-column-copy' && eventStyle !== 'date-highlight' && eventStyle !== 'simple-list') &&
                    <div class="event-list-item-right-content">
                        {(eventStyle === 'two-column-cards' && data.show_category && event.category) &&
                            <div className='event-category-label primary-bg white-txt' style={categoryStyles}>{event.category}</div>
                        }
                        <div class="event-list-item-text-content">
                            {(label && eventStyle !== 'two-column-cards') ? <p className="label" style={splashColor}>{label}</p> : null}
                            {event.title &&
                                <div class={`title strong ${eventStyle === 'two-column-cards' ? 'body-copy-txt' : color}`}>
                                    <h2 dangerouslySetInnerHTML={{ __html: event.title }} />
                                </div>
                            }
                            {event.stores &&
                                <div class='stores'>
                                    {event.stores.map((store) => <Clicker class={`related-store ${eventStyle === 'two-column-cards' ? 'primary-txt' : primaryColor}`} type='anchor' url={store.url}>{decode(store.title)}</Clicker>)}
                                </div>
                            }
                            {eventType === 'all' && <div class={`date ${eventStyle === 'two-column-cards' ? 'primary-txt' : primaryColor} strong`}>{dateString}<br />{times} {event.repeats && <FontAwesomeIcon icon={faRepeat} className="icon-repeat" aria-label="recurring event icon" />}</div>}
                            {eventType === 'upcoming' && <div class={`date ${eventStyle === 'two-column-cards' ? 'primary-txt' : primaryColor} strong`}>{dateString}<br />{times} {event.repeats && <FontAwesomeIcon icon={faRepeat} className="icon-repeat" aria-label="recurring event icon" />}</div>}
                            {event.meta.mec_comment &&
                                <p className={`comment ${eventStyle === 'two-column-cards' ? 'body-copy-txt' : color}`} dangerouslySetInnerHTML={{ __html: event.meta.mec_comment }} />
                            }
                            {(eventStyle !== 'two-column-cards' && eventStyle !== 'three-column') && <div class={`exerpt ${color}`} dangerouslySetInnerHTML={{ __html: event.exerpt }} />}
                        </div>
                        {eventStyle === 'two-column-cards' && <ShareIcons event={event} settings={settings} />}
                        {(eventType === 'all' || eventStyle === 'two-column-cards') ? <Button url={event.url} target={event.external_link} class="event-list-item-link-button" icon={'icon-one'} title={event.link_text_override ? decode(event.link_text_override) : settings.mvk_theme_config.labels?.learn_more ? settings.mvk_theme_config.labels?.learn_more : 'More Information'} tone={eventStyle === 'two-column-cards' ? 'light' : data.background_value} type={data.button_style} /> : null}
                    </div>
                }
                {eventStyle === 'three-column' &&
                    <ThreeCol data={data} event={event} date={dateString} />
                }

                {eventStyle === 'four-column-copy' &&
                    <FourCol data={data} event={event} date={dateString} eventStyle={eventStyle} />
                }

                {eventStyle === 'simple-list' &&
                    <SimpleList data={data} event={event} />
                }
            </div>
        </Clicker>
    );
};

const ThreeCol = ({ data, event, date }) => {
    const [settings, setSettings] = useContext(SettingsContext);
    const [showLocationCategory, setShowLocationCategory] = useState((data.show_location_category && !settings.current_location));

    useEffect(() => {
        setShowLocationCategory((data.show_location_category && !settings.current_location))
    }, [settings.current_location])

    const splashColor = {
        borderColor: data.background_value === 'dark' ? '#fff' : settings.mvk_theme_config.other?.enable_color_splashes ? ColorSplash(settings.mvk_theme_config.other?.cs_color) : settings.design?.colors?.body_copy_color,
        color: data.background_value === 'dark' ? '#fff' : settings.mvk_theme_config.other?.enable_color_splashes ? ColorSplash(settings.mvk_theme_config.other?.cs_color) : settings.design?.colors?.body_copy_color
    }
    const color = data.background_value === 'dark' ? 'white-txt' : 'body-copy-txt';

    var strippedString = event?.exerpt?.replace(/<[^>]*>?/gm, '');
    var splitString = strippedString?.split(' ');
    var excerptString;

    if (splitString?.length >= 25) {
        var trimmedArray = [];
        for (let i = 0; i < 26; i++) {
            trimmedArray?.push(splitString[i]);
        }

        var joinedString = trimmedArray?.join(' ');
        excerptString = joinedString?.substring(0, joinedString?.lastIndexOf(' ')) + '...';
    } else {
        excerptString = event?.exerpt?.replace(/<[^>]*>?/gm, '');
    }
    const label = data.show_category && event.category ? event.category : data.label ? data.label : 'EVENT';

    return (
        <div class={`event-content-lower ${data.hide_event_description ? 'no-blurb' : 'with-blurb'}`}>
            <div className='top'>
                {!data.show_location_category && <p className="label" style={splashColor}>{label}</p>}
                {showLocationCategory && <LocationCategory data={data} event={event} />}
                {event.title &&
                    <div class={`title strong ${color}`}>
                        <h3 dangerouslySetInnerHTML={{ __html: event.title }} />
                    </div>
                }
                {!data?.hide_event_description &&
                    <div class={`event-description ${color}`} dangerouslySetInnerHTML={{ __html: decode(excerptString) }} />
                }
            </div>
            <div className={`bottom ${color}`}>
                {date &&
                    <div class={`date-block ${data.background_value === 'dark' ? '' : 'background-bg'}`}><FontAwesomeIcon icon={faCalendarAlt} class='icon-calendar' /> {date} {event.repeats && <FontAwesomeIcon icon={faRepeat} className="icon-repeat" aria-label="recurring event icon" />}</div>
                }
                {event.meta.mec_comment &&
                    <p className="comment" dangerouslySetInnerHTML={{ __html: event.meta.mec_comment }} />
                }
                <div class='learn-more'>{event.link_text_override ? decode(event.link_text_override) : settings.mvk_theme_config.labels?.learn_more ? settings.mvk_theme_config.labels?.learn_more : 'LEARN MORE >'}</div>
            </div>
        </div>
    );
};

const FourCol = ({ data, event, date, eventStyle }) => {
    const [settings, setSettings] = useContext(SettingsContext);

    var strippedString = event?.exerpt?.replace(/<[^>]*>?/gm, '');
    var splitString = strippedString?.split(' ');
    var excerptString;

    if (splitString?.length >= 25) {
        var trimmedArray = [];
        for (let i = 0; i < 26; i++) {
            trimmedArray?.push(splitString[i]);
        }

        var joinedString = trimmedArray?.join(' ');
        excerptString = joinedString?.substring(0, joinedString?.lastIndexOf(' ')) + '...';
    } else {
        excerptString = event?.exerpt?.replace(/<[^>]*>?/gm, '');
    }
    const splashColor = {
        borderColor: data.background_value === 'dark' ? '#fff' : settings.mvk_theme_config.other?.enable_color_splashes ? ColorSplash(settings.mvk_theme_config.other?.cs_color) : settings.design?.colors?.body_copy_color,
        color: data.background_value === 'dark' ? '#fff' : settings.mvk_theme_config.other?.enable_color_splashes ? ColorSplash(settings.mvk_theme_config.other?.cs_color) : settings.design?.colors?.body_copy_color
    }
    const color = data.background_value === 'dark' ? 'white-txt' : 'body-copy-txt';
    const label = data.show_category && event.category ? event.category : data.label ? data.label : '';

    return (
        <div class='event-content-container'>
            <Image event={event} hover={eventStyle !== 'two-column-cards'} />
            {date &&
                <div class={`date-string strong ${color}`}>{date.toUpperCase()}</div>
            }
            {label ? <p className="label" style={splashColor}>{label}</p> : null}
            {event.title &&
                <div class={`title strong ${color}`}>
                    <h3 dangerouslySetInnerHTML={{ __html: event.title }} />
                </div>
            }
            {!data?.hide_event_description &&
                <div class={`event-description ${color}`} dangerouslySetInnerHTML={{ __html: decode(excerptString) }} />
            }
            <Button url={event.url} class="event-list-item-link-button" icon={'icon-one'} title={'Details'} tone={data.background_value} />
        </div>
    );
};

const SimpleList = ({ data, event }) => {
    let dateString = event.start_date_formatted;
    if (event.start_date_formatted != event.end_date_formatted) {
        dateString += ` - ${event.end_date_formatted}`;
    }
    let times = '';
    if (event.meta.mec_hide_time != "1" && event.meta.mec_allday != "1") {
        times = ` ${event.start_time_formatted}`;
        if (event.meta.mec_hide_end_time != "1") {
            times += ` - ${event.end_time_formatted}`
        }
    }

    return (
        <div className='simple-list-event'>
            {event.featured_image &&
                <div className='featured-image'><Imaging data={event.featured_image} /></div>
            }
            <div className='content-wrapper'>
                <p className={`date ${data.background_value === 'dark' ? 'tertiary-txt' : 'primary-txt'}`}>{dateString}</p>
                {times && <p className={`times ${data.background_value === 'dark' ? 'tertiary-txt' : 'primary-txt'}`}>{times}</p>}
                <h3 className={`underline ${data.background_value === 'dark' ? 'white-txt' : 'body-copy-txt'}`}>{decode(event.title)}</h3>
            </div>
        </div>
    )
}

const Image = ({ event, hover }) => {
    return (
        <div class="event-list-item-image">
            <div class={`${hover ? 'event-hover-wrapper' : 'no-hover'}`}>
                {event.featured_image && <Imaging data={event.featured_image} />}
            </div>
        </div>
    );
};

const ShareIcons = ({ event, settings }) => {

    const buttonStyle = settings?.mvk_theme_config?.other?.primary_button_style;

    var style = {
        backgroundColor: Background('light', buttonStyle),
        color: Color('light', buttonStyle),
        border: Border('light', buttonStyle)
    };

    return (
        <div className="share-icons">
            <a
                href={
                    "mailto:?body=" +
                    event.url +
                    "&subject=" +
                    decode(event.title)
                }
                onClick={() => window.open("mailto:?body=" +
                event.url +
                "&subject=" +
                decode(event.title))}
                className="social-icon"
                aria-label="Email this event"
                style={style}
            >
                <FontAwesomeIcon icon={faEnvelope} className="icon" />
            </a>
            <a
                href={
                    "https://twitter.com/home?status=" + window.location.href
                }
                onClick={() => window.open("https://twitter.com/home?status=" + window.location.href)}
                className="social-icon"
                aria-label="Share this event on Twitter"
                target="_blank"
                style={style}
            >
                <FontAwesomeIcon icon={faXTwitter} className="icon" />
            </a>
            <a
                href={
                    "https://www.facebook.com/sharer/sharer.php?u=" +
                    window.location.href
                }
                onClick={() => window.open("https://www.facebook.com/sharer/sharer.php?u=" + window.location.href)}
                className="social-icon"
                aria-label="Share this event on Facebook"
                target="_blank"
                style={style}
            >
                <FontAwesomeIcon icon={faFacebookF} className="icon" />
            </a>
        </div>
    );
};

const LocationCategory = ({ data, event }) => {
    const textColor = data.background_value === 'dark' ? 'tertiary-txt' : 'primary-txt';

    return (
        <div className='location-category'>
            {(event.multilocation_event && event.multilocation_related_events) ?
                <h6 className={textColor}>{event.multilocation_related_events.map((mre, i) => `${mre.label}${i + 1 < event.multilocation_related_events.length ? ' / ' : ''}`)}</h6>
                :
                <h6 className={textColor}>{decode(event.location_category)}</h6>
            }
        </div>
    )
}