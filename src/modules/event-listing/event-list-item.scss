/*******************************************************************************************************
   Copyright 2020 - Calise Partners LLC d/b/a Imaginuity. All rights are reserved. 
   Reproduction or transmission in whole or in part, in any form or by any means, electronic, mechanical 
or otherwise, is prohibited without the prior written consent of Calise Partners LLC d/b/a Imaginuity.
********************************************************************************************************
   Project : 10618 Maverick Platform Development
   Author : Imaginuity Developers (<PERSON>, <PERSON>)
   Description : Styles for event listing module.
   Creation Date : Wed, Jan 20, 2021
   Documentation : [documentation url - point to default wiki for project if no specific documentation for script]
 ********************************************************************************************************/

$sm_breakpoint: 768px;
$md_breakpoint: 1280px;

//  NOTES -- may need to seperate this component out if variation becomes an issue

.event-list-item__module {
    // STYLES FOR: "./all.js", "./upcoming.js"
    min-width: 150px;

    .event-list-item-right-content {
        width: 100%;
    }

    .event-list-item-image {
        img {
            display: block;
            width: 100%;
            object-fit: cover;
            overflow: hidden;
        }
    }

    .title {
        font-size: 1.375rem;
        line-height: 1.375rem;
        font-weight: 700;
        margin: 0 0 5px 0;

        @media (min-width: $sm_breakpoint) {
            // font-size: 1.75rem;
            font-size: 1.4rem;
            line-height: 1.75rem;
        }

        @media (min-width: $md_breakpoint) {
            // font-size: 2.625rem;
            font-size: 2rem;
            line-height: 2.625rem;
            margin-bottom: 18px;
        }
    }

    .stores {
        pointer-events: all;
        .related-store {
            text-decoration: underline;
            display: block;
            margin-bottom: 1rem;
        }
    }

    .date {
        font-size: 0.875rem;
        margin: 0 0 5px 0;
        display: flex;
        align-items: center;
        .icon-repeat {
            margin-left: 0.5rem;
            font-size: 1.25rem;
        }
        @media (min-width: $sm_breakpoint) {
            font-size: 1.125rem;
            margin-bottom: 10px;
        }

        @media (min-width: $md_breakpoint) {
            margin-top: -8px;
            margin-bottom: 12px;
            font-size: 1.5rem;
        }
    }

    .times {
        font-size: 0.875rem;
        margin: 0 0 5px 0;

        @media (min-width: $sm_breakpoint) {
            font-size: 1.125rem;
            margin-bottom: 10px;
        }

        @media (min-width: $md_breakpoint) {
            margin-top: -8px;
            margin-bottom: 12px;
            font-size: 1.5rem;
        }
    }

    .exerpt {
        margin: 0;

        @media (min-width: $sm_breakpoint) {
            padding-right: 5px;
        }
    }

    .event-list-item-link-button {
        text-transform: uppercase;
        margin-top: 14px;
        padding: 10px 18px;

        @media (min-width: $sm_breakpoint) {
            padding: 14px 25px;
        }

        @media (min-width: $md_breakpoint) {
            padding: 18px 40px;
        }

        @media (min-width: $sm_breakpoint) {
            margin-left: auto;
        }
    }
}

// /*******************************************************
// ********************************************************
// **
// **  style prop specific classes
// **
// ********************************************************
// ********************************************************/

.event-list-item__module.all,
.event-list-item__module.upcoming {
    // STYLES FOR: "./all.js"

    .event-list-item-content {
        padding: 1.5rem 0px;
        border-bottom: 1px solid rgba(106, 98, 97, 0.5);

        @media (min-width: $sm_breakpoint) {
            padding: 24px 0px;
        }

        @media (min-width: $md_breakpoint) {
            padding: 30px 0px;
        }
    }

    .event-list-item-image {
        display: none;

        @media (min-width: $sm_breakpoint) {
            display: block;
            width: 40%;
            max-width: 400px;
        }

        img {
            height: 250px;

            @media (min-width: $md_breakpoint) {
                height: 280px;
            }
        }
    }

    .event-list-item-right-content {
        @media (min-width: $sm_breakpoint) {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            align-items: flex-start;
            padding-left: 1rem;
        }
    }
}

.two-column-list {
    .event-listing-all,
    .event-listing-upcoming {
        & > div {
            @media (min-width: 768px) {
                display: grid;
                grid-auto-flow: row;
                grid-template-columns: 1fr 1fr;
                column-gap: 1rem;
                .event-list-item__module {
                    .event-list-item-content {
                        border: none;
                    }
                    .event-list-item-image {
                        width: 100%;
                        max-width: none;
                        margin-bottom: 1rem;
                    }
                    .event-list-item-right-content {
                        padding: 0;
                        .event-list-item-link-button {
                            margin: 1rem 0 0;
                        }
                    }
                }
            }
        }
        .event-list-item-image {
            display: block;
            width: 100%;
            max-width: none;
            .event-hover-wrapper {
                overflow: hidden;
                min-height: 375px;
                position: relative;
                img {
                    min-height: 100%;
                    object-fit: cover;
                    height: 100% !important;
                    position: absolute;
                }
                @media (min-width: $sm_breakpoint) {
                    min-height: 220px;
                   
                }
                @media (min-width: 1024px) {
                    min-height: 375px;
                }
            }
        }
    }
}