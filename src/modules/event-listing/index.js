/*******************************************************************************************************
   Copyright 2020 - Calise Partners LLC d/b/a Imaginuity. All rights are reserved. 
   Reproduction or transmission in whole or in part, in any form or by any means, electronic, mechanical 
or otherwise, is prohibited without the prior written consent of Calise Partners LLC d/b/a Imaginuity.
********************************************************************************************************
   Project : 10618 Maverick Platform Development
   Author : Imaginuity Developers (<PERSON>, <PERSON>)
   Description : Event listing module.
   Creation Date : Mon Dec 14 2020
   Documentation : [documentation url - point to default wiki for project if no specific documentation for script]
 ********************************************************************************************************/

import React, { useState, useEffect, Suspense } from 'react';
const Button = React.lazy(() => import('src/partials/button'));
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faCalendarAlt, faThList } from "@fortawesome/free-solid-svg-icons";
import { decode } from 'html-entities';
import HtmlParser from 'src/helpers/html-parser';
import { addOneDay } from 'src/helpers/date';
import { useInView } from 'react-intersection-observer';

const UpcomingEvents = React.lazy(() => import("src/modules/event-listing/upcoming"));
const AllEvents = React.lazy(() => import("src/modules/event-listing/all"));
const Calendar = React.lazy(() => import("src/modules/event-listing/calendar"));

import './index.scss';

var domain = window.location.hostname;
domain = domain.replace('.dev', '.preflight');

const Start = ({ data, settings, placeholders }) => {
    const [listings, setListings] = useState(data.events);

    useEffect(() => {
        if (settings.current_location) {
            setListings(() => data.events?.filter((i) => {
                return i.location === settings.current_location;
            }))
        } else {
            setListings(data.events);
        }
    }, [placeholders])

    return <Listings data={data} settings={settings} placeholders={placeholders} listings={listings} />
}

const Listings = ({ data, settings, placeholders, listings }) => {
    const [view, setView] = useState(data?.default_to_calendar ? 'calendar' : 'default');
    const { ref, inView } = useInView({
        triggerOnce: true,
        fallbackInView: true
    });

    let eventArray = [];
    function toggleView() {
        setView(view === 'default' ? 'calendar' : 'default');
    }

    if (data.add_calendar) {
        listings && listings?.map((item) => {
            let eventStart = '';
            let eventEnd = '';
            if (item.all_occurrences && Array.isArray(item.all_occurrences)) {
                item.all_occurrences?.map((next) => {
                    eventStart = next.start.event_date;
                    eventEnd = addOneDay(next.end.event_date);
                    eventArray.push({ id: `${item.mvk_id}${eventStart}`, title: decode(item.title), start: eventStart, end: eventEnd, url: item.url })
                });
            } else if (item.all_occurrences && !Array.isArray(item.all_occurrences)) {
                Object.keys(item.all_occurrences).map((key) => {
                    eventStart = item.all_occurrences[key].start.event_date;
                    eventEnd = addOneDay(item.all_occurrences[key].end.event_date);
                    eventArray.push({ id: `${item.mvk_id}${key}`, title: decode(item.title), start: eventStart, end: eventEnd, url: item.url })
                })
            } else {
                eventStart = item.start.event_date;
                eventEnd = addOneDay(item.end.event_date);
                eventArray.push({ id: item.mvk_id, title: decode(item.title), start: eventStart, end: eventEnd, url: item.url })
            }
        });
    }

    const bgColor = pylot.design.coloring(data.background_color, 'backgroundColor');
    const primaryColor = data.background_value === 'dark' ? 'white-txt' : 'primary-txt';
    const title = (placeholders && data.title_selection === 'dynamic') ? placeholders.single_line[data.title] : data.title
    const bgImage = (placeholders && data.background_image_selection === 'dynamic') ? placeholders.image[data?.background_image_dynamic] : data?.background_image
    const button = placeholders ? placeholders.button_link[data.button] : data?.button

    let bgStyles;
    switch (data.background_type) {
        case 'image':
            bgStyles = {
                backgroundImage: `url(${pylot.tools?.domain.add(bgImage?.url)})`,
                backgroundPosition: 'center center',
                backgroundSize: "cover",
                backgroundRepeat: 'no-repeat'
            };
            break;
        case 'color':
        default:
            bgStyles = {
                ...bgColor
            };
    };

    return (
        <Suspense fallback={<div />}>
            <div ref={ref} class="event-listing" style={bgStyles}>
                {inView ?
                    <div class={`event-listing-box ${data.style}`}>

                        {(title || data.blurb) &&
                            <div class={`grid-container event-listing-title ${primaryColor} ${data.title_alignment}`}>
                                {title && <h2 className="event-listing-section-title strong">{decode(title)}</h2>}
                                {data?.blurb && data?.style !== 'four-column-copy' && <div class='event-listing-section-blurb'><HtmlParser html={data.blurb} placeholders={placeholders} /></div>}
                            </div>
                        }

                        {data.add_calendar &&
                            <div class='grid-container'>
                                <div class='event-toggle'>
                                    <div class='icon list'>
                                        <FontAwesomeIcon icon={faThList} class={`${view === 'default' ? 'active' : null} ${primaryColor}`} onClick={view === 'calendar' ? toggleView : null} aria-label="show default view" aria-selected={view === 'default'} />
                                    </div>
                                    <div class='icon calendar'>
                                        <FontAwesomeIcon icon={faCalendarAlt} class={`${view === 'calendar' ? 'active' : null} ${primaryColor}`} onClick={view === 'default' ? toggleView : null} aria-label="show calendar view" aria-selected={view === 'calendar'} />
                                    </div>
                                </div>
                            </div>
                        }
                        {(view === 'default') && <Switcher data={data} settings={settings} placeholders={placeholders} listings={listings} />}
                        {(data.add_calendar && view === 'calendar') &&
                            <Calendar data={data} settings={settings} eventArray={eventArray} />
                        }
                        {((settings?.event && settings.event.submissions) || button) &&
                            <div class='grid-container'>
                                <div class={(button?.url && !settings?.event?.submissions) ? 'centered-button' : (button?.url && settings?.event?.submissions) ? 'button-wrapper' : 'submission-wrapper'}>
                                    {button?.url &&
                                        <Button url={button?.url} target={button?.target} type={data.button_style} title={button?.title} tone={data.background_value} />
                                    }
                                    {(settings?.event && settings.event.submissions) &&
                                        <EventSubmissionLink data={settings.event} tone={data.background_value} />
                                    }
                                </div>
                            </div>
                        }
                    </div>
                    : null}
            </div>
        </Suspense>
    );
};

const Switcher = ({ data, settings, placeholders, listings }) => {
    // force to upcoming for four column copy style
    if (data.style === 'four-column-copy') data.type = 'upcoming-events';

    if (listings && listings.length > 0) {
        switch (data.type) {
            case 'upcoming-events':
            case 'past-events':
                return (<UpcomingEvents data={data} style={data.style} settings={settings} listings={listings} />);
            case 'all-events':
            default:
                return (<AllEvents data={data} style={data.style} settings={settings} listings={listings} placeholders={placeholders} />);
        }
    } else {
        return (<NoneAvailable data={data} placeholders={placeholders} />);
    }
};

const NoneAvailable = ({ data, placeholders }) => {
    const noEventsMessageOverride = (placeholders && data.no_events_message_selection === 'dynamic') ? placeholders.single_line[data.no_events_message_override] : data.no_events_message_override
   
    return (
        <div class='grid-container'>
            <h3 class='error-header' style={{ padding: '2rem 0', textAlign: 'center' }}>{noEventsMessageOverride ? noEventsMessageOverride : 'There are no upcoming events.'}</h3>
        </div>
    );
};

const EventSubmissionLink = ({ data, tone }) => {

    return (
        <div class='post-submission-link'>
            {data.submission_description &&
                <p class={`submission-description ${tone === 'dark' ? 'white-txt' : 'body-copy-txt'}`}>{data.submission_description}</p>
            }
            <Button title={data.submission_link.title} url={data.submission_link.url} tone={tone} />
        </div>
    )
}

export default Start;
