import React from 'react';
import { decode } from 'html-entities';
import './date-highlight-item.scss';
import { ColorSplash } from '../../helpers/theme';

export default function DateHighlightItem({ data, event, settings }) {
    let event_date = (event?.next_occurrence && event.next_occurrence.start) ? new Date(event.next_occurrence.start.event_date.replace(/-/g, '\/')) : new Date(event.start.event_date.replace(/-/g, '\/'));
    let day = event_date.toLocaleDateString('default', { day: '2-digit' });
    let month = event_date.toLocaleDateString('default', { month: 'short' });
    let backgroundValue = data.background_value === 'dark' ? 'white-txt' : 'body-copy-txt';
    const splashColor = {
        borderColor: data.background_value === 'dark' ? '#fff' : settings.mvk_theme_config.other?.enable_color_splashes ? ColorSplash(settings.mvk_theme_config.other?.cs_color) : settings.design?.colors?.body_copy_color,
        color: data.background_value === 'dark' ? '#fff' : settings.mvk_theme_config.other?.enable_color_splashes ? ColorSplash(settings.mvk_theme_config.other?.cs_color) : settings.design?.colors?.body_copy_color
    }
    const label = data.show_category && event.category ? event.category : data.show_category && data.label ? data.label : '';

    return (
        <div className={`event`}>
            <div className={`event-thumbnail`} dangerouslySetInnerHTML={{ __html: event.feat_image }} />

            <div style={{ backgroundColor: data.date_box_background_color }} className="date">
                <span className={'month'}>{month}</span>
                <span className={'day'}>{day}</span>
            </div>
            {label ? <p className="label" style={splashColor}>{label}</p> : null}

            <h3 className={backgroundValue}>{decode(event.title)}</h3>
            <a className={data.background_value === 'dark' ? 'white-txt' : 'primary-txt'} href="#" aria-label={`link to ${event.title}`}>{'Learn More >'}</a>
        </div>
    );
}
