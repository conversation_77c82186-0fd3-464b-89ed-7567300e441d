.event-listing-box.date-highlight {

  .event-listing-wrapper {
    padding: 0 0 55px 0;
    @media screen and (min-width: 1001px) {
      display: block;
    }

    @media screen and (max-width: 1000px) {
      display: none;
    }

    &.slider-mobile {
      @media screen and (min-width: 1001px) {
        display: none;
      }

      @media screen and (max-width: 1000px) {
        display: block;
      }
    }

    &:not(.slider-mobile) {
      > div {
        display: flex;
        flex-wrap: wrap;
        a:nth-child(6n) {
          flex: 0 0 calc(20% - 10px);
        }
      }
    }

    &.slider-mobile {

      .custom-arrow {
        color: #fff;
        position: absolute;

        svg {
          height: 1rem;
          width: 1rem;

          @media screen and (min-width: 768px) {
            height: 1.25rem;
            width: 1.25rem;
          }

          @media screen and (min-width: 1366px) {
            height: 1.5rem;
            width: 1.5rem;
          }
        }
      }

      .slick-arrow {
        height: 65px;
        width: 30px;
        background: rgba(0,0,0,0.8);

        &.slick-prev {
          position: absolute;
          top: 50%;
          left: -0.9375rem;
          transform: translateY(-50%);

          &:before {
            content: '';
          }

          .custom-arrow {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translateY(-50%) translateX(-50%);
          }

        }

        &.slick-next {
          position: absolute;
          top: 50%;
          right: -0.9375rem;
          transform: translateY(-50%);

          &:before {
            content: '';
          }

          .custom-arrow {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translateY(-50%) translateX(-50%);
          }

        }

      }

    }



  }

  .event-list-item__module {
    flex: 0 1 calc(20% - 10px);
    margin: 0 5px 0 5px;
  }

  .slick-slide {
    > div {
      padding: 0 5px;
    }
  }

  .event-list-item__module,
  .slick-slide {
    position: relative;

    .date {
      position: absolute;
      top: 20px;
      right: 20px;
      width: 80px;
      height: 80px;
      display: flex;
      background: #fff;
      flex-direction: column;
      margin: 0;
      align-content: center;
      justify-content: center;

      @media screen and (max-width: 1000px) {
        top: 30px;
        right: 30px;
      }

    }

    &:first-child {
      margin-left: 0;
    }

    &:last-child {
      margin-right: 0;
    }

    .event-thumbnail {
      height: 230px;
      margin-bottom: 40px;
      position: relative;
      overflow: hidden;
    }

    .event-list-item-content {
      border: 0;
      padding: 0 0 30px 0;
    }

    .event {
      width: 100%;
    }

    img {
      position: absolute;
      top: 0;
      right: 0;
      bottom: 0;
      left: 0;
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    h3 {
      font-size: 1.375rem;
      margin-bottom: 25px;
    }

  }

}