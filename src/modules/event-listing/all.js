/*******************************************************************************************************
   Copyright 2020 - Calise Partners LLC d/b/a Imaginuity. All rights are reserved.
   Reproduction or transmission in whole or in part, in any form or by any means, electronic, mechanical
or otherwise, is prohibited without the prior written consent of Calise Partners LLC d/b/a Imaginuity.
********************************************************************************************************
   Project : 10618 Maverick Platform Development
   Author : Imaginuity Developers (<PERSON>, <PERSON>)
   Description : Event listing module.
   Creation Date : Mon Dec 14 2020
   Documentation : [documentation url - point to default wiki for project if no specific documentation for script]
 ********************************************************************************************************/

import React, { useState, useEffect, Fragment } from "react";
import Filtration from "src/partials/filtration";
import { getUniqueArray } from 'src/helpers';
import { decode } from 'html-entities';
import './all.scss';

import Slider from "react-slick";
import "slick-carousel/slick/slick.scss";
import "slick-carousel/slick/slick-theme.scss";
import { getMonthName } from 'src/helpers/hours';
import { eventSort } from 'src/helpers/event-sort';
import { PrevArrow, NextArrow } from 'src/helpers/slick';

import EventListItem from "./event-list-item";

const Start = ({ data, settings, listings, placeholders }) => {
    var categories = listings?.map((event) => {
        return event.mvk_item_tax?.cats?.flat();
    });

    categories = getUniqueArray(categories.flat(), ['name']);

    return (
        <Filtration categories={categories} toggleCategories={data.show_filter && !data.month_pagination} toggleSearch={data.show_search && !data.month_pagination} searchPlaceholder={data.search_placeholder} filterColor={data.background_value === 'dark' ? '#fff' : false}>
            <Container data={data} settings={settings} listings={listings} placeholders={placeholders} />
        </Filtration>
    );
};

const Container = (props) => {

    const sliderSettings = {
        nextArrow: <NextArrow />,
        prevArrow: <PrevArrow />,
        responsive: [
            {
                breakpoint: 1200,
                settings: {
                    dots: false,
                    adaptiveHeight: false,
                    arrows: true,
                    autoplay: false,
                    pauseOnHover: false,
                    infinite: false,
                    slidesToShow: 3,
                    slidesToScroll: 1,
                    speed: 800,
                    centerMode: false
                }
            },
            {
                breakpoint: 768,
                settings: {
                    dots: false,
                    adaptiveHeight: false,
                    arrows: true,
                    autoplay: false,
                    pauseOnHover: false,
                    infinite: false,
                    slidesToShow: 1,
                    slidesToScroll: 1,
                    speed: 800,
                    centerMode: true
                }
            },
        ]
    };

    if (!props.listings) {
        return (<div />);
    }
    let events = eventSort(props?.data?.sort_options, props.listings);

    return (
        <Fragment>

            {props.data.style === 'date-highlight' &&
                <Slider className={`event-listing-wrapper slider-mobile`} ref={(a) => a} {...sliderSettings}>
                    {events.map(
                        event => <EventListItem data={props.data} eventStyle={props.data.style} event={event} />
                    )}
                </Slider>
            }

            {props.data.style === 'date-highlight' &&
                <div className={`event-listing-wrapper`}>
                    <div>
                        {events.map(
                            event => <EventListItem data={props.data} eventStyle={props.data.style} event={event} />
                        )}
                    </div>
                </div>
            }

            {props.data.style !== 'date-highlight' &&
                <div className={`event-listing-all flexbox column${props.data.style === 'three-column' && props.data.list_item_alignment === 'center' ? ' align-center' : ''}`}>
                    <Results data={props.data} events={events} placeholders={props.placeholders} term={props.filtration.term} category={props.filtration.category} />
                </div>
            }

        </Fragment>
    );
};

const Results = ({ data, events, placeholders, term = false, category = false }) => {
    const [eventList, setEventList] = useState(events);
    const [current, setCurrent] = useState(new Date());
    const [noEventsMessageOverride, setNoEventsMessageOverride] = useState(placeholders ? placeholders.single_line[data.no_events_message_override] : data.no_events_message_override);

    useEffect(() => {
        if (data.month_pagination) {
            setEventList(() => events.filter((event) => {
                let startDate = new Date(event.compare_start.date);
                let endDate = new Date(event.compare_end.date);
                return new Date(startDate.getFullYear(), startDate.getMonth()) <= new Date(current.getFullYear(), current.getMonth()) && new Date(endDate.getFullYear(), endDate.getMonth()) >= new Date(current.getFullYear(), current.getMonth())
            }))
        }
        setNoEventsMessageOverride(placeholders ? placeholders.single_line[data.no_events_message_override] : data.no_events_message_override)
    }, [events, placeholders, current])

    const monthBack = () => {
        setCurrent(new Date(current.setMonth(current.getMonth() - 1)))
    }
    const monthForward = () => {
        setCurrent(new Date(current.setMonth(current.getMonth() + 1)))
    }

    return (
        <>
            {data.month_pagination &&
                <div className="grid-x">
                    <div className="cell">
                        <div className={`month-pagination${data.background_value === 'dark' ? ' white-txt' : ''}`}>
                            <h2>{getMonthName(current.getMonth())} {current.getFullYear()}</h2>
                            <div className="month-controls">
                                <PrevArrow onClick={() => monthBack()} ariaLabel={'previous month'} />
                                <NextArrow onClick={() => monthForward()} ariaLabel={'next month'} />
                            </div>
                        </div>
                    </div>
                </div>
            }
            <div>
                {eventList.length > 0 ? eventList?.filter(event => {
                    if (term) {
                        var isTerm = event?.title?.toLowerCase().includes(term.toLowerCase());
                    }
                    if (category) {
                        var isCategory = event?.mvk_item_tax?.cats?.some(function (item) {
                            return item.slug === category;
                        });
                    }

                    if (!term && !category) {
                        return event;
                    } else if (category && isCategory && term && isTerm) {
                        return event;
                    } else if (!category && term && isTerm) {
                        return event;
                    } else if (!term && category && isCategory) {
                        return event;
                    } 
                }).map(event => <EventListItem data={data} eventType="all" eventStyle={data.style} buttonIcon={data.button_icon} event={event} />)
                    :
                    <div className="grid-x">
                        <div className={`cell${data.background_value === 'dark' ? ' white-txt' : ''}`}>
                            {noEventsMessageOverride ?  <h3>{decode(noEventsMessageOverride)}</h3> : <h3>There are no upcoming events.</h3>}
                        </div>
                    </div>
                }
            </div>
        </>
    );
};

export default Start;