/*******************************************************************************************************
   Copyright 2020 - Calise Partners LLC d/b/a Imaginuity. All rights are reserved.
   Reproduction or transmission in whole or in part, in any form or by any means, electronic, mechanical
or otherwise, is prohibited without the prior written consent of Calise Partners LLC d/b/a Imaginuity.
********************************************************************************************************
   Project : 10618 Maverick Platform Development
   Author : Imaginuity Developers (<PERSON>, <PERSON>)
   Description : Styles for event listing module.
   Creation Date : Wed, Jan 20, 2021
   Documentation : [documentation url - point to default wiki for project if no specific documentation for script]
 ********************************************************************************************************/

$sm_breakpoint: 768px;
$md_breakpoint: 1200px;

#modules-container .event-listing {
    padding-top: 30px;
    padding-bottom: 30px;

    // @media (min-width: 768px) { // matching to nav / footer container
    //     padding: 30px 40px;
    // }

    // @media (min-width: 992px) { // matching to nav / footer container
    //     padding: 30px 55px;
    // }

    // .event-listing-box {
    //     .trunk {
    //         max-width: 1050px;

    //         .flexbox {
    //             .flex1 {
    //                 padding: 0px 10px;
    //             }
    //         }
    //     }
    // }

    .event-listing-section-title {
        line-height: 2.25rem;

        @media (min-width: $sm_breakpoint) {
            border-bottom: 20px;
            line-height: normal;
        }

        @media (min-width: $md_breakpoint) {
            border-bottom: 30px;
        }
    }
    .label {
        font-size: 0.75rem;
        margin: 0 0 0.75rem;
        border-bottom: 2px solid;
        display: inline-block;
    }
    .event-toggle {
        display: flex;
        justify-content: flex-end;
        align-items: center;
        margin: 1rem 0;
        svg {
            display: flex;
            height: 30px;
            width: 30px;
            padding: 5px;
            border-radius: 5px;
            transition: 0.2s;
            // &.active {
            //     background: #ebebeb;
            // }
            &:not(.active) {
                cursor: pointer;
            }
        }
        .calendar svg {
            margin-bottom: 2px;
        }
    }

    .button-wrapper {
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;
    }
    .centered-button {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        margin: 20px auto 0px auto;
    }
    .month-pagination {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin: 2rem 0;
        h2 {
            margin: 0;
        }
        .month-controls {
            display: flex;
            .site-arrow-prev, .site-arrow-next {
                position: unset;
                transform: none;
                top: unset;            
            }
        }
        @media (min-width: 768px) {
            h2 {
                margin-right: 1rem;
            }
        }
    }
    .two-column-cards {
        .event-listing-all,
        .event-listing-upcoming {
            .event-list-item__module {
                @media (max-width: $sm_breakpoint) {
                   width: 100%;
                }
                .event-list-item-content {
                    display: flex;
                    flex-wrap: wrap;
                    margin: 45px 0;
                    box-shadow: 3px 3px 6px rgba(0, 0, 0, 0.16);
                    position: relative;
                    padding: 0;
                    transition: 0.2s;
                    background: #fff;
                    @media (min-width: $sm_breakpoint) {
                        flex-wrap: nowrap;
                        margin: 45px 20px;
                    }
                    .event-list-item-image {
                        display: block;
                        width: 100%;
                        height: 100%;
                        background-size: cover;
                        background-repeat: no-repeat;
                        background-position: 50% 50%;
                        border-radius: 10px 0 0 10px;
                        @media (min-width: $sm_breakpoint) {
                            width: 50%;
                        }
                    }
                    .event-list-item-right-content {
                        display: block;
                        padding: 1.5rem 1rem 2rem;
                        width: 100%;
                        text-align: center;
                        @media (min-width: $sm_breakpoint) {
                            width: 50%;
                            text-align: left;
                            box-sizing: border-box;
                        }
                        .event-category-label {
                            margin: -1.5rem -1rem 1.5rem;
                            padding: 5px;
                            text-transform: uppercase;
                            text-align: center;
                            @media (max-width: $sm_breakpoint) {
                                border-radius: 0 !important;
                            }
                        }
                        .event-list-item-text-content {
                            display: flex;
                            flex-wrap: wrap;
                            @media (max-width: $sm_breakpoint) {
                                justify-content: center;
                            }
                            .title {
                                order: 3;
                                width: 100%;
                                margin-bottom: 0.5rem;
                                h2 {
                                    font-size: 1.25rem;
                                    @media (min-width: $md_breakpoint) {
                                        font-size: 1.5rem;
                                    }
                                }
                            }
                            .stores {
                                order: 4;
                                font-size: 0.875rem;
                                .related-store {
                                    margin-bottom: 0.75rem;
                                }
                                @media (min-width: $md_breakpoint) {
                                    font-size: 1rem;
                                }
                            }
                            .date {
                                order: 1;
                                font-size: 0.75rem;
                                @media (min-width: $md_breakpoint) {
                                    font-size: 0.875rem;
                                }
                            }
                            .comment {
                                order: 2;
                                width: 100%;
                                font-size: 0.875rem;
                                @media (min-width: $md_breakpoint) {
                                    font-size: 1rem;
                                }
                            }
                        }
                        .share-icons {
                            display: flex;
                            margin: 1rem 0;
                            // pointer-events: all;
                            @media (max-width: $sm_breakpoint) {
                                justify-content: center;
                            }
                            a {
                                border-radius: 50%;
                                display: flex;
                                align-items: center;
                                justify-content: center;
                                width: 30px;
                                height: 30px;
                                margin-right: 1rem;
                                transition: 0.2s;
                            }
                        }
                        .event-list-item-link-button {
                            position: absolute;
                            bottom: -30px;
                            left: 50%;
                            transform: translateX(-50%);
                            font-size: 0.875rem;
                            padding: 1rem 2rem;
                            @media (min-width: $sm_breakpoint) {
                                right: -20px;
                                left: unset;
                                width: auto;
                                transform: none;
                            }
                        }
                    }
                }
                &:hover .event-list-item-content {
                    box-shadow: 10px 10px 20px rgba(0, 0, 0, 0.2);
                }
                &.button-outline {
                    .event-list-item-link-button:not(:hover) {
                        background: #fff !important;
                        color: initial !important;
                    }
                }
                &.button-solid {
                    .event-list-item-link-button:hover {
                        background: #fff !important;
                        color: initial !important;
                    }
                }
            }
            @media (min-width: $sm_breakpoint) {
                & > div {
                    display: flex;
                    flex-wrap: wrap;
                    justify-content: space-between;
                }
                .event-list-item__module {
                    width: 48%;
                    .event-list-item-content {
                        border-bottom: none;
                    }
                }
            }
        }
    }
    .three-column {
        .event-listing-all,
        .event-listing-upcoming {
            &.align-center > div {
                justify-content: center;
            }
            // justify-content: center;
            .event-list-item__module {
                color: inherit;
                .event-list-item-content {
                    border: none;
                    .event-list-item-image {
                        display: block;
                        width: 100%;
                        max-width: none;
                        .event-hover-wrapper {
                            overflow: hidden;
                            min-height: 375px;
                            position: relative;
                            img {
                                min-height: 100%;
                                object-fit: cover;
                                height: 100% !important;
                                position: absolute;
                            }
                            @media (min-width: $sm_breakpoint) {
                                min-height: 220px;
                               
                            }
                            @media (min-width: 1024px) {
                                min-height: 375px;
                            }
                        }
                    }
                    .event-content-lower {
                        display: flex;
                        flex-direction: column;
                        // justify-content: space-between;
                        // @media (min-width: $md_breakpoint) {
                        //     &.no-blurb {
                        //         min-height: 250px;
                        //     }
                        //     &.with-blurb {
                        //         min-height: 300px;
                        //     }
                        // }
                        .title {
                            margin: 0;
                        }
                        .label {
                            font-size: 0.75rem;
                            margin: 1.5rem 0 0.75rem;
                            border-bottom: 2px solid;
                            display: inline-block;
                        }
                        .location-category {
                            margin-top: 1.5rem;
                        }
                        .event-description {
                            margin-top: 0.5rem;
                        }
                        .date-block {
                            display: inline-flex;
                            align-items: center;
                            padding: 0.5rem 0.75rem;
                            margin: 1.5rem 0;
                            .icon-calendar {
                                width: 1.125rem;
                                margin-right: 0.5rem;
                                margin-top: -2px;
                            }
                            .icon-repeat {
                                margin-left: 0.5rem;
                            }
                        }
                    }
                    .learn-more {
                        text-decoration: underline;
                    }
                }
            }
            @media (min-width: $sm_breakpoint) {
                & > div {
                    display: flex;
                    flex-wrap: wrap;
                }
                a {
                    width: 33%;
                    &:nth-child(3n + 1) .event-list-item-content {
                        margin: 0 0.75rem 0 0;
                    }
                    &:nth-child(3n + 2) .event-list-item-content {
                        margin: 0 0.75rem;
                    }
                    &:nth-child(3n + 3) .event-list-item-content {
                        margin: 0 0 0 0.75rem;
                    }
                    .event-list-item-content {
                        padding: 30px 0;
                    }
                }
            }
        }
    }

    .four-column-copy-inner {
        display: flex;
        flex-flow: column nowrap;

        @media (min-width: 768px) {
            display: grid;
            grid-template-columns: 1fr 3fr;
            column-gap: 1rem;
        }

        .first-column-copy {
            @media (max-width: 767px) {
                text-align: center;
                margin-bottom: 1.5rem;
            }
        }

        .event-list-wrap {
            @media (min-width: 768px) {
                display: grid;
                grid-template-columns: 1fr 1fr 1fr;
                column-gap: 1rem;
            }
        }

        .slick-list {
            padding-bottom: 2.5rem;
        }

        .slick-arrow {
            // @media (max-width: 767px) {
            //     bottom: 0;
            //     top: unset;
            //     height: 32px;
            //     width: 32px;
            //     border-top: 3px solid white;
            //     border-right: 3px solid white;
            //     border-bottom: none;
            //     border-left: none;
            // }

            &.slick-prev {
                left: 35%;
            }

            &.slick-next {
                right: 35%;
            }

            &:before {
                content: "";
            }
        }

        .event-content-container {
            width: 100%;
        }

        .event-list-item-image {
            @media (max-width: 767px) {
                margin-bottom: 0.5rem;
            }
        }

        .date-string {
            font-size: 1.75rem;

            @media (max-width: 767px) {
                font-size: 1.2rem;
                text-align: center;
            }
        }

        .title {
            @media (max-width: 767px) {
                text-align: center;
                margin-bottom: 1rem;
            }
        }

        .event-list-item-link-button {
            @media (max-width: 767px) {
                display: none;
            }
        }
    }
    .simple-list {
        .event-list-item__module {
            
            .simple-list-event {
                display: flex;
                width: 100%;
                .featured-image {
                    width: 40%;
                    max-width: 120px;
                    height: 120px;
                    margin-right: .5rem;
                    overflow: hidden;
                    img {
                        width: 100%;
                        height: 100%;
                        object-fit: cover;
                        transition: .3s;
                    }
                    @media (min-width: 640px) {
                        margin-right: 1rem;
                    }
                }
                .content-wrapper {
                    width: 60%;
                    p {
                        margin: 0 0 .25rem;
                        font-weight: bold;
                        display: block;
                    }
                    h3 {
                        margin-top: .75rem;
                    }
                }
            }
            &:hover {
                .simple-list-event {
                    .featured-image {
                        img {
                            transform: scale(1.1);
                        }
                    }
                }
            }
        }
       
    }
}
