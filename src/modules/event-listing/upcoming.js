/*******************************************************************************************************
   Copyright 2020 - Calise Partners LLC d/b/a Imaginuity. All rights are reserved.
   Reproduction or transmission in whole or in part, in any form or by any means, electronic, mechanical
or otherwise, is prohibited without the prior written consent of Calise Partners LLC d/b/a Imaginuity.
********************************************************************************************************
   Project : 10618 Maverick Platform Development
   Author : Imaginuity Developers (<PERSON>, <PERSON>)
   Description : Event listing module.
   Creation Date : Mon Dec 14 2020
   Documentation : [documentation url - point to default wiki for project if no specific documentation for script]
 ********************************************************************************************************/

import React, { useEffect, Fragment } from "react";
import Slider from "react-slick";
import "slick-carousel/slick/slick.scss";
import "slick-carousel/slick/slick-theme.scss";
import EventListItem from "./event-list-item";
import { eventSort } from 'src/helpers/event-sort';
import { PrevArrow, NextArrow } from 'src/helpers/slick';

const Start = ({ data, settings, listings }) => {

    useEffect(() => {
        var StyleSheet = document.createElement('style');
        var StyleStrings = ``;
        StyleSheet.setAttribute('id', 'event-listing-dynamic-styles');
        StyleStrings += `.event-list-item__module .event-hover-wrapper {
            position: relative;
        }`;

        StyleStrings += `.event-list-item__module .event-hover-wrapper::before {
            content: '';
            position: absolute;
            display: flex;
            justify-content: center;
            align-items: center;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0);
            transition: .4s ease-in-out opacity;
        }`;

        StyleStrings += `.event-list-item__module .hover:hover.event-hover-wrapper::before, .hover:hover .event-hover-wrapper::before {
            content: "${settings?.mvk_theme_config?.labels?.learn_more ? settings?.mvk_theme_config?.labels?.learn_more : 'Learn More'}";
            color: #fff;
            background-color: ${pylot?.design?.hexToRGB(settings.design?.colors?.secondary_color, '.65')};
        }`;

        StyleSheet.innerHTML = StyleStrings;
        const isStyleSheetAlreadyLoaded = document.contains(document.getElementById('event-listing-dynamic-styles'));
        if (!isStyleSheetAlreadyLoaded) {// remove stylesheet | Prevents duplicates, and allows styles to be reset
            document?.getElementsByTagName('head')[0]?.appendChild(StyleSheet);
        }
    }, [])

    // var { events = [], button, button_icon } = data;

    if (!listings) {
        return (<div />);
    }

    let events = eventSort(data.sort_options, listings);
    
    // slice events if quantity greater than zero
    // let quantity = data.quantity ? parseInt(data.quantity) : 0;
    let quantity;
    if (data?.style === 'four-column-copy') {
        quantity = 3;
    } else if (data?.quantity) {
        quantity = parseInt(data?.quantity);
    } else {
        quantity = 0;
    }

    let eventsSliced = quantity > 0 ? events.slice(0, quantity) : events;

    const dateHighlightSettings = {
        nextArrow: <NextArrow />,
        prevArrow: <PrevArrow />,
        slidesToShow: 5,
        centerMode: true,
        centerPadding: '0',
        dots: false,
        adaptiveHeight: false,
        infinite: false,
        slidesToScroll: 1,
        lazyLoad: true,
        responsive: [
            {
                breakpoint: 1200,
                settings: {
                    dots: false,
                    adaptiveHeight: false,
                    arrows: true,
                    autoplay: false,
                    pauseOnHover: false,
                    infinite: false,
                    slidesToShow: 3,
                    slidesToScroll: 1,
                    speed: 800,
                    centerMode: false
                }
            },
            {
                breakpoint: 768,
                settings: {
                    dots: false,
                    adaptiveHeight: false,
                    arrows: true,
                    autoplay: false,
                    pauseOnHover: false,
                    infinite: false,
                    slidesToShow: 1,
                    slidesToScroll: 1,
                    speed: 800,
                    centerMode: true
                }
            },
        ]
    };

    const sliderSettings = {
        responsive: [
            {
                breakpoint: 10000,
                settings: "unslick"
            },
            {
                breakpoint: 767,
                settings: {
                    dots: false,
                    adaptiveHeight: false,
                    arrows: true,
                    autoplay: false,
                    pauseOnHover: false,
                    infinite: true,
                    slidesToShow: 1,
                    slidesToScroll: 1,
                    speed: 800,
                }
            },
        ]
    };

    const color = data.background_value === 'dark' ? 'white-txt' : 'body-copy-txt';

    return (
        <Fragment>
            {data.style === 'date-highlight' &&
                <div className="grid-container">
                    <Slider className={`event-listing-wrapper slider-mobile`} ref={(a) => a} {...dateHighlightSettings}>
                        {eventsSliced.map(
                            event => <EventListItem data={data} eventStyle={data.style} event={event} />
                        )}
                    </Slider>
                </div>
            }

            {data.style === 'date-highlight' &&
                <div className="grid-container">
                    <div className={`event-listing-wrapper`}>
                        <div>
                            {eventsSliced.map(
                                event => <EventListItem data={data} eventStyle={data.style} event={event} />
                            )}
                        </div>
                    </div>
                </div>
            }

            {data.style !== 'date-highlight' &&

                <div className='grid-container'>
                    <div className={`event-listing-upcoming flexbox column ${data?.style}-inner${data.style === 'three-column' && data.list_item_alignment === 'center' ? ' align-center' : ''}`}>

                        {(data?.style === 'four-column-copy' && data?.first_column_copy) &&
                            <div className={`first-column-copy ${color}`}
                                dangerouslySetInnerHTML={{ __html: data?.first_column_copy }} />
                        }

                        {data?.style === 'four-column-copy' &&
                            <Slider className='event-list-wrap mobile-slider' ref={(a) => a} {...sliderSettings}>
                                {eventsSliced?.map(event => <EventListItem data={data} eventType="upcoming"
                                    eventStyle={data.style} event={event} />)}
                            </Slider>
                        }

                        {(data?.style !== 'four-column-copy' && data?.style !== 'date-highlight') &&
                            <div>
                                {eventsSliced?.map(event => <EventListItem data={data} eventType="upcoming"
                                    eventStyle={data.style} event={event} />)}
                            </div>
                        }
                    </div>
                </div>

            }

        </Fragment>

    );
};

export default Start;
