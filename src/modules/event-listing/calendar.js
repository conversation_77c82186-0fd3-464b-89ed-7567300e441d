import React, { useEffect } from "react";
import FullCalendar from '@fullcalendar/react';
import dayGridPlugin from '@fullcalendar/daygrid';
import { Background, Color } from 'src/helpers/theme';
import { Coloring } from 'src/helpers';

import './calendar.scss';

const Start = ({ data, settings, eventArray }) => {
    let buttons = document.getElementsByClassName('fc-button');
    let days = document.getElementsByClassName('fc-daygrid');

    let bgColor = data.calendar_event_background ? Coloring(data.calendar_event_background, settings) : Background('light', settings?.mvk_theme_config?.other?.primary_button_style);
    let color = data.calendar_event_background_value === 'light' ? Coloring('body_copy_color', settings) : '#fff';
    let selectedBackground = settings?.design?.colors?.background_color;

    
    useEffect(() => {

        for (var i = 0; i < days.length; i++) {
            days[i].style.backgroundColor = selectedBackground;
        }
        for (var i = 0; i < buttons.length; i++) {
            buttons[i].style.backgroundColor = bgColor;
            buttons[i].style.color = color;
        }

    }, [buttons, days, bgColor, color, selectedBackground]);

    return (
        <div class={`grid-container ${data.background_value}`}>
            <FullCalendar
                plugins={[dayGridPlugin]}
                headerToolbar={{
                    left: 'title',
                    center: '',
                    right: 'today,prev,next'
                }}
                initialView='dayGridMonth'
                eventColor={bgColor}
                eventTextColor={color}
                contentHeight='auto'
                events={eventArray}
            />
        </div>
    );
}

export default Start;