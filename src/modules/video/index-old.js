/*******************************************************************************************************
   Copyright 2020 - Calise Partners LLC d/b/a Imaginuity. All rights are reserved. 
   Reproduction or transmission in whole or in part, in any form or by any means, electronic, mechanical 
or otherwise, is prohibited without the prior written consent of Calise Partners LLC d/b/a Imaginuity.
********************************************************************************************************
   Project : 10618 Maverick Platform Development
   Author : Imaginuity Developers (<PERSON>, <PERSON>)
   Description : [example description]
   Creation Date : Wed Nov 18 2020
   Documentation : [documentation url - point to default wiki for project if no specific documentation for script]
 ********************************************************************************************************/

import React, { useState, useEffect } from "react";
import { isMobile } from 'react-device-detect';

import './index.scss';

const Start = ({data, settings}) => {
    return (<Container data={data} settings={settings} />);
};

const Container = ({data, settings}) => {
    var image = (data.background_image) ? pylot.design.background(data.background_image.url) : {};
    var color = pylot.design.coloring(data.background_color, 'backgroundColor');
    var background = { ...image, ...color };
    var other = (data.background_value == 'dark') ? 'light' : 'dark';
    var primaryColor = settings.design?.colors?.primary_color;
    
    return (
        <div class="video" style={background}>
            <div class={`player-container flexbox column trunk`}>
                <div class={`flex1 title center ${other}`}>
                    <h3>{data.title}</h3>
                </div>
                <div class={`flex1 content ${other}`}>
                    <div dangerouslySetInnerHTML={{ __html:data.content }} />
                </div>
                <Player data={data} primaryColor={primaryColor} />
            </div>
        </div>
    );
};

const Player = ({data, primaryColor}) => {
    const [player, setPlayer]   = useState(false);
    const [swiping, setSwiping] = useState(false);

    const Touching = (e) => { setSwiping(false); }
    const Swiping  = (e) => { setSwiping(true); }
    
    var background = pylot.design.background(data.thumbnail_image.url);

    const Click = (e) => {
        if ((e.type == 'click' && isMobile) || swiping) { return; }
        e.target.parentNode.remove();
        e.target.remove();
        setPlayer(true);
    }
    
    return (
        <div class="flex1 player">
            <div class="overlay" style={background}>
                <span class='play-button' style={{backgroundColor: primaryColor}} onTouchStart={Touching} onTouchMove={Swiping} onTouchEnd={Click} onClick={Click} />
            </div>
            {player && <Screen data={data} />}
        </div>
    );
};

const Screen = ({data}) => {
    if (data.video_type == "youtube") {
        return (<YouTube data={data} />);
    } else if (data.video_type == "vimeo") {
        return (<Vimeo data={data}/>);
    } else {
        return (<div>PLAYER NOT AVAILABLE</div>);
    }
};

const YouTube = ({ data }) => {
    return (
        <iframe width="600" height="350" src={`https://www.youtube.com/embed/${data.youtube_id}?autoplay=1`} frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowfullscreen />
    );
};

const Vimeo = ({ data }) => {
    return (
        <iframe src={`https://player.vimeo.com/video/${data.vimeo_id}?color=ef0800&title=0&byline=0&portrait=0&autoplay=1`} width="600" height="350" frameborder="0" allow="autoplay; fullscreen" allowfullscreen />
    );
};

export default Start;