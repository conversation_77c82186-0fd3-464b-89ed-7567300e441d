/*******************************************************************************************************
   Copyright 2020 - Calise Partners LLC d/b/a Imaginuity. All rights are reserved. 
   Reproduction or transmission in whole or in part, in any form or by any means, electronic, mechanical 
or otherwise, is prohibited without the prior written consent of Calise Partners LLC d/b/a Imaginuity.
********************************************************************************************************
   Project : 10618 Maverick Platform Development
   Author : Imaginuity Developers (<PERSON>, <PERSON>)
   Description : [example description]
   Creation Date : Wed Nov 18 2020
   Documentation : [documentation url - point to default wiki for project if no specific documentation for script]
 ********************************************************************************************************/

import React, { useState } from "react";
import { isMobile } from 'react-device-detect';

import './index.scss';

const Start = ({ data, settings }) => {
    return (<Container data={data} settings={settings} />);
};

const Container = ({ data, settings }) => {
    var image = (data.background_image) ? pylot.design.background(data.background_image.url) : {};
    var color = pylot.design.coloring(data.background_color, 'backgroundColor');
    var background = { ...image, ...color };
    var other = (data.background_value == 'dark') ? 'light' : 'dark';
    var primaryColor = settings.design?.colors?.primary_color;

    return (
        <div class={`video grid-container ${data.restrict_module_width ? 'restricted' : ''}`}>
            <div class={`title-content-container`}>
                {data.title &&
                    <div class={`title center ${other}`}>
                        <h3>{data.title}</h3>
                    </div>
                }
                {data.content &&
                    <div class={`content ${other}`}>
                        <div dangerouslySetInnerHTML={{ __html: data.content }} />
                    </div>
                }
            </div>
            <Player data={data} primaryColor={primaryColor} />
        </div>
    );
};

const Player = ({ data, primaryColor }) => {
    const [swiping, setSwiping] = useState(false);

    const Touching = (e) => { setSwiping(false); }
    const Swiping = (e) => { setSwiping(true); }

    var background = pylot.design.background(data.thumbnail_image.url);

    const Click = (e) => {
        if ((e.type == 'click' && isMobile) || swiping) { return; }
        e.target.parentNode.remove();
        e.target.remove();
    }

    return (
        <div class="player">
            <div class="overlay" style={background}>
                <span class='play-button' style={{ backgroundColor: primaryColor }} onTouchStart={Touching} onTouchMove={Swiping} onTouchEnd={Click} onClick={Click} />
            </div>
            <Screen data={data} />
        </div>
    );
};

const Screen = ({ data }) => {
    if (data.video_type == "youtube") {
        return (<YouTube data={data} />);
    } else if (data.video_type == "vimeo") {
        return (<Vimeo data={data} />);
    } else {
        return (<div>PLAYER NOT AVAILABLE</div>);
    }
};

const YouTube = ({ data }) => {
    return (
        <div class='youtube-video-container' dangerouslySetInnerHTML={{ __html: data.youtube_video }} />
    );
};

const Vimeo = ({ data }) => {
    return (
        <div class='vimeo-video-container' dangerouslySetInnerHTML={{ __html: data.vimeo_video }} />
    );
};

export default Start;