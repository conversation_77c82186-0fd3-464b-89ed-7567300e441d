/*******************************************************************************************************
   Copyright 2020 - Calise Partners LLC d/b/a Imaginuity. All rights are reserved. 
   Reproduction or transmission in whole or in part, in any form or by any means, electronic, mechanical 
or otherwise, is prohibited without the prior written consent of Calise Partners LLC d/b/a Imaginuity.
********************************************************************************************************
   Project : 10618 Maverick Platform Development
   Author : Imaginuity Developers (<PERSON>, <PERSON>)
   Description : SCSS for module/Video
   Creation Date : Wed Nov 18 2020
   Documentation : [documentation url - point to default wiki for project if no specific documentation for script]
 ********************************************************************************************************/

#modules-container {
    .video {
        padding-top: 2rem;
        padding-bottom: 2rem;

        .title {
            margin-bottom: 2rem;

            h3 {
                font-size: 2.8125rem;
                line-height: 3.4375rem;
            }
        }

        .content {
            text-align: center;
            font-size: 1.3125rem;
            line-height: 2rem;
        }

        .player {
            position: relative;
        }

        .overlay {
            position: absolute;
            left: 50%;
            top: 50%;
            z-index: 500;
            transform: translate(-50%, -50%);
            height: 100%;
            width: 100%;
            background-color: black;

            .play-button {
                position: absolute;
                top: 50%;
                left: 50%;
                z-index: 501;
                transform: translate(-50%, -50%);
                cursor: pointer;
                padding: 1rem;
                height: 3.875rem;
                width: 3.875rem;
                display: flex;
                align-items: center;
                justify-content: center;
                border-radius: 50%;
                transition: 0.3s;

                &:after {
                    content: "";
                    width: 0;
                    height: 0;
                    border-top: 1rem solid transparent;
                    border-bottom: 1rem solid transparent;
                    border-left: 2rem solid #fff;
                    margin-left: 0.3125rem;
                }
            }
        }
    }
}
