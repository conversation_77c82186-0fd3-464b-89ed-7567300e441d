import React, { useState, createContext } from 'react';
import { useNavigate } from 'react-router-dom';

const defaultQuizData = {
    quizSections: [],
    potentialResults: [],
    isActiveQuizSectionValidated: false,
    activeQuizSectionIndex: 0,
    activeResultIndex: 0,
    isQuizStarted: false,
    isQuizComplete: false,
    errorMsg: ''
}

const validateQuizResults = (quizSections, potentialResults, resultRuleSets) => {
    let quizResults = [];

    if(!quizSections?.length || !resultRuleSets?.length) return {};

    let firstResultIndexMatched = null;

    resultRuleSets.forEach(({ matchedResultIndex, rules }) => {
        let isRulesetMatched = true;
        quizResults.push(`${quizSections[matchedResultIndex].question}: ${quizSections[matchedResultIndex].selectedAnswer}`);

        rules.forEach(({ quizSectionIndex, condition, value }) => {

            if(condition === 'equals') { // only supports equals right now
                const answerFromMatchedQuizSection = quizSections[quizSectionIndex] ? quizSections[quizSectionIndex].selectedAnswer : {};
                if(answerFromMatchedQuizSection !== value) {
                    isRulesetMatched = false;
                }
            }
        })
        
        if((!firstResultIndexMatched && firstResultIndexMatched !== 0) && (isRulesetMatched && potentialResults[matchedResultIndex])) {
            firstResultIndexMatched = matchedResultIndex;
        }
    })

    if (!firstResultIndexMatched && firstResultIndexMatched !== 0) return {};
    // set results in storage to send later with a form
    sessionStorage.setItem('quiz_results', JSON.stringify(quizResults));

    return {
        valid: true,
        resultIndex: firstResultIndexMatched 
    }
}

const clearQuizSectionAnswers = (quizSections) => {
    if(!quizSections?.length) return quizSections;

    return quizSections.map((quizSection) => {
        quizSection.selectedAnswer = null;
    });
}

export const QuizDataContext = createContext();
export const QuizDataProvider = (props) => {
    const [quizData, setQuizData] = useState(defaultQuizData);
    const navigate = useNavigate();

    const {      
        quizSections = [],
        potentialResults = [],
        resultRuleSets = [],
        activeQuizSectionIndex = 0,
        isActiveQuizSectionValidated = false,
        activeResultIndex = 0,
        isQuizComplete = false,
        isQuizStarted = false,
        errorMsg = ''
    } = quizData;

    const initQuizData = (payload) => {
        setQuizData({ ...quizData, ...payload })
    }

    const selectQuizSectionAnswer = ({ value = '', sectionIndex = '' }) => {
        if(!value || (!sectionIndex && sectionIndex !== 0)) return null;
        if(!quizSections?.length) return null;
        if(!quizSections[sectionIndex]) return null;

        quizSections[sectionIndex].selectedAnswer = value;

        setQuizData({ ...quizData, 
            quizSections: quizSections,
            isActiveQuizSectionValidated: true
        })
    }

    const viewNextPage = () => {
        if(isQuizComplete) return null;
        if(!isQuizStarted) {
            let isSelectedAnswerOnNextSection = quizSections[0] ? !!quizSections[0].selectedAnswer : false;

            setQuizData({ ...quizData, ...{
                isQuizStarted: true,
                isActiveQuizSectionValidated: isSelectedAnswerOnNextSection
            }})
            return null;
        }

        // If no selected answer, return & set errorMsg (force users to answer)
        // => could add logic for mandatory / optional fields later
        const isLastQuizSection = activeQuizSectionIndex >= (quizSections?.length - 1);
        if(isLastQuizSection) {
            // validate quiz & check ruleSet to determine result
            const quizValidation = validateQuizResults(quizSections, potentialResults, resultRuleSets);

            setQuizData({ ...quizData, ...{// defaulting to first result for invalid forms
                isQuizComplete: true,
                activeResultIndex: quizValidation?.valid ? quizValidation.resultIndex : 0,
                quizSections: clearQuizSectionAnswers(quizSections)
            }
            })
            // send user to results link (new way);
            navigate(potentialResults[quizValidation.resultIndex].inquiryLink);
            
            return null;
        }

        let isSelectedAnswerOnNextSection = 
            quizSections[activeQuizSectionIndex + 1] ? 
                !!quizSections[activeQuizSectionIndex + 1].selectedAnswer : false

        setQuizData({ ...quizData, ...{
            activeQuizSectionIndex: activeQuizSectionIndex + 1,
            isActiveQuizSectionValidated: isSelectedAnswerOnNextSection
        }})
    }

    const viewPrevPage = () => {
        if(!isQuizStarted) return null;
        if(isQuizComplete) {
            setQuizData({ ...quizData, ...{
                isQuizComplete: false
            }})
            return null;
        }

        setQuizData({ ...quizData, ...{
            activeQuizSectionIndex: activeQuizSectionIndex > 0 ? activeQuizSectionIndex - 1 : activeQuizSectionIndex
        }})
    }

    return (
        <QuizDataContext.Provider value={{quizData, initQuizData, selectQuizSectionAnswer, viewNextPage, viewPrevPage}}>
            {props.children}
        </QuizDataContext.Provider>
    );
}

// Example QuizData => in case this needs to be mocked up in WP at a later time
const QuizDataSchemaExample = {
    quizSections: [ // array of quiz questions that will appear
        {
            question: '', // String | question title
            selectedAnswer: null, // 'value' field of the selected choice (stays null until user selects)
            choices: [ // Array of choices the user will have for this question
                {
                    flag: '', // Optional | if extra bolded / colored text is needed
                    displayValue: '', // Required | text to describe choice / answer
                    value: '' // Required | value that will be submitted
                },
            ],
        },
    ],
    potentialResults: [ // array of potential quiz results ('resultRuleSets' will determine which one appears)
        {
            title: '', // String | (no HTML)
            blurb: ``, // HTML | WYSIWYG content
            inquiryLink: '', // URL or path to page
            signupNowLink: '' // URL or path to page
        },
    ],
    // This needs to be placed in priority order (1st one to match determines result)
    // Array of potential rulesets to help match potential quiz results
    resultRuleSets: [
        { // Ex: ruleset that has two conditions which need to be true
            matchedResultIndex: 0, // index of the result that will be displayed if the rules are true
            rules: [
                { 
                    quizSectionIndex: 0, 
                    condition: 'equals', // only supports 'equals' right now
                    value: 'some-value' // 'value' field of the choice / answer you are wanting to match
                },
                { 
                    quizSectionIndex: 2,
                    condition: 'equals',
                    value: 'some-value-2'
                }
            ]
        },
        { // Ex: ruleset that only has one condition which needs to be true
            matchedResultIndex: 2,
            rules: [ 
                { 
                    quizSectionIndex: 0,
                    condition: 'equals',
                    value: '1-person' 
                }
            ]
        }
    ],
    activeQuizSectionIndex: 0, 
    activeResultIndex: 0,
    isQuizStarted: false, // switch to true to init quiz questions (otherwise show start screen)
    isQuizComplete: false, // switch to true once all questions are answered
}
