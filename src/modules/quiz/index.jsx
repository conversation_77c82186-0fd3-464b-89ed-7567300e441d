import React from 'react'
import { QuizDataProvider } from './context.jsx';
const JllFlexSpaceRecommender = React.lazy(() => import('./jll-flex-space-recommender/index.jsx'));

const translator = {
    "jll-flex-space-recommender": JllFlexSpaceRecommender
}

const Start = ({ data }) => {
    const { select_quiz, title, blurb, hide_intro, restrict_module_width, button_style, quiz_color } = data;
    const QuizModule = translator[select_quiz?.value];

    if(!QuizModule) return null;
    return (
        <QuizDataProvider>
            <QuizModule 
                title={title}
                blurb={blurb}
                hideIntro={hide_intro}
                restrictModuleWidth={restrict_module_width}
                buttonStyle={button_style}
                quizColor={quiz_color}
            />
        </QuizDataProvider>
    )
}

export default Start;
