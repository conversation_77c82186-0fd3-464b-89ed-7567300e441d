import React, { useEffect, useContext } from 'react'

import { QuizDataContext } from '../context.jsx'
import { JllFlexSpaceRecommenderQuizData } from './data'
import Clicker from 'src/helpers/clicker.js'
import './index.scss';

//PARTIALS
const Button = React.lazy(() => import('src/partials/button'));

import { QuizChoiceCheckmark, Blurb, JllQuizContainer, JllQuizInnerContainer, QuizButton, QuizChoice, QuizChoiceDisplayValue, QuizChoiceFlag, QuizChoicesContainer, QuizQuestionTitle, QuizResultsBlurb, QuizResultsContainer, QuizResultsTitle, QuizSectionContainer, Title, QuizResultsBtnContainer } from './styles'

const Start = ({
    title = '',
    blurb = '',
    hideIntro = false,
    restrictModuleWidth = false,
    buttonStyle,
    quizColor,
}) => {
    const { quizData, setQuizData, initQuizData, viewNextPage } = useContext(QuizDataContext);
    const {
        quizSections = [],
        potentialResults = [],
        resultRuleSets = [],
        activeQuizSectionIndex = 0,
        isActiveQuizSectionValidated = false,
        activeResultIndex = 0,
        isQuizComplete = false,
        isQuizStarted = false,
    } = quizData;

    useEffect(() => {
        if (hideIntro) {
            JllFlexSpaceRecommenderQuizData.isQuizStarted = true;
        }
        initQuizData(JllFlexSpaceRecommenderQuizData)
    }, [])

    return (
        <JllQuizContainer className={`quiz-container grid-container module-spacer ${restrictModuleWidth ? 'restricted' : ''}`}>
            <JllQuizInnerContainer quizColor={quizColor}>
                {title && <Title quizColor={quizColor}>{title}</Title>}
                {!isQuizStarted && blurb ?
                    <Blurb
                        dangerouslySetInnerHTML={{ __html: blurb }}
                        quizColor={quizColor}
                    />
                    : null}

                {!isQuizComplete &&
                    isQuizStarted &&
                    quizSections?.length ?
                    quizSections.map(({ question, choices, selectedAnswer }, i) => (
                        <QuizSection
                            // key={question + 'quiz-section' + i}
                            isActive={i === activeQuizSectionIndex}
                            question={question}
                            choices={choices}
                            selectedAnswer={selectedAnswer}
                            sectionIndex={i}
                            quizColor={quizColor}
                        />
                    ))
                    : null}

                {!isQuizComplete &&
                    <Button
                        class={`quiz-button started-${isQuizStarted && !isActiveQuizSectionValidated}`}
                        onClick={viewNextPage}
                        title={!isQuizStarted ? `Let's get started` : 'NEXT > >'}
                        type={buttonStyle}
                        buttonFunction={'styled'}
                    />
                }

                {isQuizComplete && potentialResults?.length &&
                    potentialResults.map(({ title, blurb, inquiryLink, signupNowLink }, i) => {

                        if (activeResultIndex === i) return (
                            <QuizResults
                                title={title}
                                blurb={blurb}
                                inquiryLink={inquiryLink}
                                signupNowLink={signupNowLink}
                                quizColor={quizColor}
                            />
                        )
                        return null;
                    })
                }
            </JllQuizInnerContainer>
        </JllQuizContainer>
    )
}

// Depending on future Quiz styles
// this should be made a helper for all Quiz's
const QuizSection = ({
    question = '',
    choices = [],
    isActive = false,
    selectedAnswer,
    sectionIndex,
    quizColor
}) => {
    const { selectQuizSectionAnswer } = useContext(QuizDataContext);
    return (
        <QuizSectionContainer
            isActive={isActive}
        >
            {question &&
                <QuizQuestionTitle quizColor={quizColor}>{question}</QuizQuestionTitle>
            }
            {choices?.length ?
                <QuizChoicesContainer quizColor={quizColor}>
                    {choices.map(({ flag, displayValue, value }, i) => (
                        <QuizChoice
                            onClick={() => selectQuizSectionAnswer({
                                value: value,
                                sectionIndex: sectionIndex
                            })}
                            isSelected={selectedAnswer === value}
                            // key={displayValue + question + i}
                            quizColor={quizColor}
                        >
                            <QuizChoiceCheckmark isSelected={selectedAnswer === value} quizColor={quizColor} />
                            {flag && <QuizChoiceFlag quizColor={quizColor}>{flag}</QuizChoiceFlag>}
                            {displayValue && <QuizChoiceDisplayValue quizColor={quizColor}>{displayValue}</QuizChoiceDisplayValue>}
                        </QuizChoice>
                    ))}
                </QuizChoicesContainer>
                : null}
        </QuizSectionContainer>
    )
}

// Depending on future Quiz styles
// this should be made a helper for all Quiz's
const QuizResults = ({ title, blurb, inquiryLink, signupNowLink, quizColor }) => (
    <QuizResultsContainer>
        {title &&
            <QuizResultsTitle
                dangerouslySetInnerHTML={{ __html: title }}
            />
        }
        {blurb &&
            <QuizResultsBlurb
                dangerouslySetInnerHTML={{ __html: blurb }}
            />
        }

        <QuizResultsBtnContainer>
            <QuizButton
                as={Clicker}
                url={inquiryLink}
            >Inquire Now</QuizButton>
            <QuizButton
                as={Clicker}
                url={inquiryLink}
            >Sign Up Now</QuizButton>
        </QuizResultsBtnContainer>
    </QuizResultsContainer>
)

export default Start;
