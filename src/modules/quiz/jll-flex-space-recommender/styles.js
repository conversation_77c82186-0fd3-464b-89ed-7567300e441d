import styled, { css } from 'styled-components';
import { breakpoints } from '../styles';


export const JllQuizContainer = styled.div`

    // this should be global in the future
    &, * {
        box-sizing: border-box;
    }
`

export const JllQuizInnerContainer = styled.div`
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    border-radius: 20px;

    @media (min-width: ${breakpoints.md}) {
        ${'' /* border: 1px solid #707070;
        padding-bottom: 65px; */}
        border: 1px solid ${props => props?.quizColor};
        padding-bottom: 45px;
    }
`

export const Title = styled.h2`
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    font-size: 2rem;
    font-weight: 100;
    line-height: 40px;
    margin-bottom: 15px;
    color: ${props => props?.quizColor};

    @media (min-width: ${breakpoints.md}) {
        background-color: white;
        padding-left: 40px;
        padding-right: 40px;
        margin-top: -20px;
        margin-bottom: 25px;
    }

    @media (min-width: ${breakpoints.lg}) {
        margin-bottom: 35px;
    }
` 

export const Blurb = styled.div`
    text-align: center;
    margin-top: 10px;
    margin-bottom: 15px;
    color: ${props => props?.quizColor};

    @media (min-width: ${breakpoints.sm}) {
        margin-top: 20px;
        margin-bottom: 40px;
    }

    @media (min-width: ${breakpoints.md}) {
        margin-top: 25px;
        margin-bottom: 45px;
    }

    p {
        font-size: 1.125rem;
        line-height: 1.5;
    }
`

export const QuizSectionContainer = styled.div`
    width: 100%;
    transition: opacity .2s ease;

    @media (min-width: ${breakpoints.md}) {
        padding: 0px 125px;
    }    

    ${props => !props.isActive && css`
        display: none;
    `}
`

export const QuizQuestionTitle = styled.h3`
    width: 100%;
    text-align: center;
    font-size: 1.75rem;
    ${'' /* font-weight: 900; */}
    font-weight: 100;
    text-transform: uppercase;
    line-height: 34px;
    margin-bottom: 40px;
    color: ${props => props?.quizColor};

    @media (min-width: ${breakpoints.sm}) {
        margin-bottom: 45px
    }  

    @media (min-width: ${breakpoints.md}) {
        margin-bottom: 35px
    } 

    @media (min-width: ${breakpoints.lg}) {
        margin-bottom: 40px
    } 
`


// QUIZ CHOICE STYLES

export const QuizChoicesContainer = styled.div`
    display: flex;
    flex-direction: column;
    align-items: center;
    ${'' /* border: 2px solid ${props => props?.theme?.colors?.primary || '#8D3803'}; */}
    border: 2px solid ${props => props?.quizColor || '#8D3803'};
    ${'' /* margin-bottom: 60px; */}
    margin-bottom: 45px;
    border-radius: 10px;

    @media (min-width: ${breakpoints.sm}) {
        flex-direction: row;
    }  
`

export const QuizChoice = styled.div`
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    padding: 10px;
    width: 100%;
    min-height: 128px;
    border-bottom: 2px solid ${props => props?.theme?.colors?.primary || '#8D3803'};
    cursor: pointer;

    &:last-of-type {
        border-bottom: none;
    }

    @media (min-width: ${breakpoints.sm}) {
        min-height: 115px;
        border-bottom: none;
        ${'' /* border-right: 2px solid ${props => props?.theme?.colors?.primary || '#8D3803'}; */}
        border-right: 2px solid ${props => props?.quizColor || '#8D3803'};

        &:last-of-type {
            border-right: none;
        }
    }  

    @media (min-width: ${breakpoints.md}) {
        min-height: 140px;
    }  

    @media (min-width: ${breakpoints.lg}) {
        min-height: 150px;
    }

    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        ${'' /* background: ${props => props?.theme?.colors?.primary || '#8D3803'}; */}
        background: ${props => props?.quizColor || '#8D3803'};
        opacity: 0;
        transition: opacity .15s ease;
    }

    &:hover::before {
        opacity: .03;
    }

    ${props => props.isSelected && css`
        cursor: default;
        &:hover::before, &::before {
            opacity: .03;
        }
    `}
`

export const QuizChoiceFlag = styled.p`
    font-weight: 600;
    ${'' /* color: ${props => props?.theme?.colors?.primary || '#8D3803'}; */}
    color: ${props => props?.quizColor || '#8D3803'};
    font-size: 1rem;
    margin-bottom: 10px;
`

export const QuizChoiceDisplayValue = styled.p`
    font-size: 1.25rem;
    font-weight: 900;
    ${'' /* color: black; */}
    color: ${props => props?.quizColor || '#000000'};
    margin: 0;
`

export const QuizChoiceCheckmark = styled.div`
    transform: translate(20px, -20px);
    transition: .15s ease transform;
    transition-delay: 0;

    &, &::before, &::after {
        content: '';
        position: absolute;
        top: 0;
        right: 0;
    }

    &::before { // check mark background     
        width: 0;
        height: 0;
        border-width: 1.5em;
        ${'' /* border: 18px solid ${props => props?.theme?.colors?.primary || '#8D3803'}; */}
        border: 18px solid ${props => props?.quizColor || '#8D3803'};
        border-left-color: transparent;
        border-bottom-color: transparent;     
    }

    &::after{ // check mark
        transform: rotate(45deg);
        margin-right: 6px; // center icon & adjust for rotation offset
        margin-top: 1px; // center icon & adjust for rotation offset
        height: 11px;
        width: 6px;
        border-bottom: 2px solid white;
        border-right: 2px solid white;
    }

    ${props => props.isSelected && css`
        transform: translate(0, 0);
        transition-delay: .05s;
    `}
`



// QUIZ RESULT STYLES

export const QuizResultsContainer = styled.div`
    padding-top: 25px;
    border-top: 2px solid ${props => props?.theme?.colors?.primary || '#8D3803'};

    @media (min-width: ${breakpoints.md}) {
        border: none;
        padding: 0px 125px;
    }    
`

export const QuizResultsTitle = styled.h3`
    width: 100%;
    font-size: 1.75rem;
    font-weight: 900;
    line-height: 34px;

    @media (min-width: ${breakpoints.md}) {
        border-top: 2px solid ${props => props?.theme?.colors?.primary || '#8D3803'};
        padding-top: 30px;
    }    
`

export const QuizResultsBlurb = styled.div`
    .mediumcase-title {
        font-size: 1.375rem;
        margin-bottom: 25px;
    }

    .smallcase-title {
        margin-bottom: 0;
        margin-top: 30px;
    }

    .boldness {
        color: ${props => props?.theme?.colors?.primary || '#8D3803'};
    }

    p, ul, li {
        font-size: 1.125rem;
        line-height: 1.5;
    }

    li {
        margin-bottom: 5px;
    }

`

export const QuizResultsBtnContainer = styled.div`
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    margin-top: 50px;

    @media (min-width: ${breakpoints.sm}) {
        flex-direction: row;
        align-items: center;
    } 
`

// May should use global "Button" component instead??
export const QuizButton = styled.button`
    display: flex;
    align-items: center;
    justify-content: center;
    background: ${props => props?.theme?.colors?.primary || '#8D3803'};
    border: 1px solid ${props => props?.theme?.colors?.primary || '#8D3803'};
    border-radius: 8px;
    min-height: 40px;
    padding: 8px 40px;
    font-size: 1rem;
    font-weight: 400;
    /* border: none; */
    color: white;
    text-transform: uppercase;
    transition: .2s ease;
    cursor: pointer;

    ${QuizResultsBtnContainer} &:first-of-type {
        margin-bottom: 15px;
        
        @media (min-width: ${breakpoints.sm}) {
            margin-bottom: 0;
            margin-right: 30px
        }   
    }

    &:hover {
        background: white;
        color:  ${props => props?.theme?.colors?.primary || '#8D3803'};
        /* border: 1px sol */
    }

    ${props => props.isDisabled && css`
        pointer-events: none;
        cursor: default;
        opacity: .3;
    `}
`
