import styled from 'styled-components';

export const FeaturedSales = styled.div`
    padding: 2rem 0;
    .title-container {
        margin-bottom: 1rem;
        h2 {
            margin-bottom: 1rem;
        }
    }
    .sale {
        &:nth-child(4n+1) .top-section {
            background-color: ${props => props.backgroundColor.color_1};
            color: ${props => props.backgroundColor.value_1 === 'dark' ? '#fff' : props => props.textColor};
            .type-of-deal {
                color: ${props => props.backgroundColor.value_1 === 'dark' ? '#fff' : props => props.secondaryColor};
            }
        }
        &:nth-child(4n+2) .top-section {
            background-color: ${props => props.backgroundColor.color_2};
            color: ${props => props.backgroundColor.value_2 === 'dark' ? '#fff' : props => props.textColor};
            .type-of-deal {
                color: ${props => props.backgroundColor.value_2 === 'dark' ? '#fff' : props => props.secondaryColor};
            }
        }
        &:nth-child(4n+3) .top-section {
            background-color: ${props => props.backgroundColor.color_3};
            color: ${props => props.backgroundColor.value_3 === 'dark' ? '#fff' : props => props.textColor};
            .type-of-deal {
                color: ${props => props.backgroundColor.value_3 === 'dark' ? '#fff' : props => props.secondaryColor};
            }
        }
        &:nth-child(4n+4) .top-section {
            background-color: ${props => props.backgroundColor.color_4};
            color: ${props => props.backgroundColor.value_4 === 'dark' ? '#fff' : props => props.textColor};
            .type-of-deal {
                color: ${props => props.backgroundColor.value_4 === 'dark' ? '#fff' : props => props.secondaryColor};
            }
        }
        .top-section {
            padding: 1.5rem;
            @media (min-width: 1200px) {
                padding: 1.5rem 2.5rem;
            }
            .title {
                font-size: 1rem;
                text-transform: uppercase;
                font-weight: 300; // for FLEX, probably remove later
                @media (min-width: 768px) {
                    font-size: 1.625rem;
                }
            }
            .featured-image {
                max-height: 45px;
                display: block;
                margin: .5rem 0;
            }
            .bottom {
                display: flex;
                justify-content: space-between;
                align-items: flex-end;
                margin-top: 2rem;
                .type-of-deal {
                    .label {
                        margin-left: 1.15rem;
                        font-weight: 300; // for FLEX, probably remove later
                    }
                    .amount {
                        font-size: 2.5rem;
                        line-height: 1;
                        font-weight: 300; // for FLEX, probably remove later
                        small, sup {
                            font-size: 1.25rem;
                        }
                        @media (min-width: 768px) {
                            font-size: 5rem;
                            small, sup {
                                font-size: 2.5rem;
                            }
                        }
                        @media (min-width: 992px) {
                            font-size: 3rem;
                            small, sup {
                                font-size: 1.5rem;
                            }
                        }
                        @media (min-width: 1200px) {
                            font-size: 5rem;
                            small, sup {
                                font-size: 2.5rem;
                            }
                        }
                    }
                }
                .button {
                    margin-top: 1rem;
                }
            }
        }
        .post-copy {
            position: relative;
            padding: 1.5rem 2.5rem;
            ul {
                padding-left: 2.5rem;
                margin: 0;
            }
        }
    }
    @media (min-width: 992px) {
        .sale-container {
            display: flex;
            flex-wrap: wrap;
            .sale {
                width: 50%;
                // display: flex;
                // flex-direction: column;
                .top-section {
                    display: flex;
                    // flex: 1;
                    justify-content: space-between;
                    flex-direction: column;
                    min-height: 300px;
                    .featured-image {
                        // max-height: 100px;
                        object-fit: contain;
                        object-position: left;
                    }
                }
                &:nth-child(even) .post-copy:after {
                    content: '';
                    position: absolute;
                    top: 1.5rem;
                    bottom: 1.5rem;
                    left: -1px;
                    border-left: 2px solid;
                }
            }
        }
    }
`;