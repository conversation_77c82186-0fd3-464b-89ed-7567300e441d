import React from "react";
import { decode } from 'html-entities';
import { useInView } from 'react-intersection-observer';

// Helpers
import { Coloring } from "src/helpers";
const Imaging = React.lazy(() => import('src/helpers/imaging'));
const HtmlParser = React.lazy(() => import('src/helpers/html-parser'));

// Partials
const Button = React.lazy(() => import('src/partials/button'));
// Styles
import { FeaturedSales } from './styles';

const Start = ({ data, settings }) => {
    const { ref, inView } = useInView({
        triggerOnce: true,
        fallbackInView: true
    });

    return (
        <FeaturedSales
            ref={ref}
            className={`featured-sales ${data.style}`}
            textColor={Coloring('body_copy_color', settings)}
            secondaryColor={Coloring('secondary_color', settings)}
            backgroundColor={data.background_colors}
        >
            {inView ? <>
                {(data.title || data.blurb) &&
                    <div className={`title-container grid-container${data.restrict_module_width ? ' restricted' : ''}`}>
                        <div className='grid-x'>
                            {data.title &&
                                <div className={`cell ${data.title_alignment}`}>
                                    <h2>{decode(data.title)}</h2>
                                </div>
                            }
                            {data.blurb &&
                                <div className='cell'>
                                    <div className='copy' dangerouslySetInnerHTML={{ __html: data.blurb }} />
                                </div>
                            }
                        </div>
                    </div>
                }
                <div className={`sale-container grid-container${data.restrict_module_width ? ' restricted' : ''}`}>
                    {data.sales && data.sales.map(sale => <Sale sale={sale} data={data} settings={settings} />)}
                </div>
            </> : null}
        </FeaturedSales>
    );
}

const Sale = ({ sale, data, settings }) => {

    const featImage = {
        url: sale.featured_image,
        alt: `${sale.title} featured image`
    }

    return (
        <div className='sale'>
            <div className='top-section'>
                <div class='top'>
                    <h3 className='title'>{decode(sale.title)}</h3>
                    {sale.featured_image &&
                        <Imaging className='featured-image' data={featImage} />
                    }
                    {sale.excerpt &&
                        <div className='blurb' dangerouslySetInnerHTML={{ __html: sale.excerpt }} />
                    }
                </div>
                <div className='bottom'>
                    {sale.type_of_deal &&
                        <div className='type-of-deal'>
                            <div className='label'>from</div>
                            <div className='amount' dangerouslySetInnerHTML={{ __html: sale.type_of_deal }} />
                        </div>
                    }
                    {sale.url &&
                        <div className='button'>
                            <Button title={sale.link_text_override ? sale.link_text_override : settings?.mvk_theme_config?.labels?.learn_more} url={sale.url} target={sale.external_link ? sale.external_link : null} tone={data.button_value} type={data.button_style} />
                        </div>
                    }
                </div>
            </div>
            {sale.post_copy &&
                <div className='post-copy'>
                    <HtmlParser html={sale.post_copy} data={data} />
                </div>
            }
        </div>
    );
}

export default Start;