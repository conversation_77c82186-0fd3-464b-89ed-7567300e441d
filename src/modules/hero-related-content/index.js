import React, { useState, useEffect } from 'react';
import Slider from "react-slick";
import "slick-carousel/slick/slick.scss";
import "slick-carousel/slick/slick-theme.scss";
import { decode } from 'html-entities';
import { useInView } from 'react-intersection-observer';

// Helpers
import { Coloring } from "src/helpers";
import Clicker from "src/helpers/clicker";
// Partials
const Button = React.lazy(() => import('src/partials/button'));
// Styled Components
import { HeroRelatedContent, Feature, RelatedContent } from './styles.js';

const Start = ({ data, settings }) => {
    const [playState] = useState('running');
    const shuffle = arr => [...arr].sort(() => Math.random() - 0.5);
    const [slides] = useState(data.slides ? shuffle(data.slides) : false);
    const siteMaxWidth = settings?.mvk_theme_config?.layout?.site_max_width ? settings?.mvk_theme_config?.layout?.site_max_width : '75';

    const sliderSettings = {
        dots: false,
        arrows: false,
        autoplay: data.slides.length > 1 ? true : false,
        autoplaySpeed: data.timer * 1000,
        adaptiveHeight: true,
        pauseOnHover: false,
        infinite: true,
        slidesToShow: 1,
        slidesToScroll: 1,
        fade: true,
        cssEase: 'linear'
    };

    return (
        <HeroRelatedContent
            className='hero-related-content'
            time={data.timer}
            timerColor={Coloring(data.timer_color, settings)}
            playState={playState}
            maxWidth={parseInt(siteMaxWidth) / 2}
        >
            <Slider className={'random-slider'} ref={(a) => a} {...sliderSettings}>
                {slides && slides.map((slide) => <Slide data={data} slide={slide} settings={settings} />)}
            </Slider>
        </HeroRelatedContent>
    );
}

const Slide = ({ data, slide, settings }) => {

    return (
        <div className='slide-wrapper'>
            <FeatureContainer data={slide} settings={settings} />
            {data.slides?.length > 1 &&
                <div className='timer'></div>
            }
            <div className='related-content-container'>
                <RelatedContentContainer data={slide.related_content_1} settings={settings} />
                <RelatedContentContainer data={slide.related_content_2} settings={settings} />
            </div>
        </div>
    )
}

const FeatureContainer = ({ data, settings }) => {
    const bgColor = Coloring(data.background_color, settings);
    const color = data.background_value === 'dark' ? '#fff' : Coloring('body_copy_color', settings);
    const { ref, inView } = useInView({
        triggerOnce: true,
        fallbackInView: true
    });

    return (
        <Feature
            ref={ref}
            className={`feature ${data.background_type} ${data.mobile_background_image ? 'with-mobile' : ''} ${`slide-align-${data.alignment}`} ${inView ? 'add-bg-image' : ''}`}
            backgroundColor={bgColor}
            backgroundImage={data.background_type === 'image' ? data.background_image?.url : ''}
            backgroundImageMobile={data.background_type === 'image' && data.mobile_background_image ? data.mobile_background_image.url : data.background_image?.url}
            textColor={color}
        >
            <div className='grid-container'>
                <div className='content-wrapper'>
                    {data.label &&
                        <p className='label'>{decode(data.label)}</p>
                    }
                    {data.header &&
                        <h1>{decode(data.header)}</h1>
                    }
                    {data.blurb &&
                        <div dangerouslySetInnerHTML={{ __html: data.blurb }} />
                    }
                </div>
            </div>
        </Feature>
    );
}

const RelatedContentContainer = ({ data, settings }) => {
    const bgColor = Coloring(data.related_background_color, settings);
    const color = data.related_background_value === 'dark' ? '#fff' : Coloring('body_copy_color', settings);
    let bgImage = data.related_background_image ? data.related_background_image?.url || data.related_background_image : '';
    if (data.image_override) {
        bgImage = data.image_override.url;
    }
    const { ref, inView } = useInView({
        triggerOnce: true,
        fallbackInView: true
    });
    return (
        <RelatedContent
            ref={ref}
            className={`related-content ${data.related_background_type} ${data.type} ${inView ? 'add-bg-image' : ''}`}
            backgroundColor={bgColor}
            backgroundImage={bgImage}
            textColor={color}
        >
            <div className='content-wrapper'>
                <div className="bulk">
                    {data.related_label &&
                        <p className='label'>{decode(data.related_label)}</p>
                    }
                    {data.related_header &&
                        <h3>{decode(data.related_header)}</h3>
                    }
                    {data.related_blurb &&
                        <p dangerouslySetInnerHTML={{ __html: data.related_blurb }} />
                    }
                </div>
                <div class='link-wrapper'>
                    {data.related_button &&
                        <div className='button'>
                            <Button title={data.related_button.title} url={data.related_button.url} target={data.related_button.target} tone={data.related_background_value} type={data.related_button_style} />
                        </div>
                    }
                    {data.related_link &&
                        <div className='link'>
                            <Clicker type='anchor' url={data.related_link.url} target={data.related_link.target} ariaLabel={`learn more about ${data.related_header}`}>{decode(data.related_link.title)}</Clicker>
                        </div>
                    }
                    {(!data.related_button && !data.related_link && data.related_post_link) &&
                        <div className='link'>
                            <Clicker type='anchor' url={data.related_post_link} ariaLabel={`learn more about ${data.related_header}`}>{settings?.mvk_theme_config?.labels?.learn_more ? settings?.mvk_theme_config?.labels?.learn_more : 'Learn More'}</Clicker>
                        </div>
                    }
                </div>
            </div>
        </RelatedContent>
    )
}

export default Start;