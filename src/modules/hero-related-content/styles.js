import styled from 'styled-components';

export const HeroRelatedContent = styled.div`
    .slick-slide {
        pointer-events: none;
        .timer:after {
            content: '';
            position: absolute;
            display: block;
            height: 4px;
            background-color: ${props => props.timerColor};
            transition: width ${props => props.time}s linear;
            animation-duration: ${props => props.time}s;
            // animation-iteration-count: infinite;
            animation-fill-mode: forwards;
            animation-delay: .5s;
        }
        &.slick-active {
            pointer-events: all;
            .timer:after {
                animation-name: timerWidth;
                animation-play-state: ${props => props.playState};
            }
        }
        &[aria-hidden='true'] * {
            display: none;
        }
    }
    @keyframes timerWidth {
        0% {
            width: 0;
        }
        100% {
            width: 100%;
        }
    }

    @media (min-width: 768px) {
        .related-content-container {
            display: flex;
        }
    }


    @media (min-width: 1200px) {
        .related-content-container {
            .related-content {
                .content-wrapper {
                    width: ${props => props.maxWidth}rem;
                    // width: 540px;
                    padding: 15px;
                    box-sizing: border-box;
                    > p {
                        max-width: 75%;
                    }
                }
                &:first-of-type {
                    .content-wrapper {
                        margin: 0 15px 0 auto;
                    }
                }
                &:last-of-type {
                    .content-wrapper {
                        margin-left: 30px;
                    }
                }
            }
        }
    }
`;

export const Feature = styled.section`
    display: flex;
    align-items: center;
    padding: 2rem 0;
    min-height: 400px;
    color: ${props => props.textColor};
    &.color {
        background-color: ${props => props.backgroundColor};
    }
    &.image {
        background-position: center center;
        background-size: cover;
        background-repeat: no-repeat;
        &.add-bg-image {
            background-image: url(${props => props.backgroundImage});
            &.with-mobile {
                @media (max-width: 767px) {
                    background-image: url(${props => props.backgroundImageMobile});
                }
            }
        }
    }
    
    .grid-container {
        width: 100%;
        .content-wrapper {
            .label {
                font-weight: bold;
                margin-bottom: .5rem;
                letter-spacing: .25rem;
            }
            h1 {
                margin-bottom: 1rem;
            }
        }
    }
    &.slide-align {
        &-center, &-center-contained, &-left-centered-text, &-right-centered-text {
            text-align: center;
        }
        &-right, &-right-contained {
            text-align: right;
        }
        @media (min-width: 1024px) {
            &-center-contained {
                .content-wrapper {
                    max-width: 70%;
                    margin: auto;
                }
            }
            &-left-contained {
                 .content-wrapper {
                    max-width: 70%;
                }
            }
            &-left-centered-text {
                .content-wrapper {
                    max-width: 53%;
                }
            }
            &-right-contained {
                .content-wrapper {
                    max-width: 70%;
                    margin: 0 0 0 auto;
                }
            }
            &-right-centered-text {
                .content-wrapper {
                    max-width: 53%;
                    margin: 0 0 0 auto;
                }
            }
        }
    }
    @media (min-width: 1024px) {
        min-height: 500px;
    }
`;

export const RelatedContent = styled.div`
    padding: 2rem 10px;
    color: ${props => props.textColor};
    &.color {
        background-color: ${props => props.backgroundColor};
    }
    &.image, &.select-post {
        background-position: center center;
        background-size: cover;
        background-repeat: no-repeat;
        &.add-bg-image {
            background-image: url(${props => props.backgroundImage});
        }
    }
    .content-wrapper {
        .label {
            font-weight: bold;
            margin-bottom: .5rem;
            letter-spacing: .25rem;
        }
        .link-wrapper {
            margin-top: 1.5rem;
            .button {
                margin-bottom: 1rem;
            }
            .link a {
                text-decoration: underline;
                color: ${props => props.textColor};
            }
        }
    }

    @media (min-width: 768px) {
        display: flex;
        align-items: center;
        width: 50%;
        padding: 3rem 15px;
        .content-wrapper {
            display: flex;
            flex-direction: column;
            height: 100%;
            justify-content: space-between;
            .link-wrapper {
                display: flex;
                align-items: center;
                flex-wrap: wrap;
                .button {
                    margin: 1rem 1rem 1rem 0;
                }
                .link {
                    margin: 1rem 0;
                }
            }
        }
    }

    @media (min-width: 1200px) {
        padding: 3rem 0;
        min-height: 335px;
        box-sizing: border-box;
    }

`;