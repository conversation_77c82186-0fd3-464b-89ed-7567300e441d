import React, { useEffect, useState, useContext } from 'react';
import { useInView } from 'react-intersection-observer';
import Slider from "react-slick";
import { AppContext } from 'src/contexts/app';
// Helpers
import HtmlParser from 'src/helpers/html-parser';
import Imaging from 'src/helpers/imaging';
import { Coloring } from 'src/helpers';
import { PrevArrow, NextArrow } from 'src/helpers/slick';

// Styles
import * as S from './styles';
import "slick-carousel/slick/slick.scss";
import "slick-carousel/slick/slick-theme.scss";

const Start = ({ data, settings, placeholders }) => {
    const appContext = useContext(AppContext);
    const { ref, inView } = useInView({
        triggerOnce: true,
        fallbackInView: true
    });
    const bgColor = Coloring(data.background_color, settings);
    const color = data.background_value === 'dark' ? '#fff' : Coloring('body_copy_color', settings);
    const bgImage = (placeholders && data.background_image_selection === 'dynamic') ? placeholders.image[data?.background_image_dynamic] : data?.background_image
    const images = (placeholders && data.mobile_images_selection === 'dynamic') ? placeholders.image[data?.images_dynamic] : data?.images
    const desktopImage = (placeholders && data.desktop_image_selection === 'dynamic') ? placeholders.image[data?.desktop_image_dynamic] : data.desktop_image
    const brandElement = (placeholders && data.brand_element_selection === 'dynamic') ? placeholders.image[data?.brand_element_dynamic] : data.brand_element

    return (
        <S.ImageReel
            ref={ref}
            className={`image-reel ${data.background_type} ${data.layout_options}${(inView && data.background_type === 'image') ? ' add-bg-image' : ''}`}
            bgColor={bgColor}
            textColor={color}
            bgImage={bgImage ? bgImage.url : ''}
        >
            {inView ?
                <div className='grid-container'>
                    <div className='grid-x grid-margin-x'>
                        <div className='content cell medium-6'>
                            <div className='inner-wrapper'>
                                <HtmlParser html={data.content} placeholders={placeholders} />
                            </div>
                        </div>
                        <div className='images cell medium-6 large-5' role="presentation">
                            {(images && appContext.width < 640) && <ImageSlider images={images} />}
                            {(desktopImage && appContext.width > 639) && <ImageBackground image={desktopImage} />}
                        </div>
                    </div>
                    {brandElement && <div className='brand-element show-for-medium' aria-hidden='true'><Imaging data={brandElement} /></div>}
                </div>
                : null}
        </S.ImageReel>
    )
}

const ImageSlider = ({ images }) => {

    const sliderSettings = {
        vertical: false,
        arrows: true,
        speed: 300,
        nextArrow: <NextArrow />,
        prevArrow: <PrevArrow />,
        mobileFirst: true,
        adaptiveHeight: true,
        responsive: [
            {
                breakpoint: 1023,
            },
            {
                breakpoint: 10000,
                settings: 'unslick'
            }
        ]
    }

    return (
        <Slider ref={(a) => a} {...sliderSettings}>
            {images?.map((image) =>
                <div class='slide'>
                    <Imaging data={image} forceLoad={true} />
                </div>
            )}
        </Slider>
    )
}

const ImageBackground = ({ image }) => {
    return (
        <S.BackgroundImage
            className='background-images'
            bgImage={image.url}
        >
        </S.BackgroundImage>
    )
}

export default Start;