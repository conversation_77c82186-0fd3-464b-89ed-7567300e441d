import styled from 'styled-components';

export const ImageReel = styled.div`
    overflow: hidden;
    color: ${props => props.textColor};
    padding: 3rem 0;
    &.color {
        background-color: ${props => props.bgColor};
    }
    &.image {
        background-position: center center;
        background-size: cover;
        background-repeat: no-repeat;
        &.add-bg-image {
            background-image: url(${props => props.bgImage});
        }
    }
    .content {
        margin-bottom: 2rem;
        @media (min-width: 640px) {
            margin-bottom: 0;
        }
    }
    @media (min-width: 640px) {
        &.reel-left {
            .content {
                order: 2;
            }
        }
    }
    .images {
        @media (min-width: 640px) {
            pointer-events: none;
        }
        .slide {
            img {
                width: 100%;
            }
        }
    }
    @media (min-width: 640px) {
        padding: 0;
        .grid-container {
            position: relative;
            .brand-element {
                position: absolute;
                bottom: 80px;
                right: 0;
                max-width: 200px;
            }
        }
        .content {
            margin-bottom: 0;
            display: flex;
            align-items: center;
            min-height: 800px;
        }
        .images {
            .slick-slider div {
                transition: none;
            }
            margin: auto;
            .slick-list {
                min-height: 800px;
                box-sizing: border-box;
                .slick-slide {
                    margin: 0;
                }
                .slide {
                    margin: .75rem 0;
                }
            }
        }
        &.reel-left {
            .grid-container {
                .brand-element {
                    right: unset;
                    left: 0;
                }
            }
        }
    }
`

export const BackgroundImage = styled.div`
    background-image: url(${props => props.bgImage});
    background-size: cover;
    height: 800px;
    animation: scroll 40s linear infinite;
    transform: translate3d(0, 0, 0);
    @keyframes scroll {
        from {background-position: center 0;}
        to {background-position: center calc(100% - 800px);}
    }
`
