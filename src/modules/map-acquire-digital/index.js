import React, { useEffect } from 'react';
import Map from 'src/partials/map/acquire-digital';
import { Coloring } from "src/helpers";
import { AdMap } from "./styles";
import './index.scss';

const Start = ({ data, settings }) => {
    const bgColor = Coloring(data.background_color, settings);

    return (
        <AdMap
            className={`aquire-digital-map-module ${data.background_type} ${data.background_value}`}
            backgroundColor={bgColor}
            backgroundImage={data.background_type === 'image' ? data.background_image?.url : ''}
            textColor={data.background_value === 'dark' ? '#fff' : Coloring('body_copy_color', settings)}
        >
            {(data.title || data.blurb) &&
                <div className='grid-container'>
                    <div className='grid-x'>
                        {data.title &&
                            <div class='cell title center'>
                                <h2>{data.title}</h2>
                            </div>
                        }
                        {data.blurb &&
                            <div class='cell blurb' dangerouslySetInnerHTML={{ __html: data.blurb }} />
                        }
                    </div>
                </div>
            }
            <Map />
        </AdMap>
    );
};

export default Start;