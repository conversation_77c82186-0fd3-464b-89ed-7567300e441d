import styled from 'styled-components'

export const SaleBanner = styled.div`
    &.color {
        background-color: ${props => props.bgColor};
    }
    &.image {
        background-image: url(${props => props.bgImage});
        background-position: center center;
        background-size: cover;
        background-repeat: no-repeat;
    }
    .sale-wrapper {
        color: ${props => props.textColor};
        padding: .5rem 0;
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
        h5 {
            margin: 0;
        }
        .sale-title {
            display: flex;
            align-items: center;
            margin-bottom: .5rem;
            img {
                width: 30px;
                margin-right: 1rem;
            }
        }
        .sale-link-text {
            text-decoration: underline;
        }
        @media (min-width: 640px) {
            flex-direction: row;
            flex-wrap: wrap;
            .sale-title {
                margin: 0 3rem 0 0;
            }
        }
    }
`