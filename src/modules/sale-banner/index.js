import React from 'react'
import { decode } from 'html-entities';
// Helpers
import { Coloring } from 'src/helpers';
import Clicker from 'src/helpers/clicker';
import Imaging from 'src/helpers/imaging';
// Styles
import * as S from './styles';

const Start = ({ data, settings }) => {

    if (!data.sale) return null;
    return (
        <S.SaleBanner
            className={`sale-banner ${data.background_type}`}
            bgColor={Coloring(data.background_color, settings)}
            bgImage={(data.background_type == 'image' && data.background_image) ? data.background_image?.url : ''}
            textColor={data.background_value === 'dark' ? '#fff' : Coloring('body_copy_color', settings)}
        >
            <div className='grid-container'>
                <div className='grid-x'>
                    <div className='cell center'>
                        <Clicker type="anchor" url={data.sale?.url} className='sale-wrapper'>
                            <h5 className='sale-title'>
                                {data.sale_icon && <Imaging data={data.sale_icon} />}
                                {decode(data.sale?.title)}
                            </h5>
                            {data.link_text &&
                                <h5 className='sale-link-text'>
                                    {decode(data.link_text)}
                                </h5>
                            }
                        </Clicker>
                    </div>
                </div>
            </div>
        </S.SaleBanner>
    )
}

export default Start;