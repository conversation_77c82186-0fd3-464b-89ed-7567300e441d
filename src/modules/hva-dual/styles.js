import styled from 'styled-components';

export const TopSection = styled.div`
    padding: 2rem 0;
    text-align: center;
    color: ${props => props.textColor};
    &.color {
        background-color: ${props => props.backgroundColor};
    }
    &.image {
        background-image: url(${props => props.backgroundImage});
        background-position: center center;
        background-size: cover;
        background-repeat: no-repeat;
    }
    .title {
        margin-bottom: 1rem;
    }
    .blurb {
        line-height: 1.5;
    }
    .form-module {
        margin: 0;
        padding: 1rem 0;
        button[type="submit"] {
            margin: 0;
        }
    }
    @media (min-width: 640px) {
        text-align: left;
        .grid-x {
            align-items: center;
        }
        .form-module {
            padding: 0;
        }
    }
    @media (min-width: 1024px) {
        padding: 4rem 0;
        .content-wrapper {
            max-width: 75%;
        }
    }
`;

export const BottomSection = styled.div`
    padding: 2rem 0 0;
    text-align: center;
    color: ${props => props.textColor};
    &.color {
        background-color: ${props => props.backgroundColor};
    }
    &.image {
        background-image: url(${props => props.backgroundImage});
        background-position: center center;
        background-size: cover;
        background-repeat: no-repeat;
    }
    .title {
        margin-bottom: 1rem;
    }
    .blurb {
        line-height: 1.5;
    }
    .featured-image-container {
        order: 2;
        .featured-image {
            margin: 2rem 0 0;
            img {
                object-fit: contain;
                max-height: 200px;
                display: block;
            }
        }
    }
    @media (min-width: 640px) {
        text-align: left;
        .featured-image-container {
            order: unset;
            display: flex;
            align-items: flex-end;
            .featured-image {
                margin: 0;
                img {
                    max-height: 250px;
                }
            }
        }
        .content-wrapper {
            padding: 0 0 2rem;
        }
    }
     @media (min-width: 1024px) {
        padding: 4rem 0 0;
        .featured-image-container {
            .featured-image {
                img {
                    max-height: 420px;
                    margin-top: -5rem;
                }
            }
        }
        .content-wrapper {
            padding: 2rem 0 4rem;
        }
    }
`;