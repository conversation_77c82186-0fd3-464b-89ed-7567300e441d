import React from "react";
import { decode } from 'html-entities';
import { Form } from 'src/modules/gravity-forms';
import { useInView } from 'react-intersection-observer';

// Helpers
import { Coloring } from "src/helpers";
const Imaging = React.lazy(() => import('src/helpers/imaging'));
// Partials
const Button = React.lazy(() => import('src/partials/button'));
// Styles
import { TopSection, BottomSection } from './styles';

const Start = ({ data, settings }) => {
    const { ref, inView } = useInView({
        triggerOnce: true,
        fallbackInView: true
    });

    return (
        <div ref={ref} className={`hva-dual`}>
            {inView ? <>
                <Top data={data} settings={settings} />
                <Bottom data={data} settings={settings} />
            </> : null}
        </div>
    );
}

const Top = ({ data, settings }) => {
    const bgColor = data.top_section_background_color ? Coloring(data.top_section_background_color, settings) : '';
    const color = data.top_section_background_value === 'dark' ? '#fff' : Coloring('body_copy_color', settings);
    const bgImage = data.top_section_background_image ? data.top_section_background_image.url : '';

    return (
        <TopSection
            className={`top-section ${data.top_section_background_type}`}
            backgroundColor={bgColor}
            textColor={color}
            backgroundImage={bgImage}
        >
            <div className="grid-container">
                <div className="grid-x grid-margin-x">
                    <div className="cell medium-6 large-8">
                        <div className="content-wrapper">
                            {data.top_section_content?.title &&
                                <h2 className='title'>{decode(data.top_section_content?.title)}</h2>
                            }
                            {data.top_section_content?.blurb &&
                                <div className='blurb' dangerouslySetInnerHTML={{ __html: data.top_section_content.blurb }} />
                            }
                        </div>
                    </div>
                    <div className="cell medium-6 large-4">
                        {data.form_selector &&
                            <div className="form-module">
                                <Form data={data.form_selector} settings={settings} bgValue={data.top_section_background_value} styleOverride={settings?.mvk_theme_config?.other?.form_styles ? settings?.mvk_theme_config?.other?.form_styles : 'box'} />
                            </div>
                        }
                    </div>
                </div>
            </div>
        </TopSection>
    )
}

const Bottom = ({ data, settings }) => {
    const bgColor = data.bottom_section_background_color ? Coloring(data.bottom_section_background_color, settings) : '';
    const color = data.bottom_section_background_value === 'dark' ? '#fff' : Coloring('body_copy_color', settings);
    const bgImage = data.bottom_section_background_image ? data.bottom_section_background_image.url : '';

    return (
        <BottomSection
            className={`bottom-section ${data.bottom_section_background_type}`}
            backgroundColor={bgColor}
            textColor={color}
            backgroundImage={bgImage}
        >
            <div className="grid-container">
                <div className="grid-x">
                    <div className="cell medium-6 large-7 featured-image-container">
                        {data.featured_image &&
                            <div className='featured-image'>
                                <Imaging data={data.featured_image} />
                            </div>
                        }
                    </div>
                    <div className="cell medium-6 large-5">
                        <div className="content-wrapper">
                            {data.bottom_section_content?.title &&
                                <h3 className='title'>{decode(data.bottom_section_content?.title)}</h3>
                            }
                            {data.bottom_section_content?.blurb &&
                                <div className='blurb' dangerouslySetInnerHTML={{ __html: data.bottom_section_content.blurb }} />
                            }
                            {data.bottom_section_content.button &&
                                <Button title={data?.bottom_section_content?.button?.title} url={data?.bottom_section_content?.button?.url} target={data?.bottom_section_content?.button?.target} tone={data?.bottom_section_background_value} type={data?.bottom_section_content?.button_style} />
                            }
                        </div>
                    </div>
                </div>
            </div>
        </BottomSection>
    )
}

export default Start;