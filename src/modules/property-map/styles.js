import styled from 'styled-components';

export const PropertyMapContainer = styled.div`
    padding: 2rem 0;
    color: ${props => props.textColor};
    .title-container .title {
        margin-bottom: 1rem;
    }
    &.color {
        background-color: ${props => props.backgroundColor};
        &.partial-fill {
            .title-container {
                background-color: ${props => props.backgroundColor};
            }
        }
    }
    &.image {
        background-image: url(${props => props.backgroundImage});
        background-position: center center;
        background-size: cover;
        background-repeat: no-repeat;
        &.partial-fill {
            .title-container {
                background-image: url(${props => props.backgroundImage});
                background-position: center center;
                background-size: cover;
                background-repeat: no-repeat;
            }
        }
    }
    &.partial-fill {
        padding-top: 0;
        background: none;
        .title-container {
            padding-top: 2rem;
            padding-bottom: 200px;
        }
        .map-wrapper {
            margin-top: -180px;
        }
    }
    @media (min-width: 1200px) {
        padding: 4rem 0;
        &.color, &.image {
            &.partial-fill {
                padding-top: 0;
                .title-container {
                    padding-top: 4rem;
                }
            }
        }
    }
`;

export const MapWrapper = styled.div`
    height: 500px;
    color: ${props => props.bodyCopyColor};
    .title {
        color: ${props => props.primaryColor};
    }
    .map-container {
        height: 500px;
    }
    @media (min-width: 1200px) {
        padding: 0 !important;
        height: 600px;
        border: 5px solid #fff;
        box-sizing: border-box;
        .map-container {
            height: 600px;
        }
    }
    /* INFO WINDOW OVERRIDES */
    .gm-style-iw {
        padding: 0 !important;
        max-height: 500px !important;
        overflow: hidden !important;
        min-width: 240px !important;
        .gm-style-iw-d {
            max-height: 500px !important;
            &::-webkit-scrollbar {
                display: none;
            }
        }
    }
    .gm-style-iw-tc {
        filter: drop-shadow(0 0 0 #fff) !important;
        &:after {
            transform: rotate(-45deg);
            left: -123px !important;
            top: -20px !important;
            width: 46px !important;
            height: 23px !important;
        }
    }
    
    div[role="dialog"] {
        .gm-style-iw-chr {
            height: 0 !important;
            button[title="Close"] {
                right: 0 !important;
                top: 0 !important;
                opacity: 1 !important;
                width: 34px !important;
                height: 34px !important;
                filter: drop-shadow(1px 1px 2px #000);
                span {
                    background: #fff;
                    width: 2rem !important;
                    height: 2rem !important;
                    margin: 0 !important;
                }
            }
        }
    }
`;

export const InfoWindow = styled.div`
    max-width: 240px;
     .feat-image {
        display: block;
        width: 240px;
        height: 140px;
        object-fit: cover;
    }
    .content-wrapper {
        padding: 0.75rem 1rem;
        & > * {
            margin-bottom: .75rem;
        }
        .cat {
            text-transform: uppercase;
            letter-spacing: 2px;
        }
        .address {
            font-size: .875rem;
        }
        .property-link {
            display: inline-block;
            padding: 0.5rem 2rem;
            border-radius: 20px;
        }
        .status {
            margin-bottom: 0;
        }
    }

    @media (min-width: 768px) {
        .content-wrapper {
            .title {
                font-size: 1.5rem;
            }
        }
    }
`;
