import { useLayoutEffect } from 'react';

// TODO - Convert this to a helper for "partials/filtration" 
// => And replace all other manual filter instances that happen before using <Filtration />
// set this to TRUE if all categories must be matched, otherwise it will return if only one is matched

export const filterStoreDirectoryStores = ({ storeData = [], term = '', category = '', categories = [], enableMultipleCategories = false, isGreedyCategoryMatch = false }) => {
    // return storeData if no filters were selected
    if (!storeData?.length || (!categories?.length && !term && !category)) return storeData;

    const status = storeData.filter(({ title, mvk_item_tax }) => {
        let shouldStoreBeShown = false;

        // only filter if 'term' was passed (no term == viewall)
        let isStoreMatchingTerm = (term) ? title.toLowerCase().includes(term.toLowerCase()) : true;

        let isStoreMatchingCategoryFilters = true; 
        
        if(!enableMultipleCategories && category) {
            isStoreMatchingCategoryFilters = mvk_item_tax.cats ? mvk_item_tax.cats.some(({ slug }) => slug === category) : false;
        }

        // only filter if 'categories' was passed (no category === viewall)
        if (enableMultipleCategories && categories?.length && mvk_item_tax?.cats) { 
            let isStoreMatchingSomeCategories = false;
            let isStoreMatchingAllCategories = true;

            // Could refactor nested loop if store numbers hinder performance (felt this read cleaner)
            categories.forEach((category) => {
                mvk_item_tax.cats.forEach(({ slug }) => {
                    if(slug === category) {
                        isStoreMatchingSomeCategories = true;
                    }
                    if(slug !== category) {
                        isStoreMatchingAllCategories = false;
                    }
                })
            })

            if(isGreedyCategoryMatch) { // must match all categories
                isStoreMatchingCategoryFilters = isStoreMatchingAllCategories;
            }

            if(!isGreedyCategoryMatch) { // must match one category
                isStoreMatchingCategoryFilters = isStoreMatchingSomeCategories;
            }
        }

        if(isStoreMatchingTerm && isStoreMatchingCategoryFilters) {
            shouldStoreBeShown = true;
        }

        return shouldStoreBeShown;
    });



    return status;
}
