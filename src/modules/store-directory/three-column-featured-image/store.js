import React, { useEffect } from "react";
import { decode } from 'html-entities';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faPhone } from "@fortawesome/free-solid-svg-icons";
import { faFacebookF, faInstagram, faPinterest, faYoutube } from '@fortawesome/fontawesome-free-brands';
import { faTiktok, faXTwitter } from "@fortawesome/free-brands-svg-icons";

//helpers
import Clicker from "src/helpers/clicker";
const Imaging = React.lazy(() => import('src/helpers/imaging'));

// SCSS.
import "./store.scss";

const Store = ({ store, data, settings }) => {

    useEffect(() => {
        var StyleSheet = document.createElement('style');
        var StyleStrings = ``;

        StyleStrings += `.store-directory__three-column-featured .phone-wrapper {
            background-color: ${settings.design?.colors?.background_color};
        }`;

        StyleSheet.innerHTML = StyleStrings;
        document.getElementsByTagName('head')[0].appendChild(StyleSheet);
    }, [])

    const socialItems = [];
    socialItems.push(
        (!!store.facebook),
        (!!store.twitter),
        (!!store.instagram),
        (!!store.pinterest),
        (!!store.youtube)
    );

    var socialStyle = {
        color: `${settings.design?.colors?.body_copy_color}`,
        fill: `${settings.design?.colors?.body_copy_color}`,
    };

    let storeCopy = store.store_copy;
    storeCopy = decode(storeCopy).substring(0, 150);
    storeCopy = storeCopy.substring(0, storeCopy.lastIndexOf(" ")) + '...';

    // truncate title if it's longer than 60 chars for this style
    let cardTitle = decode(store.title);
    if (cardTitle && cardTitle.length > 60) {
        cardTitle = cardTitle.substring(0, 60) + '...';
    }
    return (
        <div class='store'>
            <Clicker type="anchor" url={store.url} ariaLabel={`link to ${store.title}`}>
                <div class='featured-image-wrapper'>
                    {store.featured_image &&
                        <Imaging class='featured-image' data={store.featured_image} />
                    }
                    {!store.featured_image && store.logo_color &&
                        <Imaging class='logo-color' data={store.logo_color} />
                    }

                    {!store.featured_image && !store.logo_color && store.logo_monochrome &&
                        <Imaging class='logo-monochrome' data={store.logo_monochrome} />
                    }
                    {(!store.featured_image && !store.logo_color && !store.logo_monochrome) &&
                        <h4 class='store-title'>{cardTitle}</h4>
                    }
                </div>
            </Clicker>

            <div class='categories-social-wrapper'>
                <div class='category-wrapper'>
                    {store?.mvk_item_tax?.cats?.length > 0 &&
                        <div class='category'>
                            {decode(store.mvk_item_tax.cats[0].name)}
                        </div>
                    }
                </div>

                <div class='social-media-container'>
                    {socialItems.includes(true) &&
                        <div class='social-icons-wrapper'>
                            {store.facebook &&
                                <a
                                    class='icon facebook'
                                    href={store.facebook}
                                    rel="noopener"
                                    target='_blank'
                                    title='Facebook'
                                    style={socialStyle}
                                    aria-label='link to Facebook'
                                >
                                    <FontAwesomeIcon icon={faFacebookF} />
                                </a>
                            }
                            {store.twitter &&
                                <a
                                    class='icon twitter'
                                    href={store.twitter}
                                    rel="noopener"
                                    target='_blank'
                                    title='Twitter'
                                    style={socialStyle}
                                    aria-label='link to X'
                                >
                                    <FontAwesomeIcon icon={faXTwitter} />
                                </a>
                            }
                            {store.instagram &&
                                <a
                                    class='icon instagram'
                                    href={store.instagram}
                                    rel="noopener"
                                    target='_blank'
                                    title='Instagram'
                                    style={socialStyle}
                                    aria-label='link to Instagram'
                                >
                                    <FontAwesomeIcon icon={faInstagram} />
                                </a>
                            }
                            {store.pinterest &&
                                <a
                                    class='icon pinterest'
                                    href={store.pinterest}
                                    rel="noopener"
                                    target='_blank'
                                    title='Pinterest'
                                    style={socialStyle}
                                    aria-label='link to Pinterest'
                                >
                                    <FontAwesomeIcon icon={faPinterest} />
                                </a>
                            }
                            {store.youtube &&
                                <a
                                    class='icon youtube'
                                    href={store.youtube}
                                    rel="noopener"
                                    target='_blank'
                                    title='YouTube'
                                    style={socialStyle}
                                    aria-label='link to YouTube'
                                >
                                    <FontAwesomeIcon icon={faYoutube} />
                                </a>
                            }
                        </div>
                    }
                </div>
            </div>

            <div class='main-content'>
                {store.title &&
                    <h4 class='store-title'>{decode(store.title)}</h4>
                }

                {store.phone_number &&
                    <div class='phone-wrapper phone'>
                        <a class='phone-link-icon body-copy-txt' href={"tel:" + store.phone_number} aria-label='store phone number icon'>
                            <FontAwesomeIcon icon={faPhone} class='phone icon' style={socialStyle} />
                        </a>
                        <a class='phone-link body-copy-txt' href={`tel: ${store.phone_number}`} aria-label='store phone number'>
                            {store.phone_number}
                        </a>
                    </div>
                }

                {store.store_copy &&
                    <div class='store-copy' dangerouslySetInnerHTML={{ __html: storeCopy }} />
                }

                {store.url &&
                    <Clicker type="anchor" class='store-button body-copy-txt' url={store.url} title={store.title} ariaLabel={`link to ${store.title}`}>
                        {`${settings?.mvk_theme_config?.labels?.learn_more ? settings?.mvk_theme_config?.labels?.learn_more : 'Learn More'} >`}
                    </Clicker>
                }
            </div>
        </div>
    );
};

export default Store;
