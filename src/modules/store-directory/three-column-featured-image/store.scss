$sm_breakpoint: 768px;
$md_breakpoint: 1200px;

.store-directory__three-column-featured {
    .store {
        padding: 1.5rem 0;
    }
    @media screen and (min-width: 768px) {
        // display: grid; Kellan I changed this because there were some problems and I don't do it this way so was easier to switch to something I know
        // grid-auto-flow: row;
        // grid-template-columns: repeat(3, 1fr);
        // grid-template-rows: repeat(2, 1fr);
        // column-gap: 1rem;
        // row-gap: 3rem;
        display: flex;
        flex-wrap: wrap;
        width: 100%;
        padding-top: 3rem;
        padding-bottom: 5rem;
        .store {
            overflow: hidden;
            width: 33.3333%;
            padding: 1.5rem 1rem;
            box-sizing: border-box;
        }
    }

    .featured-image-wrapper {
        margin-bottom: 1rem;
        min-height: 220px;
        display: flex;
        align-items: center;
        justify-content: center;
        overflow: hidden;
        img {
            width: 100%;
            height: 100%;
            object-fit: contain;
        }
        @media (min-width: $sm_breakpoint) {
            height: 220px;
        }
        @media (min-width: $md_breakpoint) {
            height: 375px;
        }
    }

    .categories-social-wrapper {
        display: flex;
        flex-flow: column nowrap;
        align-items: flex-start;
        width: 100%;
        margin-bottom: 0.5rem;

        @media (min-width: 1024px) {
            display: grid;
            grid-auto-flow: row;
            grid-template-columns: repeat(2, 1fr);
            column-gap: .5rem;
        }
    }

    .category-wrapper {
        display: flex;
        align-items: center;

        @media (max-width: 1023px) {
            margin-bottom: .5rem;
        }
    }

    .category {
        width: fit-content;
        text-decoration: underline;
    }

    .social-media-container {
        display: flex;
        justify-content: flex-end;
        align-items: center;
    }

    .social-icons-wrapper {
        .icon {
            margin-right: 1rem;

            // @media (min-width: 1024px) {
            //     margin-left: 1rem;
            // }

            svg {
                height: 1.25em;
                // width: 1.25em;
            }
        }
    }

    .main-content {
    }

    .store-title {
        margin-bottom: 1rem;
    }

    .phone-wrapper {
        display: flex;
        align-items: center;
        margin-bottom: 1rem;
        width: fit-content;
        padding: 0.5rem 1rem;

        svg {
            height: 1em;
            width: 1em;
        }
    }

    .phone-link-icon {
        margin-right: 1rem;
    }

    .phone-link {
        text-decoration: underline;
    }

    .store-copy {
    }

    .store-button {
        text-decoration: underline;
        cursor: pointer;
        width: fit-content;
    }
}
