.store-directory__checkerboard {
    .mobile-slider {
        .slick-arrow {
            font-size: 1.5rem;
            height: 1.5rem;
            top: 25%;
            z-index: 3;

            @media screen and (min-width: 640px) {
                top: 50%;
            }
        }

        .slick-prev {
            left: 0;
        }

        .slick-next {
            right: 0;
        }
    }

    .store {
        display: flex;
        flex-flow: column nowrap;
        margin: 1rem 0;

        @media screen and (max-width: 767px) {
            padding: 0 2rem;
            width: auto;
        }

        @media screen and (min-width: 640px) {
            flex-flow: row nowrap;
        }

        @media screen and (min-width: 768px) {
            &:nth-child(odd) {
                .image-wrapper {
                    padding: 0 1rem 0 0;
                }
            }

            &:nth-child(2n) {
                .image-wrapper {
                    order: 2;
                    padding: 0 0 0 1rem;
                }
            }
        }

        @media screen and (max-width: 1023px) {
            align-items: center;
        }

        @media screen and (min-width: 1024px) {
            margin: 1.5rem 0;

            &:nth-child(odd) {
                padding-right: 1.5rem;
                box-sizing: border-box;
            }

            &:nth-child(2n) {
                .image-wrapper {
                    order: 0;
                }
            }

            &:nth-child(4n + 1),
            &:nth-child(4n + 2) {
                // .content-wrapper {
                //     padding: 0 0 0 1.5rem;
                // }
                .image-wrapper {
                    padding: 0 1.5rem 0 0;
                }
            }
            &:nth-child(4n + 3),
            &:nth-child(4n + 4) {
                .image-wrapper {
                    order: 2;
                    padding: 0 0 0 1.5rem;
                }
            }
        }

        .store-button {
            @media screen and (max-width: 639px) {
                font-size: 1rem;
            }
        }
    }

    .image-wrapper {
        img {
            border-radius: 5px;

            @media screen and (min-width: 768px) {
                box-shadow: 5px 10px 20px rgba(0, 0, 0, 0.3);
            }
        }

        @media screen and (max-width: 639px) {
            margin-bottom: 1.5rem;
        }

        @media screen and (min-width: 640px) {
            width: 50%;
        }
    }

    .clicker {
        cursor: pointer;
    }

    .content-wrapper {
        @media screen and (min-width: 640px) {
            width: 50%;
        }

        @media screen and (min-width: 640px) and (max-width: 767px) {
            padding-left: 1rem;
        }
    }
}
