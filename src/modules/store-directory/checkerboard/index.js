import React, { useState, useEffect } from 'react';
import Filtration from "src/partials/filtration";
import Clicker from "src/helpers/clicker";

import Store from './store';

import Slider from "react-slick";
import "slick-carousel/slick/slick.scss";
import "slick-carousel/slick/slick-theme.scss";
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faChevronLeft, faChevronRight } from '@fortawesome/free-solid-svg-icons'
import { filterStoreDirectoryStores } from '../_helpers';

const Start = ({ data, settings, categories }) => {
    var initialQuantity = parseInt(data.quantity);
    var totalQuantity = data.stores?.length;
    const [quantity, setQuantity] = useState(initialQuantity);

    useEffect(() => {
        var StyleSheet = document.createElement('style');
        var StyleStrings = ``;

        StyleStrings += `.store-directory__checkerboard .mobile-slider .slick-arrow {
            color: ${settings.design?.colors?.primary_color};
        }`;

        StyleSheet.innerHTML = StyleStrings;
        document.getElementsByTagName('head')[0].appendChild(StyleSheet);
    }, [])

    if (data.show_load_more && data.stores?.length >= parseInt(quantity)) {
        var storeData = data.stores.slice(0, parseInt(quantity));
    } else {
        storeData = data.stores;
    }

    var buttonStyles = {
        fontFamily: cache.fonts.body,
        backgroundColor: settings.design?.colors?.primary_color,
        color: 'white',
        borderRadius: (settings.mvk_theme_config.other?.enable_border_radius) ? `${settings.mvk_theme_config.other?.border_radius_size}px` : "0px",
        border: 'none',
    };

    let LoadMoreComponent;
    data.show_load_more && quantity < totalQuantity ?
        LoadMoreComponent = <div class='load-more'><button onClick={() => setQuantity(quantity + initialQuantity)} style={buttonStyles}>+ Load More</button></div>
        :
        LoadMoreComponent = null;

    let filterColor;
    if (data.background_value === 'dark') {
        filterColor = '#fff';
    } else {
        filterColor = false;
    }

    return (
        <Filtration
            moduleTitle={data.title}
            titleAlignment={data.title_alignment}
            blurb={data.blurb}
            categories={categories}
            toggleCategories={data.show_filter}
            toggleSearch={data.show_search}
            searchPlaceholder={data.search_placeholder}
            listViewLabel={data.list_view_label}
            mapViewLabel={data.map_view_label}
            filterColor={filterColor}
            filterStyle={data.filter_style}
            interactiveMap={settings.center_info.interactive_map ? settings.center_info.interactive_map : false}
            printableDirectory={settings.center_info.printable_directory ? settings.center_info.printable_directory : false}
            totalQuantity={totalQuantity}
            addStoreMap={data.add_store_map}
        >
            <Container data={data} settings={settings} storeData={storeData} LoadMoreComponent={LoadMoreComponent} />
        </Filtration>
    );
};

const Container = (props) => {
    if (!props.data.stores) {
        return (<div />);
    }

    var term = (props.filtration.term) ? props.filtration.term : null;
    var category = (props.filtration.category) ? props.filtration.category : '';
    var categories = (props?.filtration?.categories) ? props.filtration.categories: [];

    var stores = filterStoreDirectoryStores({
        storeData: props.storeData,
        term: term,
        category: category,
        categories: categories,
        enableMultipleCategories: props?.data?.filter_style === 'list',
        isGreedyCategoryMatch: false 
    })

    const slickSettings = {
        dots: false,
        draggable: true,
        infinite: false,
        speed: 400,
        slidesToShow: 1,
        slidesToScroll: 1,
        prevArrow: <FontAwesomeIcon icon={faChevronLeft} style={{ color: props.settings.design?.colors?.primary_color }} />,
        nextArrow: <FontAwesomeIcon icon={faChevronRight} style={{ color: props.settings.design?.colors?.primary_color }} />,
        responsive: [
            {
                breakpoint: 767,
            },
            {
                breakpoint: 10000,
                settings: 'unslick'
            }
        ]
    };

    return (
        <div class='store-directory__checkerboard'>
            {stores.length > 0 && 
                <Slider className='mobile-slider grid-x' {...slickSettings}>
                    {stores.map(store => <Store store={store} data={props.data} settings={props.settings} />)}
                </Slider>
            }
            {stores.length > 0 && props.LoadMoreComponent && props.LoadMoreComponent}
            {stores.length < 1 && <NoResults Reset={props.Reset} settings={props.settings} />}
        </div>
    );
};

const NoResults = ({ Reset, settings }) => {
    return (
        <div id="noresults" class='grid-container'>
            <div class="title">{settings.mvk_theme_config?.labels?.store_directory_labels?.no_results_found ? settings.mvk_theme_config?.labels?.store_directory_labels?.no_results_found : 'No Results Found.'}</div>
            <Clicker process={Reset} class="showfull">{settings.mvk_theme_config?.labels?.store_directory_labels?.click_here_to_show_full_list ? settings.mvk_theme_config?.labels?.store_directory_labels?.click_here_to_show_full_list : 'Click Here To Show Full List'}</Clicker>
        </div>
    );
};

export default Start;