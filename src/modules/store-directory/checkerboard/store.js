import React from "react";
import Clicker from "src/helpers/clicker";
const Button = React.lazy(() => import('src/partials/button'));
const Imaging = React.lazy(() => import('src/helpers/imaging'));

import "./store.scss";

import { decode } from 'html-entities';


const Store = ({ store, data, settings }) => {

    let storeCopy = store.store_copy;
    storeCopy = decode(storeCopy).replace(/<\/?[^>]+(>|$)/g, "");
    storeCopy = storeCopy.substring(0, 280);
    storeCopy = storeCopy.substring(0, storeCopy.lastIndexOf(" ")) + '...';

    return (
        <div class={`store cell small-12 large-6`}>
            <div class='image-wrapper'>
                {store.logo_color &&
                    <Clicker class='clicker' url={store.url} ariaLabel={`link to ${decode(store.title)}`}><Imaging data={store.logo_color} /></Clicker>
                }
            </div>
            <div class='content-wrapper'>
                <h3>{decode(store.title)}</h3>
                {/* <div dangerouslySetInnerHTML={{ __html: storeCopy }} /> */}
                <div class='copy-container'>
                    <p class='store-copy'>{storeCopy}</p>
                </div>
                <Button class='store-button' title={'See Store Details'} url={store.url} />
            </div>
        </div>
    );
};

export default Store;