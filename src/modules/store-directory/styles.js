import styled from 'styled-components';

export const StoreDirectoryOuter = styled.div`
    padding-top: 2rem;
    color: ${props => props.color};
    &.color {
        background-color: ${props => props.backgroundColor};
    }
    &.image {
        background-image: url(${props => props.backgroundImage});
        background-position: center center;
        background-size: cover;
        background-repeat: no-repeat;
    }
`;

export const SocialContainer = styled.div`
    display: flex;
    flex-flow: row wrap;
    width: 100%;

    .social-icon {
        margin-right: 0.5rem;
        margin-bottom: 0.5rem;
        width: 25px;
        height: 25px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        font-size: 2rem !important;

        svg {
            width: 0.5em;
            height: 0.5em;
        }
    }

    @media screen and (min-width: 992px) {
        .social-icon {
            margin-right: 1rem;
            margin-bottom: 1rem;
            width: 30px;
            height: 30px;
        }
    }

`;

export const SocialIcon = styled.a`
    border-color: ${props => props.styles.borderColor};
    color: ${props => props.styles.color};
    background-color: ${props => props.styles.backgroundColor};
    transition: .2s;
    &:hover {
        color: ${props => props.styles.backgroundColor};
        background-color: ${props => props.styles.color};
    }
`;