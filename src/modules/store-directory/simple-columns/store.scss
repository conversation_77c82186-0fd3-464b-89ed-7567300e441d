.store-directory__simple-columns {
    padding: 1rem 0;

    .store {
        display: flex;
        flex-flow: column nowrap;
        align-items: center;
        justify-content: center;
        text-align: center;
        position: relative;
        padding: 1rem 0;
        transition: all 0.5s linear;

        &:hover {
            background-color: #f1f1f1;
        }

        &.has-flags {
            &:after {
                position: absolute;
                top: 0;
                left: 0;
                content: "";
                width: 100%;
                height: 100%;
                box-sizing: border-box;
                z-index: 1;
            }
        }

        @media screen and (max-width: 639px) {
            border-bottom: 1px dotted #e0e0dd;
        }

        @media screen and (min-width: 640px) {
            &:nth-child(odd) {
                &:after {
                    position: absolute;
                    top: 0;
                    right: 0;
                    content: "";
                    height: 100%;
                    border-right: 1px dotted #e0e0dd;
                }
            }
        }

        @media screen and (min-width: 1024px) {
            &:nth-child(2n) {
                &:after {
                    position: absolute;
                    top: 0;
                    right: 0;
                    content: "";
                    height: 100%;
                    border-right: 1px dotted #e0e0dd;
                }
            }

            &:nth-child(3n) {
                &:after {
                    border-right: none;
                }
            }
        }
    }

    .store-flag-container {
        display: none;
        writing-mode: vertical-rl;

        &.light {
            color: #eee;
        }

        &.dark {
            color: #000000
        }

        @media screen and (min-width: 500px) {
            display: flex;
            justify-content: center;
            position: absolute;
            top: 0;
            left: 0;
            max-width: 2.5rem;
            height: 100%;
            padding: 0 0.2rem;
        }
    }

    .store-title {
        text-decoration: underline;
        margin-bottom: 0.5rem;
        // padding: 0 0.5rem;
        margin: 0 0.5rem;
        color: unset;
        z-index: 5;

        @media screen and (min-width: 500px) {
            // padding: 0 2.75rem;
            // margin: 0 2.75rem;
            margin: 0 3rem;
        }

        h4 {
            font-size: 1.125rem;
        }
    }

    .store-hours {
        @media screen and (min-width: 500px) {
            // padding: 0 2.75rem;
            padding: 0 3rem;
        }
    }

    .store-phone {
        color: #2b637f;
        text-decoration: underline;
        margin: 0.5rem 0 0 0;
        z-index: 5;
    }
}
