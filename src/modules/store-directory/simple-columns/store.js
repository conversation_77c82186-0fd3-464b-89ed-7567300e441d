import React, { useEffect } from "react";
import { decode } from 'html-entities';

// HELPERS.
import { StoreHours } from "src/helpers/hours";
import Clicker from "src/helpers/clicker";

// SCSS.
import "src/modules/store-directory/simple-columns/store.scss";

const Store = ({ store, data, settings }) => {
    var flagsClass = store.store_flag ? 'has-flags' : 'no-flags';

    return (
        <div class={`store ${flagsClass} cell small-12 medium-6 large-4`}>
            {store.store_flag && (
                <aside class={`store-flag-container ${data.flag_value}`}>
                    {/* <span class='store-flag'>{store.store_flag}</span> */}
                    {store.store_flag}
                </aside>
            )}
            <Clicker class="store-title" type="anchor" url={store.url}>
                <h4>{decode(store.title)}</h4>
            </Clicker>
            <Hours store={store} settings={settings} page={data} />
            <a class="store-phone" href={"tel:" + store.phone_number} aria-label='store phone number'>
                {store.phone_number}
            </a>
        </div>
    );
};

const Hours = ({ store, settings, page }) => {
    const today = new Date();
    const hours = StoreHours(store.hours, today);

    return <span class="store-hours" dangerouslySetInnerHTML={{ __html: `${settings?.hours?.standard_hours[0]?.todays_hours_label ? settings?.hours?.standard_hours[0]?.todays_hours_label : "Today's Hours"}: ${hours}` }} />;
};

export default Store;
