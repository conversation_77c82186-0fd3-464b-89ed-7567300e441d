/*******************************************************************************************************
   Copyright 2021 - Calise Partners LLC d/b/a Imaginuity. All rights are reserved. 
   Reproduction or transmission in whole or in part, in any form or by any means, electronic, mechanical 
or otherwise, is prohibited without the prior written consent of Calise Partners LLC d/b/a Imaginuity.
********************************************************************************************************
   Project : 10618 Maverick Platform Development
   Author : Imaginuity Developers (<PERSON>, <PERSON>)
   Description : renders a full width store directory list
   Creation Date : Fri Jul 23 2021
   Documentation : [documentation url - point to default wiki for project if no specific documentation for script]
 ********************************************************************************************************/

import React from 'react';

import './full-width-list_logo-grid.scss';
import './individual-card.scss';
const Button = React.lazy(() => import('src/partials/button'));
const Clicker = React.lazy(() => import('src/helpers/clicker'));

import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faPhone, faLocationDot } from "@fortawesome/free-solid-svg-icons";
const Imaging = React.lazy(() => import('src/helpers/imaging'));

import { decode } from 'html-entities';
import { StoreSocials } from '../store-socials';


const IndividualCard = ({ store, data, settings }) => {
    let location_link = '';
    if (!data.hide_location && store.location) {
        location_link = settings?.store?.location_link === 'google-maps' ? `https://www.google.com/maps/place/${store?.location?.replace(/ /g, '+')}` : settings?.center_info?.printable_directory;
    }
    // truncate title if it's longer than 60 chars for this style
    let cardTitle = decode(store.title);
    let truncateTitle = false;
    if (cardTitle && cardTitle.length > 60) {
        cardTitle = cardTitle.substring(0, 60) + '...';
        truncateTitle = true;
    }

    return (
        <div class={`store card cell medium-6 large-4`}>

            <div class='store-inner-wrapper'>
                <div class='logo-title-wrapper'>
                    <Clicker type='anchor' url={store.url} title={decode(store.title)} ariaLabel={`link to ${cardTitle}`}>
                        {store.logo_color &&
                            <div class='logo'><Imaging data={store.logo_color} /></div>
                        }

                        {(!store.logo_color && !truncateTitle) &&
                            <h3 class='store-title'>{cardTitle}</h3>
                        }
                        {(!store.logo_color && truncateTitle) &&
                            <h4 class='store-title'>{cardTitle}</h4>
                        }
                    </Clicker>
                    {store.store_flag &&
                        <div class='store-flags-container primary-bg'>
                            {store.store_flag}
                        </div>
                    }
                </div>
                <div class='store-details'>
                    <div class='store-title-container body-copy-txt'>
                        <Clicker type='anchor' class='body-copy-txt bold' url={store.url} ariaLabel={`link to ${cardTitle}`}>{decode(store.title)}</Clicker>
                    </div>

                    {(!data.hide_phone_number && store.phone_number) &&
                        <div class='phone cell small-2 medium-auto'>
                            <a href={"tel:" + store.phone_number} aria-label='click to call store'>
                                <FontAwesomeIcon icon={faPhone} class='icon' />
                                <span>{store.phone_number}</span>
                            </a>
                        </div>
                    }
                    {(!data.hide_location) &&
                        <div class='location'>
                            {((settings?.store?.location_link === 'google-maps' || settings?.store?.location_link === 'printable-directory') && location_link) &&
                                <a href={location_link} target='_blank'>
                                    <FontAwesomeIcon icon={faLocationDot} class='icon' />
                                    {store.location}
                                </a>
                            }
                            {(settings?.store?.location_link === 'interactive-map' && settings?.store?.interactive_map_page) &&
                                <Clicker type='anchor' url={settings?.store?.interactive_map_page?.url}  ariaLabel="link to interactive map">
                                    <FontAwesomeIcon icon={faLocationDot} class='icon' />
                                    {settings?.store?.interactive_map_page.title ? settings?.store?.interactive_map_page.title : 'View on Mall Map'}
                                </Clicker>
                            }
                            {(settings?.store?.location_link === 'no-link' && store.location) &&
                                <>
                                    <FontAwesomeIcon icon={faLocationDot} class='icon' />
                                    {store.location}
                                </>
                            }
                        </div>
                    }
                    <StoreSocials data={data} store={store} settings={settings} />
                </div>
                <div class='store-button'>
                    {!data.hide_link_indiv_stores &&
                        <Clicker type='anchor' class='primary-bg white-txt' url={store.url} ariaLabel={`link to ${cardTitle}`}>{data.individual_store_link_text?.length > 0 ? data.individual_store_link_text : 'More Details'}</Clicker>
                    }
                </div>
            </div>


        </div>
    );
};

export default IndividualCard;