@import "src/scss/variables.scss";

.store-directory-module.cards {
    .store.card {
        box-shadow: none;
        &:hover {
            transform: none;
        }
        .store-inner-wrapper {
            display: flex;
            flex-wrap: wrap;
            padding: 0;
            border: 1px solid;
            .logo-title-wrapper {
                width: 100%;
                display: flex;
                justify-content: center;
                height: 180px;
                position: relative;
                overflow: hidden;
                a {
                    display: flex;
                    padding: 1rem;
                    .logo {
                        display: flex;
                        img {
                            object-fit: contain;
                            min-height: 60px;
                            width: 100%;
                        }
                    }
                    .store-title {
                        margin: auto;
                    }
                }
                .store-flags-container {
                    position: absolute;
                    color: #fff;
                    padding: 0.25rem 0.5rem;
                    right: 0;
                }
            }
            .store-details {
                width: 100%;
                margin: 0 1rem 1rem;
                padding: 1rem 0;
                border-top: 1px solid;
                min-height: 147px; // equals 180px with padding + margin to match logo wrapper
                & > div {
                    margin-bottom: 0.5rem;
                    .icon {
                        width: 1rem;
                        height: 1rem;
                        margin-right: 0.5rem;
                    }
                }
                .phone {
                    text-decoration: none;
                }
            }
            .store-button {
                width: 100%;
                text-align: center;
                a {
                    display: block;
                    width: auto;
                }
            }
        }
    }
}
