import React, { useMemo, useState } from "react";

// HELPERS.
import Clicker from "src/helpers/clicker";

import Filtration from "src/partials/filtration";
import IndividualStore from './individual-store';
import IndividualCard from './individual-card';
import { filterStoreDirectoryStores } from "../_helpers";

const Start = ({ data, settings, categories }) => {
    var initialQuantity = parseInt(data.quantity);
    var totalQuantity   = data?.stores?.length;
    const [ quantity, setQuantity ] = useState(initialQuantity);

    var storeData = useMemo(() => {
        if (data.show_load_more && data.stores?.length >= parseInt(quantity)) return data.stores.slice(0, parseInt(quantity));
        else return data.stores;
    }, [])

    var buttonStyles = {
        fontFamily: cache.fonts.body,
        backgroundColor: settings?.design?.colors?.primary_color,
        color: 'white',
        borderRadius: (settings?.mvk_theme_config?.other?.enable_border_radius) ? `${settings?.mvk_theme_config?.other?.border_radius_size}px` : "0px",
        border: 'none',
    };

    let LoadMoreComponent;
    data.show_load_more && quantity < totalQuantity ?
        LoadMoreComponent = <div class='load-more'><button onClick={() => setQuantity(quantity + initialQuantity)} style={buttonStyles}>+ Load More</button></div>
        :
        LoadMoreComponent = null;

    let filterColor;
    if (data.background_value === 'dark') {
        filterColor = '#fff';
    } else {
        filterColor = false;
    }

    return (
        <Filtration
            moduleTitle={data.title}
            titleAlignment={data.title_alignment}
            blurb={data.blurb}
            categories={categories}
            toggleCategories={data.show_filter}
            toggleSearch={data.show_search}
            searchPlaceholder={data.search_placeholder}
            listViewLabel={data.list_view_label}
            mapViewLabel={data.map_view_label}
            filterColor={filterColor}
            filterStyle={data.filter_style}
            interactiveMap={settings.center_info.interactive_map ? settings.center_info.interactive_map : false}
            printableDirectory={settings.center_info.printable_directory ? settings.center_info.printable_directory : false}
            totalQuantity={totalQuantity}
            addStoreMap={data.add_store_map}
        >
            <Container data={data} settings={settings} storeData={storeData} LoadMoreComponent={LoadMoreComponent} />
        </Filtration>
    );
};

const Container = (props) => {
    if (!props.data.stores) {
        return (<div />);
    }

    var term = (props.filtration.term) ? props.filtration.term : null;
    var category = (props.filtration.category) ? props.filtration.category : '';
    var categories = (props?.filtration?.categories) ? props.filtration.categories : [];

    var stores = filterStoreDirectoryStores({
        storeData: props.storeData,
        term: term,
        category: category,
        categories: categories,
        enableMultipleCategories: props?.data?.filter_style === 'list',
        isGreedyCategoryMatch: false
    })

    return (
        <div class={`store-directory-module ${props.data.style}${props.data.style === 'cards' ? ' grid-x grid-margin-x grid-margin-y' : ''}`}>
            {/* <div class="store-directory-module__full-width"> */}
            {stores.length > 0 && stores.map(store => props.data.style === 'cards' ? <IndividualCard store={store} data={props.data} settings={props.settings} /> : <IndividualStore store={store} data={props.data} settings={props.settings} />)}
            {stores.length > 0 && props.LoadMoreComponent && props.LoadMoreComponent}
            {stores.length < 1 && <NoResults Reset={props.Reset} settings={props.settings} />}
        </div>
    );
};

const NoResults = ({ Reset, settings }) => {
    return (
        <div id="noresults" class='grid-container'>
            <div class="title">{settings.mvk_theme_config?.labels?.store_directory_labels?.no_results_found ? settings.mvk_theme_config?.labels?.store_directory_labels?.no_results_found : 'No Results Found.'}</div>
            <Clicker process={Reset} class="showfull">{settings.mvk_theme_config?.labels?.store_directory_labels?.click_here_to_show_full_list ? settings.mvk_theme_config?.labels?.store_directory_labels?.click_here_to_show_full_list : 'Click Here To Show Full List'}</Clicker>
        </div>
    );
};

export default Start;