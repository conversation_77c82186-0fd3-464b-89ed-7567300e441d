/*******************************************************************************************************
   Copyright 2021 - Calise Partners LLC d/b/a Imaginuity. All rights are reserved. 
   Reproduction or transmission in whole or in part, in any form or by any means, electronic, mechanical 
or otherwise, is prohibited without the prior written consent of Calise Partners LLC d/b/a Imaginuity.
********************************************************************************************************
   Project : 10618 Maverick Platform Development
   Author : Imaginuity Developers (<PERSON>, <PERSON>)
   Description : stylesheet for full width store directory module
   Creation Date : Fri Jul 23 2021
   Documentation : [documentation url - point to default wiki for project if no specific documentation for script]
 ********************************************************************************************************/

// .store-directory-module__full-width {
.store-directory-module {
    padding-top: 1rem;
    padding-bottom: 1rem;

    .store:not(.card) {
        background-color: #fff;
        border-radius: 0.3125rem;
        font-size: 1rem;
        margin: 1rem 0;
        box-shadow: 3px 3px 6px rgba(0, 0, 0, 0.2);
        transition: 0.2s;
        &:hover {
            transform: translateY(-2px);
            box-shadow: 4px 4px 8px rgba(0, 0, 0, 0.3);
        }
    }

    .store-inner-wrapper {
        display: flex;
        align-items: center;
        padding: 1rem;
    }

    .store-title-container {
        @media screen and (max-width: 639px) {
            padding-bottom: 0.25rem;
        }
    }

    .store-flags-container {
        @media screen and (max-width: 639px) {
            padding-bottom: 0.25rem;
        }
    }

    .hidden-sm {
        // @media screen and (max-width: 991px) {
        @media screen and (max-width: 1023px) {
            display: none;
        }
    }

    .visible-sm {
        // @media screen and (min-width: 992px) {
        @media screen and (min-width: 1024px) {
            display: none;
        }
    }

    .location {
        @media screen and (max-width: 639px) {
            padding-bottom: 0.25rem;
        }
    }

    .phone {
        text-decoration: underline;
        @media screen and (max-width: 639px) {
            padding-bottom: 0.25rem;
        }

        .icon {
            width: 1em;
            height: 1em;
        }
    }

    .hours {
        @media screen and (max-width: 639px) {
            padding-bottom: 0.25rem;
        }
    }

    .store-button {
        a {
            // padding: .7rem 2rem;
            padding: 0.6rem 1.5rem;

            @media screen and (min-width: 640px) {
                padding: 0.8rem 0;
                width: 100%;
                text-align: center;
            }
        }
    }

    .load-more {
        width: fit-content;
        padding-top: 1rem;
        padding-bottom: 2rem;
        margin: 0 auto;

        button {
            cursor: pointer;
            padding: 0.6rem 1.5rem;

            @media screen and (min-width: 640px) {
                padding: 0.8rem 2rem;
            }
        }
    }

    //*** Styles for Full Width List ***
    &.full_width_list {
        .store-flags-desktop {
            display: none;
        }

        .logo-title-wrapper {
            display: none;
        }
        .social-media-container {
            width: auto;
            margin-left: 0.625rem;
            margin-right: 0.625rem;
            .social-icon {
                margin: 0.5rem .5rem .5rem 0;
            }
        }
        @media (min-width: 1024px) {
            .phone {
                min-width: 110px;
            }
        }
    }

    //*** Styles for Logo Grid ***
    &.logo_grid {
        .social-media-container {
            display: none;
        }
        @media screen and (min-width: 992px) {
            display: flex;
            flex-flow: row wrap;
        }

        .store {
            @media screen and (min-width: 992px) {
                position: relative;
                border-radius: unset;
                box-shadow: unset;
                border: 1px solid #a5a5a5;
                width: 31.12%;
                padding-bottom: 29%;
                margin: 1%;
                transition: all 0.5s ease-in-out;

                &:hover {
                    transform: unset;

                    .logo-color {
                        opacity: 1;
                    }

                    .logo-monochrome {
                        opacity: 0;
                    }
                }
            }

            @media screen and (min-width: 1200px) {
                width: 23.33%;
                padding-bottom: 22%;
                margin: 0.75%;
            }
        }

        .store-inner-wrapper {
            @media screen and (min-width: 992px) {
                display: none;
            }
        }

        .store-flags-desktop {
            display: none;

            @media screen and (min-width: 992px) {
                display: block;

                position: absolute;
                left: 0;
                top: 10px;
                z-index: 9;
                width: 100%;

                .custom {
                    position: absolute;
                    right: 0;
                    background-color: #959595;
                    font-size: 1.2rem;
                    // padding-top: 7px;
                    width: 195px;
                    height: 30px;
                    transition: all 0.5s ease-in-out;
                    display: flex;
                    justify-content: center;
                    align-items: center;

                    // &:before {
                    //     transition: all .5s ease-in-out;
                    //     content: "";
                    //     height: 0;
                    //     width: 0;
                    //     display: block;
                    //     position: absolute;
                    //     top: 0;
                    //     left: -15px;
                    //     border-top: 15px solid #959595;
                    //     border-bottom: 15px solid #959595;
                    //     border-right: 15px solid transparent;
                    //     border-left: 15px solid transparent;
                    // }

                    p {
                        color: #fff;
                        margin: unset;
                    }
                }
            }
        }

        .logo-title-wrapper {
            @media screen and (max-width: 991px) {
                display: none;
            }

            @media screen and (min-width: 992px) {
                display: block;

                a {
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    text-decoration: none;
                    // display: block;
                    position: absolute;
                    width: 100%;
                    height: 100%;
                    left: 0;
                    right: 0;
                }
            }
        }

        .logo {
            @media screen and (min-width: 992px) {
                position: absolute;
                top: 0;
                right: 0;
                bottom: 0;
                left: 0;
                opacity: 1;
                transition: all 0.6s ease-in-out;
                // max-width: 300px;
                margin: auto;
            }
        }

        .logo-color {
            @media screen and (min-width: 992px) {
                opacity: 0;
            }
        }

        .store-title {
            color: #5a5c66;
            text-align: center;
        }
    }
}
