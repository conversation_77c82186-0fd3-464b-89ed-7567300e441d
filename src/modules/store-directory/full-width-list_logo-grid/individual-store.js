/*******************************************************************************************************
   Copyright 2021 - Calise Partners LLC d/b/a Imaginuity. All rights are reserved. 
   Reproduction or transmission in whole or in part, in any form or by any means, electronic, mechanical 
or otherwise, is prohibited without the prior written consent of Calise Partners LLC d/b/a Imaginuity.
********************************************************************************************************
   Project : 10618 Maverick Platform Development
   Author : Imaginuity Developers (<PERSON>, <PERSON>)
   Description : renders a full width store directory list
   Creation Date : Fri Jul 23 2021
   Documentation : [documentation url - point to default wiki for project if no specific documentation for script]
 ********************************************************************************************************/

import React from 'react';

import './full-width-list_logo-grid.scss';

const Button = React.lazy(() => import('src/partials/button'));

import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faPhone } from "@fortawesome/free-solid-svg-icons";
import { StoreHours } from '../../../helpers/hours';
import { StoreSocials } from '../store-socials';
const Clicker = React.lazy(() => import('src/helpers/clicker'));

import { decode } from 'html-entities';


const IndividualStore = ({ store, data, settings }) => {

    var colorLogo = {
        backgroundImage: `url(${store?.logo_color?.url})`,
        backgroundPosition: 'center center',
        backgroundSize: 'contain',
        backgroundRepeat: 'no-repeat',
        position: 'absolute',
        // height: '100%',
        // width: '100%',
    };

    var monoLogo = {
        backgroundImage: `url(${store?.logo_monochrome?.url})`,
        backgroundPosition: 'center center',
        backgroundSize: 'contain',
        backgroundRepeat: 'no-repeat',
        position: 'absolute',
        // height: '100%',
        // width: '100%',
    };

    // truncate title if it's longer than 60 chars for this style
    let cardTitle = decode(store.title);
    if (cardTitle && cardTitle.length > 60) {
        cardTitle = cardTitle.substring(0, 60) + '...';
    }

    return (
        <div class={`store drop-shadow-${settings?.mvk_theme_config?.other.drop_shadow}`}>
            {store.store_flag &&
                <div class='store-flags-desktop'><span class='custom'><p>{store.store_flag}</p></span></div>
            }

            <div class='store-inner-wrapper grid-x grid-margin-x'>

                <div class='store-title-container cell medium-auto large-3 body-copy-txt'>
                    <Clicker type='anchor' class='body-copy-txt bold' url={store.url}>{decode(store.title)}</Clicker>
                </div>
                <StoreSocials data={data} store={store} settings={settings} />
                {/* {store.store_flag && */}
                    <div class='store-flags-container cell medium-auto large-auto body-copy-txt'>
                        {store.store_flag}
                    </div>
                {/* } */}
                {!data.hide_location &&
                    <div class='location cell medium-auto large-auto'>{store.location}</div>
                }
                {!data.hide_phone_number &&
                    <div class='phone cell small-2 medium-auto'>
                        {/* <div class='phone cell small-2 medium-shrink large-shrink'> */}
                        <a class='body-copy-txt' href={"tel:" + store.phone_number} aria-label='store phone number'>
                            <span class='hidden-sm'>{store.phone_number}</span>
                            <FontAwesomeIcon icon={faPhone} class='icon visible-sm' />
                        </a>
                    </div>
                }
                {!data.hide_hours &&
                    <div class='hours cell small-10 medium-auto large-2'><Hours store={store} /></div>
                }
                <div class='store-button cell medium-auto large-2'>
                    {!data.hide_link_indiv_stores && <Button title={data.individual_store_link_text?.length > 0 ? data.individual_store_link_text : 'View Store'} url={store.url} />}
                </div>

            </div>

            <div class='logo-title-wrapper'>
                <Clicker type='anchor' url={store.url} title={decode(store.title)} ariaLabel={`link to ${decode(store.title)}`}>
                    {store.logo_color &&
                        <div style={colorLogo} role='img' aria-label={store.logo_color.alt} class={`logo ${(store.logo_monochrome && store.logo_monochrome[0] !== '') ? 'logo-color' : false}`}>
                            {/* <p className="sr-only">{decode(data.title.rendered)}</p> */}
                        </div>
                    }
                    {store.logo_monochrome &&
                        <div style={monoLogo} role='img' aria-label={store.logo_monochrome.alt} class={`logo ${store.logo_color ? 'logo-monochrome' : false}`}>
                            {/* <p className="sr-only">{decode(data.title.rendered)}</p> */}
                        </div>
                    }
                    {!store.logo_color && !store.logo_monochrome &&
                        <p class='store-title'>{cardTitle}</p>
                    }
                </Clicker>
            </div>
        </div>
    );
};

//todo *** extract the hours portion out into a resusable partial that will be shared across the platform
const Hours = ({ store }) => {
    const today = new Date();
    const hours = StoreHours(store.hours, today);

    return (
        <span dangerouslySetInnerHTML={{ __html: hours }} />
    );
};

export default IndividualStore;