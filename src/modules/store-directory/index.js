import React, { Suspense } from 'react';
import { Coloring } from "src/helpers";
import { getUniqueArray } from 'src/helpers';
import { useInView } from 'react-intersection-observer';

const Checkerboard = React.lazy(() => import('./checkerboard/index'));
const FullWidthListOrLogoGrid = React.lazy(() => import('./full-width-list_logo-grid/full-width-list_logo-grid'));
const ListImages = React.lazy(() => import('./list-with-images/index'));
const SimpleColumns = React.lazy(() => import('./simple-columns/index'));
const ThreeColumnFeaturedImage = React.lazy(() => import('./three-column-featured-image/index'));
import { StoreDirectoryOuter } from './styles';

const Start = ({ data, settings }) => {
    const { ref, inView } = useInView({
        triggerOnce: true,
        fallbackInView: true
    });
    const bgColor = Coloring(data.background_color, settings);
    const color = data.background_value === 'dark' ? '#fff' : Coloring('body_copy_color', settings);
    let categories = [];

    if (data.filter_style === 'list') {
        // sets categories for any style, grouping children under their parent
        data.store_categories?.parent_terms?.forEach((parent) => {
            let children = [];
            data.store_categories.child_terms?.forEach((child) => {
                if (child.parent == parent.term_id) {
                    children.push(child);
                }
            });
            parent.children = children;
            categories.push(parent);
        });

    } else {
        // all categories for the given stores, no hierarchy 
        categories = data?.stores?.map((store) => {
            return store.mvk_item_tax?.cats ? store.mvk_item_tax?.cats?.flat() : '';
        });
        if (categories) {
            categories = getUniqueArray(categories.flat(), ['name']);
            // sort alphabetically
            categories.sort(function (a, b) {
                if (a.slug > b.slug)
                    return 1;
                if (a.slug < b.slug)
                    return -1;
                return 0;
            });
        }
    }
    return (
        <Suspense fallback={<div />}>
            <StoreDirectoryOuter ref={ref} className={`sd-outer ${data.background_type}`} backgroundColor={bgColor} backgroundImage={data.background_type === 'image' ? data.background_image?.url : ''} color={color}>
                {inView ? <>
                    {(data.style === 'full_width_list' || data.style === 'logo_grid' || data.style === 'cards') && <FullWidthListOrLogoGrid data={data} settings={settings} categories={categories} />}
                    {data.style === 'checkerboard' && <Checkerboard data={data} settings={settings} categories={categories} />}
                    {data.style === 'simple_columns' && <SimpleColumns data={data} settings={settings} categories={categories} />}
                    {data.style === 'three_col_image' && <ThreeColumnFeaturedImage data={data} settings={settings} categories={categories} />}
                    {data.style === 'list_images' && <ListImages data={data} settings={settings} categories={categories} />}
                </> : null}
            </StoreDirectoryOuter>
        </Suspense>
    );
};


export default Start;