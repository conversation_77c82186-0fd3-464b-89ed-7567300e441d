import React, { useState, useEffect } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faFacebookF, faInstagram, faPinterest, faYoutube } from '@fortawesome/fontawesome-free-brands';
import { faTiktok, faXTwitter } from "@fortawesome/free-brands-svg-icons";

import { BackgroundColor } from 'src/helpers/theme';
import { Coloring } from 'src/helpers';
import { SocialContainer, SocialIcon } from './styles';

export const StoreSocials = ({ store, data, settings }) => {

    const socialItems = [];
    // check if not negative
    if (!data.hide_social) {
        socialItems.push(
            (!!store.facebook),
            (!!store.twitter),
            (!!store.instagram),
            (!!store.pinterest),
            (!!store.youtube),
            (!!store.tiktok)
        );
    }

    let ctaColor = settings?.store?.cta_background_color ? BackgroundColor(settings.store.cta_background_color) : 'grey';

    let socialStyle = {
        borderColor: data.social_icon_color ? Coloring(data.social_icon_color, settings) : ctaColor,
        color: data.social_icon_color && data.social_icon_color === 'white' ? ctaColor : '#fff',
        backgroundColor: data.social_icon_color ? Coloring(data.social_icon_color, settings) : ctaColor
    }

    return (
        <>
            {
                socialItems.includes(true) ?
                    <SocialContainer className='social-media-container'>
                        {store.facebook &&
                            <SocialIcon className='facebook social-icon' href={store.facebook} rel="noopener" target='_blank' title='Facebook' styles={socialStyle} aria-label='link to Facebook'>
                                <FontAwesomeIcon icon={faFacebookF} />
                            </SocialIcon>
                        }
                        {store.twitter &&
                            <SocialIcon className='twitter social-icon' href={store.twitter} rel="noopener" target='_blank' title='X' styles={socialStyle} aria-label='link to X'>
                                <FontAwesomeIcon icon={faXTwitter} />
                            </SocialIcon>
                        }
                        {store.instagram &&
                            <SocialIcon className='instagram social-icon' href={store.instagram} rel="noopener" target='_blank' title='Instagram' styles={socialStyle} aria-label='link to Instagram'>
                                <FontAwesomeIcon icon={faInstagram} />
                            </SocialIcon>
                        }
                        {store.pinterest &&
                            <SocialIcon className='pinterest social-icon' href={store.pinterest} rel="noopener" target='_blank' title='Pinterest' styles={socialStyle} aria-label='link to Pinterest'>
                                <FontAwesomeIcon icon={faPinterest} />
                            </SocialIcon>
                        }
                        {store.youtube &&
                            <SocialIcon className='youtube social-icon' href={store.youtube} rel="noopener" target='_blank' title='YouTube' styles={socialStyle} aria-label='link to YouTube'>
                                <FontAwesomeIcon icon={faYoutube} />
                            </SocialIcon>
                        }
                        {store.tiktok &&
                            <SocialIcon className='tiktok social-icon' href={store.tiktok} rel="noopener" target='_blank' title='YouTube' styles={socialStyle} aria-label='link to YouTube'>
                                <FontAwesomeIcon icon={faTiktok} />
                            </SocialIcon>
                        }
                    </SocialContainer>
                    : null
            }
        </>
    )
}

