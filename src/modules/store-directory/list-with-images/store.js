import React, { useEffect } from "react";
import { decode } from 'html-entities';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faPhone, faLocationDot } from "@fortawesome/free-solid-svg-icons";

// HELPERS & Partials.
import { StoreHours } from "src/helpers/hours";
import Clicker from "src/helpers/clicker";
const Button = React.lazy(() => import('src/partials/button'));
const Imaging = React.lazy(() => import('src/helpers/imaging'));

// SCSS.
import "src/modules/store-directory/list-with-images/store.scss";
import { StoreSocials } from '../store-socials';

const Store = ({ store, data, settings }) => {

    useEffect(() => {
        var StyleSheet = document.createElement('style');
        var StyleStrings = ``;

        StyleStrings += `.store-directory__list-with-images .store:hover {
            background-color: ${settings.design?.colors?.tertiary_color};
        }`;

        StyleSheet.innerHTML = StyleStrings;
        document.getElementsByTagName('head')[0].appendChild(StyleSheet);
    }, [])

    let storeCopy = store.store_copy;
    storeCopy = decode(storeCopy).substr(0, 200);
    storeCopy = storeCopy.substr(0, storeCopy.lastIndexOf(" ")) + '...';

    let location_link = '';
    if (!data.hide_location && store.location) {
        location_link = settings?.store?.location_link === 'google-maps' ? `https://www.google.com/maps/place/${store?.location?.replace(/ /g, '+')}` : settings?.center_info?.printable_directory;
    }


    return (
        <div class='store cell small-12'>
            <div class='store-inner-wrapper'>
                <div class='image-wrapper'>
                    {store.featured_image &&

                        <Clicker class='clicker' url={store.url} ariaLabel={`link to ${decode(store.title)}`}><Imaging data={store.featured_image} /></Clicker>
                    }
                    {!store.featured_image && store.logo_color &&
                        <Clicker class='clicker' url={store.url} ariaLabel={`link to ${decode(store.title)}`}><Imaging data={store.logo_color} /></Clicker>
                    }

                </div>

                <div class='content-wrapper'>
                    <Clicker class='clicker' url={store.url}><h3 class='store-title'>{decode(store.title)}</h3></Clicker>
                    {!data.hide_hours &&
                        <Hours store={store} settings={settings} page={data} />
                    }
                    <div class='store-copy' dangerouslySetInnerHTML={{ __html: storeCopy }} />
                </div>

                <div class='action-corner'>
                    <StoreSocials data={data} store={store} settings={settings} />

                    <div class='icon-container'>
                        {store?.mvk_item_tax.cats[0]?.cat_icon && store?.mvk_item_tax.cats[0]?.cat_icon[0] !== false && store?.mvk_item_tax.cats[0]?.cat_icon?.url !== undefined && store?.mvk_item_tax.cats?.length > 0 &&
                            <div class='icon'><Imaging data={store?.mvk_item_tax.cats[0]?.cat_icon} /></div>
                        }
                        {(!data.hide_location) &&
                            <div class='location'>
                                {((settings?.store?.location_link === 'google-maps' || settings?.store?.location_link === 'printable-directory') && location_link) &&
                                    <a href={location_link} target='_blank'>
                                        <FontAwesomeIcon icon={faLocationDot} class='icon' />
                                        {store.location}
                                    </a>
                                }
                                {(settings?.store?.location_link === 'interactive-map' && settings?.store?.interactive_map_page) &&
                                    <Clicker type='anchor' url={settings?.store?.interactive_map_page?.url}>
                                        <FontAwesomeIcon icon={faLocationDot} class='icon' />
                                        {settings?.store?.interactive_map_page.title ? settings?.store?.interactive_map_page.title : 'View on Mall Map'}
                                    </Clicker>
                                }
                                {(settings?.store?.location_link === 'no-link' && store.location) &&
                                    <>
                                        <FontAwesomeIcon icon={faLocationDot} class='icon' />
                                        {store.location}
                                    </>
                                }
                            </div>
                        }
                        {(!data.hide_phone_number && store.phone_number) &&
                            <div class='phone'>
                                <a href={"tel:" + store.phone_number} aria-label='store phone number'>
                                    <FontAwesomeIcon icon={faPhone} class='icon' />
                                </a>
                            </div>
                        }
                    </div>

                    {!data.hide_link_indiv_stores &&
                        <div class='button-wrapper'>
                            <Button class='store-button' title={data.individual_store_link_text?.length > 0 ? data.individual_store_link_text : 'View Details'} url={store.url} aria-label={`link to ${decode(store.title)}`}/>
                        </div>
                    }
                </div>

            </div>
        </div>
    );
};

const Hours = ({ store, settings, page }) => {
    const today = new Date();
    const hours = StoreHours(store.hours, today);

    return <div class="store-hours" dangerouslySetInnerHTML={{ __html: `Hours: ${hours}` }} />;
};

export default Store;
