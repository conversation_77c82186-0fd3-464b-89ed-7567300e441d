.store-directory__list-with-images {
    margin: 3rem 0;

    .store {
        margin-bottom: 1rem;
        box-shadow: 3px 3px 10px #ccc;
        border-radius: 10px;
        transition: 0.3s;

        &:hover {
            -webkit-transform: translateY(-3px);
            -ms-transform: translateY(-3px);
            transform: translateY(-3px);
            -webkit-box-shadow: 3px 5px 20px #aaa;
            box-shadow: 3px 5px 20px #aaa;
        }

        @media screen and (min-width: 768px) {
            display: flex;
            position: relative;
        }
    }

    .store-inner-wrapper {
        padding: 1rem;

        @media screen and (min-width: 768px) {
            display: flex;
            align-items: center;
            width: 100%;
            // padding: 1rem 1rem 1rem 4rem;
            padding: 0.5rem 1rem 0.5rem 3rem;
        }
    }

    .clicker {
        cursor: pointer;

        &:hover {
            text-decoration: underline;
        }
    }

    .image-wrapper {
        display: none;

        @media screen and (min-width: 768px) {
            overflow: hidden;
            display: flex;
            justify-content: center;
            width: 25%;
            img {
                object-fit: contain;
                max-height: 220px;
            }
        }
    }

    .content-wrapper {
        @media screen and (min-width: 768px) {
            width: 50%;
            margin-left: 1rem;
        }

        .clicker {
            margin: 0.5rem 0;
            height: fit-content;
            width: fit-content;
        }

        .store-title {
            height: fit-content;
            width: fit-content;
        }

        .store-hours {
            margin: 0.5rem 0;
        }

        .store-copy {
            @media screen and (max-width: 767px) {
                display: none;
            }
        }
    }

    .action-corner {
        @media screen and (min-width: 768px) {
            width: 25%;
            margin-left: 1rem;
        }
    }

    .social-media-container {
        display: none;

        @media screen and (min-width: 768px) {
            display: flex;
            justify-content: flex-end;
        }
    }

    .icon-container {
        display: flex;
        align-items: center;
        margin: 1.5rem 0;

        .icon {
            height: 2em;
            width: 2em;
            margin-right: 2rem;

            @media screen and (min-width: 768px) {
                margin: unset;
                height: 3.5em;
                width: 3.5em;
                position: absolute;
                left: 0.75rem;
                top: 50%;
                transform: translateY(-50%);
            }
        }

        @media screen and (min-width: 768px) {
            .location {
                display: none;
            }

            .phone {
                display: none;
            }
        }
    }

    .button-wrapper {
        @media screen and (min-width: 768px) {
            display: flex;
            justify-content: flex-end;
        }
    }
}
