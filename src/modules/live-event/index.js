import React, { useState, useEffect } from 'react';
import { decode } from 'html-entities';
import { useInView } from 'react-intersection-observer';
//Helpers
import { Coloring } from 'src/helpers';
import HtmlParser from 'src/helpers/html-parser';
import Imaging from 'src/helpers/imaging';
import { eventSort } from 'src/helpers/event-sort';
import { useQuery } from 'src/hooks/query';
//Partials
const Button = React.lazy(() => import("src/partials/button"));
//Styles
import * as S from './styles';

const Start = ({ data, settings, placeholders }) => {
    const { ref, inView } = useInView({
        triggerOnce: true,
        fallbackInView: true
    });
    const title = (placeholders && data.title_selection === 'dynamic') ? placeholders.single_line[data.title] : data.title;
    const bgImage = (placeholders && data.background_image_selection === 'dynamic') ? placeholders.image[data?.background_image_dynamic] : data?.background_image
    const [listings, setListings] = useState([]);

    useEffect(() => {
        if (settings.current_location && data.events) {
            setListings(() => data.events?.filter((i) => {
                return i.location === settings.current_location;
            }))
        } else {
            setListings(data.events ? data.events : false);
        }
    }, [placeholders]);

    let events = listings ? eventSort('start-date-asc', listings) : false;

    return (
        <S.LiveEvent
            ref={ref}
            className={`live-event ${data.background_type}`}
            bgColor={Coloring(data.background_color, settings)}
            bgImage={bgImage ? bgImage.url : ''}
            textColor={data.background_value === 'dark' ? '#fff' : Coloring('body_copy_color', settings)}
        >
            {inView ?
                <div className='grid-container'>
                    <div className='grid-x grid-margin-x'>
                        {(title || data.blurb) &&
                            <div className={`cell title-container ${data.title_alignment}`}>
                                {title && <h2>{decode(title)}</h2>}
                                {data.blurb && <HtmlParser html={data.blurb} placeholders={placeholders} />}
                            </div>
                        }
                        {events ? <Event events={events} data={data} placeholders={placeholders} /> : <div className='cell no-events'><h2>No Upcoming Events</h2></div>}
                    </div>
                </div>
                : null}
        </S.LiveEvent>
    )
}

const Event = ({ events, data, placeholders }) => {
    const query = useQuery();
    const simulateDate = query.get('simulate-date');
    const [currentEvent, setCurrentEvent] = useState(null);
    const [currentDate] = useState(simulateDate ? new Date(simulateDate) : new Date());
    const eventDetailBackground = (placeholders && data.event_detail_image_selection === 'dynamic') ? placeholders.image[data.detail_background_image_dynamic] : data.detail_background_image

    useEffect(() => {
        setCurrentEvent(() => events.find((event) => {
            const eventEnd = new Date(event.compare_end?.date)
            return currentDate < eventEnd;
        }))
    }, [events, currentDate]);

    if (currentEvent) {
        return (
            <>
                {currentEvent.featured_image &&
                    <div className='cell medium-6 featured-image'>
                        <Imaging data={currentEvent.featured_image} />
                    </div>
                }
                <div className='cell medium-6 content-container'>
                    <div className='inner-wrapper'>
                        {eventDetailBackground && <Imaging className='detail-bg' data={eventDetailBackground} />}
                        <h2>{decode(currentEvent.title)}</h2>
                        <p className='date-time'>
                            {currentEvent.start_date_formatted}
                            {(currentEvent.end_date_formatted != currentEvent.start_date_formatted) && ` - ${currentEvent.end_date_formatted}`}
                            {(currentEvent.meta.mec_hide_time != '1') && ` - ${currentEvent.start_time_formatted}`}
                            {(currentEvent.meta.mec_hide_time != '1' && currentEvent.meta.mec_hide_end_time != '1') && ` - ${currentEvent.end_time_formatted}`}
                        </p>
                        <Button url={currentEvent.url} title={data.button_text ? data.button_text : 'Learn More'} type={data.button_style} tone={data.background_value} aria-label={`link to ${currentEvent.title}`} />
                    </div>
                </div>
            </>
        )
    } else {
        return (
            <div className='cell no-events'><h2>No Upcoming Events</h2></div>
        )
    }
}

export default Start;