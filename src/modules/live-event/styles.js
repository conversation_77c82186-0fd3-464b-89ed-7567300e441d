import styled from 'styled-components';

export const LiveEvent = styled.div`
    color: ${props => props.textColor};
    &.color {
        background-color: ${props => props.bgColor};
    }
    &.image {
        background-image: url(${props => props.bgImage});
        background-position: center center;
        background-size: cover;
        background-repeat: no-repeat;
    }
    padding: 3rem 0;
    .title-container {
        margin-bottom: 1rem;
    }
    .featured-image {
        img {
            width: 100%;
        }
        @media (max-width: 639px) {
            margin-bottom: 1rem;
        }
    }
    .content-container {
        position: relative;
        .inner-wrapper > * {
            position: relative;
        }
        .detail-bg {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }
        .date-time {
            font-weight: bold !important;
            margin-bottom: 2rem;
        }
        @media (min-width: 640px) {
            display: flex;
            align-items: center;
        }
    }
`