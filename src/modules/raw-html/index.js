import postscribe from 'postscribe';
import React, { useEffect, useState } from "react";

import './index.scss';

const Start = ({ data, settings }) => {
    return (
        <div class="raw-html">
            <BackgroundType data={data} setting={settings} />
        </div>
    );
};

const BackgroundType = ({ data, settings }) => {
    const bgColor = pylot?.design?.coloring(data.background_color, 'backgroundColor');
    let bgStyles = {};
   
    if (data.background_type === 'image') {
        bgStyles = {
            ...bgColor,
            backgroundImage: `url(${pylot?.tools?.domain?.add(data.background_image.url)})`,
            backgroundPosition: 'center center',
            backgroundSize: "cover",
            backgroundRepeat: 'no-repeat',
        };

    } else if (data.background_type === 'color') {
        bgStyles = {
            ...bgColor
        };
    }
    let role = data.background_type === 'image' ? 'img' : null;
    let bgAlt = (data.background_type === 'image' && data.background_image.alt) ? data.background_image.alt : null;

    return (
        <div class="raw-html-container" role={role} aria-label={bgAlt} style={bgStyles}>
            <DisplayOptions data={data} />
        </div>
    );
};

const DisplayOptions = ({ data }) => {
    let classes;

    switch (data.display_options) {
        case 'two-column':
            classes = 'cell small-6 medium-6 large-6';
            break;
        case 'one-column':
        default:
            classes = 'cell small-12 medium-12 large-12';
    }

    return (
        <div class="grid-container">
            {data?.title && <h3>{data.title}</h3>}
            <div class="grid-x grid-margin-x">
                {data?.columns?.map(column => <Column data={data} column={column} classes={classes} />)}
            </div>
        </div>
    );
};

const Column = ({ data, column, classes }) => {
       
    return (
        <div class={classes}>
            <div class={`${data.background_value}`}>
                {column.title && <h2>{column.title}</h2>}
                {column.content && <div class="raw-html-content" dangerouslySetInnerHTML={{ __html: column.content }} />}
                {column.html_code && <HtmlCode data={column.html_code} />}
            </div>
        </div>
    )
};

export const HtmlCode = ({ data }) => {
    const [id] = useState(Math.floor(Math.random() * 10000));
    
    useEffect(() => {
        postscribe(`#${id}`, data);
    }, []);

    return (<div id={`${id}`} />)
}

export default Start;