import React, { useEffect } from "react";

import './index.scss';

const Start = ({ data, settings }) => {
    var StyleSheet = document.createElement('style');
    var StylesString = ` .content-api ul li.header { background-color: ${settings.design.colors.primary_color}; } `;
    StylesString += ` .content-api ul li .toggler { color: ${settings.design.colors.secondary_color}; } `;
    StyleSheet.innerHTML = StylesString;
    document.getElementsByTagName('head')[0].appendChild(StyleSheet);

    useEffect(() => {
        $('.collapse').hide('fast');
        $('.toggler').click(function(e) {
            e.stopPropagation();
            e.preventDefault();
            var id = $(this).attr('aria-controls');
            var element = document.getElementById(id);
            if (window.getComputedStyle(element).display === "none") {
                $(`#${id}`).show('fast');
            } else {
                $(`#${id}`).hide('fast');
            }
        });
    }, []);

    return (
        <div class="content-api">
            <div dangerouslySetInnerHTML={{ __html:data.content }}/>
        </div>
    );
}

export default Start;