
.content-api {
    margin: 50px 0px;
    padding:20px;

    .breadcrumb,
    h2,
    hr {
        display: none;
    }

    li.header {
        padding: 10px 20px;
        margin-bottom: 2px;
        font-size: 20px;
        line-height: 1.75em;
        background-color: #004270;
        color:white;
    }

    li:not(.header) {
        padding: 10px 20px;
        background-color: #eceded;
        margin-bottom: 2px;
        font-size: 16px;
    }

    a {
        &[role="button"] {
            padding: 0px;
        }

        &.toggler {
            width: 100%;
            // margin-bottom: 10px;
        }
    }

    ul {
        list-style-type:none;
        padding:0px;
        margin:0px;
    }

    li {
        list-style:none;
        background-image:none;
        background-repeat:none;
        background-position:0;
    }

    .card {
        margin: 20px auto;
        max-width: 640px;
    }

    .hytPlayerWrap {
        position: relative;
        padding-bottom: 56.25%; /* assumes 16:9 ratio */ 
        height: 0;
        overflow: hidden;
        max-width: 100%;
        // max-width: 400px;

        iframe {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
        }
    }

    .fa-plus-circle,
    .fa-download {
        float: right;
    }

}