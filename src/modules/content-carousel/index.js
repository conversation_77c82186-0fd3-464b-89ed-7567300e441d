import React, { useEffect } from "react";
import Slider from "react-slick";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faCircle } from '@fortawesome/free-solid-svg-icons';
import { useInView } from 'react-intersection-observer';
import { decode } from 'html-entities';
// HELPERS.
import HtmlParser from 'src/helpers/html-parser'
import { PrevArrow, NextArrow } from 'src/helpers/slick';
// SCSS.
import "slick-carousel/slick/slick.scss";
import "slick-carousel/slick/slick-theme.scss";
// EDIT THIS FOR MACRO & MICRO STYLING.
import 'src/modules/content-carousel/index.scss';
// EDIT THIS ONLY FOR SLICK SLIDER STYLING.
import 'src/modules/content-carousel/slick-styling.scss';

const Start = ({ data, settings }) => {
    // var display = (data.carousel_items.length < 6) ? data.carousel_items.length : 5;
    var numberOfSlidesToShow = data.slides_to_show?.length < 1 ? 3 : parseInt(data.slides_to_show);
    var numberOfSlidesToScroll = data.slides_to_scroll?.length < 1 ? 1 : parseInt(data.slides_to_scroll);

    switch (data.speed) {
        case 'slow':
            var carouselSpeed = 1200;
            break;
        case 'medium':
            var carouselSpeed = 800;
            break;
        case 'fast':
            var carouselSpeed = 300;
            break;
    };

    const sliderSettings = {
        dots: data.dots,
        dotClass: "slick-dots slick-test",
        arrows: data.arrows,
        adaptiveHeight: false,
        autoplay: data.autoplay,
        autoplaySpeed: 5000,
        pauseOnHover: false,
        infinite: data.infinite,
        slidesToShow: numberOfSlidesToShow,
        slidesToScroll: numberOfSlidesToScroll,
        speed: carouselSpeed,
        responsive: [
            {
                breakpoint: 1000,
                settings: {
                    slidesToShow: 3,
                    slidesToScroll: 1,
                },
            },
            {
                breakpoint: 767,
                settings: {
                    slidesToShow: 2,
                    slidesToScroll: 1,
                },
            },
            {
                breakpoint: 500,
                settings: {
                    slidesToShow: 1,
                    slidesToScroll: 1,
                },
            },
        ],
        nextArrow: <NextArrow />,
        prevArrow: <PrevArrow />,
        appendDots: dots => (
            <div style={{ color: settings?.design?.colors?.primary_color }}>
                <ul>{dots}</ul>
            </div>
        ),
        customPaging: i => {
            return (<FontAwesomeIcon icon={faCircle} />);
        }
    };

    var StyleSheet = document.createElement('style');
    var StylesString = `.cc-container .slick-slider .slick-dots li button:before { border-color: ${settings?.design?.colors?.primary_color}; } `;
    StylesString += `.cc-container .slick-dots li.slick-active button:before { color: ${settings?.design?.colors?.primary_color}; } `;
    StylesString += `.cc-container .slick-slider button.slick-arrow:before { border-color: ${settings?.design?.colors?.primary_color}; } `;

    StyleSheet.innerHTML = StylesString;
    document.getElementsByTagName('head')[0].appendChild(StyleSheet);

    var bgColor = pylot.design.coloring(data.background_color, 'backgroundColor');
    let bgStyles = {};
    if (data.background_type === 'image') {
        bgStyles = {
            ...bgColor,
            backgroundImage: `url(${data.background_image.url})`,
            backgroundPosition: 'center center',
            backgroundSize: "cover",
            backgroundRepeat: 'no-repeat',
        };
    } else if (data.background_type === 'color') {
        bgStyles = {
            ...bgColor,
        };
    }

    let role = data.background_type === 'image' ? 'img' : null;
    let bgAlt = (data.background_type === 'image' && data.background_image.alt) ? data.background_image.alt : null;

    const { ref, inView } = useInView({
        triggerOnce: true,
        fallbackInView: true
    });

    return (
        <div ref={ref} className='content-carousel-module' role={role} aria-label={bgAlt} style={bgStyles}>
            {inView ?
                <div class="grid-container">
                    {data.title && <Title title={data.title} alignment={data.title_alignment} />}
                    {data.blurb &&
                        <div className="blurb"><HtmlParser html={data.blurb} data={data} /></div>
                    }
                    <Slider class="cc-slider" {...sliderSettings}>
                        {data?.carousel_items && data.carousel_items.map(item => <Content item={item} settings={settings} />)}
                    </Slider>
                </div>
                : null}
        </div>
    );
}

const Title = ({ title, alignment }) => {
    return (
        <div class={`cc-title ${alignment}`}>
            <h2>{decode(title)}</h2>
        </div>
    );
}

const Content = ({ item, settings }) => {
    return (
        <div class="cc-slide">
            {item.image && <ItemImage image={item.image} />}
            {item.title && <ItemTitle title={item.title} />}
            <div class="cc-blurb" dangerouslySetInnerHTML={{ __html: item.content }} />
        </div>
    );
};

const ItemTitle = ({ title }) => {
    return (
        <div class="cc-heading">
            <h3>{decode(title)}</h3>
        </div>
    );
};

const ItemImage = ({ image }) => {
    return (<div class="cc-image" dangerouslySetInnerHTML={{ __html: image }} />);
};

export default Start;
