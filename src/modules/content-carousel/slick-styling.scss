@import "src/scss/variables.scss";

#modules-container {
    .content-carousel-module {
        .grid-container {
            .slick-slider {
                position: relative;
                // overflow: hidden;

                display: -webkit-box;
                display: -moz-box;
                display: -ms-flexbox;
                display: -webkit-flex;
                display: flex;
                flex-direction: row;
                @media (min-width: 1366px) {
                    .slick-arrow {
                        &.slick-prev {
                            left: -25px;
                        }
                        &.slick-next {
                            right: -25px;
                        }
                    }
                }
                .slick-list {
                    flex: 1 0;
                    -webkit-flex: 1 0;
                    overflow: hidden;

                    .slick-track {
                        display: -webkit-box;
                        display: -moz-box;
                        display: -ms-flexbox;
                        display: -webkit-flex;
                        display: flex;
                        flex-direction: row;
                        gap: 15px;

                        .slick-slide {
                            flex: 1;
                            -webkit-flex: 1;
                        }
                    }
                }

                .slick-dots {
                    position: absolute;
                    bottom: -30px;
                    display: block;
                    width: 100%;
                    padding: 0;
                    margin: 0;
                    list-style: none;
                    text-align: center;

                    ul {
                        display: block;
                        list-style-type: disc;
                        margin-block-start: 0px;
                        margin-block-end: 0px;
                        margin-inline-start: 0px;
                        margin-inline-end: 0px;
                        padding-inline-start: 0px;

                        li {
                            position: relative;
                            display: inline-block;
                            margin: 0px 10px;
                            padding: 0px;
                            cursor: pointer;
                            opacity: 0.5;

                            &.slick-active {
                                opacity: 1;
                            }

                            svg {
                                width: 15px;
                                height: 15px;
                            }
                        }
                    }
                }
            }
        }
    }
}
