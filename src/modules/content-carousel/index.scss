@import "src/scss/variables.scss";

#modules-container {
    .content-carousel-module {
        .grid-container {
            // margin: 0 auto;
            padding-top: 50px;
            padding-bottom: 100px;
            // max-width: 1300px;

            .blurb {
                margin-bottom: 30px;
            }
            .slick-slider {
                .slick-list {
                    .slick-track {
                        .slick-slide {
                            margin: 10px 0px;
                            height: inherit !important;
                            flex: 1;
                            -webkit-flex: 1;
                            align-self: stretch;
                            box-shadow: 0px 0px 5px rgba(0, 0, 0, 0.2);
                            background: #fff;

                            .cc-slide {
                                margin: 0px;
                                padding: 20px;
                                border: 0px;
                                max-width: 400px;
                                height: 100%;

                                .cc-image {
                                    margin-bottom: 20px;
                                    img {
                                        max-width: 200px;
                                    }
                                }

                                .cc-heading {
                                    margin-bottom: 10px;
                                    font-size: 1.5rem;
                                }

                                .cc-blurb {
                                    font-size: 0.875rem;
                                    line-height: 24px;
                                }

                                .cc-link {
                                    font-size: 1rem;
                                    margin-top: 1rem;
                                    text-decoration: none;

                                    @media screen and (min-width: 768px) {
                                        font-size: 1.125rem;
                                    }

                                    a:hover {
                                        text-decoration: underline;
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
