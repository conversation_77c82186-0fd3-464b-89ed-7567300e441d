import styled from 'styled-components';

export const PropertyListModule = styled.div`
    padding: 2rem 0;
    color: ${props => props.textColor};
    .title-container .title {
        margin-bottom: 1rem;
    }
   
    &.color {
        background-color: ${props => props.backgroundColor};
         &.partial-fill {
            .title-container {
                background-color: ${props => props.backgroundColor};
            }
        }
    }
    &.image {
        background-image: url(${props => props.backgroundImage});
        background-position: center center;
        background-size: cover;
        background-repeat: no-repeat;
        &.partial-fill {
            .title-container {
                background-image: url(${props => props.backgroundImage});
                background-position: center center;
                background-size: cover;
                background-repeat: no-repeat;
            }
        }
    }
    &.partial-fill {
        padding-top: 0;
        background: none;
        .title-container {
            padding-top: 2rem;
            padding-bottom: 200px;
        }
        .list-container {
            margin-top: -180px;
        }
    }
    @media (min-width: 1200px) {
        padding: 4rem 0;
        &.color, &.image {
            &.partial-fill {
                padding-top: 0;
                .title-container {
                    padding-top: 4rem;
                }
            }
        }
    }
`;

export const ListContainer = styled.div`

`;

export const DefaultProperty = styled.div`
    .featured-image {
        position: relative;
        display: flex;
        margin-bottom: 1rem;
        img {
            min-height: 100%;
            min-width: 100%;
            object-fit: cover;
        }
        @media (min-width: 640px) {
            height: 250px;
        }
        &:after {
            content: '${props => props.ctaText}';
            position: absolute;
            height: 100%;
            width: 100%;
            background: ${props => props.primaryColor}bb;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: #fff;
            transition: .2s;
            opacity: 0;
        }
    }
    .content-wrapper {
        color: ${props => props.textColor};
        .title {
            color: ${props => props.headingColor};
        }
        & > * {
            margin-bottom: .5rem;
        }
    }
    a:hover {
        .featured-image:after {
            opacity: 1;
        }
    }
`;

export const Loadmore = styled.div`
        padding-top: 1rem;
        padding-bottom: 2rem;
        text-align: center;

        button {
            cursor: pointer;
            padding: 0.6rem 1.5rem;

            @media screen and (min-width: 640px) {
                padding: 0.8rem 2rem;
            }
        }
`;