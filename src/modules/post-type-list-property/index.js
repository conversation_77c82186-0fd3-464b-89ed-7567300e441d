import React, { useState } from "react";
import { decode } from 'html-entities';
import { useInView } from 'react-intersection-observer';
// HELPERS 
import { Coloring } from "src/helpers";
import { ColumnClass } from 'src/helpers/foundation';
import Button from 'src/partials/button';

// STYLES
import { PropertyListModule, ListContainer, Loadmore } from './styles';

// SINGLE STYLES
const Default = React.lazy(() => import('./default'));


const Start = ({ data, settings }) => {
    const { ref, inView } = useInView({
        triggerOnce: true,
        fallbackInView: true
    });

    return (
        <PropertyListModule
            ref={ref}
            className={`property-list ${data.background_type}${data.partial_fill ? ' partial-fill' : ''}`}
            backgroundColor={Coloring(data.background_color, settings)}
            backgroundImage={data.background_type === 'image' ? data.background_image?.url : ''}
            textColor={data.background_value === 'dark' ? '#fff' : Coloring('body_copy_color', settings)}
        >
            {inView ? <>
                <div className="title-container">
                    <div className='grid-container'>
                        <div className='grid-x'>
                            {data.title &&
                                <div className={`cell ${data.title_alignment} ${data.background_value === 'dark' ? 'white-txt' : 'primary-txt'}`}>
                                    <h2 className='title'>{decode(data.title)}</h2>
                                </div>
                            }
                            {data.blurb &&
                                <div className='cell'>
                                    <div className='blurb' dangerouslySetInnerHTML={{ __html: data.blurb }} />
                                </div>
                            }
                        </div>
                    </div>
                </div>
                <ListingColumns data={data} />
            </> : null}
        </PropertyListModule>
    );
}

const ListingColumns = ({ data }) => {
    let properties = data.properties;
    let initialQuantity = parseInt(data.quantity);
    let totalQuantity = properties?.length;
    const [quantity, setQuantity] = useState(initialQuantity);

    if (data.properties && data.sort_by === 'featured') {
        properties = [
            ...properties.filter(({ featured_property }) => featured_property),
            ...properties.filter(({ featured_property }) => !featured_property)
        ]
    }
    if (data.show_load_more && properties?.length >= parseInt(quantity)) {
        properties = properties?.slice(0, parseInt(quantity));
    }

    return (
        <ListContainer className={`list-container grid-container ${data.display_options} ${data.style}`}>
            <div className='grid-x grid-margin-x grid-margin-y'>
                {properties?.map(property => <div className={ColumnClass(data.display_options)}><Property data={data} property={property} /></div>)}
                {(data.show_load_more && quantity < totalQuantity) ?
                    <Loadmore className='cell load-more'><Button title='+ Load More' buttonFunction='styled' onClick={() => setQuantity(quantity + initialQuantity)} tone={!data.partial_fill ? data.background_value : 'light'} /></Loadmore>
                    :
                    null
                }
            </div>
        </ListContainer>
    )
}

const Property = ({ data, property }) => {

    switch (data.style) {
        case 'default':
        default:
            return <Default data={data} property={property} />;
    }
}


export default Start;