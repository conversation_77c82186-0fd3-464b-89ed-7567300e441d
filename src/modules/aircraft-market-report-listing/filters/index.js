import React, { useEffect, useState, useContext } from "react";
import { useNavigate } from 'react-router-dom';

// Context
import { AircraftFilterContext } from "../context";
//PARTIALS
import Button from 'src/partials/button';
// Helpers
import { Coloring } from 'src/helpers';
// Styles
import * as S from '../styles';


const Start = ({ data, settings, resultCount }) => {
    const [aircraftFilters, setAircraftFiltration] = useContext(AircraftFilterContext);
    const navigate = useNavigate();
    const [reset, setReset] = useState(false);
    const [selectedManufacturer, setSelectedManufacturer] = useState(aircraftFilters.manufacturer);
    const [selectedModel, setSelectedModel] = useState(aircraftFilters.model);

    useEffect(() => {
        if (reset) {
            setSelectedManufacturer([])
            setSelectedModel([])
            navigate({ search: '' })
            setAircraftFiltration({
                ...aircraftFilters, ...{
                    manufacturer: [],
                    model: [],
                    cabin_class: [],
                    sold: false,
                    show_filters: false,
                    reset: false,
                    search: false
                }
            })
            setReset(false);
        } 
        // else {
        //     let queryString = '';
        //     Object.keys(aircraftFilters).map((key) => {
        //         queryString += aircraftFilters[key] && (Boolean(aircraftFilters[key].length) || key === 'sold') ? `&${key}=${aircraftFilters[key]}` : ''
        //     })
        //     navigate({ search: `${queryString.substring(1)}` })
        // }

    }, [aircraftFilters, reset]);

    useEffect(() => {
        if (selectedManufacturer) {
            setAircraftFiltration({ ...aircraftFilters, ...{ manufacturer: selectedManufacturer, reset: false } })
        }
    }, [selectedManufacturer]);

    useEffect(() => {
        if (selectedModel) {
            setAircraftFiltration({ ...aircraftFilters, ...{ model: selectedModel, reset: false } })
        }
    }, [selectedModel])

    const handleChange = (e, child) => {
        if (parseInt(e.target.dataset.parent) === 0 && !selectedManufacturer.includes(e.target.value)) {
            if (e.target.checked) {
                setSelectedManufacturer(selectedManufacturer => [...selectedManufacturer, e.target.value])
            } else {
                const index = selectedManufacturer.indexOf(e.target.value);
                if (index > -1) {
                    setSelectedManufacturer((selectedManufacturer) => selectedManufacturer.filter((i) => i !== e.target.value))
                }
            }
        } else if (parseInt(e.target.dataset.parent) === 0 && selectedManufacturer.includes(e.target.value)) {
            const index = selectedManufacturer.indexOf(e.target.value);
            if (index > -1) {
                // setSelectedManufacturer(selectedManufacturer.filter(i => i !== e.target.value));
                const childArray = e.target.dataset.children ? e.target.dataset.children.split(',') : [];
                childArray.forEach(child => {
                    setSelectedModel(selectedModel => selectedModel.filter(i => i !== child));
                });
                setSelectedManufacturer(selectedManufacturer.filter(i => i !== e.target.value));
                setAircraftFiltration({...aircraftFilters, ...{ manufacturer: aircraftFilters.manufacturer.filter(man => man !== e.target.value), model: selectedModel, reset: false }})
            }
        } else {
            if (e.target.checked || child) {
                setSelectedModel(selectedModel => [...selectedModel, e.target.value || child])
            } else {
                const index = selectedModel.indexOf(e.target.value || child);
                if (index > -1) {
                    setSelectedModel((selectedModel) => selectedModel.filter((i) => i !== e.target.value || child))
                }
            }
        }

        return;
    }

    return (
        <S.Filters
            className={`filter-panel ${aircraftFilters.show_filters ? 'show' : ''}`}
            textColor={settings?.design?.colors?.body_copy_color}
        >
            <div className='close-button' onClick={() => setAircraftFiltration({ ...aircraftFilters, ...{ show_filters: false } })}></div>
            <div className="inner-wrapper">
                <p>Narrow down your results by choosing from the following filters.</p>
                <div className="category-filters">
                    {data.filter_categories && data.filter_categories.map((category) => <Category data={category} checked={handleChange} aircraftFilters={aircraftFilters} />)}
                </div>
                <Button className='view-results' title={`View ${resultCount} Results`} buttonFunction={'styled'} aria-label='reset filters' onClick={() => setAircraftFiltration({ ...aircraftFilters, ...{ show_filters: false } })} />

                <Button className='reset-filters' title='Reset Filters' buttonFunction={'styled'} aria-label='reset filters' onClick={() => setReset(true)} />
            </div>
        </S.Filters>
    );
}

const Category = ({ data, checked, aircraftFilters }) => {
    // const [aircraftFilters, setAircraftFiltration] = useContext(AircraftFilterContext);
    const [expanded, setExpanded] = useState(false);
    const [manufacturers, setManufacturers] = useState(aircraftFilters.manufacturer);
    const [checkboxState, setCheckboxState] = useState(false)
    
    let childArray = [];

    const updateSelected = (e, children) => {
        setCheckboxState(!checkboxState)
        checked(e)
    }
    useEffect(() => {
        if (aircraftFilters.reset) {
            setExpanded(false);
        }
        setManufacturers(aircraftFilters.manufacturer);
    },[aircraftFilters])
    if (data.children && Array.isArray(data.children)) {
        data.children?.map((child) => {
            childArray.push(child.slug);
        })
    }

    return (
        <div className={`cat-group${data.parent === 0 ? ' parent' : ''}${expanded ? ' selected' : ''}`}>
            <div className="inner-wrapper">
                <label for={`${data.slug}-filter`}>
                    <input
                        type="checkbox"
                        id={`${data.slug}-filter`}
                        value={data.slug}
                        data-parent={data.parent}
                        data-children={childArray}
                        name={`${data.slug}-filter`}
                        onClick={(e) => updateSelected(e, childArray)}
                        checked={(checkboxState && manufacturers && manufacturers.includes(data.slug)) || (aircraftFilters.model && aircraftFilters.model.includes(data.slug))}
                        aria-checked={(checkboxState && manufacturers && manufacturers.includes(data.slug)) || (aircraftFilters.model && aircraftFilters.model.includes(data.slug))}
                        tabIndex={0}
                    />
                    <span>{data.name}</span>
                </label>
                {data.parent === 0 && <span className="plusminus" onClick={() => setExpanded(!expanded)}></span>}
            </div>
            {(data.children && Array.isArray(data.children)) &&
                <div className={'children'}>
                    {data.children.map((child) => <Category data={child} checked={checked} aircraftFilters={aircraftFilters} />)}
                </div>
            }
        </div>
    )
}

export default Start;