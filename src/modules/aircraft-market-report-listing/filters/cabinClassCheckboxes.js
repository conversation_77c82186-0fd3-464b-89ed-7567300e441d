import React, { useContext, useState, useEffect } from "react";
import { AircraftFilterContext } from "../context";

const Start = ({ filter }) => {
    const [aircraftFilters, setAircraftFiltration] = useContext(AircraftFilterContext);
    const [selected, setSelected] = useState(aircraftFilters[filter]);

    useEffect(() => {
        if (aircraftFilters.reset) {
            setSelected([])
        } else {
            setAircraftFiltration({ ...aircraftFilters, ...{ [filter]: selected, reset: false } })
        }

    }, [selected, aircraftFilters.reset]);

    const handleChange = e => {

        if (e.target.checked) {
            setSelected(selected => [...selected, e.target.value])
        } else {
            const index = selected.indexOf(e.target.value);
            if (index > -1) {
                setSelected((selected) => selected.filter((i) => i !== e.target.value))
            }
        }
        return;
    }

    return (
        <>
            <div className="cell large-4">
                <svg xmlns="http://www.w3.org/2000/svg" xmlnsXlink="http://www.w3.org/1999/xlink" width="62.915" height="33.002" viewBox="0 0 62.915 33.002">
                    <defs>
                        <clipPath>
                        <rect data-name="Rectangle 9672" width="62.915" height="33.002" transform="translate(0 0)" fill="none"/>
                        </clipPath>
                    </defs>
                    <g data-name="Group 8268" transform="translate(0 0)">
                        <g data-name="Group 8239" clip-path="url(#clip-path)">
                        <path data-name="Path 15467" d="M43.694,22.064l-2.32,10a.361.361,0,0,0,.277.428.417.417,0,0,0,.07.008h1.493a2.113,2.113,0,0,0,1.823-1.047l7.44-12.737h5.391c2.512,0,4.547-.993,4.547-2.217s-2.04-2.218-4.547-2.218H52.477L45.037,1.546A2.112,2.112,0,0,0,43.214.5h-1.49a.36.36,0,0,0-.355.365.344.344,0,0,0,.008.071l2.319,10v3.345h-8.52l-2.88-3.36a1.932,1.932,0,0,0-1.468-.677h-1.1a.292.292,0,0,0-.291.293.283.283,0,0,0,.016.1l1.977,5.705a.492.492,0,0,1,0,.33l-1.977,5.706a.289.289,0,0,0,.275.386h1.114a1.931,1.931,0,0,0,1.47-.677l2.88-3.36h8.52Z" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
                        <line data-name="Line 728" x1="6.074" transform="translate(21.664 16.5)" fill="none" stroke="#000" stroke-linecap="square" stroke-linejoin="round" stroke-width="1.325"/>
                        <line data-name="Line 729" x1="6.074" transform="translate(11.864 16.5)" fill="none" stroke="#000" stroke-linecap="square" stroke-linejoin="round" stroke-width="1.325"/>
                        <path data-name="Path 15468" d="M5.932,2.734A5.932,5.932,0,0,0,0,8.666a7.658,7.658,0,0,0,1.324,4.092c1.29,1.991,4.608,7.057,4.608,7.057s3.318-5.066,4.608-7.057a7.658,7.658,0,0,0,1.324-4.092A5.932,5.932,0,0,0,5.932,2.734m0,9.249A3.317,3.317,0,1,1,9.249,8.666a3.317,3.317,0,0,1-3.317,3.317"/>
                        </g>
                    </g>
                </svg>
                <div className="checkbox">
                    <label for={`${filter}-filter-light`}>
                        <input
                            type="checkbox"
                            id={`${filter}-filter-light`}
                            value={'light'}
                            name={`${filter}-filter-light`}
                            onClick={handleChange}
                            checked={aircraftFilters[filter] && aircraftFilters[filter].includes('light')}
                            aria-checked={aircraftFilters[filter] && aircraftFilters[filter].includes('light')}
                            tabIndex={0}
                        />
                        <span><h5 className="cabin-class-title">LIGHT</h5><br />Perfect for groups up to 6 and landing at the smallest airports.</span>
                    </label>
                </div>
            </div>
            <div className="cell large-4">
                <svg xmlns="http://www.w3.org/2000/svg" xmlnsXlink="http://www.w3.org/1999/xlink" width="130.539" height="33.002" viewBox="0 0 130.539 33.002">
                    <defs>
                        <clipPath>
                        <rect data-name="Rectangle 9671" width="130.539" height="33.002" transform="translate(0 0)" fill="none"/>
                        </clipPath>
                    </defs>
                    <g data-name="Group 8267" transform="translate(0 0)">
                        <g data-name="Group 8237" clip-path="url(#clip-path)">
                        <path data-name="Path 15465" d="M111.318,22.064l-2.32,10a.361.361,0,0,0,.277.428.417.417,0,0,0,.07.008h1.493a2.113,2.113,0,0,0,1.823-1.047l7.44-12.737h5.391c2.512,0,4.547-.993,4.547-2.217s-2.04-2.218-4.547-2.218H120.1l-7.44-12.737A2.112,2.112,0,0,0,110.838.5h-1.49a.36.36,0,0,0-.355.365A.344.344,0,0,0,109,.936l2.319,10v3.345H102.8l-2.88-3.36a1.932,1.932,0,0,0-1.468-.677h-1.1a.292.292,0,0,0-.291.293.283.283,0,0,0,.016.1l1.977,5.705a.492.492,0,0,1,0,.33l-1.977,5.706a.289.289,0,0,0,.275.386h1.114a1.931,1.931,0,0,0,1.47-.677l2.88-3.36h8.52Z" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
                        <line data-name="Line 726" x1="2.785" transform="translate(90.388 16.5)" fill="none" stroke="#000" stroke-linecap="square" stroke-linejoin="round" stroke-width="1.406"/>
                        <line data-name="Line 727" x1="73.55" transform="translate(12.579 16.5)" fill="none" stroke="#000" stroke-linecap="square" stroke-linejoin="round" stroke-width="1.406" stroke-dasharray="5.769 5.769"/>
                        <path data-name="Path 15466" d="M5.932,2.734A5.932,5.932,0,0,0,0,8.666a7.658,7.658,0,0,0,1.324,4.092c1.29,1.991,4.608,7.057,4.608,7.057s3.318-5.066,4.608-7.057a7.658,7.658,0,0,0,1.324-4.092A5.932,5.932,0,0,0,5.932,2.734m0,9.249A3.317,3.317,0,1,1,9.249,8.666a3.317,3.317,0,0,1-3.317,3.317"/>
                        </g>
                    </g>
                </svg>
                <div className="checkbox">
                    <label for={`${filter}-filter-mid`}>
                        <input
                            type="checkbox"
                            id={`${filter}-filter-mid`}
                            value={'mid-super-mid'}
                            name={`${filter}-filter-mid`}
                            onClick={handleChange}
                            checked={aircraftFilters[filter] && aircraftFilters[filter].includes('mid-super-mid')}
                            aria-checked={aircraftFilters[filter] && aircraftFilters[filter].includes('mid-super-mid')}
                            tabIndex={0}
                        />
                        <span><h5 className="cabin-class-title">MID/SUPER MID</h5><br />Comfortable for longer commutes and mid-size groups of up to 9.</span>
                    </label>
                </div>
            </div>
            <div className="cell large-4">
                <svg xmlns="http://www.w3.org/2000/svg" xmlnsXlink="http://www.w3.org/1999/xlink" width="211.256" height="33.002" viewBox="0 0 211.256 33.002">
                    <defs>
                        <clipPath>
                        <rect data-name="Rectangle 9670" width="211.256" height="33.002" transform="translate(0 0)" fill="none"/>
                        </clipPath>
                    </defs>
                    <g data-name="Group 8269" transform="translate(0 0)">
                        <g data-name="Group 8235" clip-path="url(#clip-path)">
                        <path data-name="Path 15463" d="M192.035,22.064l-2.32,10a.361.361,0,0,0,.277.428.417.417,0,0,0,.07.008h1.493a2.113,2.113,0,0,0,1.823-1.047l7.44-12.737h5.391c2.512,0,4.547-.993,4.547-2.217s-2.04-2.218-4.547-2.218h-5.391l-7.44-12.737A2.112,2.112,0,0,0,191.555.5h-1.49a.36.36,0,0,0-.355.365.343.343,0,0,0,.008.071l2.319,10v3.345h-8.52l-2.88-3.36a1.932,1.932,0,0,0-1.468-.677h-1.1a.292.292,0,0,0-.291.293.283.283,0,0,0,.016.1l1.977,5.705a.492.492,0,0,1,0,.33l-1.977,5.706a.289.289,0,0,0,.275.386h1.114a1.932,1.932,0,0,0,1.47-.677l2.88-3.36h8.52Z" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
                        <line data-name="Line 723" x1="2.785" transform="translate(171.105 16.5)" fill="none" stroke="#000" stroke-linecap="square" stroke-linejoin="round" stroke-width="1.406"/>
                        <line data-name="Line 724" x1="147.1" transform="translate(18.236 16.5)" fill="none" stroke="#000" stroke-linecap="square" stroke-linejoin="round" stroke-width="1.406" stroke-dasharray="5.769 5.769"/>
                        <line data-name="Line 725" x1="2.785" transform="translate(12.568 16.5)" fill="none" stroke="#000" stroke-linecap="square" stroke-linejoin="round" stroke-width="1.406"/>
                        <path data-name="Path 15464" d="M5.932,2.734A5.932,5.932,0,0,0,0,8.666a7.658,7.658,0,0,0,1.324,4.092c1.29,1.991,4.608,7.057,4.608,7.057s3.318-5.066,4.608-7.057a7.658,7.658,0,0,0,1.324-4.092A5.932,5.932,0,0,0,5.932,2.734m0,9.249A3.317,3.317,0,1,1,9.249,8.666a3.317,3.317,0,0,1-3.317,3.317"/>
                        </g>
                    </g>
                </svg>                
                <div className="checkbox">
                    <label for={`${filter}-filter-long_range`}>
                        <input
                            type="checkbox"
                            id={`${filter}-filter-long_range`}
                            value={'long-range'}
                            name={`${filter}-filter-long_range`}
                            onClick={handleChange}
                            checked={aircraftFilters[filter] && aircraftFilters[filter].includes('long-range')}
                            aria-checked={aircraftFilters[filter] && aircraftFilters[filter].includes('long-range')}
                            tabIndex={0}
                        />
                        <span><h5 className="cabin-class-title">LONG RANGE</h5><br />Long range and accommodation for large groups up to 14 passengers.</span>
                    </label>
                </div>
            </div>
        </>
    )
}

export default Start;