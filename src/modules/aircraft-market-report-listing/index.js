import React, { useContext, useState, useEffect } from "react";
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faSliders } from '@fortawesome/free-solid-svg-icons';
import { decode } from 'html-entities';
import { useInView } from 'react-intersection-observer';
import { Coloring } from "src/helpers";
import HtmlParser from 'src/helpers/html-parser'
// Context 
import { AircraftFilterContext, AircraftFilterProvider } from "./context";
// Partials
import Button from "src/partials/button";
// Helpers
import Clicker from 'src/helpers/clicker';
import Imaging from 'src/helpers/imaging';
// Styles
import * as S from './styles';
// Partials
const CabinClassCheckboxes = React.lazy(() => import('./filters/cabinClassCheckboxes'));
const Filters = React.lazy(() => import('./filters'));

const Start = ({ data, settings }) => {
    const { ref, inView } = useInView({
        triggerOnce: true,
        fallbackInView: true,
        threshold: 0
    });

    return (
        <AircraftFilterProvider>
            <S.AircraftMarketReportListing
                ref={ref}
                className={`aircraft-market-report-listing${inView && data.background_type === 'image' ? ' add-bg-image' : ''}`}
                bgColor={Coloring(data.background_color, settings)}
                bgImage={data.background_image}
                textColor={data.background_value === 'dark' ? '#fff' : settings.design?.colors?.body_copy_color}
            >
                {inView ?
                    <>
                        {(data.listing_source === 'aircraft' && data.add_filters) &&
                            <CabinClassFilters data={data} settings={settings} />
                        }
                        <div className='title-container grid-container'>
                            {(data.title || data.blurb) &&
                                <div className="grid-x">
                                    {data.title &&
                                        <div className={`cell module-title ${data.title_alignment}`}><h2>{decode(data.title)}</h2></div>
                                    }
                                    {data.blurb &&
                                        <div className="cell blurb">
                                            <HtmlParser html={data.blurb} />
                                        </div>
                                    }
                                </div>
                            }
                        </div>
                        <Listing data={data} settings={settings} />
                        {data.row_button &&
                            <div class='grid-container'>
                                <div class='grid-x'>
                                    <div class={`cell ${data?.title_alignment}`}>
                                        <Button class='module-button below' title={data?.row_button?.title} url={data?.row_button?.url} target={data?.row_button?.target} tone={data.background_value} type={data?.row_button_style} />
                                    </div>
                                </div>
                            </div>
                        }
                    </>
                    : null}
            </S.AircraftMarketReportListing>
        </AircraftFilterProvider>
    );
}

const CabinClassFilters = ({ data, settings }) => {

    return (
        <S.CabinClassFilters
            className={`cabin-class-filters${data.cf_background_type === 'image' ? ' add-bg-image' : ''}`}
            bgColor={Coloring(data.cf_background_color, settings)}
            bgImage={data.cf_background_image}
            textColor={data.cf_background_value === 'dark' ? '#fff' : settings.design?.colors?.body_copy_color}
        >
            <div className="grid-container">
                <div className="grid-x grid-margin-x">
                    <div className="cell">
                        <h2>Search Aircraft by Cabin Class</h2>
                    </div>
                    <CabinClassCheckboxes filter={'cabin_class'} />
                </div>
            </div>
        </S.CabinClassFilters>
    )
}

const Listing = ({ data, settings }) => {
    const [aircraftFilters, setAircraftFiltration] = useContext(AircraftFilterContext);
    const [results, setResults] = useState(data.results);

    useEffect(() => {
        // ********* THIS WAS OUR ORIGNAL CODE *********

        // setResults(() => data.results.filter((i) => {
        //     if (data.listing_source === 'aircraft') {
        //         const includeItem = (!aircraftFilters.search || i.title?.toLowerCase().includes(aircraftFilters.search?.toLowerCase())) &&
        //             (!aircraftFilters.sold || i.sold) &&
        //             (!aircraftFilters.cabin_class.length || aircraftFilters.cabin_class.includes(i.cabin_class)) &&
        //             (!aircraftFilters.manufacturer.length || (aircraftFilters.manufacturer.includes(i.category?.manufacturer) || aircraftFilters.model.includes(i.category?.model))) &&
        //             (!aircraftFilters.model.length || aircraftFilters.model.includes(i.category?.model) || aircraftFilters.manufacturer.includes(i.category?.manufacturer) && aircraftFilters.model.some(r => data.reduced_cats[i.category.manufacturer].includes(i.category.model)))

        //             // if a model is selected, deselect that specific manufacturer
        //             if (aircraftFilters.model.length) {
        //                 const manufacturersToRemove = aircraftFilters.model.map(model => {
        //                     return Object.keys(data.reduced_cats).find(manufacturer => data.reduced_cats[manufacturer].includes(model));
        //                 });
        //                 aircraftFilters.manufacturer = aircraftFilters.manufacturer.filter(manufacturer => !manufacturersToRemove.includes(manufacturer));
        //             }

        //             // if a manufacturer has any of it's child models selected, and then that parent manufacturer is selected, deselect that parent manufacturer
        //             if (aircraftFilters.manufacturer.length) {
        //                 const modelsToRemove = aircraftFilters.manufacturer.flatMap(manufacturer => data.reduced_cats[manufacturer]);
        //                 aircraftFilters.model = aircraftFilters.model.filter(model => !modelsToRemove.includes(model));
        //             }

        //         return includeItem;
        //     } else {
        //         const includeItem = !aircraftFilters.search || i.title?.toLowerCase().includes(aircraftFilters.search?.toLowerCase());
        //         return includeItem;
        //     }
        // }))

        /*******************************************************************************************************
        Author : Kellan Benn
        Description : [enter description]
        Creation Date : Thu Apr 18 2024
        *** imag-ai-generated ***
        ********************************************************************************************************/

        const filterAircraft = (aircraft) => {

            const searchMatch = (!aircraftFilters.search || aircraft.title.toLowerCase().includes(aircraftFilters.search.toLowerCase()));

            const soldMatch = (!aircraftFilters.sold || aircraft.sold);

            const cabinClassMatch = (!aircraftFilters.cabin_class.length || aircraftFilters.cabin_class.includes(aircraft.category?.cabin_class));

            const manufacturerMatch = (!aircraftFilters.manufacturer.length || (aircraftFilters.manufacturer.includes(aircraft.category.manufacturer) || aircraftFilters.model.includes(aircraft.category.model)));

            const modelMatch = (!aircraftFilters.model.length || aircraftFilters.model.includes(aircraft.category.model) || aircraftFilters.manufacturer.includes(aircraft.category.manufacturer) && aircraftFilters.model.some(r => data.reduced_cats[aircraft.category.manufacturer].includes(aircraft.category.model)));

            // Logic to handle conflicts between selected manufacturers and models
            const manufacturersToRemove = getConflictingManufacturers();
            aircraftFilters.manufacturer = aircraftFilters.manufacturer.filter(m => !manufacturersToRemove.includes(m));

            const modelsToRemove = getConflictingModels();
            aircraftFilters.model = aircraftFilters.model.filter(m => !modelsToRemove.includes(m));

            return searchMatch && soldMatch && cabinClassMatch && manufacturerMatch && modelMatch;

        };

        const filterReports = (report) => {
            return !aircraftFilters.search || report.title.toLowerCase().includes(aircraftFilters.search.toLowerCase());
        };

        setResults(() => data.results.filter((i) => {
            return data.listing_source === 'aircraft' ? filterAircraft(i) : filterReports(i);
        }));

        function getConflictingManufacturers() {
            return aircraftFilters.model.map(model => {
                return Object.keys(data.reduced_cats).find(manufacturer => data.reduced_cats[manufacturer].includes(model));
            });
        }

        function getConflictingModels() {
            return aircraftFilters.manufacturer.flatMap(manufacturer => data.reduced_cats[manufacturer]);
        }
    }, [aircraftFilters])

    return (
        <S.ListingContainer>
            {(data.add_filters || data.add_search) &&
                <div className="grid-container filter-container">
                    <div className="grid-x grid-margin-x filter-wrapper">
                        {data.add_search &&
                            <Search data={data} settings={settings} />
                        }
                        {(data.add_filters && data.listing_source === 'aircraft') &&
                            <>
                                <div className="filter-toggle-wrapper cell medium-3 large-2">
                                    <div className="filter-toggle" onClick={() => setAircraftFiltration({ ...aircraftFilters, ...{ show_filters: !aircraftFilters.show_filters } })}>
                                        <FontAwesomeIcon icon={faSliders} />Filter Aircraft
                                    </div>
                                </div>

                                <div className="sold checkbox cell medium-3 large-2">
                                    <label class="sold-filter" for={`sold-filter`}>
                                        <input
                                            type="checkbox"
                                            id={`sold-filter`}
                                            name={`sold-filter`}
                                            onClick={() => setAircraftFiltration({ ...aircraftFilters, ...{ sold: !aircraftFilters.sold } })}
                                            checked={aircraftFilters.sold}
                                            aria-checked={aircraftFilters.sold}
                                            tabIndex={0}
                                        />
                                        <span>Show Only Sold</span>
                                    </label>
                                </div>
                            </>
                        }
                    </div>
                </div>
            }
            {(data.add_filters && data.listing_source === 'aircraft') && <Filters data={data} settings={settings} resultCount={results.length} />}
            <div className="grid-container">
                {data.listing_source === 'aircraft' &&
                    <Aircraft data={data} settings={settings} results={results} />
                }
                {data.listing_source === 'market_reports' &&
                    <MarketReports data={data} settings={settings} results={results} />
                }
            </div>
        </S.ListingContainer>
    )

}

const Aircraft = ({ data, settings, results }) => {
    let initialQuantity = parseInt(data.quantity) > -1 ? parseInt(data.quantity) : 10;
    let totalQuantity = results?.length;
    const [quantity, setQuantity] = useState(initialQuantity);

    if (data.see_more) {
        results = results.slice(0, parseInt(quantity))
    }

    return (
        <S.Aircraft
            className={`aircraft-results ${data?.listing_style} grid-x grid-margin-x`}
        >
            {results.map((card) => <AircraftCard card={card} settings={settings} data={data} />)}
            {(!results || !results.length) && <div className="no-results cell"><h3>No results found.</h3></div>}
            {(data.see_more && quantity < totalQuantity) ?
                <S.Loadmore className='cell load-more' onClick={() => setQuantity(quantity + initialQuantity)}>SEE MORE</S.Loadmore>
                :
                null
            }
        </S.Aircraft>
    )
}

const AircraftCard = ({ card, settings, data }) => {
    return (
        <S.AircraftCard
            key={card.title}
            className={`aircraft-card cell ${data?.listing_style === 'two_col_cards' ? 'large-6' : 'large-12'} ${card?.sold ? 'sold' : null}`}
            textColor={settings?.design?.colors?.body_copy_color}
        >
            <Clicker className={'wrapper-link'} type={card.sold ? null : 'anchor'} url={card.sold ? null : card.url} ariaLabel={`link to ${decode(card.title)}`}>
                <div className="details-container">
                    <div className="inner-card">
                        <div className="title">
                            {data?.listing_style === 'two_col_cards' ?
                                <h5>{decode(card.title)}</h5>
                                :
                                <h3>{decode(card.title)}</h3>
                            }
                        </div>
                        <hr />
                        <div className="details">
                            {card.category?.passengers &&
                                <div className="passengers">
                                    {settings.aircraft?.passenger_icon && <Imaging className='icon' data={settings.aircraft?.passenger_icon} />}
                                    {data?.listing_style === 'two_col_cards' ? card.category?.passengers : <><span>Passenger Count:</span>{card.category?.passengers}</>}
                                </div>
                            }
                            {card.location &&
                                <div className="location">
                                    {settings.aircraft?.location_icon && <Imaging className='icon' data={settings.aircraft?.location_icon} />}
                                    {data?.listing_style === 'two_col_cards' ? card.location : <><span>Location:</span>{card.location}</>}
                                </div>
                            }
                            <div className="programs">
                                {settings.aircraft?.engine_program_icon && <Imaging className='icon' data={settings.aircraft?.engine_program_icon} />}
                                {<><span>Programs:</span>{card.engine_program ? 'Yes' : 'No'}</>}
                            </div>
                            {card.total_time &&
                                <div className="total-time">
                                    {settings.aircraft?.total_time_icon && <Imaging className='icon' data={settings.aircraft?.total_time_icon} />}
                                    {data?.listing_style === 'two_col_cards' ? card.total_time : <><span>Total Time:</span>{card.total_time}</>}
                                </div>
                            }
                        </div>
                    </div>
                </div>
                {card.featured_image &&
                    <div className="featured-image">
                        <Imaging data={card.featured_image} />
                        {card.sold && <div className="sold-flag">SOLD</div>}
                    </div>
                }
                {data?.listing_style === 'full_width_cards' &&
                    <div class='aircraft-features'>
                        <h5>Aircraft Features</h5>
                        <hr />
                        {card.aircraft_features_for_listings && <div className="features" dangerouslySetInnerHTML={{ __html: card.aircraft_features_for_listings }} />}
                    </div>
                }
            </Clicker>
        </S.AircraftCard>
    )
}

const MarketReports = ({ data, settings, results }) => {
    let initialQuantity = parseInt(data.quantity) > -1 ? parseInt(data.quantity) : 10;
    let totalQuantity = results?.length;
    const [quantity, setQuantity] = useState(initialQuantity);

    if (data.see_more) {
        results = results.slice(0, parseInt(quantity))
    }

    return (
        <S.MarketReports
            className="market-report-results grid-x grid-margin-x grid-margin-y"
        >
            {results.map((card) => <MarketReportCard data={data} card={card} settings={settings} />)}
            {(!results || !results.length) && <div className="no-results cell"><h3>No results found.</h3></div>}
            {(data.see_more && quantity < totalQuantity) ?
                <S.Loadmore className='cell load-more' onClick={() => setQuantity(quantity + initialQuantity)}>SEE MORE</S.Loadmore>
                :
                null
            }
        </S.MarketReports>
    )
}

const MarketReportCard = ({ data, card, settings }) => {

    const featuredImageData = {
        url: card.featured_image,
        alt: `featured image for ${card.title} market report`,
        height: 250
    }

    return (
        <S.MarketReportCard
            key={card.title}
            className="market-report-card cell small-6 medium-4 large-3"
            textColor={data.background_value === 'dark' ? '#fff' : settings?.design?.colors?.body_copy_color}
        >
            <Clicker type='anchor' url={card.url} ariaLabel={`link to ${decode(card.title)}`}>

                {card.featured_image &&
                    <div className="featured-image">
                        <Imaging data={featuredImageData} />
                    </div>
                }
                <div className="title"><h4>{decode(card.title)}</h4></div>
                <div className="view-details">{`VIEW DETAILS >`}</div>
            </Clicker>
        </S.MarketReportCard>
    )
}

const Search = ({ data, settings }) => {
    const [aircraftFilters, setAircraftFiltration] = useContext(AircraftFilterContext);

    const SetTerm = (e) => {
        e?.preventDefault();
        var term = document?.getElementById(`aircraft-search`)?.value;
        if (term && term != null) {
            setAircraftFiltration({ ...aircraftFilters, ...{ search: term } })
        } else {
            setAircraftFiltration({ ...aircraftFilters, ...{ search: false } })
        }
    };

    return (
        <div className="search-wrapper cell medium-6 large-4">
            <S.SearchSVG className="search-icon" filterColor={data.background_value === 'dark' ? '#fff' : settings?.design?.colors?.body_copy_color} onClick={SetTerm} version="1.1" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" aria-label="search icon">
                <g id="info" />
                <g id="icons">
                    <path d="M22.4,19.6l-4.8-4.8c0.9-1.4,1.4-3,1.4-4.8c0-5-4-9-9-9s-9,4-9,9s4,9,9,9c1.8,0,3.4-0.5,4.8-1.4l4.8,4.8   c0.4,0.4,0.9,0.6,1.4,0.6c1.1,0,2-0.9,2-2C23,20.4,22.8,19.9,22.4,19.6z M5,10c0-2.8,2.2-5,5-5s5,2.2,5,5s-2.2,5-5,5S5,12.8,5,10z" id="icon-search" />
                </g>
            </S.SearchSVG>
            <form id="filter-search" method="post" action="#" autocomplete="off" onSubmit={SetTerm} onChange={SetTerm} class="flex-child-grow" role="search" aria-label="aircraft">
                <label htmlFor={`aircraft-search`}>
                    <S.SearchInput id={`aircraft-search`} filterColor={data.background_value === 'dark' ? '#fff' : settings?.design?.colors?.body_copy_color} type="text" className="search" value={aircraftFilters.search ? aircraftFilters.search : ''} placeholder={`${data.listing_source === 'aircraft' ? 'Search Aircraft' : 'Search Market Reports'}`} />
                </label>
            </form>
        </div>
    )
}

export default Start;