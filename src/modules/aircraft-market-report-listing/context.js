import React, { useState, createContext } from 'react';
// HOOKS.
import { useQuery } from 'src/hooks/query';

export const AircraftFilterContext = createContext();
export const AircraftFilterProvider = (props) => {
    const query = useQuery();
    const [aircraftFilters, setAircraftFiltration] = useState({
        manufacturer: query.get('manufacturer')?.split(',') || [],
        model: query.get('model')?.split(',') || [],
        cabin_class: query.get('cabin_class')?.split(',') || [],
        sold: query.get('sold') || false,
        show_filters: false,
        reset: false,
        search: query.get('search') || false
    });

    return (
        <AircraftFilterContext.Provider value={[aircraftFilters, setAircraftFiltration]}>
            {props.children}
        </AircraftFilterContext.Provider>
    );
}

/**
 *   min_baggage_capacity: query.get('min_baggage_capacity') || null,
        max_baggage_capacity: query.get('max_baggage_capacity') || null,
        min_distance: query.get('min_distance') || null,
        max_distance: query.get('max_distance') || null,
 */