import styled from 'styled-components';

export const AircraftMarketReportListing = styled.div`
    background-color: ${props => props.bgColor};
    color: ${props => props.textColor};
    &.add-bg-image {
        background-image: url('${props => props.bgImage?.url}');
        background-position: center center;
        background-size: cover;
    }
    .title-container {
        padding-top: 2rem !important;
    }
    input[type="checkbox"] {
        /* Add if not using autoprefixer */
        -webkit-appearance: none;
        appearance: none;
        appearance: none;
        background-color: #fff;
        margin: 0 .5rem 0 0;
        font: inherit;
        color: currentColor;
        width: 1em;
        height: 1em;
        border: 0.15em solid currentColor;
        border-radius: 0.15em;
        // transform: translateY(-0.075em);
        display: grid;
        place-content: center;
        &::before {
            content: "";
            transform-origin: bottom left;
            clip-path: polygon(14% 44%, 0 65%, 50% 100%, 100% 16%, 80% 0%, 43% 62%);
            width: 0.5em;
            height: 0.5em;
            transform: scale(0);
            transition: 120ms transform ease-in-out;
            box-shadow: inset 1em 1em var(--form-control-color);
        }
        &:checked::before {
            transform: scale(1);
            background: currentColor;
        }
    }
    .module-button {
        margin-bottom: 2rem;
    }
    .cabin-class-filters {
        svg {
            fill: ${props => props.textColor};
        }

        .cabin-class-title {
            margin-bottom: unset;
        }
    }
`

export const Aircraft = styled.div`
    ${'' /* margin-top: 2rem;
    @media (min-width: 640px) {
        margin-top: 4rem;
    } */}
    &.two_col_cards {
        .aircraft-card {
            .wrapper-link {
                @media (max-width: 639px) {
                    display: block;
                    padding: 1rem .75rem 1.5rem;
                }
            }
            &:not(.sold) {
                &:hover, &:focus {
                    box-shadow: rgba(0, 0, 0, 0.5) 4px 7px 15px;
                }
            }
        }
    }
    &.full_width_cards {
        .aircraft-card {
            border: 1px solid black;
            box-shadow: rgba(0, 0, 0, 0.25) 2px 2px 8px;
            padding: 1rem .75rem;
            &:not(.sold) {
                &:hover, &:focus {
                    box-shadow: rgba(0, 0, 0, 0.5) 4px 7px 15px;
                }
            }

            @media (max-width: 639px) {
                width: fit-content;
            }

            @media (min-width: 640px) {
                padding: unset;
                margin-bottom: 3rem;
            }
        }

        .details-container {
            @media (min-width: 640px) {
                width: 25%;
            }
        }

        .inner-card {
            padding-bottom: .5rem;

            @media (min-width: 640px) {
                padding-bottom: unset;
            }
        }

        .details {
            flex-flow: column nowrap;
            ${'' /* padding: 1rem 0 0 0; */}
        }

        .featured-image {
            padding-bottom: 1rem;

            img {
                width: 100%;
            }

            @media (min-width: 640px) {
                width: 45%;
            }
        }

        .aircraft-features {
            h5 {
                margin-bottom: 0.5rem;
                word-break: break-word;
            }
            @media (max-width: 639px) {
                .features {
                    p:last-child {
                        margin: 0;
                    }
                }
            }
            @media (min-width: 640px) {
                padding: .75rem;
                width: 30%;
            }
        }
    }
`

export const AircraftCard = styled.div`
    margin-bottom: 2rem;
    background: #fff;
    position: relative;
    overflow: hidden;
    color: ${props => props.textColor};

    .wrapper-link {
        color: ${props => props.textColor};
        &:focus {
            text-decoration: none;
        }
        @media (min-width: 640px) {
            display: flex;
            height: 100%;
        }
    }
    .title {
        position: relative;
        h3 {
            margin-bottom: .5rem;
            word-break: break-word;
        }
        h5 {
            margin-bottom: .25rem;
            word-break: break-word;
        }
    }
    hr {
        background: #b2b2b2 !important;
        height: 2px !important;
        margin-bottom: 1rem;
    }
    .details {
        & > * {
            display: flex;
            flex-wrap: wrap;
            align-items: center;
            margin: .5rem 0;
            img {
                width: 18px;
                margin-right: .5rem;
            }
            span {
                font-weight: bold;
                margin-right: .25rem;
            }
        }
    }
    .featured-image {
        display: flex;
        justify-content: center;
        img {
            width: 100%;
        }
    }
    .view-details {
        text-align: center;
        padding: 1rem 0;
    }
    .sold-flag {
        background: #4f4f4f;
        color: #fff;
        position: absolute;
        left: -40px;
        top: 20px;
        transform: rotate(-45deg);
        width: 175px;
        text-align: center;
        font-size: 1.25rem;
        @media (min-width: 640px) {
            top: 34px;
        }
    }
    @media (min-width: 640px) {
        padding: 0;
        box-shadow: 2px 2px 8px rgba(0,0,0,.25);
        transition: .3s;
        .wrapper-link {
            padding: 0;
        }
        .details-container {
            width: 25%;
            padding: .75rem;
            ${'' /* display: flex;
            flex-wrap: wrap; */}
            .title {
                text-align: left;
                &:after {
                    width: 100%;
                    bottom: -.5rem;
                }
            }
            .details {
               
                & > * {
                    width: 100%;
                    margin: .5rem 0;
                }
            }
        }
        .featured-image {
            width: 75%;
            position: relative;
            overflow: hidden;
            img {
                width: 100%;
                width: 100%;
                height: 100%;
                position: absolute;
                object-fit: cover;
                transition: .3s;
            }
        }
        .view-details {
            padding: 0;
            display: flex;
            align-self: flex-end;
        }
        &.sold {
            &:hover, &:focus {
                box-shadow: 2px 2px 8px rgba(0,0,0,.25);
            }
            .featured-image {
                img {
                    transform: unset;
                }
            }
        }
    }

    &.sold {
        .featured-image {
            img {
                opacity: .5;
            }
        }
    }
`

export const MarketReports = styled.div`
    margin-top: 2rem;
    @media (min-width: 640px) {
        margin-top: 4rem;
    }
`
export const MarketReportCard = styled.div`
    a {
        color: ${props => props.textColor};
    }
    .featured-image {
        margin-bottom: .75rem;
        position: relative;
        height: 200px;
        overflow: hidden;
        img {
            position: absolute;
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: .3s;
        }
    }
    .title h4 {
        margin: .5rem 0;
    }
    @media (min-width: 640px) {
        &:hover {
            .featured-image {
                img {
                    transform: scale(1.1);
                }
            }
        }
    }
    @media (min-width: 1024px) {
        margin-bottom: 2.5rem;
        .featured-image {
            height: 275px;
            margin-bottom: 1rem;
        }
        .title h4 {
            margin: 1rem 0;
        }
    }
`

export const CabinClassFilters = styled.div`
    padding: 1rem 0;
    background-color: ${props => props.bgColor};
    color: ${props => props.textColor};
    &.add-bg-image {
        background-image: url('${props => props.bgImage?.url}');
        background-position: center center;
        background-size: cover;
    }
    label {
        display: flex;
        margin-bottom: 1rem;
        span {
            width: 95%;
        }
    }
    input[type="checkbox"] {
        &:checked::before {
            background: #000;
        }
    }
`
export const ListingContainer = styled.div`
    position: relative;
    padding: 2rem 0;

    .filter-container {
        margin-bottom: 2rem;
    
        @media (min-width: 640px) {
            margin-bottom: 4rem;
        }
    }
    
    .search-wrapper {
        display: flex;
        svg.search-icon {
            width: 20px;
            height: 25px;
            margin-right: .5rem;
        }
        @media (max-width: 639px) {
            margin-bottom: 1rem;
        }
    }
    .filter-wrapper {
       align-items: center; 
    }
    .sold-filter {
        display: flex;
    }
    .filter-toggle-wrapper {
        display: flex;
        align-items:center;
        .filter-toggle {
            cursor: pointer;
            svg {
                margin-right: .5rem;
            }
        }    
        @media (max-width: 639px) {
            margin-bottom: 1rem;
        }
    }
    
`

export const Filters = styled.div`
    color: ${props => props.textColor};
    position: fixed;
    top: 42%;
    transform: translateY(-42%);
    background: #fff;
    width: 100%;
    padding: 2rem 1rem;
    box-sizing: border-box;
    border: 1px solid #000;
    left: -100%;
    transition: .3s;
    z-index: 10;
    &.show {
        left: 0;
    }
    .close-button {
        position: absolute;
        right: 10px;
        top: 8px;
        width: 25px;
        height: 25px;
        opacity: 0.5;
        transition: .3s;
        cursor: pointer;
        &:hover {
            opacity: 1;
        }
        &:before, &:after {
            position: absolute;
            left: 15px;
            content: " ";
            height: 25px;
            width: 2px;
            background-color: ${props => props.textColor};
        }
        &:before {
            transform: rotate(45deg);
        }
        &:after {
            transform: rotate(-45deg);
        }          
    }
    .category-filters {
        margin: 2rem 0;
        font-size: 1.25rem;
        .children {
            margin-left: 1.5rem;
        }
        .cat-group {
            margin: .75rem 0;
            &.parent {
                .inner-wrapper {
                    display: flex;
                    align-items: center;
                    position: relative;
                }
                .plusminus {
                    position: absolute;
                    width: 20px;
                    height: 20px;
                    cursor: pointer;
                    right: 0;
                    
                    &:before , &:after {
                        content: "";
                        display: block;
                        background-color: currentColor;
                        position: absolute;		
                        top: 50%; left: 0;
                        transition: .35s;
                        width: 100%;
                        height: 3px;
                    }
                    
                    &:before {		
                        transform: translatey(-50%);
                    }
                    
                    &:after {
                        transform: translatey(-50%) rotate(90deg);
                    }
                    
                } 
                &.selected .plusminus {
                    &:before {
                        transform: translatey(-50%) rotate(-90deg);
                        opacity: 0;
                    }
                    &:after {
                        transform: translatey(-50%) rotate(0);
                    }
                }
                & > .inner-wrapper input[type="checkbox"] {
                    &::before {
                        transform-origin: none;
                        clip-path: none;
                    }
                    &:checked::before {
                        transform: scale(1);
                        background: currentColor;
                    }
                }

                :has(.children input[type="checkbox"]:checked) {
                    > .inner-wrapper label > input[type="checkbox"]::before {
                        height: 0.1em;
                        transform: scale(1);
                        background: currentcolor;
                    } 
                }
            }
        }
        .cat-group:not(.selected) .children {
            display: none;
        }
    }
    .sold.checkbox {
        margin-bottom: 2rem;
        font-size: 1.25rem;
    }
    @media (max-width: 767px) {
        button { 
            margin-bottom: 1rem;
        }
    }
    @media (min-width: 768px) {
        width: 550px;
        padding: 2rem 2.5rem;
        button {
            margin-right: 1rem;
        }
    }

    label {
        display: flex;
        align-items: center;
        position: relative;
        cursor: pointer;
    }
    
`

// Search Form Input
export const SearchInput = styled.input`
    border-color: ${props => props?.filterColor} !important;
    color: ${props => props?.filterColor};
    border-radius: 0;
    -webkit-appearance: none;
    &::placeholder {
        color: ${props => props?.filterColor};
    }
`;

// Search Icon
export const SearchSVG = styled.svg`
    fill: ${props => props?.filterColor};
`;

export const Loadmore = styled.div`
        padding-top: 1rem;
        padding-bottom: 2rem;
        text-align: center;
        cursor: pointer;
`;