import React, { useEffect, useState } from 'react';
import Map from 'src/partials/map';
import { addScriptDefer } from 'src/hooks';

import './index.scss';

const Start = ({ data, settings }) => {
    const [loadMap, setLoadMap] = useState(false);
    let map_id = data.use_custom ? data.mapplic_id : settings.mapplic_id;
    let map_html = data.use_custom ? data.map_html : settings.mapplic_html;
    addScriptDefer(`https://code.jquery.com/jquery-3.7.1.min.js`);
    useEffect(() => {
        let tick = setInterval(function () {
            if (window.jQuery) {
                setLoadMap(true);
                clearInterval(tick);
            }
        }, 1000);
        return () => clearInterval(tick);
    }, [])

    return (
        <div class='mapplic-module__container grid-container'>
            {data.title &&
                <header class='mapplic-header'>
                    <h1>{data.title}</h1>
                </header>
            }

            {data.blurb &&
                <article class='mapplic-blurb' dangerouslySetInnerHTML={{ __html: data.blurb }} />
            }
            {loadMap && <Map id={map_id} html={map_html} />}
        </div>
    );
};

export default Start;