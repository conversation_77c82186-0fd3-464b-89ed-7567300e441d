import styled from 'styled-components';

export const AnimatedLayers = styled.div`
    position: relative;
    overflow: hidden;
    min-height: ${props => props.bgImageMobile.height}px;
    background-size: cover;
    &.add-bg-image {
        background-image: url(${props => props.bgImageMobile.url});
    }
    .mobile-layer, .desktop-layer {
        opacity: 0;
        animation-name: layer-fade-in;
        animation-delay: ${props => props.animationSettings.delay}s;       
        animation-duration: ${props => props.animationSettings.duration_fade_in}s;
        animation-fill-mode: forwards;
        img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            position: absolute;
        }
    }
    .grid-container {
        position: absolute;
        width: 100%;
        color: #fff;
        top: ${props => props.contentPositioning.mobile_vertical}%;
        left: 50%;
        transform: translate(${props => props.contentPositioning.slide_in === 'from-left' ? '-1000%' : '1000%'}, -${props => props.contentPositioning.mobile_vertical}%);
        animation-name: layer-slide-mobile;
        animation-delay: ${props => props.animationSettings.delay * 2}s;
        animation-duration: ${props => props.animationSettings.duration_slide_in}s;
        animation-fill-mode: forwards;
        .grid-x {
            justify-content: ${props => props.contentPositioning.mobile_horizontal};
        }
        @media (max-width: 1365px) {
            box-sizing: border-box;
        }
    }
    @media (min-width: 1024px) {
        min-height: ${props => props.bgImageDesktop.height}px;
        &.add-bg-image {
            background-image: url(${props => props.bgImageDesktop.url});
        }
        .grid-container {
            top: ${props => props.contentPositioning.desktop_vertical}%;
            transform: translate(${props => props.contentPositioning.slide_in === 'from-left' ? '-1000%' : '1000%'}, -${props => props.contentPositioning.desktop_vertical}%);
            animation-name: layer-slide-desktop;
            .grid-x {
                justify-content: ${props => props.contentPositioning.desktop_horizontal};
            }
        }
    }
    @keyframes layer-fade-in {
        0% { opacity: 0; }
        100% { opacity: 1; }
    }
    @keyframes layer-slide-mobile {
        0% { transform: translate(${props => props.contentPositioning.slide_in === 'from-left' ? '-1000%' : '1000%'}, -${props => props.contentPositioning.mobile_vertical}%);}
        100% { left: 50%; transform: translate(-50%, -${props => props.contentPositioning.mobile_vertical}%); }
    }
    @keyframes layer-slide-desktop {
        0% { transform: translate(${props => props.contentPositioning.slide_in === 'from-left' ? '-1000%' : '1000%'}, -${props => props.contentPositioning.desktop_vertical}%);}
        100% { left: 50%; transform: translate(-50%, -${props => props.contentPositioning.desktop_vertical}%); }
    }
`