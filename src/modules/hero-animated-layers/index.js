import React from "react";

// Helpers
const HtmlParser = React.lazy(() => import('src/helpers/html-parser'));
const Imaging = React.lazy(() => import('src/helpers/imaging'));
// Styles
import { AnimatedLayers } from "./styles";
// Hooks
import { useInView } from 'react-intersection-observer';

const Start = ({ data }) => {
    const { ref, inView } = useInView({
        triggerOnce: true,
        fallbackInView: true
    });

    return (
        <AnimatedLayers
            ref={ref}
            className={`hero-animated-layers${inView ? ' add-bg-image' : ''}`}
            bgImageMobile={data.image_layers?.mobile_background}
            bgImageDesktop={data.image_layers?.desktop_background}
            animationSettings={data.animation_settings}
            contentPositioning={data.content_positioning}
        >
            <div className="mobile-layer hide-for-large"><Imaging data={data.image_layers?.mobile_animated_layer} /></div>
            <div className="desktop-layer show-for-large"><Imaging data={data.image_layers?.desktop_animated_layer} /></div>
            <div className="grid-container">
                <div className="grid-x">
                    <div className="cell large-6 content-wrapper">
                        <HtmlParser html={data.content} />
                    </div>
                </div>
            </div>

        </AnimatedLayers>
    );
}

export default Start;