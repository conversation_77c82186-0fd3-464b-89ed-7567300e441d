@import 'src/scss/variables.scss';

#app {
    #modules-container {
        .post-type-list.carousel {
            .posts-outer-container.carousel {
                .slick-slider {
                    &.dots,
                    &.dashes {
                        margin-bottom: 50px;
                    }

                    .slick-dots {
                        bottom: -25px;
                        height: 10px;

                        ul {
                            position: relative;
                            display: block;
                            list-style-type: disc;
                            margin-block-start: 0px;
                            margin-block-end: 0px;
                            margin-inline-start: 0px;
                            margin-inline-end: 0px;
                            padding-inline-start: 0px;
                            height: 10px;

                            &.dots {
                                li {
                                    display: inline-flex;
                                    justify-content: center;
                                    width: 10px;
                                    height: 10px;

                                    svg {
                                        margin: 0;
                                        width: 10px;
                                        height: 10px;
                                    }
                                    &.slick-active {
                                        opacity: 1;
                                    }
                                }
                            }

                            &.dashes {
                                li {
                                    display: inline-flex;
                                    justify-content: center;
                                    width: 10px;
                                    height: 10px;

                                    svg {
                                        margin: 0;
                                        width: 20px;
                                        height: 10px;
                                    }
                                    &.slick-active {
                                        opacity: 1;
                                    }
                                }
                            }
                        }
                    }

                    .slick-list {
                        // padding: 0px 50px;

                        .slick-track {
                            display: flex;
                            flex-direction: row;
                            // gap: 8px;
                            .slick-slide {
                                width: 100%;
                                height: inherit;

                                & > div {
                                    height: inherit;
                                }
                            }
                        }
                    }

                    // NOT HALF & HALF.
                    &:not(.half_half) {
                        .slick-list {
                            transform: translate3d(0px, 0px, 0px);
                            .slick-slide {
                                box-sizing: border-box;

                            }
                        }
                    }

                    &.off-set-carousel {
                        .slick-dots {
                            right: calc((100% - 1200px) / 2);
                            @media screen and (max-width:$breakpoint-larger)  { right: 75px; }
                            @media screen and (max-width:$breakpoint-large)   { right: 50px; }
                            @media screen and (max-width:$breakpoint-tablet)  { right: 50px; }
                            @media screen and (max-width:$breakpoint-medium)  { right: 25px;}
                            @media screen and (max-width:$breakpoint-mobile)  { right: 5px; }
                            @media screen and (max-width:$breakpoint-small)   { right: 0px; }
                            @media screen and (max-width:$breakpoint-xsmall)  { right: 0px; }
                        }
                    }
                }
            }
        }
    }
}
