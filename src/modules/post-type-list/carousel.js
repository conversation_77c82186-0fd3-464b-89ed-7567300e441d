import React, { useMemo, useState } from "react";
import Slider from "react-slick";
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { faCircle, faCircleDot } from '@fortawesome/free-regular-svg-icons'
// HELPERS.
import { PrevArrow, NextArrow } from 'src/helpers/slick';
import HtmlParser from 'src/helpers/html-parser';
// POST ITEMS from the different styles
const Boxes = React.lazy(() => import('src/modules/post-type-list/styles/boxes'));
const Cards = React.lazy(() => import('src/modules/post-type-list/styles/cards'));
const Default = React.lazy(() => import('src/modules/post-type-list/styles/default'));
const FeaturedImageText = React.lazy(() => import('src/modules/post-type-list/styles/featured-image-text'));
const HalfHalf = React.lazy(() => import('src/modules/post-type-list/styles/half-half.js'));
const Hover = React.lazy(() => import('src/modules/post-type-list/styles/hover.js'));
const Separated = React.lazy(() => import('src/modules/post-type-list/styles/separated'));
const ThreeColImage = React.lazy(() => import('src/modules/post-type-list/styles/three-col-image'));
const Tiles = React.lazy(() => import('src/modules/post-type-list/styles/tiles'));
// SCSS.
import 'src/modules/post-type-list/carousel.scss';
import 'slick-carousel/slick/slick.scss';
import 'slick-carousel/slick/slick-theme.scss';

const Start = ({ currentItems, data, settings }) => {
    const [current, setCurrent] = useState(0);
    const classes = useMemo(() => {
        var newArray = ['carousel', 'cell', data.display_options, data.style, data.carousel_style];
        if (data['off-set_carousel']) newArray.push('off-set-carousel');
        return newArray.join(' ');
    }, [data])

    return (
        <Slider className={classes} {...SliderSettings(data, settings, currentItems, current, setCurrent)}>
            {currentItems.map((post, index) => <PostItem post={post} index={index} data={data} settings={settings} />)}
        </Slider>
    );
};

const PostItem = ({ post, index, data, settings }) => {
    post.parsedTitle = <HtmlParser html={post.title} />

    switch (true) {
        case data.style == 'boxes':
            return (<Boxes post={post} index={index} data={data} settings={settings} />);
        case data.style == 'cards':
            return (<Cards post={post} index={index} data={data} settings={settings} />);
        case data.style == 'default':
            return (<Default post={post} index={index} data={data} settings={settings} />);
        case data.style == 'featured_image_text':
            return (<FeaturedImageText post={post} index={index} data={data} settings={settings} />);
        case data.style == 'half_half':
            return (<HalfHalf post={post} index={index} data={data} settings={settings} />);
        case data.style == 'hover':
            return (<Hover post={post} index={index} data={data} settings={settings} />);
        case data.style == 'separated':
            return (<Separated post={post} index={index} data={data} settings={settings} />);
        case data.style == 'three_col_image':
            return (<ThreeColImage post={post} index={index} data={data} settings={settings} />);
        case data.style == 'tiles':
            return (<Tiles post={post} index={index} data={data} settings={settings} />);
        default:
            return null;
    };
};

export default Start;

function SliderSettings(data, settings, currentItems, current, setCurrent) {
  
    return {
        adaptiveHeight: false,
        autoplay: false,
        autoplaySpeed: 4500,
        centerMode: false,
        centerPadding: centerPadding(data, currentItems, window.innerWidth),
        fade: false,
        infinite: false,
        pauseOnFocus: true,
        pauseOnHover: true,
        slidesToScroll: (data.style == 'half_half') ? 2 : 1,
        slidesToShow: slidesToShow(data, currentItems),
        variableWidth: false,
        dots: !data.carousel_style.includes('arrow'),
        customPaging: (i) => <Dot index={i} current={current} data={data} settings={settings} />,
        appendDots: dots => (
            <div style={{ color: settings.design?.colors?.tertiary_color }}>
                <ul>{dots}</ul>
            </div>
        ),
        beforeChange: (current, next) => setCurrent(next),
        arrows: data.carousel_style.includes('arrow'),
        nextArrow: <NextArrow />,
        prevArrow: <PrevArrow />,
        responsive: [
            {
                breakpoint: 1900,
                settings: {
                    slidesToShow: slidesToShow(data, currentItems, 1900),
                    centerPadding: centerPadding(data, currentItems, 1900),
                }
            },
            {
                breakpoint: 1600,
                settings: {
                    slidesToShow: slidesToShow(data, currentItems, 1600),
                    centerPadding: centerPadding(data, currentItems, 1600),
                }
            },
            {
                breakpoint: 1200,
                settings: {
                    slidesToShow: slidesToShow(data, currentItems, 1200),
                    centerPadding: centerPadding(data, currentItems, 1200),
                }
            },
            {
                breakpoint: 768,
                settings: {
                    slidesToShow: slidesToShow(data, currentItems, 768),
                    centerPadding: centerPadding(data, currentItems, 768),
                }
            },
            {
                breakpoint: 640,
                settings: {
                    slidesToShow: 1,
                    centerPadding: '0px',
                }
            },
            {
                breakpoint: 428,
                settings: {
                    slidesToShow: 1,
                    slidesToScroll: 1,
                    centerPadding: '0px',
                }
            }
        ]
    };
};

const Dot = (props) => {
    const { index, current, data, settings, onClick } = props;

    const styles = useMemo(() => {
        return {
            fill: index == current ? settings.design?.colors?.tertiary_color : 'transparent',
            strokeWidth: 6,
            stroke: settings.design?.colors?.tertiary_color
        };
    }, [current]);

    if (data.carousel_style == 'dashes') {
        return (
            <div onClick={onClick}>
                <svg width="25" height="10" aria-label="dash">
                    <rect width="25" height="10" style={styles} />
                </svg>
            </div>
        );
    } else {
        return (
            <div onClick={onClick}>
                {index != current && <FontAwesomeIcon icon={faCircle} />}
                {index == current && <FontAwesomeIcon icon={faCircleDot} />}
            </div>
        );
    }
};

function slidesToShow(data, currentItems, width = false) {
    switch (true) {
        case data.style == 'half_half':
            return 2;
        case width == 1900:
            return currentItems.length < 4 ? currentItems.length : 4;
        case width == 1600:
            return currentItems.length < 3 ? currentItems.length : 3;
        case width == 1200:
            return currentItems.length < 2 ? currentItems.length : 2;
        case width == 768:
            return 1;
        default:
            return currentItems.length < 5 ? currentItems.length : 5;
    };
};

function centerPadding(data, currentItems, width = false) {
    switch (true) {
        case data.style == 'half_half':
        case data['off-set_carousel']:
            return '0px';
        case width == 1900:
        case width == 1600:
            return '60px'
        case width == 1200:
            return '30px';
        case width == 768:
            return '0px';
        default:
            return '60px';
    };
};