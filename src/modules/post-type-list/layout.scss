@import "src/scss/variables.scss";

#app {
    #modules-container {
        .post-type-list {
            .container {
                padding: 32px 0px;
                width: 100%;
                max-width: 100%;
                background-size: cover;
                &:not(.half_half) {
                    &.dots,
                    &.dashes {
                        padding-bottom: 60px;
                    }
                }

                .bulk-container {
                    .module-title {
                        margin: 0px auto 30px;
                        padding: 0px 10px;
                        width: 100%;
                        max-width: 1200px;
                    }
                    .module-blub {
                        margin: 0px auto 30px;
                        // padding: 0px 10px;
                        width: 100%;
                        &.half_half {
                            max-width: 1200px;
                        }
                    }
                    .module-buttons {
                        margin: 0px auto;
                        padding: 0px 10px;
                        width: 100%;
                        max-width: 1200px;
                    }

                    .posts-outer-container {
                        &.off-set-carousel {
                            margin-left: calc((100% - 1200px) / 2);

                            // @media screen and (max-width:$breakpoint-maximum) { }
                            // @media screen and (max-width:$breakpoint-desktop) { }
                            // @media screen and (max-width:$breakpoint-largest) { }
                            @media screen and (max-width: $breakpoint-maximum) {
                                margin-left: 150px;
                            }
                            @media screen and (max-width: $breakpoint-larger) {
                                margin-left: 125px;
                            }
                            @media screen and (max-width: $breakpoint-large) {
                                margin-left: 100px;
                            }
                            @media screen and (max-width: $breakpoint-tablet) {
                                margin-left: 75px;
                            }
                            @media screen and (max-width: $breakpoint-medium) {
                                margin-left: 50px;
                            }
                            @media screen and (max-width: $breakpoint-mobile) {
                                margin-left: 10px;
                            }
                            @media screen and (max-width: $breakpoint-small) {
                                margin-left: 0px;
                            }
                            @media screen and (max-width: $breakpoint-xsmall) {
                                margin-left: 0px;
                            }
                        }
                    }
                }
            }
            &.partial-fill {
                overflow: hidden;
                .container {
                    padding-top: 0;
                    .title-container {
                        position: relative;
                        padding: 2rem 0 200px;
                        &:before, &:after {
                            content: "";
                            position: absolute;
                            top: 0;
                            height: 100%;
                            width: 100%;
                            background: inherit;
                        }
                        &:before {
                            right: 100%;
                        }
                        &:after {
                            left: 100%;
                        }
                    }
                    .posts-outer-container {
                        margin-top: -200px;
                        z-index: 1;
                    }
                }
            }
        }
    }
}
