/*******************************************************************************************************
   Copyright 2021 - Calise Partners LLC d/b/a Imaginuity. All rights are reserved.
   Reproduction or transmission in whole or in part, in any form or by any means, electronic, mechanical
or otherwise, is prohibited without the prior written consent of Calise Partners LLC d/b/a Imaginuity.
********************************************************************************************************
   Project : 10618 Maverick Platform Development
   Author : Imaginuity Developers (<PERSON>, <PERSON>)
   Description : renders the card style of post listing
   Creation Date : Fri Feb 05 2021
   Documentation : [documentation url - point to default wiki for project if no specific documentation for script]
 ********************************************************************************************************/

import React, { useEffect, useState } from 'react';
import PyLink from 'src/helpers/pylink';
import Button from 'src/partials/button';
import { decode } from 'html-entities';


import './cards.scss';

const Cards = ({ data, settings }) => {

    const [postItems, setPostItems] = useState([]);

    useEffect(() => {
        setPostItems(buildPostItems(data.posts));
    }, [data.posts]);

    function buildPostItems(postData) {
        var posts = postData?.map((post, index) => {
            return (
                <div key={index} class='single-post-container'>
                    <div class='cards-image-content-inner-container'>
                        {post.feat_image && (
                            <div class='cards-image-wrapper' dangerouslySetInnerHTML={{ __html: post.feat_image }} />
                        )}
                        <div class='cards-content-wrapper'>
                            <h3 class={`cards-title ${data?.title_alignment}`}>{decode(post.title)}</h3>
                            <p class='cards-exerpt'>
                                {decode(post.exerpt)}
                            </p>
                            {!data.hide_link_to_individual_posts &&
                                <PyLink class='cards-link' url={post.url} aria-label={`Read More about ${post.title}`} style={{ color: settings.design?.colors?.primary_color}} >
                                    {data.post_link_text.length < 1 ? 'Read More' : decode(data.post_link_text)}<i>{'>'}</i>
                                </PyLink>
                            }
                        </div>
                    </div>
                </div>
            );
        });

        return posts;
    };

    var bgColor = pylot.design.coloring(data.background_color, 'backgroundColor');

    if (data.background_type === 'image') {
        var backgroundStyles = {
            ...bgColor,
            backgroundImage: `url(${data.background_image.url})`,
            backgroundPosition: 'center center',
            backgroundSize: "cover",
            backgroundRepeat: 'no-repeat',
        };
    } else if (data.background_type === 'color') {
        var backgroundStyles = {
            ...bgColor,
        };
    }

    return (
        <div class={`cards__posts-outer-container ${data.display_options}`} style={backgroundStyles}>
            <div class='cards-content-container'>

                {data.title &&
                    <div class='cards-listing-title'>
                        <h2 style={{color: settings.design?.colors?.primary_color}}>{decode(data.title)}</h2>
                    </div>
                }

                <div class='cards-post-items-wrapper'>
                    {postItems}
                </div>

                {data.button &&
                    <div class='cards-button-container'>
                        <Button title={data.button?.title} url={data.button.url} target={data.button.target} icon={data.button_icon} />
                    </div>
                }

            </div>
        </div>
    );
};

export default Cards;
