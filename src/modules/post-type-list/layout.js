import React, { useState, useEffect, useMemo } from 'react';
import { decode } from 'html-entities';
import { useInView } from 'react-intersection-observer';

// PARTIALS & HELPERS.
import Button from "src/partials/button";
import { Coloring } from 'src/helpers';
import HtmlParser from 'src/helpers/html-parser'

// SCSS. {data.bleed_module_upwards ? 'bleed-module-upwards' : ''
import 'src/modules/post-type-list/layout.scss';

const Start = ({ data, settings, children, ...otherProps }) => {
    return (<div class={`post-type-list ${data.display_options} ${data.style} ${(data.background_type === 'color' && data.partial_fill) ? 'partial-fill' : ''}`} children={children} {...otherProps} />);
};
Start.displayName = 'modules, post-type-list, layout: Start';

Start.Container = React.memo(({ data, settings, placeholders, children, ...otherProps }) => {
    const { ref, inView } = useInView({
        triggerOnce: true,
        fallbackInView: true
    });
    const classes = useMemo(() => {
        var newArray = ['container', data.display_options, data.style, data.background_type, data.carousel_style ];
        if (data.bleed_module_upwards) newArray.push('bleed-module-upwards');
        return newArray.join(' ');
    }, [data]);
    const bgImage = (placeholders && data.background_image_selection === 'dynamic') ? placeholders.image[data?.background_image_dynamic] : data?.background_image
   
    const styles = !data.partial_fill ? useMemo(() => {
        return {
            backgroundImage: data.background_type === 'image' ? `url('${bgImage?.url}')` : 'unset',
            backgroundColor: data.bleed_module_upwards && data.bleed_background_color ? Coloring(data.bleed_background_color, settings) : Coloring(data.background_color, settings),
            color: data.background_value === 'dark' ? '#fff' : Coloring('body_copy_color', settings)
        };
    }, [data]) : null;
    return (<div ref={ref} class={classes} style={styles} children={inView ? children : null} {...otherProps} />);
});
Start.Container.displayName = 'modules, post-type-list, layout: Start.Container';

Start.Container.TitleContainer = React.memo(({ data, settings, children, ...otherProps }) => {
    
    const styles = (data.background_type === 'color' && data.partial_fill) ? useMemo(() => {
        return {
            backgroundColor: data.bleed_module_upwards && data.bleed_background_color ? Coloring(data.bleed_background_color, settings) : Coloring(data.background_color, settings),
            color: data.background_value === 'dark' ? '#fff' : Coloring('body_copy_color', settings)
        };
    }, [data]) : null;
    return (<div class={'title-container'} style={styles} children={children} {...otherProps} />);
});
Start.Container.TitleContainer.displayName = 'modules, post-type-list, layout: Start.Container.TitleContainer';

Start.Container.Title = React.memo(({ data, settings, placeholders, children, ...otherProps }) => {
    otherProps.class = useMemo(() => {
        var newArray = ['module-title', data.display_options, data.style, data.background_type, data?.post_list_post_type, data?.title_alignment ];
        if (data['off-set_carousel']) newArray.push('off-set-carousel');
        return newArray.join(' ');
    }, [data]);
    const title = (placeholders && data.title_selection === 'dynamic') ? placeholders.single_line[data.title] : data.title;

    if (!(title)) return null;

    return (
        <div {...otherProps}>
            <h2 class={data?.background_value === 'dark' ? 'white-txt' : 'primary-txt'}>{decode(title)}</h2>
        </div>
    );
});
Start.Container.Title.displayName = 'modules, post-type-list, layout: Start.Container.Title';

Start.Container.Blub = React.memo(({ data, settings, placeholders, children, ...otherProps }) => {
    otherProps.class = useMemo(() => {
        var newArray = ['module-blub', data?.display_options, data?.style, data?.background_type, data?.post_list_post_type, data?.background_value ];
        if (data?.background_value === 'dark') newArray.push('white-txt');
        if (data['off-set_carousel']) newArray.push('off-set-carousel');
        return newArray.join(' ');
    }, [data]);
    return (<div {...otherProps}>{data.blurb && <HtmlParser html={data.blurb} placeholders={placeholders} />}</div>);å
});
Start.Container.Blub.displayName = 'modules, post-type-list, layout: Start.Container.Blub';

Start.Container.Posts = ({ data, settings, children, ...otherProps }) => {
    otherProps.class = useMemo(() => {
        var newArray = ['posts-outer-container', data.display_options, data.style, data.background_type, data?.post_list_post_type ];
        if (data.display_options != 'carousel') newArray.push('grid-x', 'grid-margin-x', 'grid-margin-y');
        if (data.list_item_alignment === 'center') newArray.push('align-center');
        if (data['off-set_carousel']) newArray.push('off-set-carousel');
        return newArray.join(' ');
    }, [data]);
    return (<div children={children} {...otherProps} />);
};
Start.Container.Posts.displayName = 'modules, post-type-list, layout: Start.Container.Posts';

Start.Container.Posts.Item = ({ data, settings, children, ...otherProps }) => {
    let pointerClass = data?.hide_link_to_individual_posts ? 'no-pointer' : null;
    otherProps.class = useMemo(() => {
        var newArray = ['single-post-container', data.display_options, data.style, data.background_type, data?.post_list_post_type, pointerClass];
        if (data.display_options != 'carousel' && data.style != 'tiles') newArray.push('cell', ClassGenerator(data.display_options, data.style));
        if (data.hide_card_outline) newArray.push('hide-border');
        return newArray.join(' ');
    }, [data]);
    return (<div children={children} {...otherProps} />);
};
Start.Container.Posts.Item.displayName = 'modules, post-type-list, layout: Start.Container.Posts.Item';

Start.Container.Posts.Item.Overlay = ({ column, data, settings, children, ...otherProps }) => {
    if (column.overlay_type == 'none') return null; // DON'T SHOW IF NONE.
    const RGB = Colors.hexToRgb(column.overlay_color);
    otherProps.class = useMemo(() => [ 'cta-v2-overlay', column?.overlay_type ].join(' '), [data]);
    otherProps.style = useMemo(() => {
        // console.log(`linear-gradient(${column?.overlay_type}, rgba(${RGB.r}, ${RGB.b}, ${RGB.g}, ${column.overlay_transparency}), rgba(${RGB.r}, ${RGB.b}, ${RGB.g}, 0))`)
        return (column?.overlay_type == 'solid') ? {
            backgroundColor:  `rgba(${RGB.r}, ${RGB.b}, ${RGB.g}, ${column.overlay_transparency})`
        } : {
            backgroundImage: `linear-gradient(${column?.overlay_type}, rgba(${RGB.r}, ${RGB.b}, ${RGB.g}, ${column.overlay_transparency}), rgba(${RGB.r}, ${RGB.b}, ${RGB.g}, 0))`
        };
    }, [data]);
    return (<div children={children} {...otherProps} />);
};
Start.Container.Posts.Item.Overlay.displayName = 'modules, post-type-list, layout: start.container.posts.item.overlay';



Start.Container.Buttons = React.memo(({ data, settings, placeholders, children, ...otherProps }) => {
    otherProps.class = useMemo(() => {
        var newArray = ['module-buttons', data.display_options, data.style, data.background_type, data?.post_list_post_type, data?.title_alignment ];
        return newArray.join(' ');
    }, [data]);
    const buttonLink = placeholders ? placeholders.button_link[data.button] : data?.button
    
    // DON'T DISPLAY BUTTON IF NOT PROVIDED.
    if (!(buttonLink)) return null;
    return (
        <div {...otherProps}>
            <Button title={buttonLink?.title} url={buttonLink?.url} target={buttonLink?.target} tone={data?.background_value} type={data?.button_style} />
        </div>
    );
});
Start.Container.Buttons.displayName = 'modules, post-type-list, layout: Start.Container.Buttons';

export default Start;



export function ClassGenerator (displayOption, style) {
    switch (true) {
        case style == 'three_col_image':
            return 'large-4 medium-4 small-12';
        case displayOption == 'one-column':
            return 'large-12 medium-12 small-12';
        case displayOption == 'two-column':
            return 'large-6 medium-6 small-12';
        case displayOption == 'three-column':
            return 'large-4 medium-4 small-12';
        case displayOption == 'four-column':
            return 'large-3 medium-3 small-12';
        case displayOption == 'six-column':
            return 'large-2 medium-3 small-12';
        default:
            return 'large-12 medium-12 small-12';
    };
};