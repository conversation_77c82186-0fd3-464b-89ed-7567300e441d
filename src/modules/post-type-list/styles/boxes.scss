@import 'src/scss/variables.scss';

#app {
    #modules-container {
        .post-type-list.boxes {
            .posts-outer-container.boxes {
                .single-post-container.boxes {
                    padding: 32px 24px;

                    .label-container {
                        p {
                            margin-block-start: 0px;
                            margin-block-end: 0px;
                            margin-inline-start: 0px;
                            margin-inline-end: 0px;
                        }
                    }

                    // color: ${props => props.textColor};
                    // border-radius: ${props => props.borderRadius}px;
                    &.color {
                        // background-color: ${props => props.backgroundColor};
                    }
                    &.image {
                        // background-image: url(${props => props.backgroundImage});
                        background-position: center center;
                        background-size: cover;
                        background-repeat: no-repeat;
                    }
                    .label {
                        display: inline-block;
                        padding: 0.25rem 0.75rem;
                        font-size: 0.75rem;
                    }
                    a {
                        font-size: .75rem;
                    }
                    .post-title {
                        font-weight: 900;
                    }
                    .post-exerpt, 
                    p, 
                    .related-store {
                        font-size: .875rem !important;
                        line-height: 22px;
                    }
                    .post-link {
                        text-decoration: none !important;
                        letter-spacing: 2px;
                        span {
                            margin-left: .25rem;
                        }
                    }
                }
            }
        }
    }
}