@import 'src/scss/variables.scss';

#app {
    #modules-container {
        .post-type-list.hover {
            .posts-outer-container.hover {
                .single-post-container.hover {
                    margin:7px;

                    position: relative;
                    overflow: hidden;
                    border: none;
                    max-width: none;

                    .background {
                        position: absolute;
                        height: 100%;
                        width: 100%;
                        top: 0;

                        div {
                            height: inherit;
                            width: inherit;
                        }
                        img { 
                            height: 100%;
                            width: 100%;
                            object-fit: cover;
                        }
                    }

                    .hover-container {
                        position: relative;
                        overflow: hidden;
                        min-height: 420px;
                        display: flex;
                        align-items: flex-end;
                        &:after {
                            content: "";
                            position: absolute;
                            top: 0;
                            left: 0;
                            right: 0;
                            bottom: 0;
                            background: rgba(0, 0, 0, 0.3);
                            transition: 0.2s;
                        }
                        img {
                            position: absolute;
                            height: 100%;
                            width: 100%;
                            object-fit: cover;
                            top: 0;
                        }
                        .cards-content-wrapper {
                            padding: 1.5rem;
                            z-index: 1;
                            width: 100%;
                            transition: 0.2s;
                            .label {
                                font-size: 0.75rem;
                                margin: 1.5rem 0 0.5rem;
                                display: inline-block;
                            }
                            .cards-title {
                                margin-bottom: 0.5rem;
                            }
                            .cards-link {
                                text-decoration: none;
                            }
                        }
                        .hover-wrapper {
                            position: absolute;
                            top: 0;
                            left: 0;
                            right: 0;
                            bottom: 0;
                            display: flex;
                            align-items: flex-end;
                            padding: 1.5rem;
                            opacity: 0;
                            transition: 0.2s;
                            z-index: 10;
                            .cards-title {
                                margin-bottom: 0.5rem;
                            }

                            .inner-wrapper {
                                width: 100%;
                            }
                        }
                    }
                    &:hover {
                        .hover-container {
                            .cards-content-wrapper {
                                opacity: 0;
                            }
                            .hover-wrapper {
                                opacity: 0.8;
                            }
                            &:after {
                                opacity: 0;
                            }
                        }
                    }

                    &.no-pointer {
                        cursor: default;
                    }
                }
                @media (min-width: 768px) {
                    &.two-column,
                    &.three-column {
                        .single-post-container.style-hover {
                            max-width: 50%;
                        }
                    }
                }
                @media (min-width: 1200px) {
                    &.three-column .single-post-container.style-hover {
                        max-width: 33.33%;
                    }
                }
            }
        }
    }
}