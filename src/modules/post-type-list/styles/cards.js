import React, { useEffect, useState, useMemo } from 'react';
import { decode } from 'html-entities';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faLock } from '@fortawesome/free-solid-svg-icons'
// HELPERS & PARTIALS.
import Clicker from 'src/helpers/clicker';
import Imaging from 'src/helpers/imaging';
import HtmlParser from 'src/helpers/html-parser'
// LAYOUT & CAROUSEL.
import Layout from 'src/modules/post-type-list/layout';

// SCSS.
import 'src/modules/post-type-list/styles/cards.scss';

const Start = ({ post, index, data, settings }) => {
    let imgData = post?.feat_image_field;

    if (!post?.feat_image_field?.id) {
        imgData = {
            url: post?.feat_image_field,
            alt: post?.parsedTitle
        }
    }

    var strippedString = post?.exerpt?.replace(/<[^>]*>?/gm, '');
    var splitString = strippedString?.split(' ');
    var splitString = post?.exerpt?.split(' ');
    var excerptString;

    if (splitString?.length >= 25) {
        var trimmedArray = [];
        for (let i = 0; i < 26; i++) {
            trimmedArray?.push(splitString[i]);
        }

        var joinedString = trimmedArray?.join(' ');
        excerptString = joinedString?.substring(0, joinedString?.lastIndexOf(' ')) + '...';
    } else {
        excerptString = post?.exerpt?.replace(/<[^>]*>?/gm, '');
    }

    const classes = useMemo(() => {
        var newArray = [ 'cards-image-content-inner-container', data.display_options, data.style, 'test' ]
        if (data.hide_card_outline) newArray.push('hide-border');
        return newArray.join(' ');
    }, [data]);

    return (
        <Layout.Container.Posts.Item data={data} settings={settings}>
            <div class={classes}>

                {post?.feat_image && <div class='cards-image-wrapper' dangerouslySetInnerHTML={{ __html: post?.feat_image }} />}

                {post?.feat_image_field && !post?.feat_image &&
                    <div class='cards-image-wrapper'>
                        <Imaging data={imgData} />
                    </div>
                }

                <div class='cards-content-wrapper'>

                    {data?.show_post_date && 
                        <p class='post-date'>{post?.post_date}</p>
                    }

                    <h3 class={`cards-title ${data?.post_title_label_alignment}`}>{decode(post?.parsedTitle)}{post.locked && <FontAwesomeIcon icon={faLock} className="locked" />}</h3>

                    {post?.related_store &&
                        <p>
                            <Clicker class='related-store' type='anchor' url={post?.related_store?.url}>{decode(post?.related_store?.title)}</Clicker>
                        </p>
                    }

                    {data?.show_excerpt &&
                        <p class={`cards-exerpt ${data?.post_title_label_alignment}`}>
                            <HtmlParser html={decode(post?.exerpt)} />
                        </p>
                    }

                    {!data?.hide_link_to_individual_posts &&
                        <div class={`link-container ${data?.post_title_label_alignment}`}>
                            <Clicker class='cards-link' target={post?.external_link} type='anchor' url={post?.url} ariaLabel={`Read More about ${post?.parsedTitle}`}>
                                {post?.link_text_override ? post?.link_text_override : data?.post_link_text?.length < 1 ? 'Read More' : decode(data?.post_link_text)}<i>{'>'}</i>
                            </Clicker>
                        </div>
                    }

                </div>
            </div>
        </Layout.Container.Posts.Item>
    );
};

export default Start;