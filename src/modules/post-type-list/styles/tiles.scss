@import "src/scss/variables.scss";

#app {
    #modules-container {
        .post-type-list.tiles {
            .posts-outer-container.tiles {
                .single-post-container.tiles {
                    box-sizing: border-box;
                    // padding: 5px;

                    .tiles-inner-wrapper {
                        position: relative;
                        overflow: hidden;
                        min-height: 400px;
                        display: flex;
                        align-items: flex-end;
                        border-radius: 7px;
                        &:before {
                            content: "";
                            position: absolute;
                            background-image: linear-gradient(rgba(0, 0, 0, 0.1) 21%, rgba(0, 0, 0, 0.7));
                            top: 0;
                            left: 0;
                            right: 0;
                            bottom: 0;
                            z-index: 1;
                        }
                        img {
                            position: absolute;
                            top: 50%;
                            left: 50%;
                            transform: translate(-50%, -50%);
                            // max-width: fit-content;
                            min-width: 100%;
                            min-height: 100%;
                            object-fit: cover;
                            transition: 0.3s;
                        }
                        .tiles-content-wrapper {
                            color: #fff;
                            z-index: 1;
                            padding: 1.5rem;
                            width: 100%;
                            .label {
                                margin: 0;
                            }
                            .tiles-title {
                                margin-bottom: 1rem;
                            }
                            .post-date {
                                margin-bottom: 8px;
                            }
                        }
                    }
                    &:hover {
                        .tiles-inner-wrapper {
                            img {
                                transform: translate(-50%, -50%) scale(1.05);
                            }
                        }
                    }
                }
                @media (min-width: $breakpoint-mobile) {
                    display: flex;
                    flex-wrap: wrap;
                    .single-post-container {
                        width: 100%;
                    }
                }
                @media (min-width: $breakpoint-tablet) {
                    display: flex;
                    flex-wrap: wrap;
                    .single-post-container {
                        &:nth-child(8n-6),
                        &:nth-child(8n-5),
                        &:nth-child(8n-4),
                        &:nth-child(8n-3) {
                            width: 25%;
                        }
                        &:nth-child(8n-2),
                        &:nth-child(8n-1),
                        &:nth-child(8n-7),
                        &:nth-child(8n) {
                            width: 50%;
                        }
                    }
                }
            }
        }
    }
}
