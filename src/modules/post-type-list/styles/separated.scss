@import 'src/scss/variables.scss';

#app {
    #modules-container {
        .post-type-list.separated {
            .posts-outer-container.separated {

                .slick-slider {
                    .slick-list {
                        .slick-track {
                            .slick-slide {
                                position: relative;

                                &::after {
                                    content: '';
                                    position: absolute;
                                    width: calc(100% - 10px);
                                    height: 60%;
                                    border-right: 1px solid;
                                    top: 50%;
                                    left: 0px;
                                    bottom: 0px;
                                    right: 0px;
                                    transform: translateY(-50%);
                                }
                            }


                            // div.slick-active:not(:last-child)::after {
                            //     content: '';
                            //     display: none;
                            //     border-right: 0px;
                            // }
                        }
                    }
                }
                




                .single-post-container.separated {
                            
                    position: relative;
                    border: none;

                    .cards-image-content-inner-container {
                        @media (max-width: 767px) {
                            padding: 0;
                        }
                        .label {
                            font-size: 0.75rem;
                            margin: 1.5rem 0 0.75rem;
                            border-bottom: 2px solid;
                            display: inline-block;
                        }
                        .date {
                            margin-bottom: .75rem;
                        }
                        .link-container{
                            font-size: 1rem;
                            text-decoration: underline;
                            font-weight: 700;
                            color: inherit;
                        }

                    }

                    // LINES.
                    &.two-column:nth-child(odd) {
                        @media (min-width: 768px) {
                            &:after {
                                content: "";
                                position: absolute;
                                height: 60%;
                                border-right: 1px solid;
                                top: 50%;
                                left: 100%;
                                transform: translateY(-50%);
                            }
                            &:last-of-type:after {
                                display: none;
                            }
                        }
                    }
                    &.three-column:not(:nth-child(3n+3)) {
                        @media (min-width: 768px) {
                            &:after {
                                content: "";
                                position: absolute;
                                height: 60%;
                                border-right: 1px solid;
                                top: 50%;
                                left: 100%;
                                transform: translateY(-50%);
                            }
                            &:last-of-type:after {
                                display: none;
                            }
                        }
                    }
                        
                    &.four-column:not(:nth-child(4n+4)) {
                        @media (min-width: 768px) {
                            &:after {
                                content: "";
                                position: absolute;
                                height: 60%;
                                border-right: 1px solid;
                                top: 50%;
                                left: 100%;
                                transform: translateY(-50%);
                            }
                            &:last-of-type:after {
                                display: none;
                            }
                        }
                    }

        
        




                    @media (min-width: 768px) {
                        .cards-post-items-wrapper {
                            flex-wrap: wrap;
                            .single-post-container.separated {
                                width: 50%;
                                max-width: none;
                                margin: 0;
                                &:nth-child(even)::after {
                                    display: none;
                                }
                            }
                        }
                        &.one-column {
                            .cards-post-items-wrapper {
                                .single-post-container.separated {
                                    width: 100%;
                                    &::after {
                                        display: none;
                                    }
                                }
                            }
                        }
                    }
                    &.three-column {
                        @media (min-width: 1024px) {
                            .cards-post-items-wrapper {
                                .single-post-container.separated {
                                    width: 33%;
                                    margin: 0;
                                    &:nth-child(even):after {
                                        display: initial;
                                    }
                                    &:nth-child(3n+3):after {
                                        display: none;
                                    }
                                }
                            }
                        }
                    }
                    &.carousel {
                        .cards-post-items-wrapper .single-post-container.separated {
                            width: auto;
                            .cards-image-content-inner-container {
                                border-right: 1px solid;
                            }
                        }
                    }
                }
            }
        }
    }
}

