import React, { Fragment } from 'react';
import { decode } from 'html-entities';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faLock } from '@fortawesome/free-solid-svg-icons'
// HELPERS & PARTIALS.
import Button from 'src/partials/button';
import Clicker from 'src/helpers/clicker';
import Imaging from 'src/helpers/imaging';
import HtmlParser from 'src/helpers/html-parser'
// LAYOUT & CAROUSEL.
import Layout from 'src/modules/post-type-list/layout';

// SCSS.
import 'src/modules/post-type-list/styles/default.scss';

const Start = ({ post, index, data, settings }) => {
    let imgData = post?.feat_image_field;

    if (!post?.feat_image_field?.id) {
        imgData = {
            url: post?.feat_image_field,
            alt: post?.parsedTitle
        }
    }

    return (
        <Layout.Container.Posts.Item data={data} settings={settings}>

            {(post?.feat_image) && <div class='default-image-wrapper' dangerouslySetInnerHTML={{ __html: post?.feat_image }} />}

            {(post?.feat_image_field && !post?.feat_image) &&
                <div class='default-image-wrapper'>
                    <Imaging data={imgData} />
                </div>
            }

            <div class='default-content-wrapper'>
                <h3 class={`${data?.post_title_label_alignment}`}>{post?.parsedTitle}{post.locked && <FontAwesomeIcon icon={faLock} className="locked" />}</h3>
                {post?.related_store &&
                    <p>
                        <Clicker class='related-store' type='anchor' url={post?.related_store?.url} style={{ color: settings?.design?.colors?.primary_color }}>{decode(post?.related_store?.title)}</Clicker>
                    </p>
                }
                <p>
                    {data?.show_excerpt &&
                        <div class='cards-exerpt'>
                            <HtmlParser html={decode(post?.exerpt)} />
                        </div>
                    }
                    {!data?.hide_link_to_individual_posts &&
                        <div class={`link-container ${data?.post_title_label_alignment}`}>
                            <Clicker type='anchor' url={post?.url} target={post?.external_link} ariaLabel={`Read More about ${post?.parsedTitle}`} style={{ color: settings?.design?.colors?.primary_color }} >
                                {post?.link_text_override ? post?.link_text_override : data?.post_link_text?.length < 1 ? 'Read More' : decode(data?.post_link_text)}<i>{'>'}</i>
                            </Clicker>
                        </div>
                    }
                </p>
            </div>

        </Layout.Container.Posts.Item>
    );
};

const PostsButton = ({ post, index, data, settings }) => {
    return (
        <div class={`default-button-container ${data?.display_options === 'carousel' ? 'grid-container' : ''} ${data?.title_alignment}`}>
            <Button title={data?.button?.title} url={data?.button?.url} target={data?.button?.target} icon={data?.button_icon} tone={data?.background_value} type={data?.button_style} />
        </div>
    );
};

export default Start;