@import 'src/scss/variables.scss';

 #app {
    #modules-container {
        .post-type-list.cards {
            .posts-outer-container.cards {
                .single-post-container.cards {

                    &:not(.hide-border) {
                        border: 1px solid rgba(0,0,0,0.5);
                        border-radius: 3px;
                    }

                    &.hide-outline {
                        border: none;
                    }

                    .cards-image-content-inner-container {
                        display: flex;
                        flex-direction: column;
                        gap: 1rem;
                        padding:10px;
                        box-sizing: border-box;

                        @media screen and (max-width: $breakpoint-tablet) {
                            flex-direction: column;
                        }

                        &.one-column {
                            flex-direction: row;
                        }

                        .cards-image-wrapper {
                            // width: 40%;
                            // max-width: 184px;
                            // min-width: 184px;
                            text-align: center;
                        }
                        .cards-content-wrapper {
                            // width: 60%;
                            .cards-exerpt {
                                line-height: 26px;
                            }
                            .cards-link,
                            .related-store {
                                font-size: 1rem;
                                text-decoration: underline;
                                font-weight: 700;
                                color: inherit;
                                display: block;
                                margin-top: 15px;
                            }
                            .label-container {
                                width: 100%;
                            }
                        }
                    }

                    &.carousel {
                        // padding: 3rem 0 5rem;
                        .cards-content-container {
                            max-width: none;
                            .cards-listing-title {
                                &.grid-container {
                                    width: 100%;
                                    box-sizing: border-box;
                                }
                                h2 {
                                    // max-width: 1200px;
                                    margin: 0 auto 1.5rem;
                                    // padding: 0 1rem;
                                }
                            }
                            .cards-button-container {
                                margin: 4rem auto 1rem;
                                width: 100%;
                                box-sizing: border-box;
                                // max-width: 1200px;
                            }
                            .single-post-container {
                                width: auto;
                            }
                            .slick-dots {
                                bottom: -50px;
                                li {
                                    display: inline-flex;
                                    justify-content: center;
                                    button {
                                        border: 2px solid currentColor;
                                        border-radius: 100%;
                                        margin: 0;
                                        width: 10px;
                                        height: 10px;
                                        &:before {
                                            color: transparent;
                                            line-height: 18px;
                                        }
                                    }
                                    &.slick-active {
                                        button:before {
                                            opacity: 1;
                                            font-size: 1.5rem;
                                            color: inherit;
                                        }
                                    }
                                }
                            }
                            .dashes {
                                .slick-dots li {
                                    width: 45px;
                                    border-radius: 0;
                                    button {
                                        width: 45px;
                                        border-radius: 0;
                                        height: 7px;
                                        padding: 0;
                                        &:before {
                                            display: none;
                                        }
                                    }
                                    &.slick-active button {
                                        background: currentColor;
                                    }
                                }
                            }
                            .arrows,
                            .arrows-boxed {
                                .custom-arrow {
                                    font-size: 2rem;
                                    &.boxed {
                                        background: rgba(0, 0, 0, 0.3);
                                        padding: 1rem 0.5rem;
                                    }
                                }
                                .slick-arrow {
                                    z-index: 1;
                                    &:before {
                                        display: none;
                                    }
                                    &.slick-prev {
                                        left: 1rem;
                                    }
                                    &.slick-next {
                                        right: 1rem;
                                    }
                                }
                            }
                            .arrows-boxed {
                                .slick-arrow.slick-prev {
                                    left: 0.5rem;
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}