@import 'src/scss/variables.scss';

#app {
    #modules-container {
        .post-type-list.featured_image_text {
            .posts-outer-container.featured_image_text {

                .single-post-container.featured_image_text {
                    // margin:7px;
                    
                    border: none;
                    max-width: none;

                    position: relative;
                    overflow: hidden;
                    min-height: 420px;
                    display: flex;
                    align-items: flex-end;
                    &:after {
                        content: "";
                        position: absolute;
                        top: 0;
                        left: 0;
                        right: 0;
                        bottom: 0;
                        background: rgba(0, 0, 0, 0.4);
                    }

                    &.carousel {
                        // min-height: 330px;
                    }

                    .background {
                        position: absolute;
                        height: 100%;
                        width: 100%;
                        object-fit: cover;
                        top: 0;

                        div {
                            height: 100%;
                            width: 100%;
                        }

                        img {
                            height: 100%;
                            width: 100%;
                            object-fit: cover;
                        }
                    }

                    .content-wrapper {
                        position: absolute;
                        z-index: +1;
                        bottom: 0px;
                        left: 0px;
                        
                        padding: 1.5rem;
                        color: #fff;
                        width: 100%;

                        font-weight: 700;

                        .label {
                            font-size: 0.75rem;
                            margin: 1.5rem 0 0.5rem;
                            display: inline-block;
                        }
                        .post-title {
                            margin-bottom: .5rem;
                            font-size: 1.5rem;
                            line-height: 2rem;
                            // font-weight: 400;
                        }
                        .post-link {
                            text-decoration: none;
                        }
                    }
                }
                @media (min-width: 768px) {
                    &.two-column,
                    &.three-column {
                        .single-post-container.featured_image_text {
                            max-width: 48%;
                            // max-width: 50%;
                        }
                    }
                }
                @media (min-width: 1200px) {
                    &.three-column .single-post-container.featured_image_text {
                        max-width: 32%;
                        // max-width: 33.33%;
                    }
                }       
            }
        }
    }
}
