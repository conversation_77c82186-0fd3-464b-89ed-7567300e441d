import React, { useMemo } from 'react';
import { decode } from 'html-entities';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faLock } from '@fortawesome/free-solid-svg-icons'
// HELPERS & PARTIALS
const Clicker = React.lazy(() => import('src/helpers/clicker'));
const Imaging = React.lazy(() => import('src/helpers/imaging'));
import { Background } from "src/helpers/theme";
import { Coloring } from "src/helpers";

// LAYOUT & CAROUSEL.
import Layout from 'src/modules/post-type-list/layout';

// SCSS.
import 'src/modules/post-type-list/styles/hover.scss';

const Start = ({ post, index, data, settings }) => {
    var postItemStyle = {
        borderRadius: (settings?.mvk_theme_config?.other?.enable_border_radius) ? `${settings?.mvk_theme_config?.other?.border_radius_size}px` : "0px",
        color: data?.post_background_value === 'dark' ? '#fff' : settings?.design?.colors?.body_copy_color
    }
    let imgData = post?.feat_image_field;

    if (!post?.feat_image_field?.id) {
        imgData = {
            url: post?.feat_image_field,
            alt: post?.parsedTitle
        }
    }
    let hoverStyle = data?.hover_overlay_color === 'solid-fill-gradient' ? {
        backgroundImage: Background('light', data?.hover_overlay_color),
        color: data?.post_background_value === 'dark' ? '#fff' : settings?.design?.colors?.body_copy_color
    } : {
        backgroundColor: Background('light', data?.hover_overlay_color),
        color: data?.post_background_value === 'dark' ? '#fff' : settings?.design?.colors?.body_copy_color
    }

    return (
        <Layout.Container.Posts.Item data={data} settings={settings} style={postItemStyle}>
            <div class="background">
                {post?.feat_image && <div dangerouslySetInnerHTML={{ __html: post?.feat_image }} />}
                {post?.feat_image_field && !post?.feat_image && <Imaging data={imgData} />}
            </div>

            {!data?.hide_link_to_individual_posts &&
                <Clicker class='cards-link' type='anchor' target={post?.external_link} url={post?.url} ariaLabel={`Read More about ${post?.parsedTitle}`}>
                    <PostContent data={data} settings={settings} post={post} postItemStyle={postItemStyle} hoverStyle={hoverStyle} />
                </Clicker>
            }
            {data?.hide_link_to_individual_posts &&
                <PostContent data={data} settings={settings} post={post} postItemStyle={postItemStyle} hoverStyle={hoverStyle} />
            }
        </Layout.Container.Posts.Item>
    );
};

const PostContent = ({ data, settings, post, postItemStyle, hoverStyle }) => {
    const color = data?.post_background_value === 'dark' ? '#fff' : Coloring('body_copy_color', settings);

    return (
        <div class='hover-container' style={postItemStyle}>
            <div class='cards-content-wrapper'>
                {data?.default_text === 'post-title' && <h3 class={`cards-title ${data?.post_title_label_alignment}`}>{decode(post?.parsedTitle)}</h3>}
                {data?.default_text === 'post-excerpt' && <p class={`cards-exerpt ${data?.post_title_label_alignment}`} dangerouslySetInnerHTML={{ __html: decode(post?.exerpt) }} />}
                {!data?.hide_link_to_individual_posts && data?.post_link_text && <div class={`post-link ${data?.post_title_label_alignment}`}>{decode(data?.post_link_text)}<i>{'>'}</i></div>}
            </div>
            <div class='hover-wrapper' style={hoverStyle}>
                <div className="inner-wrapper">
                    {data?.hover_text == 'post-title' && <h3 class={`cards-title ${data?.post_title_label_alignment}`}>{decode(post?.parsedTitle)}{post.locked && <FontAwesomeIcon icon={faLock} className="locked" />}</h3>}
                    {(data?.hover_text == 'post-excerpt' && post?.exerpt) && <p class={`cards-exerpt ${data?.post_title_label_alignment}`} dangerouslySetInnerHTML={{ __html: decode(post?.exerpt) }} />}
                    {!data?.hide_link_to_individual_posts && (data?.post_link_text || post?.link_text_override) && <div class={`post-link ${data?.post_title_label_alignment}`} style={{ color:color }}>{decode(post?.link_text_override ? post?.link_text_override : data?.post_link_text)}<i>{'>'}</i></div>}
                </div>
            </div>
        </div>
    );
};

export default Start;