@import 'src/scss/variables.scss';

 #app {
    #modules-container {
        .post-type-list.default {
            .posts-outer-container.default {
                .single-post-container.default {
                    display: flex;
                    flex-flow: row nowrap;
                    flex-direction: row;
                    gap: 1rem;

                    &.one-column {
                        border-bottom: 1px solid rgba(106, 98, 97, 0.5);

                        @media screen and (min-width: 768px) {
                            justify-content: flex-start;
                            border-bottom: none;
                            margin-bottom: unset;
                        }

                        @media screen and (min-width: 992px) {
                            max-width: 96%;
                            min-width: 96%;
                            margin-bottom: 20px;
                        }

                        .default-image-wrapper {
                            text-align: left;

                            img {
                                height: auto;
                                max-width: 100%;
                            }

                            @media screen and (max-width: 500px) {
                                text-align: center;
                            }

                            @media screen and (min-width: 768px) {
                                text-align: unset;
                                width: 40%;
                                max-width: 400px;
                            }
                        }

                        .default-content-wrapper {
                            h6 {
                                font-size: 1.375rem;
                                line-height: 22px;
                                font-weight: bold;
                            }

                            p {
                                a {
                                    text-decoration: underline;
                                    padding-left: 8px;
                                }
                            }

                            @media screen and (min-width: 768px) {
                                max-width: 70%;
                                width: 70%;
                            }

                            @media screen and (min-width: 1200px) {
                                h6 {
                                    font-size: 1.625rem;
                                    line-height: 30px;
                                }

                                p {
                                    font-size: 0.875rem;
                                    line-height: 20px;

                                    a {
                                        font-size: inherit;
                                    }
                                }
                            }

                            @media screen and (min-width: 1600px) {
                                p {
                                    font-size: 1.125rem;
                                    line-height: 28px;

                                    a {
                                        font-size: inherit;
                                    }
                                }
                            }
                        }

                        .default-button-container {
                            display: flex;
                            justify-content: center;
                        }
                    }

                    &.two-column {
                        border-bottom: 1px solid rgba(106, 98, 97, 0.5);

                        @media screen and (min-width: 768px) {
                            justify-content: space-between;
                            align-items: flex-start;
                        }

                        @media screen and (min-width: 768px) {
                            justify-content: flex-start;
                            border-bottom: none;
                        }

                        .default-image-wrapper {
                            text-align: left;

                            img {
                                height: auto;
                                max-width: 100%;
                            }

                            @media screen and (max-width: 500px) {
                                text-align: center;
                            }

                            @media screen and (min-width: 768px) {
                                text-align: unset;
                                width: 40%;
                                max-width: 400px;
                            }
                        }

                        .default-content-wrapper {
                            h6 {
                                font-size: 1.375rem;
                                line-height: 22px;
                                font-weight: bold;
                            }

                            p {
                                a {
                                    text-decoration: underline;
                                    padding-left: 8px;
                                    i {
                                    }
                                }
                            }

                            @media screen and (min-width: 768px) {
                                max-width: 70%;
                                width: 70%;
                            }

                            @media screen and (min-width: 1200px) {
                                h6 {
                                    font-size: 1.625rem;
                                    line-height: 30px;
                                }

                                p {
                                    font-size: 0.875rem;
                                    line-height: 20px;

                                    a {
                                        font-size: inherit;
                                    }
                                }
                            }

                            @media screen and (min-width: 1600px) {
                                p {
                                    font-size: 1.125rem;
                                    line-height: 28px;

                                    a {
                                        font-size: inherit;

                                        i {
                                        }
                                    }
                                }
                            }
                        }

                        .default-button-container {
                            display: flex;
                            justify-content: center;
                        }
                    }

                    &.three-column {
                        border-bottom: 1px solid rgba(106, 98, 97, 0.5);

                        @media screen and (min-width: 768px) {
                            justify-content: flex-start;
                            align-items: flex-start;
                        }
                        
                        @media screen and (min-width: 768px) {
                            justify-content: flex-start;
                            border-bottom: none;
                        }

                        .default-image-wrapper {
                            text-align: left;

                            img {
                                height: auto;
                                max-width: 100%;
                            }

                            @media screen and (max-width: 500px) {
                                text-align: center;
                            }

                            @media screen and (min-width: 768px) {
                                text-align: unset;
                                width: 40%;
                                max-width: 400px;

                            }
                        }

                        .default-content-wrapper {
                            h6 {
                                font-size: 1.375rem;
                                line-height: 22px;
                                font-weight: bold;
                            }

                            p {
                                a {
                                    text-decoration: underline;
                                    padding-left: 8px;
                                    i {
                                    }
                                }
                            }

                            @media screen and (min-width: 768px) {
                                max-width: 70%;
                                width: 70%;
                            }

                            @media screen and (min-width: 1200px) {
                                h6 {
                                    font-size: 1.625rem;
                                    line-height: 30px;
                                }

                                p {
                                    font-size: 0.875rem;
                                    line-height: 20px;

                                    a {
                                        font-size: inherit;
                                    }
                                }
                            }

                            @media screen and (min-width: 1600px) {
                                p {
                                    font-size: 1.125rem;
                                    line-height: 28px;

                                    a {
                                        font-size: inherit;

                                        i {
                                        }
                                    }
                                }
                            }
                        }

                        .default-button-container {
                            display: flex;
                            justify-content: center;
                        }
                    }

                    &.four-column {
                        flex-flow: column wrap;
                        border-bottom: 1px solid rgba(106, 98, 97, 0.5);

                        @media screen and (min-width: 768px) {
                            flex-flow: row wrap;
                            justify-content: flex-start;
                            align-items: flex-start;
                        }
                        
                        @media screen and (min-width: 768px) {
                            flex-flow: row nowrap;
                            justify-content: flex-start;
                            border-bottom: none;
                            margin-bottom: unset;
                        }

                        @media screen and (min-width: 1200px) {
                            flex-flow: row nowrap;
                        }
                        
                        .default-image-wrapper {
                            text-align: left;

                            img {
                                height: auto;
                                max-width: 100%;
                            }

                            @media screen and (max-width: 500px) {
                                text-align: center;
                            }

                            @media screen and (min-width: 768px) {
                                text-align: unset;
                                width: 40%;
                                max-width: 400px;
                            }
                        }

                        .default-content-wrapper {
                            h6 {
                                font-size: 1.375rem;
                                line-height: 22px;
                                font-weight: bold;
                            }

                            p {
                                a {
                                    text-decoration: underline;
                                    padding-left: 8px;
                                    i {
                                    }
                                }
                            }

                            @media screen and (min-width: 768px) {
                                max-width: 70%;
                                width: 70%;
                            }

                            @media screen and (min-width: 1200px) {
                                h6 {
                                    font-size: 1.625rem;
                                    line-height: 30px;
                                }

                                p {
                                    font-size: 0.875rem;
                                    line-height: 20px;

                                    a {
                                        font-size: inherit;
                                    }
                                }
                            }

                            @media screen and (min-width: 1600px) {
                                p {
                                    font-size: 1.125rem;
                                    line-height: 28px;

                                    a {
                                        font-size: inherit;

                                        i {
                                        }
                                    }
                                }
                            }
                        }

                        .default-button-container {
                            display: flex;
                            justify-content: center;
                        }
                    }

                    &.carousel {
                        padding: 3rem 0 5rem;
                        flex-flow: column wrap;

                        .default-content-container {
                            max-width: none;

                            .default-listing-title {
                                &.grid-container {
                                    width: 100%;
                                    box-sizing: border-box;
                                }
                                h2 {
                                    margin: 0 auto 1.5rem;
                                }
                            }

                            .default-button-container {
                                margin: 4rem auto 1rem;
                                width: 100%;
                                box-sizing: border-box;
                            }

                            .single-post-container {
                                width: auto;
                                margin: 0 0.5rem;
                            }

                            .slick-dots {
                                bottom: -50px;
                                li {
                                    display: inline-flex;
                                    justify-content: center;
                                    button {
                                        border: 2px solid currentColor;
                                        border-radius: 100%;
                                        margin: 0;
                                        width: 10px;
                                        height: 10px;
                                        &:before {
                                            color: transparent;
                                            line-height: 18px;
                                        }
                                    }
                                    &.slick-active {
                                        button:before {
                                            opacity: 1;
                                            font-size: 1.5rem;
                                            color: inherit;
                                        }
                                    }
                                }
                            }

                            .dashes {
                                .slick-dots li {
                                    width: 45px;
                                    border-radius: 0;
                                    button {
                                        width: 45px;
                                        border-radius: 0;
                                        height: 7px;
                                        padding: 0;
                                        &:before {
                                            display: none;
                                        }
                                    }
                                    &.slick-active button {
                                        background: currentColor;
                                    }
                                }
                            }

                            .arrows,
                            .arrows-boxed {
                                .custom-arrow {
                                    font-size: 2rem;
                                    &.boxed {
                                        background: rgba(0, 0, 0, 0.3);
                                        padding: 1rem 0.5rem;
                                    }
                                }
                                .slick-prev {
                                    left: 1rem;
                                }
                                .slick-next {
                                    right: 1rem;
                                }
                            }
                            .arrows-boxed {
                                .slick-arrow.slick-prev {
                                    left: 0.5rem;
                                }
                            }
                        }
                    }

                    @media screen and (max-width: $breakpoint-mobile) {
                        flex-flow: column;
                        flex-direction: column;
                    }
                }
            }
        }
    }
}