import React, { useEffect, useState, Fragment } from 'react';
import { decode } from 'html-entities';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faLock } from '@fortawesome/free-solid-svg-icons'
// HELPERS & PARTIALS.
const Button = React.lazy(() => import('src/partials/button'));
const Clicker = React.lazy(() => import('src/helpers/clicker'));
import { Coloring } from "src/helpers";

// LAYOUT & CAROUSEL.
import Layout from 'src/modules/post-type-list/layout';

// SCSS.
import 'src/modules/post-type-list/styles/half-half.scss';

const Start = ({ post, data, settings }) => {
    return (
        <Layout.Container.Posts.Item data={data} settings={settings}>
            <div class="post-media">
                <div class="background" dangerouslySetInnerHTML={{ __html:post.feat_image }}/>
                <div class="overlay" />
            </div>
            <div class="post-content">
                <Clicker type="anchor" url={post.url}>
                    {data?.show_post_date && 
                        <p class='date'>{post?.post_date}</p>
                    }
                    <div class="title">{decode(post?.parsedTitle)}{post.locked && <FontAwesomeIcon icon={faLock} className="locked" />}</div>
                </Clicker>
            </div>
        </Layout.Container.Posts.Item>
    );
};

export default Start;