import React, { useEffect, useState } from 'react';
import { decode } from 'html-entities';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faLock } from '@fortawesome/free-solid-svg-icons'
// HELPERS & PARTIALS.
const Clicker = React.lazy(() => import('src/helpers/clicker'));
import { ColorSplash } from 'src/helpers/theme';

// LAYOUT & CAROUSEL.
import Layout from 'src/modules/post-type-list/layout';

// SCSS.
import 'src/modules/post-type-list/styles/separated.scss';

const Start = ({ post, index, data, settings }) => {
    const splashColor = {
        borderColor: data?.background_value === 'dark' ? '#fff' : ColorSplash(settings?.mvk_theme_config?.other?.cs_color)
    };

    var strippedString = post?.exerpt?.replace(/<[^>]*>?/gm, '');
    var splitString = strippedString?.split(' ');
    var excerptString;

    if (splitString?.length >= 25) {
        var trimmedArray = [];
        for (let i = 0; i < 26; i++) {
            trimmedArray?.push(splitString[i]);
        }
        var joinedString = trimmedArray?.join(' ');
        excerptString = joinedString?.substring(0, joinedString?.lastIndexOf(' ')) + '...';
    } else {
        excerptString = post?.exerpt?.replace(/<[^>]*>?/gm, '');
    }
    const post_type_label = data?.override_post_type_label ? data?.override_post_type_label : data?.post_list_post_type?.toUpperCase();
    const category_label = post?.category ?? '';

    return (
        <Layout.Container.Posts.Item data={data} settings={settings}>
            <div class='cards-image-content-inner-container'>
                <div class='cards-content-wrapper'>
                    {(!data?.hide_post_type_label || data?.show_category) &&
                        <div class={`label-container ${data?.post_title_label_alignment}`}>
                            <p className="label" style={splashColor}>{!data?.hide_post_type_label && post_type_label}{(!data?.hide_post_type_label && data?.show_category && category_label) && ': '}{data?.show_category && category_label}</p>
                        </div>
                    }
                    {data?.show_post_date &&
                        <p class='date'>{post?.post_date}</p>
                    }
                    <h3 class={`cards-title ${data?.post_title_label_alignment}`}>{decode(post?.parsedTitle)}{post.locked && <FontAwesomeIcon icon={faLock} className="locked" />}</h3>
                    {post?.related_store &&
                        <p>
                            <Clicker class='related-store' type='anchor' url={post?.related_store?.url}>{decode(post?.related_store?.title)}</Clicker>
                        </p>
                    }
                    {data?.show_excerpt &&
                        <p class={`cards-exerpt ${data?.post_title_label_alignment}`} dangerouslySetInnerHTML={{ __html: decode(excerptString) }} />
                    }
                    {!data?.hide_link_to_individual_posts &&
                        <div class={`link-container ${data?.post_title_label_alignment}`}>
                            <Clicker class='body-copy-txt' type='anchor' target={post?.external_link} url={post?.url} ariaLabel={`Read More about ${post?.parsedTitle}`}>
                                {post?.link_text_override ? post?.link_text_override : data?.post_link_text?.length < 1 ? 'Read More' : decode(data?.post_link_text)}<i>{'>'}</i>
                            </Clicker>
                        </div>
                    }
                </div>
            </div>
        </Layout.Container.Posts.Item>
    );
};

export default Start;
