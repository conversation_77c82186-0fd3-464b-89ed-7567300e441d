import React from 'react';
import { decode } from 'html-entities';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faLock } from '@fortawesome/free-solid-svg-icons'
// HELPERS & PARTIALS.
const Clicker = React.lazy(() => import('src/helpers/clicker'));

// LAYOUT & CAROUSEL.
import Layout from 'src/modules/post-type-list/layout';

// SCSS.
import 'src/modules/post-type-list/styles/tiles.scss';

const Start = ({ post, index, data, settings }) => {
    var strippedString = post?.exerpt?.replace(/<[^>]*>?/gm, '');
    var splitString = strippedString?.split(' ');
    var excerptString;

    if (splitString?.length >= 25) {
        var trimmedArray = [];
        for (let i = 0; i < 26; i++) {
            trimmedArray?.push(splitString[i]);
        }
        var joinedString = trimmedArray?.join(' ');
        excerptString = joinedString?.substring(0, joinedString?.lastIndexOf(' ')) + '...';
    } else {
        excerptString = post?.exerpt?.replace(/<[^>]*>?/gm, '');
    }
    const post_type_label = data?.override_post_type_label ? data?.override_post_type_label : data?.post_list_post_type?.toUpperCase();
    const category_label = post?.category ?? '';

    return (
        <Layout.Container.Posts.Item data={data} settings={settings}>
            <Clicker type='anchor' url={post?.url} target={post?.external_link} key={index}>
                <div class='tiles-inner-wrapper'>
                    {post?.feat_image && (
                        <div class='tiles-image-wrapper' dangerouslySetInnerHTML={{ __html: post?.feat_image }} />
                    )}
                    <div class='tiles-content-wrapper'>
                        {(!data?.hide_post_type_label || data?.show_category) &&
                            <div class={`label-container ${data?.post_title_label_alignment}`}>
                                <p className="label">{!data?.hide_post_type_label && post_type_label}{(!data?.hide_post_type_label && data?.show_category && category_label) && ': '}{data?.show_category && category_label}</p>
                            </div>
                        }
                        {data?.show_post_date &&
                            <p class='post-date'>{post?.post_date}</p>
                        }
                        <h3 class={`tiles-title ${data?.post_title_label_alignment}`}>{decode(post?.parsedTitle)}{post.locked && <FontAwesomeIcon icon={faLock} className="locked" />}</h3>
                        {data?.show_excerpt &&
                            <p class={`tiles-exerpt ${data?.post_title_label_alignment}`} dangerouslySetInnerHTML={{ __html: decode(excerptString) }} />
                        }
                        {(!data?.hide_link_to_individual_posts && data?.post_link_text?.length > 0) &&
                            <div class={`post-link ${data?.post_title_label_alignment}`}>{decode(post?.link_text_override ? post?.link_text_override : data?.post_link_text)}<i>{'>'}</i></div>
                        }
                    </div>
                </div>
            </Clicker>
        </Layout.Container.Posts.Item>
    );
};

export default Start;