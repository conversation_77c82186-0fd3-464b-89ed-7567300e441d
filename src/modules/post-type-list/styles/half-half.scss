@import 'src/scss/variables.scss';

#app {
    #modules-container {
        .post-type-list.half_half {
            .posts-outer-container.half_half {
                @media screen and (max-width: $breakpoint-mobile) {
                    padding-bottom: 300px;
                }

                // SLICK SLIDER.
                .slick-slider {
                    .slick-list {
                        padding: 0px;
                        .slick-track {
                            gap: 0px;
                            .slick-slide {
                                margin: 0px;
                                padding: 0px; 
                            }
                        }
                    }
                }



                .single-post-container.carousel.half_half {
                    position: relative;
                    height: 300px;
                    max-height: 300px;
                    width: 100%;

                    @media screen and (max-width: $breakpoint-mobile) {
                        height: 600px;
                        max-height: 600px;
                    }

                    .post-media {
                        position: relative;
                        height: 300px;
                        max-height: 300px;

                        .background {
                            position: relative;
                            width: 100%;
                            height: 300px;
                            max-height: 300px;
                            overflow: hidden;

                            @media screen and (max-width: $breakpoint-mobile) {
                                position: relative;
                            }
                            img {
                                width: 100%;
                                height: 100%;
                                object-fit: cover;
                            }
                        }
                        .overlay {
                            position: absolute;
                            top: 0px;
                            bottom: 0px;
                            left: 0px;
                            right: 0px;
                            background: linear-gradient(0deg, rgba(0,0,0,0.9) 0%, rgba(0,0,0,0) 100%);
                        }
                    }

                    .post-content {
                        position: absolute;
                        bottom: 0px;
                        left: 0px;
                        right: 0px;
                        box-sizing: border-box;
                        padding: 20px 30px;

                        @media screen and (max-width: $breakpoint-mobile) {
                            // position: relative;
                            color: black;
                        }

                        a { 
                            color: white; 
                        
                            @media screen and (max-width: $breakpoint-mobile) {
                                // position: relative;
                                color: black;
                            }
                        }
                        .date {
                            font-size: 0.875rem;
                        }
                        .title {
                            font-size: 1.438rem;
                        }
                    }
                }
            }
        }
    }
}