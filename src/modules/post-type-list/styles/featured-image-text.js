import React from 'react';
import { decode } from 'html-entities';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faLock } from '@fortawesome/free-solid-svg-icons'
// PARTIALS & HELPERS.
const Clicker = React.lazy(() => import('src/helpers/clicker'));
const Imaging = React.lazy(() => import('src/helpers/imaging'));
import { Coloring } from "src/helpers";

// LAYOUT & CAROUSEL.
import Layout from 'src/modules/post-type-list/layout';

// SCSS.
import 'src/modules/post-type-list/styles/featured-image-text.scss';

const Start = ({ post, index, data, settings }) => {
    var postItemStyle = {
        borderRadius: (settings?.mvk_theme_config?.other?.enable_border_radius) ? `${settings?.mvk_theme_config?.other?.border_radius_size}px` : "0px",
    }
    let imgData = post?.feat_image_field;

    if (!post?.feat_image_field?.id) {
        imgData = {
            url: post?.feat_image_field,
            alt: post?.parsedTitle
        }
    }

    var strippedString = post?.exerpt?.replace(/<[^>]*>?/gm, '');
    var splitString = strippedString?.split(' ');
    var excerptString;

    if (splitString?.length >= 25) {
        var trimmedArray = [];
        for (let i = 0; i < 26; i++) {
            trimmedArray?.push(splitString[i]);
        }

        var joinedString = trimmedArray?.join(' ');
        excerptString = joinedString?.substring(0, joinedString?.lastIndexOf(' ')) + '...';
    } else {
        excerptString = post?.exerpt?.replace(/<[^>]*>?/gm, '');
    }

    const post_type_label = data?.override_post_type_label ? data?.override_post_type_label : data?.post_list_post_type?.toUpperCase();
    const category_label = post?.category ?? '';

    return (
        <Layout.Container.Posts.Item data={data} settings={settings} style={postItemStyle}>
            <div>
                <div class="background">
                    {(post?.feat_image) && <div dangerouslySetInnerHTML={{ __html: post?.feat_image }} />}
                    {(post?.feat_image_field && !post?.feat_image) && <Imaging data={imgData} />}
                </div>

                <div class="content-wrapper white">
                    {(!data?.hide_post_type_label || data?.show_category) &&
                        <div class={`label-container ${data?.post_title_label_alignment}`}>
                            <p className="label white">{!data?.hide_post_type_label && post_type_label}{(!data?.hide_post_type_label && data?.show_category && category_label) && ': '}{data?.show_category && category_label}</p>
                        </div>
                    }
                    <h3 class={`post-title ${data?.post_title_label_alignment} white`}>{decode(post?.parsedTitle)}{post.locked && <FontAwesomeIcon icon={faLock} className="locked" />}</h3>

                    {data?.show_excerpt && <p class={`post-exerpt ${data?.post_title_label_alignment} white`} dangerouslySetInnerHTML={{ __html: decode(excerptString) }} />}

                    {!data?.hide_link_to_individual_posts &&
                        <div class={`link-container ${data?.post_title_label_alignment}`}>
                            <Clicker class='cards-link white' target={post.external_link} type='anchor' url={post?.url} ariaLabel={`Read More about ${post?.parsedTitle}`}>
                                {post?.link_text_override ? post?.link_text_override : data?.post_link_text?.length < 1 ? 'Read More' : decode(data?.post_link_text)}<i>{'>'}</i>
                            </Clicker>
                        </div>
                    }

                </div>
            </div>
        </Layout.Container.Posts.Item>
    );
};


export default Start;