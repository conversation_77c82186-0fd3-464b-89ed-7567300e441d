import React, { useEffect, useState } from 'react';
import { decode } from 'html-entities';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faLock } from '@fortawesome/free-solid-svg-icons'
// HELPERS & PARTIALS.
const Clicker = React.lazy(() => import('src/helpers/clicker'));
import { Coloring } from "src/helpers";

// LAYOUT & CAROUSEL.
import Layout from 'src/modules/post-type-list/layout';

// SCSS.
import 'src/modules/post-type-list/styles/boxes.scss';

const Start = ({ post, index, data, settings }) => {
    const bgColor = Coloring(data?.post_background_color, settings);
    const color = data?.post_background_value === 'dark' ? '#fff' : Coloring('body_copy_color', settings);
    const borderRadius = settings?.mvk_theme_config?.other?.enable_border_radius ? settings?.mvk_theme_config?.other?.border_radius_size : 0;

    var strippedString = post?.exerpt?.replace(/<[^>]*>?/gm, '');
    var splitString = strippedString?.split(' ');
    var excerptString;

    if (splitString?.length >= 25) {
        var trimmedArray = [];
        for (let i = 0; i < 26; i++) {
            trimmedArray?.push(splitString[i]);
        }

        var joinedString = trimmedArray?.join(' ');
        excerptString = joinedString?.substring(0, joinedString?.lastIndexOf(' ')) + '...';
    } else {
        excerptString = post?.exerpt?.replace(/<[^>]*>?/gm, '');
    }
    const post_type_label = data?.override_post_type_label ? data?.override_post_type_label : data?.post_list_post_type?.toUpperCase();
    const category_label = post?.category ?? '';

    return (
        <Layout.Container.Posts.Item data={data} settings={settings} style={{ backgroundColor:bgColor, color:color, borderRadius:borderRadius }}>
            <div>
                <div class="fbox fcol gap">

                    {(!data?.hide_post_type_label || data?.show_category) &&
                        <div class={`label-container ${data?.post_title_label_alignment}`}>
                            <p className="label secondary-bg white-txt">{!data?.hide_post_type_label && post_type_label}{(!data?.hide_post_type_label && data?.show_category && category_label) && ': '}{data?.show_category && category_label}</p>
                        </div>
                    }

                    <h4 class={`post-title ${data?.post_title_label_alignment}`}>{decode(post?.parsedTitle)}{post.locked && <FontAwesomeIcon icon={faLock} className="locked" />}</h4>

                    {post?.related_store &&
                        <p>
                            <Clicker class='related-store' type='anchor' url={post?.related_store?.url}>{decode(post?.related_store?.title)}</Clicker>
                        </p>
                    }

                    {data?.show_excerpt && <p class={`post-exerpt ${data?.post_title_label_alignment}`} dangerouslySetInnerHTML={{ __html: decode(excerptString) }} />}

                    {!data?.hide_link_to_individual_posts &&
                        <div class={`link-container ${data?.post_title_label_alignment}`}>
                            <Clicker class={`post-link ${data?.post_background_value === 'dark' ? 'white-txt' : 'primary-txt'}`} target={post?.external_link} type='anchor' url={post?.url} ariaLabel={`Read More about ${post?.parsedTitle}`}>
                                {post?.link_text_override ? post?.link_text_override : data?.post_link_text?.length < 1 ? 'Read More' : decode(data?.post_link_text)}<span>{'>'}</span>
                            </Clicker>
                        </div>
                    }

                </div>
            </div>
        </Layout.Container.Posts.Item>
    );
};

export default Start;