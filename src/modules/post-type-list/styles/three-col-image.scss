@import "src/scss/variables.scss";

#app {
    #modules-container {
        .post-type-list.three_col_image {
            .posts-outer-container.three_col_image {
                .single-post-container.three_col_image {
                    color: inherit;
                    margin-bottom: 2rem;
                    
                    .three-col-image-image-content-inner-container {
                        border: none;
                        .three-col-image-link {
                            display: block;
                            width: 100%;
                            max-width: none;
                            .three-col-image-image-wrapper {
                                overflow: hidden;
                                max-height: 375px;
                                img {
                                    display: block;
                                    width: 100%;
                                    height: 100%;
                                    object-fit: cover;
                                }
                                @media (min-width: $break-medium) {
                                    height: 220px;
                                }

                                @media (min-width: $break-large) {
                                    height: 375px;
                                    img {
                                        min-height: 100%;
                                    }
                                }
                            }
                        }
                        .three-col-image-content-wrapper {
                            .three-col-image-title {
                                margin: 0 0 .5rem 0;
                            }
                            .label {
                                font-size: 0.75rem;
                                margin: 1.5rem 0 0.75rem;
                                border-bottom: 2px solid;
                                display: inline-block;
                            }
                            .date-block {
                                display: inline-flex;
                                align-items: center;
                                padding: 0.5rem 0.75rem;
                                margin: 1.5rem 0;
                                .icon-calendar {
                                    width: 1.125rem;
                                    margin-right: 0.5rem;
                                    margin-top: -2px;
                                }
                                .icon-repeat {
                                    margin-left: 0.5rem;
                                }
                            }
                        }
                        .three-col-image-link {
                            // font-weight: bold;
                            text-decoration: underline;
                        }
                    }
                }
                @media (min-width: $break-medium) {
                    .three-col-image-post-items-wrapper {
                        display: flex;
                        flex-wrap: wrap;
                        .single-post-container {
                            width: 33.33%;
                            &:nth-child(3n + 1) .three-col-image-image-content-inner-container {
                                margin: 0 0.75rem 0 0;
                            }
                            &:nth-child(3n + 2) .three-col-image-image-content-inner-container {
                                margin: 0 0.75rem;
                            }
                            &:nth-child(3n + 3) .three-col-image-image-content-inner-container {
                                margin: 0 0 0 0.75rem;
                            }
                        }
                    }
                }

                &.jobs {
                    .three-col-image-excerpt {
                        margin-bottom: .5rem;
                    }
                }
            }
        }
    }
}
