@import 'src/scss/variables.scss';
@import "~foundation-sites/scss/foundation.scss";
@include foundation-xy-grid-classes;
@include foundation-float-classes;
@include foundation-flex-classes;
@include foundation-visibility-classes;
@include foundation-prototype-classes;

#app {
    #modules-container {
        .post-type-list {
            .posts-outer-container {
                
                @media screen and (max-width: $breakpoint-mobile) {
                    flex-flow: column wrap;
                    justify-content: center;
                }

                &.tiles {
                    justify-content: flex-start;
                }

                .single-post-container {
                    &.boxes, &.default, &.hover, &.three_col_image {
                        @media screen and (max-width: $breakpoint-mobile) {
                            margin-left:auto;
                            margin-right:auto;
                            @include xy-gutters($gutters: 10 auto, $gutter-type: padding, $gutter-position: top bottom left right, $negative: false);
                        }
                        @include xy-gutters($gutters: 25, $gutter-type: margin, $gutter-position: top bottom left right, $negative: false);
                    }

                    &.featured_image_text {
                        @media screen and (max-width: $breakpoint-mobile) {
                            margin-left:auto;
                            margin-right:auto;
                            @include xy-gutters($gutters: 10 auto, $gutter-type: padding, $gutter-position: top bottom left right, $negative: false);
                        }
                        @include xy-gutters($gutters: 25, $gutter-type: margin, $gutter-position: right, $negative: false);
                    }

                    &.cards {
                        @media screen and (max-width: $breakpoint-mobile) {
                            margin-left:auto;
                            margin-right:auto;
                            @include xy-gutters($gutters: 10 auto, $gutter-type: padding, $gutter-position: top bottom left right, $negative: false);
                        }
                        @include xy-gutters($gutters: 25, $gutter-type: margin, $gutter-position: top bottom left right, $negative: false);
                    }

                    &.separated {
                        @media screen and (max-width: $breakpoint-mobile) {
                            @include xy-gutters($gutters: 10, $gutter-type: padding, $gutter-position: top bottom left right, $negative: false);
                        }
                        @include xy-gutters($gutters: 20, $gutter-type: padding, $gutter-position: top bottom left right, $negative: false);
                    }

                    &.half_half {
                        @include xy-gutters($gutters: 0, $gutter-type: padding, $gutter-position: top bottom left right, $negative: false);
                    }
    
                    &.tiles {
                        @include xy-gutters($gutters: 15, $gutter-type: padding, $gutter-position: top bottom left right, $negative: false);
                    }
                }
                .locked {
                    margin-left: .5rem;
                }
                .label {
                    text-transform: uppercase;
                }
            }


            div {
                box-sizing: border-box;
            }

            &.image {
                background-position: center center;
                background-size: cover;
                background-repeat: no-repeat;
            }
            .blurb {
                width: 100%;
                margin-bottom: 1.5rem;
                @media (min-width: 1200px) {
                    margin-bottom: 2rem;
                }
            }
            .cards-listing-title, .default-listing-title {
                h2 {
                    margin: 0 auto 1.5rem;
                }
            }
            .carousel {
                .blurb {
                    box-sizing: border-box;
                }
            }
            &.bleed-module-upwards {
                padding: 0 1rem 6rem;
                .filtration {
                    padding: 0;
                    border-radius: 15px;
                    .filterbar > div {
                        margin-top: -24rem;
                    }
                    .bulk-container {
                        margin-top: -10rem;
                        background: inherit;
                        border-radius: 15px;
                        & > div {
                            padding: 1rem !important;
                        }
                    }
                }
            }
            &.off-set-carousel {
                .single-post-container {
                    .cards-image-content-inner-container {
                        padding: 32px 10px !important;
                        .cards-image-wrapper {
                            text-align: center;
                            img { width: 100%; }
                        }
                    }
                    .featured-image-with-text {
                        aspect-ratio: 1/1;
                        min-height: unset !important;
                    }
                }
                @media (min-width: 768px) {
                    overflow: hidden;
                    .filtration.grid-container {
                        .slick-list {
                            overflow: visible;
                            padding: 0 !important;
                            &:before {
                                content: '';
                                position: absolute;
                                left: -100%;
                                right: 100%;
                                top: 0;
                                bottom: 0;
                                // background: ${props => props.backgroundColor !== 'transparent' ? props => props.backgroundColor : '#fff'};
                                z-index: 1;
                            }
                        }
                        .slick-arrow {
                            &.slick-next {
                                width: 36px;
                                right: -1rem !important;
                            }
                        }
                    }
                }
            }
        }







        .slick-slider.carousel {
            .link-container {
                margin-top: 30px;
            }
        }

        ul.pagination {
            list-style: none;
            margin: 1rem auto 2rem;
            padding: 0 1rem;
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            li {
                &.disabled {
                    display: none;
                }
                &.selected {
                    box-shadow: 0px 6px 15px rgba(0, 0, 0, 0.16);
                    a {
                        color: inherit;
                    }
                }
                a {
                    padding: 8px 14px;
                    display: flex;
                    line-height: 1.5;
                    font-size: 0.75rem;
                }
            }
        }
    }
}