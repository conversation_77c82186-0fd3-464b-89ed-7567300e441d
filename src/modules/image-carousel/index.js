import React, { useMemo, useState } from 'react'
import Slider from 'react-slick';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { faCircle } from '@fortawesome/free-solid-svg-icons'
// HELPERS
import { PrevArrow, NextArrow } from 'src/helpers/slick';
import { Coloring } from 'src/helpers';
// LAYOUT.
import Layout from 'src/modules/image-carousel/layout';
const Default = React.lazy(() => import("src/modules/image-carousel/styles/default"));
// SCSS.
import 'src/modules/image-carousel/index.scss';
import 'src/modules/image-carousel/slick-styling.scss'
import "slick-carousel/slick/slick.scss";
import "slick-carousel/slick/slick-theme.scss";

const Start = ({ data, settings }) => {
    const bgStyles = useMemo(() => {
        if (data.background_type === 'image') {
            return {
                backgroundImage: `url(${data.background_image?.url})`,
                backgroundPosition: 'center center',
                backgroundSize: "cover",
                backgroundRepeat: 'no-repeat',
            };
        } else if (data.background_type === 'color') {
            return {
                color: data.background_value === 'dark' ? '#fff' : Coloring('body_copy_color', settings),
                backgroundColor: Coloring(data.background_color, settings),
            };
        }
    }, []);

    const role = useMemo(() => data.background_type == 'image' ? 'img' : null, []);
    const bgAlt = useMemo(() => data.background_type == 'image' && data.background_image?.alt ? data.background_image?.alt : null, []);

    return (<Container data={data} settings={settings} style={bgStyles} role={role} aria-label={bgAlt} />);
};

const Container = ({ data, settings, ...otherProps }) => {
    const carouselItems = data.carousel_item_input === 'gallery' ? data.carousel_items_gallery : data.carousel_items

    return (
        <Layout data={data} settings={settings} {...otherProps}>
            <Layout.Container.Title data={data} settings={settings} />
            <Layout.Container data={data} settings={settings}>
                <Layout.Container.Carousel data={data} settings={settings}>
                    <Layout.Container.Overlay data={data} settings={settings} />
                    <Slider {...sliderSettings(data, settings)}>
                        {carouselItems && carouselItems?.map((slide, index) => <DrillDown data={data} settings={settings} slide={slide} index={index} />)}
                    </Slider>
                </Layout.Container.Carousel>
                <Layout.Container.Button data={data} settings={settings} />
            </Layout.Container>
        </Layout>
    );
};

const DrillDown = ({ data, settings, slide, index }) => {
    return (<Default data={data} settings={settings} slide={slide} index={index} />);
};

export default Start;


const Dots = (props) => {
    const { className, dots } = props;
    const styles = {
        color: props.data.style == 'flex' ? props.settings.design.colors.tertiary_color : props.settings.design.colors.primary_color
    };

    return (
        <div class="slick-dots">
            <ul class={className} style={styles}>{dots}</ul>
        </div>
    );
};

const Dot = (props) => {
    const { dot, onClick } = props;
    return (
        <div onClick={onClick}>
            <FontAwesomeIcon icon={faCircle} className="icon" />
        </div>
    );
};

const sliderSettings = (data, settings) => {
    return {
        autoplay: data.autoplay,
        autoplaySpeed: 5000,
        infinite: false,
        speed: 500,
        slidesToShow: SlidesToShow(data),
        slidesToScroll: SlidesToScroll(data),
        slide: 'div',
        className: `${data.type} ${data.style}`,

        dots: data.dots,
        customPaging: dot => <Dot dot={dot} data={data} settings={settings} />,
        appendDots: dots => <Dots dots={dots} data={data} settings={settings} />,

        arrows: data.arrows,
        nextArrow: <NextArrow />,
        prevArrow: <PrevArrow />,

        responsive: [
            {
                breakpoint: 1100,
                settings: {
                    slidesToShow: 2,
                    slidesToScroll: 1
                }
            },
            {
                breakpoint: 600,
                settings: {
                    slidesToShow: 1,
                    slidesToScroll: 1
                }
            },
            {
                breakpoint: 400,
                settings: {
                    slidesToShow: 1,
                    slidesToScroll: 1
                }
            },
        ],
    };
};


const SlidesToShow = (data) => {
    const amount = data?.carousel_items?.length;
    const display = (amount < 5) ? amount : 5;
    switch (true) {
        case data.style == 'single-image':
            return 1;
        case Number(data.slides_to_show) > 0:
            return Number(data.slides_to_show);
        default:
            return display;
    };
};

const SlidesToScroll = (data) => {
    const amount = data?.carousel_items?.length;
    const scroll = (amount < 5) ? amount : 5;
    switch (true) {
        case data.style == 'single-image':
            return 1;
        case Number(data.slides_to_scroll) > 0:
            return Number(data.slides_to_scroll);
        default:
            return scroll;
    };
};
