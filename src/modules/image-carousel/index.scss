$breakpoint_mobile: 600px;
$breakpoint_tablet: 900px;
$breakpoint_desktop: 1200px;

#modules-container {
    .image-carousel {
        margin: 30px 0px;
        padding: 50px 0px;

        .grid-container {
            &.flex {
                max-width: 100%;
                padding: 0px;
            }
        }



        .heading-container {
            margin-bottom: 40px;
            .container {
                text-align: center;
            }
        }

        .module-buttons {
            padding: 0 1rem;
            margin: 2rem auto 0;
            max-width: 1200px;
        }

        .slider-container {
            position: relative;
            .container {
                position: relative;
            }
        }

        .overlay {
            position:absolute;
            z-index: +1;
            top: 0px;
            left: 0px;
            bottom: 0px;
            right: 0px;
            // background: linear-gradient(90deg, rgba(238,238,238,1) 0%, rgba(238,238,238,0) 20%, rgba(238,238,238,0) 80%, rgba(238,238,238,1) 100%);
            background: linear-gradient(90deg, rgba(255,255,255,1) 0%, rgba(255,255,255,0) 20%, rgba(255,255,255,0) 80%, rgba(255,255,255,1) 100%);
            opacity: 1;
            width: 100%;
            height: 100%;
            pointer-events: none;
        }
    }
}