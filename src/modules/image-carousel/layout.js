import React, { useMemo } from 'react';
import { useInView } from 'react-intersection-observer';

// PARTIALS
const Button = React.lazy(() => import('src/partials/button'));

const Start = ({ data, settings, children, ...otherProps }) => {
    const { ref, inView } = useInView({
        triggerOnce: true,
        fallbackInView: true
    });

    return (<div ref={ref} class={`image-carousel ${data.type} ${data.style}`} children={inView ? children : null} {...otherProps} />);
};
Start.displayName = 'modules, image-carousel, layout, start';

Start.Container = ({ data, settings, children, ...otherProps }) => {
    return (<div class={`grid-container ${data.type} ${data.style}`} children={children} {...otherProps} />);
};
Start.Container.displayName = 'modules, image-carousel, layout, start.container';

Start.Container.Title = ({ data, settings, children, ...otherProps }) => {
    var headingStyles = {
        color: data?.background_value === 'dark' ? '#fff' : settings?.design?.colors?.primary_color
    };

    if (!(data?.title) && !(data.blurb)) return null;
    return (
        <div class='grid-container heading-container'>
            <div class='grid-x'>
                <div className="cell small-12">
                    {data?.title && <h2 class={`title ${data?.title_alignment}`} style={headingStyles}>{data?.title}</h2>}
                    {data?.blurb && <div class='blurb' dangerouslySetInnerHTML={{__html: data?.blurb}} />}
                </div>
            </div>
        </div>
    );
};
Start.Container.Title.displayName = 'modules, image-carousel, layout, start.container.title';

Start.Container.Button = ({ data, settings, children, ...otherProps }) => {
    if (!(data.button?.title)) return null;
    otherProps.class = useMemo(() => ['module-buttons', data.button_alignment ].join(' '));
    return (
        <div {...otherProps}>
            <Button title={data.button?.title} url={data.button.url} target={data.button.target} icon={data.button_icon} type={data.button_style} tone={data.background_value} />
        </div>
    );
};
Start.Container.Button.displayName = 'modules, image-carousel, layout, start.container.button';

Start.Container.Overlay = ({ data, settings, children, ...otherProps }) => {
    if (data.style != 'faded-out-edges') return null
    return (<div class={`overlay ${data.type} ${data.style}`} />);
};
Start.Container.Overlay.displayName = 'modules, image-carousel, layout, start.container.overlay';

Start.Container.Carousel = ({ data, settings, children, ...otherProps }) => {
    const carouselItems = data.carousel_item_input === 'gallery' ? data.carousel_items_gallery : data.carousel_items

    if (!(carouselItems)) return null;
    return (
        <div class={`slider-container ${data.type} ${data.style}`}>
            <div class={`${data.style === 'flex' ? 'carousel-container' : 'container'} ${data.type} ${data.style}`}>
                {children}
            </div>
        </div>
    );
};
Start.Container.Carousel.displayName = 'modules, image-carousel, layout, start.container.carousel';

export default Start;
