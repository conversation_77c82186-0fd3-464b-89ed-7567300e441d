#modules-container {
    .image-carousel {
        .slider-container {
            .slick-slider {
                display: flex;
                flex-direction: row;
                flex-wrap: wrap;
                align-content: center;
                justify-items: center;
                justify-content: center;
                position: relative;

                .slick-list {
                    overflow: hidden;
                    // width: calc(100% - 60px);

                    .slick-track {
                        display: -webkit-box;
                        display: -moz-box;
                        display: -ms-flexbox;
                        display: -webkit-flex;
                        display: flex;
                        flex-direction: row;
                        justify-items: center;

                        .slick-slide {
                            display: inline-block;
                            flex: 1;
                            -webkit-flex: 1;
                            text-align: center;

                            img {
                                display: inline-block;
                            }
                        }
                    }
                }


                div.slick-dots {
                    ul.slick-dots {
                        margin: 0px auto;
                        padding: 20px 0px 20px;
                        position: relative;
                        text-align: center;
                        height: 10px;
    
                        // display: block;
                        flex: 1 0 100%;
                        list-style-type: disc;
                        margin-block-start: 0px;
                        margin-block-end: 0px;
                        margin-inline-start: 0px;
                        margin-inline-end: 0px;
                        padding-inline-start: 0px;
    
                        li {
                            position: relative;
                            display: inline-block;
                            width: 10px;
                            height: 10px;
                            margin: 0px 10px;
                            padding: 0px;
                            cursor: pointer;
                            opacity: 0.5;
                            font-family: unset;
    
                            &::before {
                                content: none;
                            }
    
                            &.slick-active {
                                opacity: 1;
                            }
    
                            svg {
                                position: relative;
                                height: 10px;
                                width: 10px;
                                vertical-align: top;
                                font-family: unset;
                                box-sizing: border-box;
                            }
                        }
                    }
                }
            }
        }
    }
}