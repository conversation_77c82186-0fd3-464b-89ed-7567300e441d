import React from 'react'
// HELPERS
import Clicker from 'src/helpers/clicker';
import Imaging from 'src/helpers/imaging';

const Start = ({ data, slide, index }) => {
    const imageData = data.carousel_item_input == 'gallery' ? slide : slide.image;
    const linkData = slide.image_link

    if (linkData) {
        return (
            <Clicker type='anchor' url={linkData?.url} target={linkData.target} key={index} class={`${data.type} ${data.style}`} ariaLabel={`link to ${linkData.url}`}>
                <Imaging data={imageData} />
            </Clicker>
        );
    } else {
        return (
            <div key={index} class={`${data.type} ${data.style}`}>
                <Imaging data={imageData} />
            </div>
        );
    }

};

export default Start;