import React, { useEffect } from 'react';

const Button = React.lazy(() => import('src/partials/button'));

import './buttons.scss';

const Start = ({ data, settings }) => {

    useEffect(() => {
        if (settings?.mvk_theme_config?.header?.header_type === 'fixed' && data?.sticky) {
            setTimeout(() => {
                // need to find a better way to handle this
                // without setTimeout - kept getting error when trying to find the headerHeight, I suspect because the header doesn't always render fast enough for the query selector to be able to find it in time
                let headerElement = document.querySelector('#header');
                let headerHeight = headerElement?.offsetHeight;
                var StyleSheet = document.createElement('style');
                var StyleStrings = ``;
        
                StyleStrings += `@media (min-width: 768px) {
                    #modules-container .section__anchors {
                        position: sticky;
                        z-index: 998;
                        top: ${headerHeight}px;
                    }
                }`;
        
                StyleSheet.innerHTML = StyleStrings;
                document.getElementsByTagName('head')[0].appendChild(StyleSheet);
            }, 1600)
        }
    }, [])
    
    const bgColor = pylot?.design?.coloring(data.background_color, 'backgroundColor');
    let bgStyles;
    switch (data.background_type) {
        case 'image':
            bgStyles = {
                backgroundImage: `url(${pylot?.tools?.domain.add(data.background_image?.url)})`,
                backgroundPosition: 'center center',
                backgroundSize: "cover",
                backgroundRepeat: 'no-repeat'
            };
            break;
        case 'color':
        default:
            bgStyles = {
                ...bgColor
            };
    };

    return (
        <div class='anchor-buttons' style={bgStyles}>
            <div class='inner-container grid-container'>
                {data?.items &&
                    data?.items.map((item, index) => (
                        <Button key={index} class='anchor-button' title={item?.item_link?.title || item.text} url={item?.item_link?.url} target={item?.item_link?.target} type={data?.button_style} />
                    ))
                }
            </div>
        </div>
    );
};

export default Start;