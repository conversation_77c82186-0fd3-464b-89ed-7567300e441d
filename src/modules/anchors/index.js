import React, { Suspense } from 'react';

const Buttons = React.lazy(() => import('./buttons'));
const Text = React.lazy(() => import('./text'));

const Start = ({ data, settings }) => {
    let style = data?.style;
    
    return (
        <Suspense fallback={<div />}>
            {style === 'buttons' && <Buttons data={data} settings={settings} />}
            {style === 'text' && <Text data={data} settings={settings} />}
        </Suspense>
    );
};

export default Start;