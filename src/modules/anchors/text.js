import React, { useEffect, useState } from 'react';
// Helpers
import { Coloring } from "src/helpers";
import { decode } from 'html-entities';
import { scrollIntoViewWithOffset } from 'src/helpers/scrollIntoViewWithOffset';

// Styles
import { AnchorText } from './styles';

const Start = ({ data, settings }) => {
    const [active, setActive] = useState(false);
    const bgColor = data.background_color ? Coloring(data.background_color, settings) : '#fff';


    const handleComponentLoaded = () => {
        // need to find a better way to handle this - took this from the button version, no time to rethink now as they've sprung a rediculous deadline on us.
        var StyleSheet = document.createElement('style');
        var StyleStrings = ``;

        StyleStrings += `
            #modules-container .section__anchors {
                position: sticky;
                z-index: 998;
                top: 0;
            }
        `;
        if (settings?.mvk_theme_config?.header?.header_type !== 'static' && settings?.mvk_theme_config?.header?.header_type !== 'absolute') {
            let headerElement = document.querySelector('#header');
            let headerHeight = headerElement?.offsetHeight;
            StyleStrings += `
                #modules-container .section__anchors {
                    top: ${headerHeight}px;
                }
            `;
        }

        StyleSheet.innerHTML = StyleStrings;
        document.getElementsByTagName('head')[0].appendChild(StyleSheet);
    }

    useEffect(() => {
        if (data?.sticky) {
            setTimeout(() => {
                handleComponentLoaded();
            }, 1000)
        }
        // on scroll find the element and check if it's in viewport
        const onScroll = () => {
            data.items.forEach((item, index) => {
                let el = document.getElementById(item?.item_link?.url.substring(1))
                if (isInViewport(el)) {
                    setActive({ [`item-${index}`]: true })
                }
            })
        }
        //scroll event listener
        document.addEventListener('scroll', onScroll, { passive: true });
        return () => {
            document.removeEventListener("scroll", onScroll);
        };
    }, [])

    // check if element is in viewport, we are not checking the bottom because we just want to know if the top of the section is between 0-500, numbers may need to be adjusted but the idea is there.
    const isInViewport = (element) => {
        const rect = element.getBoundingClientRect();
        return (
            rect.top >= 0 &&
            rect.top <= 1000 &&
            rect.left >= 0 &&
            rect.right <= (window.innerWidth || document.documentElement.clientWidth)
        );
    }

    // set active link and pass down to the links, so we know which one to highlight / underline 
    const setActiveLink = (e) => {
        setActive({ [e.target.dataset.index]: true })
    }

    return (
        <AnchorText
            className={`anchor-text ${data?.background_type} style-${data.active_style}`}
            backgroundColor={bgColor}
            backgroundImage={data.background_type === 'image' ? data.background_image?.url : ''}
            textColor={data.background_value === 'dark' ? '#fff' : Coloring('body_copy_color', settings)}
            activeColor={data.active_color}
        >
            <div class='inner-container grid-container'>
                {data?.items &&
                    data?.items.map((item, index) => <AnchorLink item={item} index={index} active={active} setActive={setActiveLink} />)
                }
            </div>
        </AnchorText>
    );
};

const AnchorLink = ({ item, index, active, setActive }) => {
    const [itemName] = useState(item?.item_link?.url.substring(1));

    const handleClick = event => {
        event.preventDefault();
        setActive(event)
        let el = document.getElementById(itemName)
        scrollIntoViewWithOffset(el, 70, 'smooth');
    };

    return (
        <a
            key={`item-${index}`}
            data-index={`item-${index}`}
            onClick={handleClick}
            class={`anchor-link ${active[`item-${index}`] ? 'active' : ''}`}
            href={item?.item_link?.url}
        >
            {decode(item?.item_link?.title || item.text)}
        </a>
    )
}

export default Start;