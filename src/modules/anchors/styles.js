import styled from 'styled-components';


export const AnchorText = styled.div`
    color: ${props => props.textColor};
    min-height: 70px; // creative request
    box-sizing: border-box;
    display: flex;
    align-items: center;
    padding-top: 1rem;
    padding-bottom: 1rem;
    &.color {
        background-color: ${props => props.backgroundColor};
    }
    &.image {
        background-image: url(${props => props.backgroundImage});
        background-position: center center;
        background-size: cover;
        background-repeat: no-repeat;
    }
    &.style-underline {
        .anchor-link {
            text-decoration: none;
            &:after {
                content: '';
                display: block;
                width: 0;
                height: 2px;
                background: ${props => props.activeColor};
                transition: width .3s;
            }
            &.active:after, &:hover:after {
                width: 100%;
            }
        }
    }
    .inner-container {
        display: flex;
        overflow: auto;
        width: 100%;
        -ms-overflow-style: none; /* IE and Edge */
        scrollbar-width: none; /* Firefox */
        &::-webkit-scrollbar {
            display: none;
        }
    }

    .anchor-link {
        color: ${props => props.textColor};
        white-space: nowrap;
        margin-right: 1.5rem;
        line-height: 1.75;
        @media (min-width: 1200px) {
            margin-right: 2.5rem;
        }
    }
`;
