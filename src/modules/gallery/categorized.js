import React, { useState } from "react";
import Slider from 'react-slick';
import Modal from 'src/helpers/modal';
import { decode } from 'html-entities';
import { PrevArrow, NextArrow } from 'src/helpers/slick';

import * as S from './styles';
import "slick-carousel/slick/slick.scss";
import "slick-carousel/slick/slick-theme.scss";

const Imaging = React.lazy(() => import('src/helpers/imaging'));

const Start = ({ data, settings, placeholders }) => {
    const [activeIndex, setActiveIndex] = useState(data.style === 'categorized-grid' && !data.hide_all_tab ? 'all' : 0);
    const [number, setNumber] = useState(Math.floor(Math.random() * 10000));
    const [completeGallery, setCompleteGallery] = useState(data.style === 'categorized-grid' ? flattenGallery(data.categorized_gallery, placeholders) : null);

    return (
        <S.Categorized
            className={`${data.style}`}
            primaryColor={settings?.design?.colors?.primary_color}
        >
            <div className="grid-x">
                <div className={`categories${data.style === 'categorized-carousel' ? ' large-4' : ''} cell`} role='tablist' aria-label={'carousel tabs'}>
                    {(data.style === 'categorized-grid' && !data.hide_all_tab) && <div className={`category-heading primary-txt`} onClick={() => setActiveIndex('all')} role='tab' aria-selected={activeIndex === 'all'} aria-controls={`gallery-${number}-all`} id={`tab-${number}-all`} tabIndex={0}><div className={`heading${activeIndex === 'all' ? ' active' : ''}`}>All</div></div>}
                    {data.categorized_gallery && data.categorized_gallery.map((cg, i) => <div className={`category-heading primary-txt`} onClick={() => setActiveIndex(i)} role='tab' aria-selected={activeIndex === i} aria-controls={`gallery-${number}-${i}`} id={`tab-${number}-${i}`} tabIndex={0}><div className={`heading${activeIndex === i ? ' active' : ''}`}>{(placeholders && cg.gallery?.category_selection === 'dynamic') ? decode(placeholders.single_line[cg.gallery?.category_dynamic]) : decode(cg.gallery?.category)}</div></div>)}
                </div>
                {data.style === 'categorized-carousel' &&
                    <div className="gallery-carousel large-8 cell">
                        {data.categorized_gallery && data.categorized_gallery.map((cg, i) => {
                            if (activeIndex === i) return <GalleryCarousel gallery={cg.gallery} placeholders={placeholders} i={i} number={number} />
                        })}
                    </div>
                }
                {data.style === 'categorized-grid' &&
                    <div className="gallery-grid cell">
                        {(activeIndex === 'all') && <GalleryGrid gallery={completeGallery} placeholders={placeholders} i={'all'} number={number} />}

                        {data.categorized_gallery && data.categorized_gallery.map((cg, i) => {
                            if (activeIndex === i) return <GalleryGrid gallery={cg.gallery} placeholders={placeholders} i={i} number={number} />
                        })}
                    </div>
                }
            </div>
        </S.Categorized>
    );
}

const GalleryCarousel = ({ gallery, placeholders, i, number }) => {
    const galleryImages = (placeholders && gallery.image_selection === 'dynamic') ? placeholders.image[gallery.images_dynamic] : gallery.images

    let sliderSettings = {
        slidesToShow: 1,
        slidesToScroll: 1,
        infinite: false,
        adaptiveHeight: true,
        arrows: true,
        nextArrow: <NextArrow />,
        prevArrow: <PrevArrow />
    };

    return (
        <div className="slider-wrapper" key={`gallery-${number}-${i}`} id={`gallery-${number}-${i}`} role='tabpanel' tabIndex={0} aria-labelledby={`tab-${number}-${i}`}>
            <Slider ref={(a) => a} {...sliderSettings} >
                {(galleryImages && Array.isArray(galleryImages)) && galleryImages.map((image, i) =>
                    <div key={`slide-${number}-${i}`} className="slide">
                        <Imaging key={`image-${i}`} data={image} />
                        <div className="details">
                            {image.caption ? <div className="caption">{decode(image.caption)}</div> : <div className="spacer"></div>}
                            <div className="image-count primary-txt">
                                <span>{i + 1}/{galleryImages.length}</span>
                            </div>
                        </div>
                    </div>
                )}
            </Slider>
        </div>
    )
}

const GalleryGrid = ({ gallery, placeholders, i, number }) => {
    const images = i === 'all' ? gallery : ((placeholders && gallery.image_selection === 'dynamic') ? placeholders.image[gallery.images_dynamic] : gallery.images)
    const [showModal, setShowModal] = useState(false);
    const [activeIndex, setActiveIndex] = useState(0);

    const openModal = (i) => {
        setActiveIndex(i);
        setShowModal(true);
    }
    const close = () => {
        setShowModal(false);
    };
    let sliderSettings = {
        slidesToShow: 1,
        slidesToScroll: 1,
        slickGoTo: activeIndex,
        initialSlide: activeIndex,
        arrows: true,
        nextArrow: <NextArrow />,
        prevArrow: <PrevArrow />
    };

    return (
        <div className="grid-wrapper" key={`gallery-${number}-${i}`} id={`gallery-${number}-${i}`} role='tabpanel' tabIndex={0} aria-labelledby={`tab-${number}-${i}`}>
            {(images && Array.isArray(images)) && images?.map((image, i) =>
                <div key={`image-${number}-${i}`} onClick={() => openModal(i)} className="image-wrapper">
                    <Imaging className="thumbnail" key={`image-${i}`} data={image} />
                </div>
            )}
            <Modal close={close} closeIcon={true} active={showModal} class='slider'>
                <div className="slider-container">
                    <Slider ref={(a) => a} {...sliderSettings}>
                        {(images && Array.isArray(images)) && images?.map((image, i) =>
                            <div className="slide-wrapper">
                                <div className="inner-wrapper">
                                    <Imaging key={`image-${i}`} data={image} />
                                    {image.caption && <div className="caption">{decode(image.caption)}</div>}
                                </div>
                            </div>
                        )}
                    </Slider>
                </div>
            </Modal>
        </div>
    )
}

const flattenGallery = (galleries, placeholders) => {
    let allImages = []
    galleries.map(cg => {
        allImages.push((placeholders && cg.gallery.image_selection === 'dynamic') ? placeholders.image[cg.gallery.images_dynamic] : cg.gallery.images);
    })
    return [].concat(...allImages);
}

export default Start;