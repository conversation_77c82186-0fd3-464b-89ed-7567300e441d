import React, { useState, useEffect } from 'react';
const Imaging = React.lazy(() => import('src/helpers/imaging'));

const Start = ({ data, placeholders }) => {
    const imageGroup1 = placeholders ? placeholders.image[data.image_group_1] : data.image_group_1
    const imageGroup2 = placeholders ? placeholders.image[data.image_group_2] : data.image_group_2
    const imageGroup3 = placeholders ? placeholders.image[data.image_group_3] : data.image_group_3
    const imageGroup4 = placeholders ? placeholders.image[data.image_group_4] : data.image_group_4

    return (
        <div className={`grid-container live-grid ${data?.add_gutters && 'with-gutters'}`}>
            <div className="section-1">
                <div className='image-group-1'>
                    <ImageGroup images={imageGroup1} delay={0} />
                </div>
            </div>
            <div className="section-2">
                <div className='image-group-2'>
                    <ImageGroup images={imageGroup2} delay={8000} />
                </div>
                <div className='image-group-3'>
                    <ImageGroup images={imageGroup3} delay={4000} />
                </div>
                <div className='image-group-4'>
                    <ImageGroup images={imageGroup4} delay={12000} />
                </div>
            </div>
        </div>
    )
}

const ImageGroup = ({ images, delay }) => {
    const [currentImage, setCurrentImage] = useState(images[0]);
    const total = images?.length;
    let index = 1;
    useEffect(() => {
        setTimeout(() => {
            setInterval(() => {
                if (index == total) {
                    index = 0
                }
                setCurrentImage(images[index]);
                index++;
            }, 16000);
        }, delay);
    }, [index, total]);

    return (
        <Imaging data={currentImage} class='background-bg' />
    )
}

export default Start;