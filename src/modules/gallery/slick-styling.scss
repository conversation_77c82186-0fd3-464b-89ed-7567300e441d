@import 'src/scss/variables.scss';

#modules-container {
    .gallery {
        #gallery-lightbox {
            .slick-slider {
                position: relative;
                height: 100vh;
    
                display: -webkit-box;
                display: -moz-box;
                display: -ms-flexbox;
                display: -webkit-flex;
                display: flex;
                flex-direction: row;
                align-items: center;

                .slick-arrow {
                    position: absolute;
                    z-index: +1;
                    top: calc(50% - 15px);

                    flex: 0 1;
                    -webkit-flex: 0 1;
                    min-width: 30px;
                    width:20px;
                    height:30px;
    
                    &::before { content: none; }

                    &.slick-prev {
                        left: 50px;
                        @media screen and (max-width: $breakpoint-large) { left: 0px; }
                        // @media screen and (max-width: $breakpoint-mobile) { left: 0px; }
                    }
                    &.slick-next {
                        right: 50px;
                        @media screen and (max-width: $breakpoint-large) { right: 0px; }
                        // @media screen and (max-width: $breakpoint-mobile) { right: 0px; }
                    }
    
                    svg {
                        height: 30px;
                        width: 30px;
                        font-size: 1.875rem;
                        color: white;
                        fill: white;
                        &:hover { cursor: pointer; }
                    }
                }
        
                .slick-list {
                    flex: 1 0;
                    -webkit-flex: 1 0;
                    overflow: hidden;
                    max-height: 80vh;
                    padding: 0px;
    
                    .slick-track {
                        display: -webkit-box;
                        display: -moz-box;
                        display: -ms-flexbox;
                        display: -webkit-flex;
                        display: flex;
                        flex-direction: row;
                        // gap:15px;
                        height: 80vh;
                        max-height: 100%;
                        .slick-slide {
                            flex: 1;
                            -webkit-flex: 1;
                            visibility: hidden;
                            height: inherit;
                            &.slick-active { visibility: visible; }
                            & > div {
                                height: inherit;
                                max-height: 80vh;
                            }
                        }
                    }
                }
            }
        }
    }
}