@import "src/scss/variables.scss";

#modal {
    #gallery-lightbox {
        overflow: hidden;
        position: relative;
        margin: 0px auto 0px;
        height: 100vh;

        #lightbox-close {
            position: absolute;
            z-index: +1;
            top: 20px;
            right: 20px;

            @media screen and (max-width: $break-mobile) {
                top: 10px;
                right: 10px;
            }

            svg {
                width: 30px;
                height: 30px;
                color: white;
                fill: white;
            }
        }

        #lightbox-container {
            position: relative;
            height: 100vh;

            .lightbox-slide {
                height: 80vh;
                max-height: 80vh;

                .slide-container {
                    height: 80vh;
                    max-height: 80vh;
                    display: -webkit-box;
                    display: -moz-box;
                    display: -ms-flexbox;
                    display: -webkit-flex;
                    display: flex;
                    flex-direction: column;
                    gap: 10px;

                    // overflow: scroll;

                    .slide-media {
                        margin: 0px auto;
                        width: 100%;
                        max-width: 1200px;
                        height: 100%;
                        // max-height: calc(80vh - 32px);
                        text-align: center;
                        flex-grow: 0;
                        flex-shrink: 1;

                        img {
                            margin: 0px auto;
                            margin-left: auto;
                            margin-right: auto;
                            max-height: 100%;
                            max-width: 100% !important;
                            // height: 100%;
                            object-fit: contain;

                            @media screen and (max-width: $break-mobile) {
                                margin: 0px;
                            }
                        }

                        .slide-media-video {
                            height: 100%;
                            max-height: 100%;

                            .mvk-responsive-video {
                                padding: 0px;
                                height: 100%;

                                iframe {
                                    position: relative;
                                }
                            }
                        }
                    }

                    .slide-description {
                        text-align: center;
                        color: white;
                        flex-grow: 0;
                        flex-shrink: 1;
                    }
                }
            }
        }
    }

    .slick-slider {
        top: 50%;
        transform: translateY(-50%);

        .slick-arrow {
            position: absolute;
            z-index: +1;
            top: calc(50% - 15px);

            flex: 0 1;
            -webkit-flex: 0 1;
            min-width: 30px;
            width: 20px;
            height: 30px;

            &::before {
                content: none;
            }

            &.slick-prev {
                left: 50px;
                @media screen and (max-width: $breakpoint-large) {
                    left: 0px;
                }
                // @media screen and (max-width: $breakpoint-mobile) { left: 0px; }
            }
            &.slick-next {
                right: 50px;
                @media screen and (max-width: $breakpoint-large) {
                    right: 0px;
                }
                // @media screen and (max-width: $breakpoint-mobile) { right: 0px; }
            }

            svg {
                height: 30px;
                width: 30px;
                font-size: 1.875rem;
                color: white;
                fill: white;
                &:hover {
                    cursor: pointer;
                }
            }
        }
    }
}
