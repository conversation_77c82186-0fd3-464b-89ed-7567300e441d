import styled from 'styled-components';

export const Gallery = styled.div`
    color: ${props => props.textColor};
    &.color {
        background-color: ${props => props.bgColor};
    }
    &.image {
        background-image: url('${props => props.bgImage?.url}');
        background-position: center center;
        background-size: cover;
    }
    &:has(.full-width-carousel) {
        padding: 3rem 0;
    }
    .full-width-carousel {
        .masonry-brick {
            img {
                width: 100%;
            }
            .caption-container {
                display: flex;
                justify-content: space-between;
                margin: 1rem 0;
                .caption {
                    margin: 0;
                }
            }
        }
    }
`

export const Categorized = styled.div`
    .categories {
        display: flex;
        flex-wrap: wrap;
        margin-bottom: 1rem;
        .category-heading {
            .heading {
                font-size: 1.25rem;
                margin: 0 2rem .5rem 0;
                padding-bottom: 2px;
                position: relative;
                cursor: pointer;
                &:after {
                    content: '';
                    position: absolute;
                    bottom: -3px;
                    display: block;
                    height: 3px;
                    width: 0;
                    transition: .3s;
                    background-color: ${props => props.primaryColor};
                }
                &.active {
                    font-weight: bold;
                    &:after {
                        width: 100%;
                    }
                }
            }
        }
       
    }
    &.categorized-carousel {
        @media (min-width: 1024px) {
            .categories {
                display: block;
                .category-heading {
                    margin-bottom: 1.5rem;
                    .heading {
                        display: inline-block;
                    }
                }
            }
        }
    }
    .gallery-carousel {
        .slide {
            img {
                width: 100%;
            }
            .details {
                display: flex;
                justify-content: space-between;
                margin: .75rem 0 1rem;
                .image-count {
                    font-weight: bold;
                }
            }
        }
    }
    &.categorized-grid {
        margin-bottom: 2rem;
        .categories {
            margin-bottom: 2rem;
        }
        .gallery-grid {
            outline: none;
            
            .grid-wrapper {
                display: grid;
                grid-auto-flow: row;
                grid-template-columns: 1fr 1fr;
                gap: 8px;
                .image-wrapper {
                    height: 185px;
                    position: relative;
                    overflow: hidden;
                    .thumbnail {
                        object-fit: cover;
                        width: 100%;
                        height: 100%;
                        transition: .3s;
                        cursor: pointer;
                    }
                    &:hover {
                        .thumbnail {
                            transform: scale(1.1);
                        }
                    }
                }
                @media (min-width: 768px) {
                    grid-template-columns: 1fr 1fr 1fr 1fr;
                }
                @media (min-width: 1024px) {
                    .image-wrapper {
                        height: 250px;
                    }
                }
                @media (min-width: 1200px) {
                    gap: 1rem;
                    .image-wrapper {
                        height: 305px;
                    }
                }
            }
        }
    }
`