import React, { useCallback, useState, useEffect } from "react";
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faCirclePlay } from '@fortawesome/free-regular-svg-icons';
import Slider from "react-slick";
import "slick-carousel/slick/slick.scss";
import "slick-carousel/slick/slick-theme.scss";
import { useInView } from 'react-intersection-observer';
import { decode } from 'html-entities';
// HELPERS.
import Imaging from 'src/helpers/imaging';
import Clicker from 'src/helpers/clicker';
import HtmlParser from 'src/helpers/html-parser'
import { Coloring } from 'src/helpers';
import { PrevArrow, NextArrow } from 'src/helpers/slick';
// SUB PARTIALS.
const Lightbox = React.lazy(() => import('src/modules/gallery/lightbox'));
const LiveGrid = React.lazy(() => import('src/modules/gallery/live-grid'));
const Categorized = React.lazy(() => import('src/modules/gallery/categorized'));

// SCSS.
import 'src/modules/gallery/index.scss';
import { Gallery } from './styles';

const Start = ({ data, settings, placeholders }) => {
    const [gallery, setGallery] = useState([])
    const bgImage = (placeholders && data.background_image_selection === 'dynamic') ? placeholders.image[data?.background_image_dynamic] : data?.background_image

    useEffect(() => {
        if (data.gallery) {
            setGallery(() => data?.gallery?.filter((item) => {
                const galleryImage = (placeholders && item.image_selection === 'dynamic') ? placeholders.image[item.image_dynamic] : item.image
                const thumbnailImage = (placeholders && item.thumbnail_selection === 'dynamic') ? placeholders.image[item.thumbnail_image_dynamic] : item.thumbnail_image
        
                let image = (item.type == 'video') ? thumbnailImage : galleryImage;
                return (image);
            }))
        }
    }, [data.gallery, placeholders])

    const { ref, inView } = useInView({
        triggerOnce: true,
        fallbackInView: true
    });

    return (
        <Gallery
            ref={ref}
            className={`gallery ${data.background_type ?? ''}`}
            bgColor={Coloring(data.background_color, settings)}
            bgImage={bgImage}
            textColor={data.background_value === 'dark' ? '#fff' : settings.design?.colors?.body_copy_color}
        >
            {inView ?
                <>
                    <div class="grid-container flexbox column">
                        <TextContent data={data} placeholders={placeholders} />
                        {data.style === 'staggered' &&
                            <Staggared data={data} items={data.gallery} settings={settings} placeholders={placeholders} />
                        }
                        {data?.style === 'full-width-carousel' &&
                            <Carousel data={data} items={gallery} settings={settings} placeholders={placeholders} />
                        }
                        {(data?.style === 'categorized-carousel' || data?.style === 'categorized-grid') &&
                            <Categorized data={data} settings={settings} placeholders={placeholders} />
                        }
                    </div>
                    {data.style === 'live-grid' &&
                        <LiveGrid data={data} placeholders={placeholders} />
                    }
                </>
                : null}
        </Gallery>
    );
};

const TextContent = ({ data, placeholders }) => {
    const title = (placeholders && data.title_selection === 'dynamic') ? placeholders.single_line[data.title] : data.title

    return (
        <div class='title-blurb-container'>
            <div class='inner-wrapper'>
                {title && <h2 class={`title ${data?.title_alignment}`}>{decode(title)}</h2>}
                {data?.content && <div class='blurb'><HtmlParser html={data.content} placeholders={placeholders} /></div>}
            </div>
        </div>
    );
};

const Staggared = ({ data, items, settings, placeholders }) => {
    return (
        <div class="gallery-masonry flexbox gap wrap">
            {items && items.map((item, index) => <DisplayItem item={item} index={index} gallery={items} data={data} settings={settings} placeholders={placeholders} />)}
        </div>
    );
};

const Carousel = ({ data, items, settings, placeholders }) => {

    const sliderSettings = {
        dots: false,
        adaptiveHeight: false,
        arrows: true,
        infinite: (items.length > 1),
        nextArrow: <NextArrow />,
        prevArrow: <PrevArrow />
    };

    return (
        <Slider className={`full-width-carousel ${data?.offset ? 'offset' : 'no-offset'}`} ref={(a) => a} {...sliderSettings}>
            {items && items.map((item, index) => <DisplayItem item={item} index={index} gallery={items} data={data} settings={settings} placeholders={placeholders} />)}
        </Slider>
    );
};

const DisplayItem = ({ item, index, gallery, data, settings, placeholders }) => {
    const [modal, setModal] = useState(false);
    const [overlay, setOverlay] = useState(false);
    const [screenSize, setSize] = useState({ width: window.innerWidth });
    const galleryImage = (placeholders && item.image_selection === 'dynamic') ? placeholders.image[item.image_dynamic] : item.image
    const thumbnailImage = (placeholders && item.thumbnail_selection === 'dynamic') ? placeholders.image[item.thumbnail_image_dynamic] : item.thumbnail_image
    let image = (item.type == 'video') ? thumbnailImage : galleryImage;

    if (typeof image === 'object' && image !== null) {
        if (screenSize.width >= 1024) {
            image.height = 385
        } else {
            image.height = 285
        }
    }
    const description = (item.type == 'video') ? thumbnailImage.description : galleryImage.description;

    const activate = useCallback((e) => {
        setOverlay(false);
        setModal(true);
    }, []);
    const closeModal = useCallback((e) => setModal(false), []);
    const mouseEnter = useCallback((e) => setOverlay(true), []);
    const mouseLeave = useCallback((e) => setOverlay(false), []);

    return (
        <Clicker class="masonry-brick hover" onMouseEnter={mouseEnter} onMouseLeave={mouseLeave} process={activate} ariaLabel="view image in lightbox">
            <Imaging data={image} />
            <div className="caption-container">
                {item?.show_caption ? <p class='caption'>{item?.type === 'image' ? galleryImage?.caption : thumbnailImage?.caption}</p> : <span className="spacer"></span>}
                {data.show_image_count && <p className="image-count"><span>{index + 1}/{gallery?.length}</span></p>}
            </div>
            {overlay && data?.style !== 'full-width-carousel' && <Overlay item={item} description={description} settings={settings} close={closeModal} />}
            {data?.style !== 'full-width-carousel' && <Lightbox selected={index} gallery={gallery} placeholders={placeholders} modal={modal} close={closeModal} />}
        </Clicker>
    );
};

const Overlay = ({ item, description, settings }) => {
    var styles = { backgroundColor: `rgba(${hexToRgb(settings?.design?.colors?.primary_color)}, 0.7)`, color: 'tan' }
    return (
        <div class="overlay" style={styles}>
            {item.type == "video" &&
                <div class="overlay-playbutton">
                    <FontAwesomeIcon icon={faCirclePlay} aria-label="play button" />
                </div>
            }
            <div class="overlay-text">{description}</div>
        </div>
    );
};

export default Start;

function hexToRgb(hex) {
    var result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
    var array = [parseInt(result[1], 16), parseInt(result[2], 16), parseInt(result[3], 16)];
    return result ? array.join(',') : null;
};