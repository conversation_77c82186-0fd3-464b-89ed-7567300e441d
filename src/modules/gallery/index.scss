@import "src/scss/variables.scss";

#modules-container {
    .gallery {
        overflow: hidden;
        .grid-container {
            // max-width: 1200px;

            .inner-wrapper {
                @media (min-width: 1200px) {
                    padding: unset;
                    margin: unset;
                    max-width: unset;
                }
            }

            .title,
            .blurb {
                margin-bottom: 30px;
            }

            .gallery-masonry {
                margin: 50px auto;

                .masonry-brick {
                    position: relative;
                    flex: auto;
                    min-height: 230px;
                    max-height: 385px;
                    min-width: 150px;
                    overflow: hidden;
                    width: 100%;

                    img {
                        // max-width: unset;
                        height: 100%;
                        width: 100%;
                        object-fit: cover;
                    }
                    @media (min-width: 640px) {
                        &:nth-child(4n + 1) {
                            max-width: calc(60% - 1rem);
                        }
                        &:nth-child(4n + 2) {
                            max-width: 40%;
                        }
                        &:nth-child(4n + 3) {
                            max-width: calc(40% - 1rem);
                        }
                        &:nth-child(4n + 4) {
                            max-width: 60%;
                        }
                    }
                    @media (min-width: 1024px) {
                        height: 385px;

                        &:nth-child(5n + 1) {
                            max-width: calc(50% - 1rem);
                        }
                        &:nth-child(5n + 2) {
                            max-width: 50%;
                        }
                        &:nth-child(5n + 3) {
                            max-width: calc(28% - 1rem);
                        }
                        &:nth-child(5n + 4) {
                            max-width: calc(44% - 1rem);
                        }
                        &:nth-child(5n + 5) {
                            max-width: 28%;
                        }
                    }

                    .overlay {
                        position: absolute;
                        top: 0px;
                        bottom: 0px;
                        left: 0px;
                        right: 0px;
                        z-index: +1;
                        text-align: center;
                        pointer-events: none;

                        .overlay-playbutton {
                            margin: 0;
                            position: absolute;
                            top: 50%;
                            left: 50%;
                            color: white;
                            font-size: 3.75rem;
                            transform: translate(-50%, -50%);

                            @media screen and (max-width: $break-mobile) {
                                font-size: 1.875rem;
                            }
                        }

                        .overlay-text {
                            margin: 0;
                            position: absolute;
                            bottom: 30px;
                            left: 30px;
                            color: white;
                            font-size: 1.188rem;
                        }
                    }
                }
            }
        }
        .live-grid {
            .section-2 {
                display: flex;
                flex-wrap: wrap;
            }
            .image-group-1 {
                height: 570px;
                width: 100%;
            }
            .image-group-2,
            .image-group-3 {
                width: 50%;
                height: 285px;
            }
            .image-group-4 {
                height: 285px;
                width: 100%;
            }
            .image-group-1,
            .image-group-2,
            .image-group-3,
            .image-group-4 {
                position: relative;
                overflow: hidden;
            }
            img {
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                min-height: 100%;
                min-width: 100%;
                object-fit: cover;
            }
            @media (min-width: 1024px) {
                display: flex;
                .section-1,
                .section-2 {
                    width: 50%;
                }
            }

            &.with-gutters {
                .image-group-1 {
                    img {
                        padding-bottom: 10px;
                        height: 100%;
                    }
                }

                .image-group-2 {
                    img {
                        padding-right: 10px;
                        padding-bottom: 10px;
                        height: 100%;
                    }
                }

                .image-group-3 {
                    img {
                        padding-bottom: 10px;
                        height: 100%;
                    }
                }

                @media (min-width: 1024px) {
                    .image-group-1 {
                        img {
                            padding-right: 10px;
                            padding-bottom: unset;
                        }
                    }
                }
            }
        }

        .full-width-carousel {
            .masonry-brick {
                cursor: unset;
            }

            &.offset {
                .slick-list {
                    overflow: visible;

                    &:before {
                        content: "";
                        background-color: #fff;
                        position: absolute;
                        width: 1000px;
                        height: 100%;
                        left: -1000px;
                        z-index: 2;
                    }
                }

                .slick-next {
                    right: 1rem;
                }

                .masonry-brick {
                    margin-right: 1rem;
                }
            }
        }
    }
}
