import React, { useEffect, useState } from "react";
import ReactSlick from 'react-slick';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faXmark } from '@fortawesome/free-solid-svg-icons';

// HELPERS.
const Clicker = React.lazy(() => import('src/helpers/clicker'));
const Imaging = React.lazy(() => import('src/helpers/imaging'));
import Modal from 'src/helpers/modal';
import { PrevArrow, NextArrow } from 'src/helpers/slick';
// SCSS.
import 'src/modules/gallery/lightbox.scss';
// import 'src/modules/gallery/slick-styling.scss';
import 'slick-carousel/slick/slick.scss';
import 'slick-carousel/slick/slick-theme.scss';

const Start = ({ selected, gallery, placeholders, modal, close }) => {
    return (
        <Modal active={modal} close={close} class="fullpage">
            <Container selected={selected} gallery={gallery} placeholders={placeholders} close={close} />
        </Modal>
    );
};

const Container = ({ selected, gallery, placeholders, close }) => {
    const sliderSettings = {
        dots: false,
        arrows: true,
        adaptiveHeight: false,
        autoplay: false,
        autoplaySpeed: 5000,
        pauseOnHover: false,
        infinite: true,
        initialSlide: selected,
        slidesToShow: 1,
        slidesToScroll: 1,
        speed: 700,
        nextArrow: <NextArrow />,
        prevArrow: <PrevArrow />,
    };

    return (
        <div id="gallery-lightbox">
            <div id="lightbox-container">
                <Clicker id="lightbox-close" process={close} ariaLabel="close lightbox">
                    <FontAwesomeIcon icon={faXmark} aria-label="close icon" />
                </Clicker>
                <ReactSlick {...sliderSettings}>
                    {gallery && gallery.map(item => <Slide item={item} placeholders={placeholders} />)}
                </ReactSlick>
            </div>
        </div>
    );
};

const Slide = ({ item, placeholders }) => {
    const galleryImage = (placeholders && item.image_selection === 'dynamic') ? placeholders.image[item.image_dynamic] : item.image;
    const thumbnailImage = (placeholders & item.thumbnail_selection === 'dynamic') ? placeholders.image[item.thumbnail_image_dynamic] : item.thumbnail_image;
    const [number] = useState(Math.floor(Math.random() * 9999999));
    const description = (item.type == 'video') ? thumbnailImage.description : galleryImage.description;

    useEffect(() => {
        var slideHeight = document.getElementById(`slide-${number}`).offsetHeight;
        var descriptionHeight = document.getElementById(`slide-description-${number}`).offsetHeight;
        document.getElementById(`slide-media-${number}`).style.maxHeight = `calc(${slideHeight}px - ${descriptionHeight}px - 10px)`;
    }, []);

    return (
        <div class="lightbox-slide">
            <div id={`slide-${number}`} class="slide-container">
                <div id={`slide-media-${number}`} class="slide-media">
                    <Content item={item} galleryImage={galleryImage} placeholders={placeholders} />
                </div>
                <div id={`slide-description-${number}`} class="slide-description">{description}</div>
            </div>
        </div>
    );
};

const Content = ({ item, galleryImage, placeholders }) => {
    switch (item.type) {
        case 'image':
            return (<Imaging data={galleryImage} />);
        case 'video':
            return (<Video item={item} placeholders={placeholders} />);
        default:
            return null;
    };
};

const Video = ({ item, placeholders }) => {
    const vimeoVideo = placeholders ? placeholders.single_line[item.vimeo_video] : item.vimeo_video;
    const youtubeVideo = placeholders ? placeholders.single_line[item.youtube_video] : item.youtube_video;

    switch (item.video_type) {
        case 'vimeo':
            return (<div class="slide-media-video" dangerouslySetInnerHTML={{ __html: vimeoVideo }} />);
        case 'youtube':
            return (<div class="slide-media-video" dangerouslySetInnerHTML={{ __html: youtubeVideo }} />);
        default:
            return null;
    };
};

export default Start;


