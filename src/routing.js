import React, { Suspense, useLayoutEffect } from "react";
import { BrowserRouter as Router, Routes, Route, useParams } from "react-router-dom";

const Load = React.lazy(() => import('src/load'));

// SCSS.
import "src/scss/body.scss";
import "src/scss/containers.scss";
import "src/scss/flexbox.scss";
import "src/scss/visibility.scss";
import "src/scss/forms.scss";
import "src/scss/spacers.scss";
import "src/scss/text.scss";
import "src/scss/wordpress.scss";
import "src/scss/chat.scss";
import "src/scss/tables.scss";
import "src/scss/hours.scss";
import "src/scss/templates.scss";

const Start = (props) => {
    return (
        <Router>
            <Routes>
                <Route path="/:first/:second/:third/:forth/:fifth/:slug" element={<Pathway />} />
                <Route path="/:first/:second/:third/:forth/:slug" element={<Pathway />} />
                <Route path="/:first/:second/:third/:slug" element={<Pathway />} />
                <Route path="/:first/:second/:slug" element={<Pathway />} />
                <Route path="/:first/:slug" element={<Pathway />} />
                <Route path="/:slug" element={<Pathway />} />
                <Route path="/" element={<Pathway />} />
            </Routes>
        </Router>
    );
};
Start.displayName  = 'routing';
Router.displayName = 'router';
Routes.displayName = 'routes';
Route.displayName  = 'route';

const Pathway = (props) => {
    return (
        <Suspense fallback={null}>
            <Load />
        </Suspense>
    );
};



export default Start;