import React, { useState, createContext, useLayoutEffect, useEffect, useTransition } from 'react';
import { useLocation } from "react-router-dom";

export const ModulesContext = createContext();
ModulesContext.displayName = 'context, modules: ModulesContext';

export const ModulesProvider = ({ page, settings, children }) => {
    const [ isPending, startTransition ] = useTransition();

    const [ loading, setLoading ] = useState(true);
    const [ nothing, setNothing ] = useState(false);
    const [ layout, setLayout ]   = useState(false);
    const location = useLocation();

    useLayoutEffect(() => {
        setNothing(false);
        setLayout(false);
        startTransition(() => {
            if (pylot.cache.layouts?.[location.key]) {
                setLayout(pylot.cache.layouts?.[location.key]);
                setNothing(false);
                // console.log('already');
            } else {
                var newLayout = page?.mvk_item_content?.custom_fields?.layout || false;
                pylot.cache.layouts[location.key] = newLayout;
                setLayout(newLayout);
                setNothing(newLayout ? false : true);
                // console.log('new');
            }
        });
    }, [ page ]);

    useEffect(() => setLoading(isPending), [ isPending ]);

    return (
        <ModulesContext.Provider value={{ loading, nothing, page, layout }}>
            {children}
        </ModulesContext.Provider>
    );
};
ModulesProvider.displayName = 'context, modules: ModulesProvider';