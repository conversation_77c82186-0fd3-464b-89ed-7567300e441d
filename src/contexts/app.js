import React, { useState, useLayoutEffect, useEffect, createContext, useCallback } from 'react';
import { useLocation } from 'react-router';

// CONFIG.
import config from 'src/config';

export const AppContext = createContext();
AppContext.displayName = 'context / AppContext';

export const AppProvider = (props) => {
    // USED FOR RENDERING COMPONENTS BASED ON WIDTH OR SCREEN CLASS NAME.
    const [ screen, setScreen ]     = useState(getScreen(window.visualViewport.width));
    const [ width, setWidth ]       = useState(window.visualViewport.width);
    // USED FOR RESTORATION UPON HISTORY CHANGE, DO NOT USE FOR ANYTHING ELSE.
    const [ restores, setRestores ] = useState({});
    // USED FOR TRACKING (location.key)
    const location = useLocation();

    useLayoutEffect(() => {
        // UPDATE SCREEN & WIDTH STATES.
        const updateResize = (e) => {
            setScreen(getScreen(window.visualViewport.width));
            setWidth(window.visualViewport.width);
        };
        // LISTEN FOR SIZE CHANGES.
        window.addEventListener('resize', updateResize);
        // NOT NEED TO REMOVE LISTENER, SHOULD ONLY BE REMOVED AFTER LEAVING SITE.
        // return () => window.removeEventListener('resize', updateResize);
    }, []);

    const save = useCallback((key, value) => {
        // console.log('save', key, value);
        switch (key) {
            case 'restore':
                setRestores({ ...restores, ...{ [location.key]: { scrollback:value }}});
                break;
        };
    }, [ location.key ]);

    return (
        <AppContext.Provider value={{ config, screen, width, restores, save, check }}>
            {props.children}
        </AppContext.Provider>
    );
};
AppProvider.displayName = 'context / AppProvider';

// GET SCREEN SIZE CLASS, WITH INCONJUCTION WITH SCSS.
function getScreen (width) {
    // THESE WIDTHS MUST MATCH SRC/SCSS/VARIABLES.SCSS (prefix: $breakpoint)
    switch (true) {
        case width <= 360:
            return 'xsmall';
        case width <= 390:
            return 'small';
        case width <= 428:
            return 'mobile';
        case width <= 640:
            return 'medium';
        case width <= 768:
            return 'tablet';
        case width <= 960:
            return 'large';
        case width <= 1024:
            return 'larger';
        case width <= 1280:
            return 'largest';
        default:
            return 'maximum';
    };
};

// CONDITION CHECK CURRENT WINDOW WIDTH VS. SIZE PARAM.
// size: maximum, desktop, largest, large, medium, small, xsmall.
// type: lte, lt, gte, gt (ie, less than or equals, less than, greater than or equals, greater than)
function check (size, type = 'lte') {
    // console.log(size, type, config.screens[size], window.visualViewport.width);
    if (!(config.screens)) console.warn(`PYLOT: Missing screens obj (config.screens)`);
    if (!(config.screens[size])) console.warn(`PYLOT: Missing screen size: ${size}`);
    switch (true) {
        case type == 'lte' && config.screens[size] >= window.visualViewport.width:
        case type == 'lt'  && config.screens[size] >  window.visualViewport.width:
        case type == 'gte' && config.screens[size] <= window.visualViewport.width:
        case type == 'gt'  && config.screens[size] <  window.visualViewport.width:
            return true;
        default:
            return false;
    };
};