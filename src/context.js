import React, { useState, useEffect, createContext, useContext } from 'react';

// APIs
import * as API from "src/api/requests";
import APIprincipal  from 'src/api/principal';
// import APIfleet      from 'src/api/fleet';
import APInavigation from 'src/api/navigation';

// Must be a top level provider (can't use react-browser-router in here)
export const TranslationContext = createContext();
export const TranslationsProvider = (props) => {
    const [ translation, setTranslation ] = useState(localStorage.getItem("translation") ? localStorage.getItem("translation").replace(/\/$/, '') : false);
    return (
        <TranslationContext.Provider value={[ translation, setTranslation ]}>
            {props.children}
        </TranslationContext.Provider>
    );
};

// PROVIDER FOR PRIMARY (Principal) DOMAIN.
export const PrincipalContext = createContext();
export const PrincipalProvider = (props) => {
    const [translation] = useContext(TranslationContext);
    const [principal, setPrincipal] = useState({ loading: true, trigger: false });

    useEffect(() => {
        if (!principal?.hasPrincipalRan || (translation && translation !== principal?.activeTranslation)) {
            APIprincipal.Request(translation).then(response => {
                // var fleet = (response.settings?.fleet_setup) ? response.settings?.fleet_setup : false;
                // var fleetHomeUrl = response.settings?.fleet_home_url ? response.settings?.fleet_home_url : false;
                var leadRotationForm = response.settings?.lead_rotation_form ? response.settings?.lead_rotation_form : false;
                var rentalAnalysisLeadRotationForm = response.settings?.rental_analysis_lead_rotation_form ? response.settings?.rental_analysis_lead_rotation_form : false;
                var defaultLang = response.settings?.default_language ? response.settings?.default_language?.value : false;
                setPrincipal({
                    // fleet: fleet,
                    subsite: false,
                    // fleetHomeUrl: fleetHomeUrl,
                    leadRotationForm: leadRotationForm,
                    rentalAnalysisLeadRotationForm: rentalAnalysisLeadRotationForm,
                    settings: response['settings'],
                    primaryHours: response['primaryHours'],
                    pages: response['pages'],
                    trigger: false,
                    hasPrincipalRan: true,
                    activeTranslation: translation ? translation : defaultLang,
                    isFormSubmitted: false
                });
            }).catch(error => console.log(error));
        }
    }, [ translation ]);


    return (
        <PrincipalContext.Provider value={[principal, setPrincipal]}>
            {props.children}
        </PrincipalContext.Provider>
    );
};

// PROVIDER FOR FLEET/SUBSITE.
// export const FleetContext = createContext();
// export const FleetProvider = (props) => {
//     const [fleet, setFleet] = useState({ loading: true });

//     useEffect(() => {
//         setFleet({ loading: true });
//         if (props.subsite) {
//             APIfleet.Request(props.subsite).then(response => {
//                 setFleet({ settings: response['settings'], pages: response['pages'] });
//             }).catch(err => console.log(err));
//         }
//     }, [props.subsite]);

//     return (
//         <FleetContext.Provider value={[fleet, setFleet]}>
//             {props.children}
//         </FleetContext.Provider>
//     );
// };

export const SettingsContext = createContext();
export const SettingsProvider = (props) => {
    const [settings, setSettings] = useState(props.settings);

    useEffect(() => {
        cache.settings = props.settings;
        setSettings(props.settings);
    }, [props.settings]);

    return (
        <SettingsContext.Provider value={[settings, setSettings]}>
            {props.children}
        </SettingsContext.Provider>
    );
};

export const PagesContext = createContext();
export const PagesProvider = (props) => {
    const [pages, setPages] = useState(props.pages);

    useEffect(() => {
        cache.pages = props.pages
        setPages(props.pages);
    }, [props.pages]);

    return (
        <PagesContext.Provider value={[pages, setPages]}>
            {props.children}
        </PagesContext.Provider>
    );
};

export const NavigationContext = createContext();
export const NavigationProvider = (props) => {
    const [navigation, setNavigation] = useState(cache.navigation ? cache.navigation : { loading: true });
    useEffect(() => {
        if (!cache.navigation) {
            APInavigation.Request(props.subsite, props.translation, props.main2, props.utility).then(response => {
                cache.navigation = response;
                setNavigation(response);
            }).catch(error => console.log(error));
        }
    }, [props.subsite, props.translation, props.main2, props.utility]);

    return (
        <NavigationContext.Provider value={[navigation, setNavigation]}>
            {props.children}
        </NavigationContext.Provider>
    );
};

export const StoresContext = createContext();
export const StoresProvider = (props) => {
    const [stores, setStores] = useState(cache.stores ? cache.stores : { loading: true });

    useEffect(() => {
        if (!cache.stores) {
            API.Stores(props.subsite, props.translation).then(response => {
                cache.stores = response;
                setStores(response);
            }).catch(error => console.log(error));
        }
    }, [props.subsite, props.translation]);

    return (
        <StoresContext.Provider value={[stores, setStores]}>
            {props.children}
        </StoresContext.Provider>
    );
};

export const SalesContext = createContext();
export const SalesProvider = (props) => {
    const [sales, setSales] = useState(cache.sales ? cache.sales : { loading: true });

    useEffect(() => {
        if (!cache.sales) {
            API.Sales(props.subsite, props.translation).then(response => {
                cache.sales = response;
                setSales(response);
            }).catch(error => console.log(error));
        }
    }, [props.subsite, props.translation]);

    return (
        <SalesContext.Provider value={[sales, setSales]}>
            {props.children}
        </SalesContext.Provider>
    );
};

export const GlobalSalesContext = createContext();
export const GlobalSalesProvider = (props) => {
    const [globalSales, setGlobalSales] = useState(cache.globalSales ? cache.globalSales : { loading: true });

    useEffect(() => {
        if (!cache.globalSales) {
            API.GlobalSales(props.subsite, props.translation).then(response => {
                cache.globalSales = response;
                setGlobalSales(response);
            }).catch(error => console.log(error));
        }
    }, [props.subsite, props.translation]);

    return (
        <GlobalSalesContext.Provider value={[globalSales, setGlobalSales]}>
            {props.children}
        </GlobalSalesContext.Provider>
    );
};

export const PostsContext = createContext();
export const PostsProvider = (props) => {
    const [posts, setPosts] = useState(cache.posts ? cache.posts : { loading: true });

    useEffect(() => {
        if (!cache.posts) {
            API.Posts(props.subsite, props.translation).then(response => {
                cache.posts = response;
                setPosts(response);
            }).catch(error => console.log(error));
        }
    }, [props.subsite, props.translation]);

    return (
        <PostsContext.Provider value={[posts, setPosts]}>
            {props.children}
        </PostsContext.Provider>
    );
};

export const BlogTemplatesContext = createContext();
export const BlogTemplatesProvider = (props) => {
    const [blogTemplates, setBlogTemplates] = useState(cache.blogTemplates ? cache.blogTemplates : { loading: true });

    useEffect(() => {
        if (!cache.blogTemplates) {
            API.BlogTemplates(props.subsite, props.translation).then(response => {
                cache.blogTemplates = response;
                setBlogTemplates(response);
            }).catch(error => console.log(error));
        }
    }, [props.subsite, props.translation]);

    return (
        <BlogTemplatesContext.Provider value={[blogTemplates, setBlogTemplates]}>
            {props.children}
        </BlogTemplatesContext.Provider>
    );
};

export const CustomTemplatesContext = createContext();
export const CustomTemplatesProvider = (props) => {
    const [customTemplates, setCustomTemplates] = useState(cache.customTemplates ? cache.customTemplates : { loading: true });

    useEffect(() => {
        if (!cache.customTemplates) {
            API.CustomTemplates(props.subsite, props.translation).then(response => {
                cache.customTemplates = response;
                setCustomTemplates(response);
            }).catch(error => console.log(error));
        }
    }, [props.subsite, props.translation]);

    return (
        <CustomTemplatesContext.Provider value={[customTemplates, setCustomTemplates]}>
            {props.children}
        </CustomTemplatesContext.Provider>
    );
};

export const ServicesContext = createContext();
export const ServicesProvider = (props) => {
    const [services, setServices] = useState(cache.services ? cache.services : { loading: true });

    useEffect(() => {
        if (!cache.services) {
            API.Services(props.subsite, props.translation).then(response => {
                cache.services = response;
                setServices(response);
            }).catch(error => console.log(error));
        }
    }, [props.subsite, props.translation]);

    return (
        <ServicesContext.Provider value={[services, setServices]}>
            {props.children}
        </ServicesContext.Provider>
    );
};

export const ServiceTemplatesContext = createContext();
export const ServiceTemplatesProvider = (props) => {
    const [serviceTemplates, setServiceTemplates] = useState(cache.serviceTemplates ? cache.serviceTemplates : { loading: true });

    useEffect(() => {
        if (!cache.serviceTemplates) {
            API.ServiceTemplates(props.subsite, props.translation).then(response => {
                cache.serviceTemplates = response;
                setServiceTemplates(response);
            }).catch(error => console.log(error));
        }
    }, [props.subsite, props.translation]);

    return (
        <ServiceTemplatesContext.Provider value={[serviceTemplates, setServiceTemplates]}>
            {props.children}
        </ServiceTemplatesContext.Provider>
    );
};

export const EventsContext = createContext();
export const EventsProvider = (props) => {
    const [events, setEvents] = useState(cache.events ? cache.events : { loading: true });

    useEffect(() => {
        if (!cache.events) {
            API.Events(props.subsite, props.translation).then(response => {
                cache.events = response;
                setEvents(response);
            }).catch(error => console.log(error));
        }
    }, [props.subsite, props.translation]);

    return (
        <EventsContext.Provider value={[events, setEvents]}>
            {props.children}
        </EventsContext.Provider>
    );
};

export const JobsContext = createContext();
export const JobsProvider = (props) => {
    const [jobs, setJobs] = useState(cache.jobs ? cache.jobs : { loading: true });

    useEffect(() => {
        if (!cache.jobs) {
            API.Jobs(props.subsite, props.translation).then(response => {
                cache.jobs = response;
                setJobs(response);
            }).catch(error => console.log(error));
        }
    }, [props.subsite, props.translation]);

    return (
        <JobsContext.Provider value={[jobs, setJobs]}>
            {props.children}
        </JobsContext.Provider>
    );
};

export const PropertiesContext = createContext();
export const PropertiesProvider = (props) => {
    const [properties, setProperties] = useState(cache.properties ? cache.properties : { loading: true });

    useEffect(() => {
        if (!cache.properties) {
            API.Properties(props.subsite, props.translation).then(response => {
                cache.properties = response;
                setProperties(response)
            }).catch(error => console.log(error));
        }
    }, [props.subsite, props.translation]);

    return (
        <PropertiesContext.Provider value={[properties, setProperties]}>
            {props.children}
        </PropertiesContext.Provider>
    );
};

export const FloorplansContext = createContext();
export const FloorplansProvider = (props) => {
    const [floorplans, setFloorplans] = useState(cache.floorplans ? cache.floorplans : { loading: true });

    useEffect(() => {
        if (!cache.floorplans) {
            API.Floorplans(props.subsite, props.translation).then(response => {
                cache.floorplans = response;
                setFloorplans(response)
            }).catch(error => console.log(error));
        }
    }, [props.subsite, props.translation]);

    return (
        <FloorplansContext.Provider value={[floorplans, setFloorplans]}>
            {props.children}
        </FloorplansContext.Provider>
    );
};

export const AircraftContext = createContext();
export const AircraftProvider = (props) => {
    const [aircraft, setAircraft] = useState(cache.aircraft ? cache.aircraft : { loading: true });

    useEffect(() => {
        if (!cache.aircraft) {
            API.Aircraft(props.subsite, props.translation).then(response => {
                cache.aircraft = response;
                setAircraft(response)
            }).catch(error => console.log(error));
        }
    }, [props.subsite, props.translation]);

    return (
        <AircraftContext.Provider value={[aircraft, setAircraft]}>
            {props.children}
        </AircraftContext.Provider>
    );
};

export const MarketReportsContext = createContext();
export const MarketReportsProvider = (props) => {
    const [marketReports, setMarketReports] = useState(cache.marketReports ? cache.marketReports : { loading: true });

    useEffect(() => {
        if (!cache.marketReports) {
            API.MarketReports(props.subsite, props.translation).then(response => {
                cache.properties = response;
                setMarketReports(response)
            }).catch(error => console.log(error));
        }
    }, [props.subsite, props.translation]);

    return (
        <MarketReportsContext.Provider value={[marketReports, setMarketReports]}>
            {props.children}
        </MarketReportsContext.Provider>
    );
};

export const LocationsContext = createContext();
export const LocationsProvider = (props) => {
    const [locations, setLocations] = useState(cache.locations ? cache.locations : { loading: true });

    useEffect(() => {
        if (!cache.locations) {
            API.Locations(props.subsite, props.translation).then(response => {
                cache.locations = response;
                setLocations(response)
            }).catch(error => console.log(error));
        }
    }, [props.subsite, props.translation]);

    return (
        <LocationsContext.Provider value={[locations, setLocations]}>
            {props.children}
        </LocationsContext.Provider>
    );
};

export const PageTemplatesContext = createContext();
export const PageTemplatesProvider = (props) => {
    const [pageTemplates, setPageTemplates] = useState(cache.pageTemplates ? cache.pageTemplates : { loading: true });

    useEffect(() => {
        if (!cache.pageTemplates) {
            API.PageTemplates(props.subsite, props.translation).then(response => {
                cache.pageTemplates = response;
                setPageTemplates(response)
            }).catch(error => console.log(error));
        }
    }, [props.subsite, props.translation]);

    return (
        <PageTemplatesContext.Provider value={[pageTemplates, setPageTemplates]}>
            {props.children}
        </PageTemplatesContext.Provider>
    );
};

export const SpacesContext = createContext();
export const SpacesProvider = (props) => {
    const [spaces, setSpaces] = useState(cache.spaces ? cache.spaces : { loading: true });

    useEffect(() => {
        if (!cache.spaces) {
            API.Spaces(props.subsite, props.translation).then(response => {
                cache.spaces = response;
                setSpaces(response)
            }).catch(error => console.log(error));
        }
    }, [props.subsite, props.translation]);

    return (
        <SpacesContext.Provider value={[spaces, setSpaces]}>
            {props.children}
        </SpacesContext.Provider>
    );
};

export const TeamContext = createContext();
export const TeamProvider = (props) => {
    const [team, setTeam] = useState(cache.team ? cache.team : { loading: true });

    useEffect(() => {
        if (!cache.team) {
            API.Team(props.subsite, props.translation).then(response => {
                cache.team = response;
                setTeam(response)
            }).catch(error => console.log(error));
        }
    }, [props.subsite, props.translation]);

    return (
        <TeamContext.Provider value={[team, setTeam]}>
            {props.children}
        </TeamContext.Provider>
    );
};

export const TestimonialsContext = createContext();
export const TestimonialsProvider = (props) => {
    const [testimonials, setTestimonials] = useState(cache.testimonials ? cache.testimonials : { loading: true });

    useEffect(() => {
        if (!cache.testimonials) {
            API.Testimonials(props.subsite, props.translation).then(response => {
                cache.testimonials = response;
                setTestimonials(response)
            }).catch(error => console.log(error));
        }
    }, [props.subsite, props.translation]);

    return (
        <TestimonialsContext.Provider value={[testimonials, setTestimonials]}>
            {props.children}
        </TestimonialsContext.Provider>
    );
};

export const ContentTypesContext = createContext();
export const ContentTypesProvider = (props) => {
    const [contentTypes, setContentTypes] = useState({ loading: true });

    useEffect(() => {
        API.ContentTypes(props.subsite).then(response => {
            cache.contentTypes = response;
            setContentTypes(response);
        }).catch(error => console.log(error));
    }, [props.subsite]);

    return (
        <ContentTypesContext.Provider value={[contentTypes, setContentTypes]}>
            {props.children}
        </ContentTypesContext.Provider>
    );
};

export const PageContext = createContext();
export const PageProvider = (props) => {
    const [page, setPage] = useState({ loading: true });

    return (
        <PageContext.Provider value={[page, setPage]}>
            {props.children}
        </PageContext.Provider>
    );
};
