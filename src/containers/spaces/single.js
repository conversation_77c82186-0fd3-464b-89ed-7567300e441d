import React, { useState, useContext } from 'react'
import Slider from 'react-slick';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faChevronCircleLeft, faChevronCircleRight } from '@fortawesome/free-solid-svg-icons'

// CONFIG.
import config from 'src/config';

// Helpers
const Imaging = React.lazy(() => import('src/helpers/imaging'));
const Clicker = React.lazy(() => import('src/helpers/clicker')); 
import Modal from 'src/helpers/modal';

// CONTEXT.
import { SpaceContext } from 'src/containers/spaces/context';

// SCSS.
import 'src/containers/spaces/single.scss';
import 'slick-carousel/slick/slick.scss';
import 'slick-carousel/slick/slick-theme.scss';

const Start = ({ images }) => {
    const [ space, setSpace ] = useContext(SpaceContext);

    function close (e) {
        setSpace({ modal: false });
    };

    if (space.modal) {
        return (
            <Modal close={close} active={true} class="fullpage">
                <div class="single-slider">
                    <ImageSlider images={images} index={space.index} />
                </div>
            </Modal>
        );
    } else {
        return null;
    }
};

const ImageSlider = ({ images, index }) => {
    sliderSingle.initialSlide = index;
    return (
        <Slider {...sliderSingle}>
            {images && images.map((item, index) => <Image item={item} />)}
        </Slider>
    )
}

const Image = ({ item }) => {
    const [ space, setSpace ] = useContext(SpaceContext);
    const [ swipe, setSwipe ] = useState(false); 

    const imageData = {
        url: item.gallery_image.url,
        alt: item.gallery_image.alt
    }

    function toggle (e) {
        if (!swipe) {
            setSpace({ modal:false });
        } else {
            setSwipe(false);
        }
    };

    function mousing (e) {
        if (!swipe) {
            setSwipe(e.clientX);
        } else if (swipe == e.clientX) {
            setSwipe(false);
        }
    };

    return (
        <Clicker class="single-image" process={toggle} onMouseDown={mousing} onMouseUp={mousing}>
            <Imaging data={imageData} />
        </Clicker>
    );
};

export default Start

function PrevArrow (props) {
    const { className, style, onClick } = props;
    return (
        <Clicker class={className} process={onClick} style={{ ...style }}>
            <FontAwesomeIcon icon={faChevronCircleLeft} />
        </Clicker>
    );
};

function NextArrow (props) {
    const { className, style, onClick } = props;
    return (
        <Clicker class={className} process={onClick} style={{ ...style }}>
            <FontAwesomeIcon icon={faChevronCircleRight} />
        </Clicker>
    );
};

const sliderSingle = {
    dots: false,
    arrows: true,
    infinite: true,
    adaptiveHeight: false,
    centerMode: false,
    slidesToShow: 1,
    slidesToScroll: 1,
    nextArrow: <NextArrow />,
    prevArrow: <PrevArrow />
};