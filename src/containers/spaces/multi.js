import React, { useState, useContext } from 'react'
import Slider from 'react-slick';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faChevronCircleLeft, faChevronCircleRight, faChevronLeft, faChevronRight } from '@fortawesome/free-solid-svg-icons'

// CONFIG.
import config from 'src/config';

// Helpers
const Imaging = React.lazy(() => import('src/helpers/imaging'));
const Clicker = React.lazy(() => import('src/helpers/clicker')); 

// CONTEXT.
import { SpaceContext } from 'src/containers/spaces/context';

// SCSS.
import 'src/containers/spaces/multi.scss';
import 'slick-carousel/slick/slick.scss';
import 'slick-carousel/slick/slick-theme.scss';

const Start = ({ images, settings }) => {

    function PrevArrow(props) {
        const { className, style, onClick } = props;

        return (
            <Clicker class={className} process={onClick} style={{ ...style }}>
                <FontAwesomeIcon icon={settings?.space?.style === 'half-half' ? faChevronLeft : faChevronCircleLeft} />
            </Clicker>
        );
    };

    function NextArrow(props) {
        const { className, style, onClick } = props;

        return (
            <Clicker class={className} process={onClick} style={{ ...style }}>
                <FontAwesomeIcon icon={settings?.space?.style === 'half-half' ? faChevronRight : faChevronCircleRight} />
            </Clicker>
        );
    };

    const sliderMulti = {
        dots: settings?.space?.style === 'half-half' ? true : false,
        arrows: true,
        infinite: true,
        variableWidth: settings?.space?.style === 'half-half' ? false : true,
        adaptiveHeight: true,
        slidesToShow: settings?.space?.style === 'half-half' ? 1 : 3,
        nextArrow: <NextArrow />,
        prevArrow: <PrevArrow />,
        responsive: [
            {
                breakpoint: 1200,
                settings: {
                    slidesToShow: settings?.space?.style === 'half-half' ? 1 : 2
                }
            },
            {
                breakpoint: 768,
                settings: {
                    slidesToShow: 1,
                    slidesToScroll: 1,
                    variableWidth: settings?.space?.style === 'half-half' ? false : true
                }
            }
        ]
    };
    // console.log("HELLO", sliderMulti)

    return (
        <div class={settings?.space?.style === 'half-half' ? 'half-half-slider' : 'multi-slider'}>
            <Slider {...sliderMulti}>
                {images && images.map((item, index) => <Image item={item} index={index} />)}
            </Slider>
        </div>
    )
};

const Image = ({ item, index }) => {
    const [ space, setSpace ] = useContext(SpaceContext);
    const [ swipe, setSwipe ] = useState(false);

    const imageData = {
        url: item.gallery_image.url,
        alt: item.gallery_image.alt,
        // height: 300,
        // width: 400
    };

    function toggle (e) {
        if (!swipe) {
            setSpace({ modal:true, index:index });
        } else {
            setSwipe(false);
        }
    };

    function Mousing (e) {
        if (!swipe) {
            setSwipe(e.clientX);
        } else if (swipe == e.clientX) {
            setSwipe(false);
        }
    };

    return (
        <Clicker class='slide-image' process={toggle} onMouseDown={Mousing} onMouseUp={Mousing}>
            <Imaging data={imageData} />
        </Clicker>
    );
};

export default Start

