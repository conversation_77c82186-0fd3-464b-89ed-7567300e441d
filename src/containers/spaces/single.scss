@import 'src/scss/variables.scss';

#space {
    #space-container {

        .single-slider {
            position: absolute;
            top:0px;
            bottom:0px;
            left:0px;
            right:0px;
    
            margin: 50px auto;
            max-width: 900px;
            height: inherit;
            min-height: inherit;
            text-align: center;

            &:hover {
                cursor: pointer;
            }

            .single-image {
                width: 100%;
                img {
                    margin: 0px auto;
                }
            }

            .slick-slider {
                height: inherit;
                min-height: 100%;

                .slick-list {
                    position: absolute;
                    top:0px;
                    bottom:0px;
                    left:0px;
                    right:0px;

                    .slick-track {
                        position: absolute;
                        top: 25%;
                        bottom: 25%;

                        display: flex;
                        align-items: center;
                        justify-content: center;

                        height: inherit;

                        .slick-slide {
                            position: relative;
                            height: auto;
                            flex: 0 1 auto;
                            line-height: 100%;

                            height: inherit;
                            min-height: inherit;
                            vertical-align: middle;
    
                            display: inline-block;
                            vertical-align: middle;
                            float:none;

                            .slide-image {
                                position: relative;
                                height: 400px;
                                overflow: hidden;
                
                                &:hover {
                                    cursor: pointer;
                                }
                                img {
                                    object-fit: cover;
                                }
                
                                .space-image-modal {
                                    position: absolute;
                                    top: 0px;
                                    bottom: 0px;
                                    right: 0px;
                                    left: 0px;
                                    background-color: rgba(0,0,0,0.2);
                                }
                            }
                        }
                    }
                }
            
    
                .slick-arrow {
                    z-index: 1;
                    width:2rem;
                    height:2rem;
                    &.slick-prev {
                        left: -3rem;

                        @media (max-width: $break-medium) {
                            left: 1rem;
                        }
                        &::before {
                            content: none;
                        }
                    }
                    &.slick-next {
                        right: -3rem;

                        @media (max-width: $break-medium) {
                            right: 1rem;
                        }
                        &::before {
                            content: none;
                        }
                    }
                    &::before {
                        z-index: 1;
                        font-size: 2rem;
                    }
                
                    svg {
                        font-size: 2rem;
                        color:white !important;
                    }
                
                }
            }
        }
    }
}