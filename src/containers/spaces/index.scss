@import "src/scss/variables.scss";

#app {
    .space-hero {
        position: relative;
        img {
            width: 100%;
        }
        .half-half-space-title {
            position: absolute;
            top: 50%;
            left: 1rem;
            transform: translateY(-50%);
            @media (min-width: 1200px) {
                left: 10%;
            }
        }
    }
    #space {
        padding: 30px 0px 50px;
        #space-container {
            margin: 0px auto;
            max-width: 1200px;
            .content-wrapper {
                margin-bottom: 2rem;
                .content-button {
                    margin-top: 1rem;
                }
            }
            @media (max-width: $break-medium) {
                padding: 10px;
            }

            @media (max-width: 1200px) {
                padding: 20px;
            }

            &.half-half {
                @media (min-width: $break-medium) {
                    .inner-wrapper {
                        display: flex;
                        .content-wrapper {
                            margin: 0 2rem 2rem 0;
                        }
                        & > div {
                            width: 50%;
                        }
                    }
                }
                @media (min-width: 1200px) {
                    padding-top: 2rem;
                }
                .half-half-slider {
                    margin-bottom: 2rem;
                    .slide-image {
                        position: relative;
                        height: 400px;
                        img {
                            height: 100%;
                            width: 100%;
                            object-fit: cover;
                        }
                    }
                    .slick-arrow {
                        svg {
                            color: #fff;
                            font-size: 1.5rem;
                        }
                        &:before {
                            display: none;
                        }
                        &.slick-prev {
                            left: 1rem;
                            z-index: 1;
                        }
                        &.slick-next {
                            right: 1rem;
                        }
                    }
                    .slick-dots {
                        margin-top: 1rem;
                        li {
                            width: 5px;
                            height: 5px;
                            button {
                                &:before {
                                    content: "";
                                    content: "";
                                    border: 2px solid #333;
                                    border-radius: 50%;
                                    width: 5px;
                                    height: 5px;
                                }
                            }
                            &.slick-active {
                                button:before {
                                    background: #333;
                                }
                            }
                        }
                    }
                }
            }
            .sales-container {
                margin: 2rem 0;
                display: flex;
                flex-wrap: wrap;
                .related-sale {
                    display: block;
                    width: 100%;
                    .inner-sale {
                        padding: 1rem;
                        margin: 1rem 0;
                        .label {
                            display: inline-block;
                            padding: 0.5rem 0.5rem 0.25rem;
                            margin-bottom: 1rem;
                            font-size: 0.75rem;
                        }
                        .title {
                            margin-bottom: 1rem;
                        }
                    }
                }
                @media (min-width: $break-medium) {
                    .related-sale {
                        width: 50%;
                        &:nth-child(odd) .inner-sale {
                            margin: 1rem 1rem 1rem 0;
                        }
                        &:nth-child(even) .inner-sale {
                            margin: 1rem 0 1rem 1rem;
                        }
                    }
                }
            }
        }
    }
}
