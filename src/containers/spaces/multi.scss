#app #space {
    #space-container {
        .multi-slider {
            margin-bottom: 20px;
            // max-height: 400px;

            .slick-slider {
                position: relative;
                overflow: hidden;

                .slide-image {
                    position: relative;
                    height: 400px;
                    overflow: hidden;
    
                    &:hover {
                        cursor: pointer;
                    }
                    img {
                        object-fit: cover;
                        max-width: unset;
                        height: 100%;
                    }
                }
    
                .slick-arrow {
                    z-index: 1;
                    &.slick-prev {
                        left: 0.25rem;
                        width: 30px;
                    }
                    &.slick-next {
                        right: 0.25rem;
                        width: 30px;
                    }
                    &::before {
                        content: '';
                    }
                    svg {
                        font-size: 1.875rem;
                        color: white;
                        fill: white;
                    }
                }
            }
        }
    }
}