import React, { useState, useContext, useEffect } from 'react'
import { decode } from 'html-entities';

// CONFIG.
import config from 'src/config';

// CONTEXT.
import { SpaceProvider, SpaceContext } from 'src/containers/spaces/context';

// PARTIALS.
const Multi = React.lazy(() => import("src/containers/spaces/multi"));
const Single = React.lazy(() => import("src/containers/spaces/single"));
const Button = React.lazy(() => import('src/partials/button'));

// HELPERS.
const Imaging = React.lazy(() => import('src/helpers/imaging'));
const Clicker = React.lazy(() => import('src/helpers/clicker'));
const HtmlParser = React.lazy(() => import('src/helpers/html-parser'));

// SCSS.
import './index.scss';


const Start = ({ page, settings }) => {

    var title = (page?.mvk_item_content?.title?.length > 0) ? page?.mvk_item_content?.title : false;

    return (
        <SpaceProvider>
            {(settings?.space && settings.space?.hero) || page?.mvk_item_content?.custom_fields?.hero_image ?
                <div class='space-hero'>
                    <Imaging data={page?.mvk_item_content?.custom_fields?.hero_image ? page?.mvk_item_content?.custom_fields?.hero_image : settings.space?.hero_image} />
                    {(settings?.space?.style === 'half-half' && title) &&
                        <div className='half-half-space-title white-txt'>
                            <h1>{decode(title)}</h1>
                        </div>
                    }
                </div>
                : null
            }
            {(page?.mvk_item_content?.custom_fields?.content_area || page?.mvk_item_content?.custom_fields?.gallery || page?.mvk_item_content?.custom_fields?.button) &&
                < div id="space">
                    <Container page={page} settings={settings} title={title} />
                </div>
            }
        </SpaceProvider>
    );
};

const Container = ({ page, settings, title }) => {

    var images = (page?.mvk_item_content?.custom_fields?.gallery?.length > 0) ? page?.mvk_item_content?.custom_fields?.gallery : false

    return (
        <div id="space-container" class={`${config.device} ${settings.space?.style}`}>
            {(settings?.space?.style !== 'half-half' && title && page?.mvk_item_meta?.template !== 'modular-template') && <Title title={title} />}
            <div class='inner-wrapper'>
                {page?.mvk_item_content?.custom_fields?.content_area &&
                    <div className='content-wrapper'>
                        <HtmlParser html={page?.mvk_item_content?.custom_fields?.content_area} />
                        {page?.mvk_item_content?.custom_fields?.button &&
                            <Button className='content-button' title={page?.mvk_item_content?.custom_fields?.button.title} url={page?.mvk_item_content?.custom_fields?.button.url} target={page?.mvk_item_content?.custom_fields?.button.target} />
                        }
                    </div>
                }
                {images && <Multi images={images} settings={settings} />}
            </div>
            {images && <Single images={images} />}
            {page?.mvk_item_content?.custom_fields?.related_sales &&
                <div className='sales-container'>
                    {page?.mvk_item_content?.custom_fields?.related_sales.map((sale, i) => <RelatedSale key={i} sale={sale} settings={settings} />)}
                </div>
            }
            {page?.mvk_item_content?.content &&
                <div class="space-content">
                    <HtmlParser html={page.mvk_item_content.content} />
                </div>
            }
        </div>
    );
};

const Title = ({ title }) => {
    return (
        <div class="space-title">
            <h3>{decode(title)}</h3>
        </div>
    );
};

const RelatedSale = ({ sale, settings }) => {

    return (
        <Clicker type='anchor' url={sale.url} target={sale.external_link} className='related-sale' ariaLabel={`link to ${sale.title}`}>
            <div className='inner-sale background-bg'>
                <div class='label body-copy-bg white-txt'>{settings?.space?.current_offer_label ? settings?.space?.current_offer_label : 'CURRENT OFFER'}</div>
                <h4 class='title body-copy-txt'>{decode(sale.title)}</h4>
                <div className='excerpt body-copy-txt'>
                    <HtmlParser html={sale.exerpt} />
                </div>
                <div class='learn-more primary-txt'>{`${sale.link_text_override ? sale.link_text_override : settings?.mvk_theme_config?.labels?.learn_more ? settings?.mvk_theme_config?.labels?.learn_more : 'LEARN MORE'} >`}</div>
            </div>
        </Clicker>
    )
}

export default Start
