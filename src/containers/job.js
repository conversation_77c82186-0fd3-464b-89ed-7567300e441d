import React, { useState, useEffect, useContext } from 'react'
import { decode } from 'html-entities';


import './job.scss'

// Helpers
const Imaging = React.lazy(() => import('src/helpers/imaging'));
const Clicker = React.lazy(() => import('src/helpers/clicker'));
const HtmlParser = React.lazy(() => import('src/helpers/html-parser'));

// Context
import { SettingsContext } from "src/context";

const Start = ({ page }) => {
    const [settings, setSettings] = useContext(SettingsContext);

    return (
        <div>
            {(settings?.job && settings.job.hero) &&
                <div class='job-hero'>
                    <Imaging data={settings.job.hero_image} />
                </div>
            }
            <div class='grid-container'>
                <div class="job-detail__module">
                    <div class={`grid-container flexbox wrap ${settings.job.style} ${page.mvk_item_content.custom_fields.thumbnail_image ? 'with-image' : ''}`}>
                        <TextContent page={page} />
                        {page.mvk_item_content.custom_fields.thumbnail_image &&
                            <MediaItems page={page} />
                        }
                    </div>
                </div>
            </div>
        </div>
    );
}

const TextContent = ({ page }) => {
  
    return (
        <div className="job-detail-text-content">
            <h1 className="job-detail-title primary-txt" dangerouslySetInnerHTML={{ __html: page?.mvk_item_content?.title }} />
            {page?.mvk_item_content?.custom_fields?.related_store &&
                <h2 className="job-detail-subtitle">Posted for: <Clicker class='related-store' type='anchor' url={page?.mvk_item_content?.custom_fields?.related_store?.url}>{decode(page?.mvk_item_content?.custom_fields?.related_store?.title)}</Clicker></h2>
            }
            {/* {page.mvk_item_tax.job_type && page.mvk_item_tax.job_type.map(type => <h2 className="job-detail-subtitle" dangerouslySetInnerHTML={{ __html: type.name }} />)} */}
            {page?.mvk_item_content?.job_post_date &&
                <p><strong>Post Date: </strong>{page.mvk_item_content.job_post_date}</p>
            }
            <div className="job-detail-description">
                {page?.mvk_item_content.content && <div className="content-wrapper"><HtmlParser html={page.mvk_item_content.content} /></div>}
            </div>
            {page?.mvk_item_content?.custom_fields?.contact_info && <div className='contact-info'><strong>Contact:</strong> <HtmlParser html={page.mvk_item_content.custom_fields.contact_info} /></div>}
        </div>
    )
};

const MediaItems = ({ page }) => {

    const imageData = {
        url: page.mvk_item_content.custom_fields.thumbnail_image,
        alt: `${page.mvk_item_content.title}`
    }

    return (
        <div className="job-detail-media-items">
            {page.mvk_item_content.custom_fields.thumbnail_image && <Imaging className="job-detail-image" data={imageData} />}
        </div>
    );
}

export default Start
