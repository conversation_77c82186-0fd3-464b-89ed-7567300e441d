import React, { useContext, useState, useEffect } from "react";
import { decode } from 'html-entities';

// PARTIALS.
import Loading from "src/partials/loading";
const Imaging = React.lazy(() => import('src/helpers/imaging'));
const Clicker = React.lazy(() => import('src/helpers/clicker'));

// APIs
import * as API from "src/api/requests";
import { SettingsContext } from 'src/context';
// Styles
import { ImageWrapper } from './styles';

const Start = ({ searchText }) => {
    const [settings, setSettings] = useContext(SettingsContext);
    const [results, setResults] = useState({ loading: true });
    const [searchTerm, setSearchTerm] = useState(searchText);
    let headers = {
        'MVK-BUILDER': settings.mvk_builder ? settings.mvk_builder : 'charter',
        'Content-Type': 'application/json'
    }
    useEffect(() => {   
        API.Search(searchText, headers).then(response => {
            setResults(response);
        }).catch(error => console.log(error));
        if (searchText !== searchTerm) {
            setResults({ loading: true });
        }
    }, [searchText]);

    if (results.loading) {
        return (<Loading type="loading-text" text='Getting Search Results...' />);
    } else {
        return (<Container term={searchText} results={results} settings={settings} />);
    }
};

const Container = ({ term, results, settings }) => {
    return (
        <div id="search-results" class="grid-container">
            <div className="grid-x grid-margin-x">
                <div className="cell">
                    <h1>Your Results</h1>
                    <p class="search-title">{decode(results.response)}</p>
                </div>
                {results.results && results.results.map((result, index) => <Result data={result} settings={settings} />)}
            </div>
        </div>
    );
};

const Result = ({ data, settings }) => {
    
    return (
        <>
            <div className='cell medium-4'>
                <ImageWrapper className={`image-wrapper ${data.thumbnail ? 'featured' : 'site-logo'}`} backgroundColor={settings.design.colors.main_nav.nav_bg_color}>
                    <Imaging data={data.thumbnail ? data.thumbnail : settings?.branding?.main_logo} />
                </ImageWrapper>
            </div>
            <div className='cell medium-8'>
                <div className='content-wrapper'>
                    {data.title && <h3 class="title">{decode(data.title)}</h3>}
                    {data.excerpt &&
                        <p dangerouslySetInnerHTML={{ __html: data.excerpt }} />
                    }
                    <Clicker className="link" type='anchor' url={data.url}>{data.url.replace(/(^\w+:|^)\/\//, '')}</Clicker>
                </div>
            </div>
        </>
    );
};

export default Start;
