import styled from 'styled-components';

export const PropertyDetails = styled.div`
    padding-top: 1rem;
    padding-bottom: 2rem;
    .back-to-listing {
        color: ${props => props.bodyCopyColor};
        display: block;
        margin-bottom: 1rem;
        text-transform: uppercase;
        text-decoration: underline;
    }
    .content-wrapper {
        @media (min-width: 640px) {
            h1 {
                padding-right: 20px; // because of the pseudo element to the right
            }
        }
        & > * {
            margin-bottom: 1rem;
            display: block;
        }
        .address, .website {
            letter-spacing: 2px;
            text-transform: uppercase;
            @media (min-width: 768px) {
                font-size: 1.125rem;
            }
        }
        .website {
            text-decoration: underline;
        }
    }
    .contact-wrapper {
        .contact {
            position: relative;
            padding: 2rem 1.5rem;
            &:before {
                content: '';
                position: absolute;
                left: -20px;
                top: 0;
                width: 20px;
                height: 100%;
                background: ${props => props.primaryColor};
            }
            &:after {
                content: '';
                position: absolute;
                right: -10000px;
                top: 0;
                width: 10000px;
                height: 100%;
                background: ${props => props.backgroundColor};
            }
            & > * {
                margin-bottom: .25rem;
            }
            .label {
                margin-bottom: 1rem;
                text-transform: uppercase;
                letter-spacing: 2px;
            }
            a {
                display: block;
            }
            @media (min-width: 640px) {
                .email {
                    overflow-wrap: break-word;
                }
            }
        }
    }
    .button-wrapper .group {
        padding: 1rem 0;
        a {
            margin: 1rem auto;
        }
        @media (min-width: 768px) {
            text-align: center;
            a {
                margin: 1rem .5rem;
            }
        }
        @media (min-width: 1024px) {
            a {
                margin: 1rem;
            }
        }
    }
    .property-copy {
        margin-top: 2rem;
    }
    @media (min-width: 640px) {
        .button-wrapper {
            order: 3;
        }
    }
`;

export const ImageGallery = styled.div`
    margin-bottom: 2rem;
    .slick-list {
        margin: 2rem -20px;
        .slick-slide img {
            min-height: 360px;
            object-fit: cover;
        }
        @media (min-width: 768px) and (max-width: 1199px) {
            padding: 0 20% 0 0 !important;
        }
        @media (min-width: 768px) {
            margin: 2rem -10px;
            overflow: visible;
            .slick-slide img {
                height: 360px;
            }
            &:after {
                content: '';
                position: absolute;
                height: 100%;
                width: 100%;
                position: absolute;
                background: #fff;
                top: 0;
                right: 100%;
            }
        }
        .slick-slide > div {
            margin: 0 10px;
        }
    }
    .slick-arrow {
        &.slick-prev {
            left: -10px;
            @media (min-width: 768px) {
                left: 0;
            }
        }
        &.slick-next {
            right: -10px;
            @media (min-width: 768px) {
                right: 0;
            }
        }
    }
    .zoomed-image {
        position: fixed;
        top: 0;
        bottom: 0;
        left: 0;
        right: 0;
        background: rgba(0,0,0,.8);
        z-index: 999999;
        img {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%,-50%);
        }
    }
`;

export const AvailableSpaces = styled.div`
    margin: 2rem auto;
    h2 {
        margin-bottom: 1rem;
        @media (min-width: 768px) {
            margin-bottom: 2rem;
        }
    }
    table {
        max-width: 900px;
        margin: auto;
        width: 100%;
        text-align: center;
        border-spacing: 0;
        th, td {
            padding: 0.5rem 0.25rem;
        }
        tr:nth-child(even) {
            background: ${props => props.backgroundColor};
        }
        .site-plan {
            a {
                color: ${props => props.bodyCopyColor};
            }
        }
        .video {
            & > * {
                cursor: pointer;
            }
            img {
                max-width: 30px;
                display: block;
                margin: auto;
            }
            & > svg {
                color: ${props => props.bodyCopyColor};
            }
            .video-lightbox {
                text-align: left;
            }
        }
    }

`;

export const Highlights = styled.div`
    background-color: ${props => props.backgroundColor};
    padding-top: 2.25rem;
    padding-bottom: 2.25rem;

    @media (min-width: 640px) {
        padding-top: 3rem;
        padding-bottom: 3rem;
    }

    .title-wrapper {
        margin-bottom: 1rem;
        color: ${props => props.primaryColor};

        @media (min-width: 640px) {
            margin-bottom: 2rem;
        }
    }
    
    .highlight {
        display: grid;
        grid-template-columns: 1fr 4fr;
        position: relative;
        padding: 1rem 0;
        @media (min-width: 640px) {
            .title-blurb-wrapper {
                margin-right: .5rem;
            }
        }
        &:after {
            content: "";
            position: absolute;
            bottom: 0;
            display: block;
            background-color: ${props => props.lineColor};
            
            @media (max-width: 639px) {
                left: 50%;
                transform: translateX(-50%);
                width: 70%;
                height: 1px;
            }

            @media (min-width: 640px) {
                left: unset;
                right: 0;
                transform: none;
                width: 1px;
                height: 100%;
            }
        }

        &:last-child:after {
            background-color: transparent;
        }

        img {
            @media (min-width: 640px) {
                justify-self: center;
            }
        }
    }

    .highlight-title {
        margin-bottom: .25rem;
        color: ${props => props.primaryColor};
        @media (max-width: 1024px) {
            font-size: 1.125rem;
        }
    }

    .highliht-blurb {
        color: ${props => props.primaryColor};
    }
`

export const KeyTenants = styled.div`
    padding-top: 2rem;
    padding-bottom: 2rem;

    @media (min-width: 1024px) {
        padding-top: 3rem;
        padding-bottom: 3rem;
    }

    .key-tenants-title {
        margin-bottom: 2rem;
        color: ${props => props.primaryColor};
    }

    .slick-list {
        max-width: 80%;
        margin: 0 auto;
    }

    .slick-track {
        display: flex;
        align-items: center;
    }

    .slick-slide {
        margin: 0 1rem;
        img {
            margin: auto;
        }
        @media (min-width: 1024px) {
            margin: 0 3rem;
        }
    }
`

export const SitePlan = styled.div`
    margin: 2rem auto;
    text-align: center;
    h2 {
        margin-bottom: 1rem;
        @media (min-width: 768px) {
            margin-bottom: 2rem;
        }
    }
    img {
        width: 100%;
        max-width: 1100px;
    }
    @media (max-width: 767px) {
        .image-container {
            padding: 0;
        }
    }
    @media (min-width: 1024px) {
        margin: 3rem auto;
    }
`;