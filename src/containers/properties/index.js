import React, { useState, useEffect, useContext } from 'react'
import { decode } from 'html-entities';

// Helpers
const Imaging = React.lazy(() => import('src/helpers/imaging'));
const Clicker = React.lazy(() => import('src/helpers/clicker'));
// Context
import { SettingsContext } from "src/context";
// Styles
import './index.scss';

// Components
const PropertyDetails = React.lazy(() => import('./sections/property-details'));
const ImageGallery = React.lazy(() => import('./sections/image-gallery'));
const KeyTenants = React.lazy(() => import('./sections/key-tenants'));
const Highlights = React.lazy(() => import('./sections/highlights'));
const Video = React.lazy(() => import('./sections/video'));
const SitePlan = React.lazy(() => import('./sections/site-plan'));
const AvailableSpaces = React.lazy(() => import('./sections/available-spaces'));
const Button = React.lazy(() => import('src/partials/button'));

const Start = ({ data }) => {
    const [settings, setSettings] = useContext(SettingsContext);

    return (
        <div className='property-detail__template'>
            {data.mvk_item_content?.custom_fields?.hero_image &&
                <div className='property-hero'>
                    <Imaging data={data.mvk_item_content?.custom_fields?.hero_image} />
                </div>
            }
            <PropertyDetails data={data.mvk_item_content} settings={settings} />
            {data.mvk_item_content?.custom_fields?.image_gallery?.images &&
                <ImageGallery data={data.mvk_item_content?.custom_fields?.image_gallery} settings={settings} />
            }
            {data.mvk_item_content?.custom_fields?.key_tenants &&
                <KeyTenants data={data.mvk_item_content?.custom_fields?.key_tenants} settings={settings} />
            }
            {data.mvk_item_content?.custom_fields?.highlights &&
                <Highlights data={data.mvk_item_content?.custom_fields?.highlights} heading={data.mvk_item_content?.custom_fields?.highlights_heading} settings={settings} />
            }
            {data.mvk_item_content?.custom_fields?.video &&
                <Video data={data.mvk_item_content?.custom_fields} settings={settings} />
            }
            {(data.mvk_item_content?.custom_fields?.site_plan?.site_plan_image || data.mvk_item_content?.custom_fields?.site_plan?.type === 'interactive-map') &&
                <SitePlan data={data.mvk_item_content?.custom_fields?.site_plan} settings={settings} />
            }
            {data.mvk_item_content?.custom_fields?.available_spaces &&
                <AvailableSpaces data={data.mvk_item_content?.custom_fields?.available_spaces} heading={data.mvk_item_content?.custom_fields?.available_space_heading} settings={settings} />
            }
            {(data.mvk_item_content?.custom_fields?.site_plan?.site_plan_image || data.mvk_item_content?.custom_fields?.download_site_plan?.file) &&
                <div className='grid-container download-site-plan'>
                    <div className='grid-x'>
                        <div className='cell center'>
                            <Button title={data.mvk_item_content?.custom_fields?.download_site_plan?.button_text} url={data.mvk_item_content?.custom_fields?.download_site_plan?.file ? data.mvk_item_content?.custom_fields?.download_site_plan?.file?.url : data.mvk_item_content?.custom_fields?.site_plan?.site_plan_image?.url} target='_blank' type={settings.property?.button_style} />
                        </div>
                    </div>
                </div>
            }
        </div>
    );
}

export default Start;