.property-detail__template {
    overflow: hidden;
    .property-hero img {
        width: 100%;
        min-height: 300px;
        object-fit: cover;
    }
    .social-media {
        a {
            margin-right: 0.75rem;
        }
    }
    .download-site-plan.grid-container {
        margin-bottom: 2rem;
    }
    @media (max-width: 767px) {
        .fullwidth-video {
            .content-wrapper {
                display: none;
            }
        }
    }
    @media (min-width: 768px) {
        .fullwidth-video {
            .inner-wrapper {
                min-height: 300px;
            }
        }
    }
    @media (min-width: 1200px) {
        .fullwidth-video {
            .inner-wrapper {
                min-height: 300px;
            }
        }
    }
}
