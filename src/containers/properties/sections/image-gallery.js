import React, { useState } from "react";
import Slider from "react-slick";
// Styles
import "slick-carousel/slick/slick.scss";
import "slick-carousel/slick/slick-theme.scss";
import { ImageGallery } from '../styles';
// Partials
const Button = React.lazy(() => import('src/partials/button'));
// Helpers
import Imaging from 'src/helpers/imaging';
import { PrevArrow, NextArrow } from 'src/helpers/slick';

const Start = ({ data, settings }) => {

    return (
        <ImageGallery className='image-gallery grid-container'>
            <div className='grid-x'>
                {data.images &&
                    <div className='cell'>
                        <Gallery data={data} />
                    </div>
                }
                {data.button &&
                    <div className='cell center'>
                        <Button class='gallery-button' title={data.button?.title} url={data.button?.url} type={settings.property.button_style} />
                    </div>
                }
            </div>
        </ImageGallery>
    )

}

const Gallery = ({ data }) => {
    const [zoom, setZoom] = useState(false);

    let sliderSettings = {
        slidesToShow: 3,
        slidesToScroll: 1,
        arrows: true,
        dots: false,
        adaptiveHeight: true,
        nextArrow: <NextArrow />,
        prevArrow: <PrevArrow />,
        responsive: [
            {
                breakpoint: 1024,
                settings: {
                    slidesToShow: 2,
                    centerMode: true
                }
            },
            {
                breakpoint: 768,
                settings: {
                    slidesToShow: 1
                }
            },
        ]
    };

    function galleryZoom(index) {
        setZoom(index);
    }

    return (
        <>
            <Slider ref={(a) => a} {...sliderSettings}>
                {data.images?.map((image, index) =>
                    <Imaging index={index} data={image} onClick={() => galleryZoom(index + 1)} />
                )}
            </Slider>
            {zoom &&
                <div className='zoomed-image' onClick={() => galleryZoom(false)}>
                    <Imaging data={data.images[zoom - 1]} />
                </div>
            }
        </>
    );
}


export default Start;