import React from "react";

// Styles
import { Highlights } from '../styles';
// Helpers
const Imaging = React.lazy(() => import('src/helpers/imaging'));
import { ColumnClass } from 'src/helpers/foundation';
import { Coloring } from "src/helpers";

const Start = ({ data, heading, settings }) => {
    return (
        <Highlights
            className="highlights-section"
            primaryColor={settings?.design?.colors?.primary_color}
            backgroundColor={settings?.design?.colors?.background_color}
            lineColor={settings?.mvk_theme_config?.other?.separator_line_color ? Coloring(settings?.mvk_theme_config?.other?.separator_line_color, settings) : settings?.design?.colors?.secondary_color}
        >
            <div class='grid-container grid-x'>
                <div class='title-wrapper cell center'>
                    <h2>{heading ?? 'Highlights'}</h2>
                </div>

                <div class='highlight-container grid-x'>
                    {data && data?.map((item, index) => (
                        <div key={index} class={`highlight ${ColumnClass(data?.length?.toString())}`}>
                            {item?.highlight_icon && <Imaging data={item?.highlight_icon} />}
                            <div class='title-blurb-wrapper'>
                                {item?.title && <h3 class='highlight-title'>{item?.title}</h3>}
                                {item?.blurb && <p class='highlight-blurb'>{item?.blurb}</p>}
                            </div>
                        </div>
                    ))}
                </div>
            </div>
        </Highlights>
    );
}

export default Start;