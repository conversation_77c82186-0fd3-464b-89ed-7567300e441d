import React, { useState } from "react";
import { decode } from 'html-entities';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faFile, faCirclePlay } from "@fortawesome/free-solid-svg-icons";

// Helpers
const Imaging = React.lazy(() => import('src/helpers/imaging'));
// Partials
import { Video } from 'src/partials/video';
// Styles 
import { AvailableSpaces } from '../styles';

const Start = ({ data, heading, settings }) => {
    return (
        <AvailableSpaces
            className='available-spaces grid-container'
            backgroundColor={settings?.design?.colors?.background_color}
            bodyCopyColor={settings?.design?.colors?.body_copy_color}
        >
            <div className="grid-x">
                <div className="cell center">
                    <h2 className="primary-txt">{heading ?? 'Available Space'}</h2>
                </div>
                <div className="cell">
                    <Table data={data} settings={settings} />
                </div>
            </div>
        </AvailableSpaces>
    );
}


const Table = ({ data, settings }) => {

    return (
        <table className='as-table'>
            <tr className='primary-txt'>
                <th>RENTAL UNIT</th>
                <th>SQ. FT.</th>
                <th>SITE PLAN</th>
                <th>VIDEO</th>
            </tr>
            {data?.map(row => <Row data={row} settings={settings} />)}
        </table>
    )
}

const Row = ({ data, settings }) => {

    return (
        <tr>
            <td className='space-name'>{data.space_name ? decode(data.space_name) : null}</td>
            <td className='sq-ft'>{data.available_sq_ft ? decode(data.available_sq_ft) : null}</td>
            <td className='site-plan'>{data.site_plan ?
                <a href={data.site_plan.url} target='_blank'>
                    <FontAwesomeIcon icon={faFile} />
                </a>
                : null}
            </td>
            <td className='video'>{data.video ?
                <VideoPopup data={data} settings={settings} />
                : null}
            </td>
        </tr>
    )
}

const VideoPopup = ({ data, settings }) => {
    const [videoLightbox, setLightBox] = useState(false);

    const toggleVideo = () => {
        setLightBox(videoLightbox ? false : true);
    }

    return (
        <>
            {settings.video?.play_button ?
                <Imaging className='play-button' data={settings.video?.play_button} onClick={toggleVideo} />
                :
                <FontAwesomeIcon className='play-button' icon={faCirclePlay} onClick={toggleVideo} />
            }
            {videoLightbox &&
                <Video data={data} closeVideo={toggleVideo} />
            }
        </>
    )
}


export default Start;