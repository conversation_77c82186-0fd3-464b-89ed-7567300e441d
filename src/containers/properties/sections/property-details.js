import React from "react";
import { decode } from 'html-entities';

// Helpers
import Clicker from 'src/helpers/clicker';
// Partials
import { Icon } from 'src/partials/social-icons/social';
import Button from 'src/partials/button';
import HtmlParser from 'src/helpers/html-parser';
// Styles
import { PropertyDetails } from '../styles';


const Start = ({ data, settings }) => {

    return (
        <PropertyDetails
            className='property-details grid-container'
            backgroundColor={settings?.design?.colors?.background_color}
            primaryColor={settings?.design?.colors?.primary_color}
            bodyCopyColor={settings?.design?.colors?.body_copy_color}
        >
            <div className='grid-x'>
                <div className='cell'>
                    {settings?.property?.back_to_listing_link &&
                        <Clicker className='back-to-listing' type='anchor' url={settings?.property?.back_to_listing_link?.url}>{decode(settings?.property?.back_to_listing_link?.title) || 'RETURN TO LISTINGS'}</Clicker>
                    }
                </div>
                <div className='content-wrapper cell medium-8'>
                    <h1 className='primary-txt'>{decode(data.title)}</h1>
                    <div className='address'>
                        {data.custom_fields?.address?.street && <><span>{decode(data.custom_fields?.address?.street)}</span><br /></>}
                        {data.custom_fields?.address?.city && <span>{decode(data.custom_fields?.address?.city)}, </span>}
                        {data.custom_fields?.address?.state && <span>{decode(data.custom_fields?.address?.state)} </span>}
                        {data.custom_fields?.address?.zip && <span>{decode(data.custom_fields?.address?.zip)}</span>}
                    </div>
                    {data.custom_fields?.website &&
                        <a className='website primary-txt' href={data.custom_fields?.website} target='_blank'>{data.custom_fields?.website.replace(/^https?:\/\//, '')}</a>
                    }
                    {data.custom_fields?.social_media &&
                        <div className='social-media'>
                            {data.custom_fields?.social_media.map(icon => <Icon data={icon} settings={settings} />)}
                        </div>
                    }
                </div>
                {data.custom_fields?.links_and_files &&
                    <div className='button-wrapper cell'>
                        <div className='group'>
                            {data.custom_fields?.links_and_files.map(link => <LinkOrFile data={link} />)}
                        </div>
                    </div>
                }
                {data.custom_fields?.contacts &&
                    <div className='contact-wrapper cell medium-4'>
                        {data.custom_fields?.contacts.map(contact => <Contact data={contact} />)}
                    </div>
                }
            </div>
            {data.custom_fields?.copy &&
                <div class='grid-x property-copy'>
                    <HtmlParser html={data.custom_fields?.copy} />
                </div>
            }
        </PropertyDetails >
    );
}

const LinkOrFile = ({ data }) => {
    if (data.type === 'link' && data.link_field) {
        return (<Button url={data.link_field?.url} target={data.link_field?.target} title={data.link_field?.title} type={data.button_style} />)
    } else if (data.type === 'file' && data.file) {
        return (<Button url={data.file?.url} target={'_blank'} title={data.button_text || 'Download'} type={data.button_style} />)
    } else {
        return <div />
    }
}

const Contact = ({ data }) => {
    return (
        <div className='contact background-bg'>
            {data.label &&
                <div className='label primary-txt'>{decode(data.label)}</div>
            }
            {data.name &&
                <div className='name'>{decode(data.name)}</div>
            }
            {data.phone_number &&
                <a className='phone body-copy-txt' href={`tel:${data.phone_number}`}>{decode(data.phone_number)}</a>
            }
            {data.email &&
                <a className='email body-copy-txt' href={`mailto:${data.email}`} target='_blank'>{decode(data.email)}</a>
            }
        </div>
    )
}

export default Start;