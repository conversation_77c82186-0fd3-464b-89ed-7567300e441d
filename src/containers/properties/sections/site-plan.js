import React from "react";

// Helpers
const Imaging = React.lazy(() => import('src/helpers/imaging'));
// Partials
import Map from 'src/partials/map';
import MappedIn from 'src/partials/map/mapped-in';
import MappedInFull from 'src/partials/map/mapped-in-full';
import MapAcquireDigital from 'src/partials/map/acquire-digital';
// Styles
import { SitePlan } from '../styles';

const Start = ({ data, settings }) => {
    return (
        <SitePlan className='site-plan'>
            <div className="grid-container">
                <div className="grid-x">
                    <div className="cell center">
                        <h2 className="primary-txt">{data.heading ?? 'Site Plan'}</h2>
                    </div>
                </div>
            </div>
            {(data.type === 'image' && data.site_plan_image) &&
                <div className="image-container grid-container">
                    <div className="grid-x">
                        <div className="cell center">
                            <Imaging className='site-plan-image' data={data.site_plan_image} />
                        </div>
                    </div>
                </div>
            }
            {data.type === 'interactive-map' &&
                <>
                    {(settings.store?.store_map_type === 'mapplic' && settings.mapplic_id) &&
                        <div className='grid-container'>
                            <Map id={settings.mapplic_id} html={settings.mapplic_html} />
                        </div>
                    }
                    {(settings.store?.store_map_type === 'mappedin' && data.interactive_map_id) &&
                        <MappedIn settings={settings} locationId={data.interactive_map_id} />
                    }
                    {(settings.store?.store_map_type === 'mappedin' && !data.interactive_map_id) &&
                        <div class='mappedin-container'><MappedInFull settings={settings} /></div>
                    }
                    {settings.store?.store_map_type === 'acquire-digital' &&
                        <MapAcquireDigital locationID={data.interactive_map_id} />
                    }
                </>
            }
        </SitePlan>
    );
}

export default Start;