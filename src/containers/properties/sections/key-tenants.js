import React from "react";
import Slider from "react-slick";

// Styles
import "slick-carousel/slick/slick.scss";
import "slick-carousel/slick/slick-theme.scss";
import { KeyTenants } from '../styles';
// Helpers
import Imaging from 'src/helpers/imaging';
import Clicker from 'src/helpers/clicker';
import { PrevArrow, NextArrow } from 'src/helpers/slick';

const Start = ({ data, settings }) => {

    let sliderSettings = {
        slidesToShow: data?.length > 5 ? 5 : data?.length,
        slidesToScroll: 1,
        arrows: true,
        dots: false,
        adaptiveHeight: false,
        nextArrow: <NextArrow />,
        prevArrow: <PrevArrow />,
        responsive: [
            {
                breakpoint: 1024,
                settings: {
                    slidesToShow: 3
                }
            },
            {
                breakpoint: 768,
                settings: {
                    slidesToShow: 2
                }
            },
        ]
    };

    return (
        <KeyTenants
            className="key-tenants grid-container"
            primaryColor={settings?.design?.colors?.primary_color}
        >
            <div class='grid-x'>
                <h2 class='key-tenants-title cell center'>{settings?.property?.featured_tenant_title}</h2>

                {data &&
                    <Slider className="cell" ref={(a) => a} {...sliderSettings}>
                        {data?.map((slide, index) =>
                            <Clicker key={index} type={slide.tenant_link ? 'anchor' : null} class='tenant' url={slide.tenant_link ? slide.tenant_link.url : null} target={slide.tenant_link ? slide.tenant_link.target : null}>
                                <Imaging data={slide?.logo} forceLoad={true} />
                            </Clicker>
                        )}
                    </Slider>
                }
            </div>
        </KeyTenants>
    );
};

export default Start;