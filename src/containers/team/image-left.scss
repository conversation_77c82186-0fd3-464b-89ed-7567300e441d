:root {
    --heading-line-height: 1.2;
    /* capital letters - used in combo with the lhCrop mixin */
    --font-primary-capital-letter: 0.75;
}

/* lhCrop mixin - crop top space on text elements */
@mixin lhCrop($line-height, $capital-letter: 1) {
    &::before {
        content: "";
        display: block;
        height: 0;
        width: 0;
        margin-top: calc((#{$capital-letter} - #{$line-height}) * 0.5em);
    }
}

.team__image-left {
    .inner-container {
        padding-top: 5%;
        padding-bottom: 2rem;

        @media screen and (min-width: 992px) {
            display: grid;
            grid-auto-flow: row;
            grid-template-columns: 1fr 3fr;
            grid-column-gap: 5%;
            padding-bottom: 3.75rem;
        }
    }

    .column-one {
        @media screen and (max-width: 991px) {
            margin-bottom: 1.5625rem;
            display: flex;
            justify-content: center;
        }
    }
    .team-heading {
        @media screen and (min-width: 992px) {
            line-height: var(--heading-line-height);
            /* lhCrop mixin - crop top space on text elements */
            @include lhCrop(
                var(--heading-line-height),
                var(--font-primary-capital-letter)
            );
        }
    }

    .team-content {
        p {
            line-height: 1.875rem;
        }
    }
}
