import React from 'react';

// Helpers
const Imaging = React.lazy(() => import('src/helpers/imaging'));
const HtmlParser = React.lazy(() => import('src/helpers/html-parser'));

import { decode } from 'html-entities';


import './image-left.scss';

const Start = ({ page, settings }) => {
    return (
        <main class='team__image-left'>
            <div class='inner-container grid-container'>
                <div class='column-one'>
                    <Imaging class='team-feat-image' data={page.mvk_item_content.feat_image} />
                </div>
                
                <div class='column-two'>
                    <h1 class='team-heading'>{page.mvk_item_content.title}</h1>
                    <div class='team-content'>
                        <HtmlParser html={decode(page.mvk_item_content.content)} />
                    </div>
                </div>
            </div>
        </main>
    );
};

export default Start;