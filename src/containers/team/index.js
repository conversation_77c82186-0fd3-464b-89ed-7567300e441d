import React, { Suspense } from 'react';

const Default = React.lazy(() => import('./default'));
const ImageLeft = React.lazy(() => import('./image-left'));

const Start = ({ page, settings }) => {
    const style = settings?.team?.style ? settings?.team?.style : '';
    
    return (
        <Suspense fallback={<div />}>
            {style === 'default' && <Default page={page} settings={settings} />}
            {(!style || style === 'image_left') && <ImageLeft page={page} settings={settings} />}
        </Suspense>
    );
};

export default Start;