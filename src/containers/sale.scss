@import "src/scss/variables.scss";

.sale-detail__module {
    margin: 30px 0;
    padding-top: 2rem;

    @media (min-width: $break-medium) {
        .sale-detail-text-content {
            width: 60%;
        }
        .with-image {
            &.image-left-content-right .sale-detail-text-content {
                order: 2;
                margin-left: 2rem;
            }
        }
    }

    .sales-breadcrumb {
        text-decoration: underline;
        display: block;
        width: fit-content;
        margin-bottom: 2rem;
    }

    .sale-detail-subtitle,
    .sale-date {
        margin-bottom: 1rem;
    }

    .sale-detail-description {
        margin: 0 0 20px 0;
        max-width: 700px;

        @media (min-width: $break-medium) {
            margin-bottom: 30px;
        }
    }

    .related-store {
        text-decoration: underline;
        font-size: 1.5rem;
    }

    .sale-detail-media-items {
        width: 100%;
        .sale-detail-image {
            width: 100%;
        }
        @media (min-width: $break-medium) {
            flex: 1;
        }
    }
}
.sale-hero img {
    width: 100%;
}
