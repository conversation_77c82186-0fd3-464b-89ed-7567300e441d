import React, { useState, useEffect, useContext } from 'react'
import { decode } from 'html-entities';


import './sale.scss'

// Helpers
const Imaging = React.lazy(() => import('src/helpers/imaging'));
const Clicker = React.lazy(() => import('src/helpers/clicker'));
import { GetYYMMDD, FormattedDate, DateRange } from 'src/helpers/date';
const HtmlParser = React.lazy(() => import('src/helpers/html-parser'));

// Context
import { SettingsContext } from "src/context";

const Start = ({ page }) => {
    const [settings, setSettings] = useContext(SettingsContext);
    const [salesLink, setSalesLink] = useState(settings?.sale?.sales_link ? settings?.sale?.sales_link?.url : settings?.sales_link?.url)
    const [salesLinkTitle] = useState(settings?.sale?.sales_link ? settings?.sale?.sales_link?.title : settings?.sales_link?.title)
    useEffect(() => {
        setSalesLink(settings.current_location && page.post_type_parent_slug ? `/${settings.current_location}/${page.post_type_parent_slug}/` : (settings?.sale?.sales_link ? settings?.sale?.sales_link?.url : settings?.sales_link?.url))
    }, [settings.current_location])

    return (
        <div>
            {(settings?.sale && settings.sale.hero) &&
                <div class='sale-hero'>
                    <Imaging data={settings.sale.hero_image} />
                </div>
            }
            <div class='grid-container'>
                <div class="sale-detail__module">
                    {(settings?.sale?.enable_back_to_sales_button || settings?.enable_back_to_sales_button) &&
                        <Clicker type='anchor' class='sales-breadcrumb primary-txt' url={salesLink} title={salesLinkTitle}>{decode(salesLinkTitle)}</Clicker>
                    }
                    <div class={`grid-container flexbox wrap ${settings?.sale?.style} ${page?.mvk_item_content?.custom_fields.featured_image ? 'with-image' : ''}`}>
                        <TextContent page={page} />
                        {page.mvk_item_content.custom_fields.featured_image &&
                            <MediaItems page={page} />
                        }
                    </div>
                </div>
            </div>
        </div>
    );
}

const TextContent = ({ page }) => {
    let dateString = '';
    let startDate = page?.mvk_item_content?.custom_fields?.start_date ? page.mvk_item_content.custom_fields.start_date : '';
    let endDate = page?.mvk_item_content?.custom_fields?.end_date ? page.mvk_item_content.custom_fields.end_date : '';

    if (!page?.mvk_item_content?.custom_fields?.date_display_hide && (startDate || endDate)) {
        startDate = startDate.replace(/(\d{4})(\d{2})(\d{2})/g, '$1-$2-$3');
        endDate = endDate.replace(/(\d{4})(\d{2})(\d{2})/g, '$1-$2-$3');
        dateString = (startDate && endDate) && (startDate !== endDate) ? DateRange(startDate, endDate) : FormattedDate(startDate, 'day-month-date');
    }
    
    return (
        <div className="sale-detail-text-content">
            <h1 className="sale-detail-title primary-txt" dangerouslySetInnerHTML={{ __html: page?.mvk_item_content?.title }} />
            {page?.mvk_item_content?.custom_fields?.type_of_deal && <h2 className="sale-detail-subtitle" dangerouslySetInnerHTML={{ __html: page.mvk_item_content.custom_fields.type_of_deal }} />}
            {dateString &&
                <h3 class='sale-date'>{dateString}</h3>
            }
            {page?.mvk_item_content?.custom_fields?.related_store &&
                <p><Clicker class='related-store' type='anchor' url={page.mvk_item_content.custom_fields.related_store.url}>{decode(page.mvk_item_content.custom_fields.related_store.title)}</Clicker></p>
            }
            {page?.mvk_item_content?.custom_fields?.post_copy && <div className="sale-detail-description"><HtmlParser html={page.mvk_item_content.custom_fields.post_copy} /></div>}
        </div>
    )

    
};

const MediaItems = ({ page }) => {

    const data = {
        url: page?.mvk_item_content?.global ? page?.mvk_item_content?.custom_fields?.featured_image?.url : page?.mvk_item_content?.custom_fields?.featured_image,
        alt: `${page?.mvk_item_content.title} icon`
    }

    return (
        <div className="sale-detail-media-items">
            {page?.mvk_item_content?.custom_fields?.featured_image && <Imaging className="sale-detail-image" data={data} />}
        </div>
    );
}

export default Start
