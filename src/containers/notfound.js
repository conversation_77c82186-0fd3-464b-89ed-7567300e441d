import React, { useContext } from "react";
import { Helm<PERSON>, <PERSON>lm<PERSON><PERSON><PERSON>ider } from 'react-helmet-async';

const Button = React.lazy(() => import('src/partials/button'));
import { SettingsContext } from "src/context";
const HtmlParser = React.lazy(() => import('src/helpers/html-parser'));

import './notfound.scss'

const Start = ({ data }) => {
    const [settings, setSettings] = useContext(SettingsContext);

    if (window.location.pathname.length > 1) {
        const pathArray = window.location.pathname.split('/');
        cache.subsite = pathArray[1];
    }

    // var webaddress = cache.fleet ? `${location.protocol}//${window.location.hostname}/${cache.subsite}/` : `${location.protocol}//${window.location.hostname}`;
    var webaddress = `${location.protocol}//${window.location.hostname}`;

    return (
        <>
            <HelmetProvider>
                <Helmet defer={false}>
                    <title>{settings.not_found && settings.not_found["404_title"] ? settings.not_found["404_title"] : 'Page not found.'}</title>
                    <meta name="robots" content="noindex" />
                </Helmet>
            </HelmetProvider>
            <div id="notfound">
                <h1 class="title primary" style={{ fontFamily: cache.fonts.header }}>{settings.not_found && settings.not_found["404_title"] ? settings.not_found["404_title"] : 'Page not found.'}</h1>
                {(settings.not_found && settings.not_found["404_content"]) &&
                    <div class="message" style={{ fontFamily: cache.fonts.body }}>
                        <HtmlParser html={settings.not_found["404_content"]} />
                    </div>
                }
                <div>
                    <Button title={settings?.not_found && settings?.not_found['404_back_to_home_label'] ? settings?.not_found['404_back_to_home_label'] : 'Back To Home'} url={webaddress} icon='icon-one' />
                </div>
            </div>
        </>
    );
};

export default Start;
