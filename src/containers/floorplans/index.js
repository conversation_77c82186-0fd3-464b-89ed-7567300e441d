import React, { useState, useEffect, useContext } from 'react'
import { decode } from 'html-entities';
import { SettingsContext } from "src/context";

// Styles
import * as S from './styles'
// Sections
const Details = React.lazy(() => import('./sections/details'));
const Tabs = React.lazy(() => import('./sections/tabs'));
const Gallery = React.lazy(() => import('./sections/gallery'));

const Start = ({ data }) => {
    const [settings, setSettings] = useContext(SettingsContext);

     useEffect(() => {
        setSettings({ ...settings, ['with_hero']: false })
     }, []);
    
    return (
        <S.Floorplan>
            <div className='grid-container'>
                <div className='grid-x grid-margin-x'>
                    <div className='large-4 cell'>
                        <Details data={data.mvk_item_content} settings={settings.floorplans} />
                    </div>
                    <div className='large-8 cell'>
                        <Tabs data={data.mvk_item_content} settings={settings.floorplans} />
                        <Gallery data={data.mvk_item_content} settings={settings.floorplans} />
                    </div>
                </div>
            </div>
        </S.Floorplan>
    )
}

export default Start;