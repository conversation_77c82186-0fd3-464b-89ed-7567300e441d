import React from "react";
// Helpers
import HtmlParser from 'src/helpers/html-parser'
import { checkAvailability } from "src/helpers/checkAvailability";
//PARTIALS
const Button = React.lazy(() => import('src/partials/button'));
// Styles
import * as S from '../styles'

const Start = ({ data, settings }) => {
    const beds = data.custom_fields?.beds ? `${data.custom_fields?.beds} Bed / ` : '';
    const baths = data.custom_fields?.baths ? `${data.custom_fields?.baths} Bath` : '';
    const sqFt = data.custom_fields?.square_footage ? data.custom_fields?.square_footage : '';
    const price = data.custom_fields?.starting_price ? new Intl.NumberFormat().format(data.custom_fields?.starting_price) : '';
    const availibleUnit = data.custom_fields?.availability ? data.custom_fields?.availability[0] : false;
    const applyButton = data.custom_fields?.apply_link ? data.custom_fields?.apply_link : '';
    const scheduleTourButton = data.custom_fields?.schedule_tour_link ? data.custom_fields?.schedule_tour_link : '';

    return (
        <S.Details className="floorplan-details">
            <h1>{data.title}</h1>
            {data.custom_fields?.description && <div className='description'><HtmlParser html={data.custom_fields?.description} /></div>}
            <div className="details">
                {(beds || baths) &&
                    <div className="detail-row">
                        <p className="item">Bed / Bath</p>
                        <p className="value">{beds}{baths}</p>
                    </div>
                }
                {sqFt &&
                    <div className="detail-row">
                        <p className="item">Sq. Ft.</p>
                        <p className="value">{sqFt}</p>
                    </div>
                }
                {price &&
                    <div className="detail-row">
                        <p className="item">Price</p>
                        <p className="value">${price}</p>
                    </div>
                }
                {(availibleUnit && availibleUnit.floor) &&
                    <div className="detail-row">
                        <p className="item">Floor</p>
                        <p className="value">{availibleUnit.floor}</p>
                    </div>
                }
                {(availibleUnit && availibleUnit.unit) &&
                    <div className="detail-row">
                        <p className="item">Residence #</p>
                        <p className="value">{availibleUnit.unit}</p>
                    </div>
                }
                {(availibleUnit && availibleUnit.available_date) &&
                    <div className="detail-row">
                        <p className="item">Availibility</p>
                        <p className="value">{checkAvailability(availibleUnit.available_date, 'Now!', 'month-date-year')}</p>
                    </div>
                }
            </div>
            {applyButton &&
                <div className="button-wrapper"><Button class='contact-button' title={applyButton.title ? applyButton.title : 'Apply Today'} url={applyButton.url} aria-label={`Apply for ${data.title}`} /></div>
            }
            {scheduleTourButton &&
                <div className="button-wrapper"><Button class='contact-button' title={scheduleTourButton.title ? scheduleTourButton.title : 'Schedule A Tour'} url={scheduleTourButton.url} aria-label={`Schedule a tour for ${data.title}`} /></div>
            }
        </S.Details>
    );
}

export default Start;