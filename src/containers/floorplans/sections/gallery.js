import React, { useState } from "react";
import Slider from 'react-slick';
import { useInView } from 'react-intersection-observer';

// Helpers
import Modal from 'src/helpers/modal';
const Imaging = React.lazy(() => import('src/helpers/imaging'));
import { PrevArrow, NextArrow } from 'src/helpers/slick';

// Styles
import * as S from '../styles'
import "slick-carousel/slick/slick.scss";
import "slick-carousel/slick/slick-theme.scss";


const Start = ({ data, settings }) => {
    const virtualTour = data?.custom_fields?.virtual_tour ? data.custom_fields?.virtual_tour : false;
    const virtualTourOverlayIcon = settings?.virtual_tour_overlay_icon ? settings?.virtual_tour_overlay_icon : false;
    const imageGallery = data?.custom_fields?.image_gallery ? data?.custom_fields?.image_gallery : false;

    return (
        <S.Gallery>
            {virtualTour && <VirtualTour data={virtualTour} icon={virtualTourOverlayIcon} />}
            {imageGallery && <ImageGallery data={imageGallery} />}
        </S.Gallery>
    );
}

const VirtualTour = ({ data, icon }) => {
    const [showModal, setShowModal] = useState(false);
    const close = () => {
        setShowModal(false);
    };
    return (
        <S.VirtualTour>
            <div className="virtual-tour" onClick={() => setShowModal(true)}>
                {data.thumbnail && <Imaging className='thumbnail' data={data.thumbnail} />}
                {icon && <Imaging className='icon' data={icon} />}
            </div>
            <Modal close={close} closeIcon={true} active={showModal} class="iframe">
                <div className="iframe-container">
                    <div className="mvk-responsive-video standard">
                        <iframe src={data.url} title={data.title} frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowfullscreen />
                    </div>
                </div>
            </Modal>
        </S.VirtualTour>
    )
}

const ImageGallery = ({ data }) => {
    const [showModal, setShowModal] = useState(false);
    const [activeIndex, setActiveIndex] = useState(0);
    const { ref, inView } = useInView({
        triggerOnce: true,
        fallbackInView: true
    });

    const openModal = (i) => {
        setActiveIndex(i);
        setShowModal(true);
    }
    const close = () => {
        setShowModal(false);
    };
    let sliderSettings = {
        slidesToShow: 1,
        slidesToScroll: 1,
        slickGoTo: 0,
        arrows: true,
        nextArrow: <NextArrow />,
        prevArrow: <PrevArrow />
    };
    // set intial slide to index of clicked image
    sliderSettings.initialSlide = activeIndex;

    return (
        <S.ImageGallery ref={ref}>
            {inView ? <>
                {data.map((image, i) => <div className="gallery-image" onClick={() => openModal(i)}><Imaging data={image} /></div>)}
                <Modal close={close} closeIcon={true} active={showModal} class='slider'>
                    <div className="slider-container">
                        <Slider ref={(a) => a} {...sliderSettings}>
                            {data?.map((image, i) =>
                                <Imaging key={`image-${i}`} data={image} />
                            )}
                        </Slider>
                    </div>
                </Modal>
            </> : null}
        </S.ImageGallery>
    )
}

export default Start;