import React, {useState} from "react";

// Styles
import * as S from '../styles'
// HELPERS.
const Imaging = React.lazy(() => import('src/helpers/imaging'));

const Start = ({ data, settings }) => {
    const [active, setActive] = useState(0);

    return (
        <S.Tabs className="tab-container" role='tablist'>
            <div className="tabs">
                {settings?.floorplan_types && settings?.floorplan_types.map(({ value, label }, i) => {
                    const id = `tab-${i}`
                    const ariaControls = `tabpanel-${i}`

                    if (data.custom_fields.floorplans[value]) {
                        return (
                            <div key={id} className={`tab${active === i ? ' active' : ''}`} id={id} label={label} role='tab' aria-selected={active === i} aria-controls={ariaControls} onClick={() => setActive(i)}>{label}</div>
                        )
                    }
                })}
            </div>
            {settings?.floorplan_types && settings?.floorplan_types.map(({ value }, i) => {
                const id = `tabpanel-${i}`
                const ariaLabelledBy = `tab-${i}`

                if (data.custom_fields.floorplans[value] && active === i) {
                    return (
                        <div key={id} id={id} role='tabpanel' aria-labelled-by={ariaLabelledBy}><TabPanel value={value} data={data.custom_fields.floorplans[value]} /></div>
                    )
                }
            })}
        </S.Tabs>
    );
}


const TabPanel = ({ value, data }) => {
    switch (value) {
        case 'video':
            return (<div dangerouslySetInnerHTML={{ __html: data }} />)
        default:
            return (<Imaging data={data} />)
    }

}

export default Start;