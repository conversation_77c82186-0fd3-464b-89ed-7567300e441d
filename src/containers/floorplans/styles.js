import styled from 'styled-components';

export const Floorplan = styled.div`
    padding: 2rem 0;
    @media (min-width: 1024px) {
        padding: 4rem 0;
        .large-4 {
            order: 2;
        }
    }
`
export const Details = styled.div`
    margin-bottom: 4rem;
    @media (min-width: 1024px) {
       position: sticky;
       top: 12%;
    }
    h1 {
        margin-bottom: 1rem;
    }
    .description {
        margin-bottom: 2rem;
    }
    .details {
        margin-bottom: 2rem;
        .detail-row {
            display: flex;
            .item {
                width: 40%;
                max-width: 200px;
                font-weight: bold !important;
            }
        }
    }
    .button-wrapper {
        margin-bottom: 1rem;
    }
`
export const Tabs = styled.div`
    margin-bottom: 2rem;
    @media (min-width: 1024px) {
        margin-bottom: 4rem;
     }
   .tabs {
        display: flex;
        margin-bottom: 1rem;
        .tab {
            font-size: 1.75rem;
            line-height: 1;
            margin-right: 1.5rem;
            border-bottom: 3px solid transparent;
            cursor: pointer;
            &.active {
                font-weight: bold;
                border-color: inherit;
            }
        }
   }
   img {
        display: block;
        width: 100%;
   }
`
export const Gallery = styled.div`
   .slick-slider {
        .slick-arrow {
            position: absolute;
            z-index: +1;
            top: calc(50% - 15px);

            flex: 0 1;
            -webkit-flex: 0 1;
            min-width: 30px;
            width: 20px;
            height: 30px;

            &::before {
                content: none;
            }

            &.slick-prev {
                @media screen and (max-width: $breakpoint-large) {
                    left: 0px;
                }
            }
            &.slick-next {
                @media screen and (max-width: $breakpoint-large) {
                    right: 0px;
                }
            }

            svg {
                height: 30px;
                width: 30px;
                font-size: 1.875rem;
                color: white;
                fill: white;
                &:hover {
                    cursor: pointer;
                }
            }
        }
   }
`

export const VirtualTour = styled.div`
   margin-bottom: 1rem;
   .virtual-tour {
        position: relative;
        cursor: pointer;
        &:before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,.5);
        }
        .thumbnail {
            width: 100%;
            display: block;
        }
        .icon {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }
   }
`

export const ImageGallery = styled.div`
    display: grid;
    grid-row-gap: 1rem;
    @media (min-width: 1024px) {
        grid-template-columns: 2fr 2fr;
        grid-column-gap: 1rem;
    }
   .gallery-image {
        cursor: pointer;
        height: 250px;
        position: relative;
        overflow: hidden;
        img {
            position: absolute;
            display: block;
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: .3s;
        }
        &:hover {
            img {
                transform: scale(1.1);
            }
        }
   }
`