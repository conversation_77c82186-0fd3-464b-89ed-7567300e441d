/*******************************************************************************************************
   Copyright 2020 - Calise Partners LLC d/b/a Imaginuity. All rights are reserved.
   Reproduction or transmission in whole or in part, in any form or by any means, electronic, mechanical
or otherwise, is prohibited without the prior written consent of Calise Partners LLC d/b/a Imaginuity.
********************************************************************************************************
   Project : 10618 Maverick Platform Development
   Author : Imaginuity Developers (<PERSON>, <PERSON>)
   Description : basic post component
   Creation Date : Wed Nov 18 2020
   Documentation : [documentation url - point to default wiki for project if no specific documentation for script]
 ********************************************************************************************************/

import React from "react";
import { decode } from 'html-entities';
import { useInView } from 'react-intersection-observer';
// Helpers
const Clicker = React.lazy(() => import('src/helpers/clicker'));
const Imaging = React.lazy(() => import('src/helpers/imaging'));
const HtmlParser = React.lazy(() => import('src/helpers/html-parser'));
// Templated Blog
const TemplatedBlog = React.lazy(() => import('./templated-blog'));
// Partials
const Modules = React.lazy(() => import('src/modules'));
const Sidebar = React.lazy(() => import('./sections/sidebar'));
const ShareButtons = React.lazy(() => import('./sections/share-buttons'));
const NavLinks = React.lazy(() => import('./sections/nav-links'));
const NavImageBlocks = React.lazy(() => import('./sections/nav-image-blocks'));
import Schema from 'src/partials/schema/post';
// Styles
import * as S from "../styles";
import './post.scss';

const Start = ({ data, settings }) => {
    if (settings.post?.blog_templates && data?.mvk_template) {
        return (
            <TemplatedBlog data={data} settings={settings} />
        )
    } else {
        return (
            <S.PostContainer
                id='post'
                className={`${settings?.post?.navigation ? 'with-nav' : ''} ${settings?.post?.title_style !== 'default' ? ' block-title' : ''} ${settings?.mvk_theme_config?.header?.header_type}`}
                maxWidth={settings?.mvk_theme_config?.layout?.site_max_width ? parseInt(settings?.mvk_theme_config?.layout?.site_max_width) + 1.875 : '76.875'}
            >
                <Schema data={data} settings={settings} />
                {!data.mvk_item_meta.template.includes('modular') &&
                    <>
                        {settings?.post?.title_style !== 'default' &&
                            <S.TitleBlock
                                className={`title-color-block secondary-bg ${settings?.post?.title_style}`}
                                mobileHero={settings?.post?.mobile_hero ? settings?.post?.mobile_hero : settings?.post?.desktop_hero}
                                desktopHero={settings?.post?.desktop_hero ? settings?.post?.desktop_hero : ''}
                            >
                                <div class='grid-container'>
                                    <h1 class='white-txt'>{decode(data?.mvk_item_content?.title)}</h1>
                                </div>
                            </S.TitleBlock>
                        }
                    </>
                }
                <div class={`post-body ${settings?.post?.settings_style}${settings?.post?.settings_style === 'sidebar' ? ` sidebar-${settings?.post?.sidebar_position}` : ''}`}>
                    {!data.mvk_item_meta.template.includes('modular') &&
                        <div class='default-post grid-container'>
                            <div class='grid-x'>
                                <div className="cell title">
                                    {settings?.post?.title_style === 'default' &&
                                        <h1 class='primary-txt'>{decode(data?.mvk_item_content?.title)}</h1>
                                    }
                                    {settings.post?.show_details &&
                                        <div class='post-data'>
                                            {data?.mvk_item_content?.post_author &&
                                                <span class='author'>By {data.mvk_item_content.post_author}</span>
                                            }
                                            {data?.mvk_item_content?.post_date &&
                                                <span class='date'>{data.mvk_item_content.post_date}</span>
                                            }
                                            {data?.mvk_item_tax.cats &&
                                                <span class='cats'>
                                                    {data.mvk_item_tax.cats.map((cat, index) => (
                                                        <span key={index} class='category'>{cat.cat_name}<span class='comma'>, </span></span>
                                                    ))
                                                    }
                                                </span>
                                            }
                                        </div>
                                    }
                                </div>
                            </div>
                            <div class='grid-x'>
                                <div class='cell'>
                                    {settings?.post?.settings_style !== 'default' || data?.mvk_item_meta?.template !== 'default' && data?.mvk_item_content?.feat_image &&
                                        <Imaging class='featured-image' data={data?.mvk_item_content?.feat_image} />
                                    }
                                    <div class="message">
                                        <HtmlParser html={data?.mvk_item_content?.content} />
                                    </div>
                                </div>
                                {settings.post?.enable_company_summary &&
                                    <CompanySummary data={settings.post?.company_summary} />
                                }
                                {(settings.post?.enable_share_buttons && settings?.post?.settings_style !== 'sidebar') &&
                                    <ShareButtons data={data} settings={settings} sectionOptions={settings.post} />
                                }
                            </div>
                            {(settings?.post?.navigation && settings.post.navigation_style === 'links') &&
                                <NavLinks data={data} settings={settings} />
                            }
                        </div>
                    }
                    {data.mvk_item_meta.template.includes('modular') && <Modules page={data} settings={settings} />}

                    {settings?.post?.settings_style === 'sidebar' &&
                        <Sidebar data={data} settings={settings} />
                    }
                </div>
                {(settings.post?.enable_recent_posts && data.mvk_item_content.recent_posts && settings?.post?.settings_style !== 'sidebar') &&
                    <RecentPosts data={data} settings={settings} />
                }
                {(settings?.post?.navigation && settings.post.navigation_style === 'image-blocks') &&
                    <NavImageBlocks data={data} />
                }
            </S.PostContainer>
        );
    }
};

const CompanySummary = ({ data }) => {

    return (
        <S.CompanySummaryWrapper
            className='company-summary cell'>
            <div className="inner-wrapper">
                {data.logo &&
                    <div className="logo">
                        <Imaging data={data.logo} />
                    </div>
                }
                <div className="content-wrapper">
                    {data.title &&
                        <h4 className='company-title'>{decode(data.title)}</h4>
                    }
                    {data.blurb &&
                        <div className='company-blurb'>
                            <HtmlParser html={data.blurb} />
                        </div>
                    }
                </div>
            </div>
        </S.CompanySummaryWrapper>
    )
}

const RecentPosts = ({ data, settings }) => {
    const { ref, inView } = useInView({
        triggerOnce: true,
        fallbackInView: true
    });

    return (
        <S.RecentPostsWrapper
            ref={ref}
            className='grid-container main-recent-posts'
        >
            {inView ?
                <div className="grid-x grid-margin-x">
                    <div className='cell'>
                        <h2>{settings.post?.recent_posts_title}</h2>
                    </div>
                    {data.mvk_item_content.recent_posts.map((post, index) => (
                        <div className='cell medium-6 large-4'>
                            <Clicker key={index} class='post-wrap' type="anchor" url={post.url}>
                                {post.feat_image &&
                                    <div class='featured-image' dangerouslySetInnerHTML={{ __html: post.feat_image }} />
                                }
                                <div class='content-wrap'>
                                    <h3 class='post-title'>{decode(post.title)}</h3>
                                </div>
                            </Clicker>
                        </div>
                    ))
                    }
                </div>
                : null}
        </S.RecentPostsWrapper>
    )
}

export default Start;
