import React, { useContext } from "react";
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faSearch } from '@fortawesome/free-solid-svg-icons';
import { useNavigate } from 'react-router-dom';
import { useInView } from 'react-intersection-observer';
import { decode } from 'html-entities';
// CONTEXT.
import { PrincipalContext} from "src/context";
// Helpers
const Clicker = React.lazy(() => import('src/helpers/clicker'));
const Imaging = React.lazy(() => import('src/helpers/imaging'));
const HtmlParser = React.lazy(() => import('src/helpers/html-parser'));
// Partials
const Button = React.lazy(() => import('src/partials/button'));
const ShareButtons = React.lazy(() => import('./share-buttons'));
// Styles
import * as S from "../../styles";

const Start = ({ data, settings, template }) => {
    const sectionOptions = template ? template : settings.post;

    const { ref, inView } = useInView({
        triggerOnce: true,
        fallbackInView: true
    });

    return (
        <div ref={ref} class='sidebar grid-container'>
            {inView ?
                <div class='grid-x'>
                    {sectionOptions?.enable_share_buttons &&
                        <ShareButtons data={data} settings={settings} sectionOptions={sectionOptions} />
                    }
                    {sectionOptions?.sidebar_search &&
                        <SidebarSearch sectionOptions={sectionOptions} />
                    }
                    {sectionOptions?.enable_top_cta &&
                        <SidebarCTA data={sectionOptions?.sidebar_top_cta} settings={settings} />
                    }
                    {(sectionOptions?.sidebar_recent_posts && data.mvk_item_content.recent_posts) &&
                        <div className='cell'>
                            <h4>{sectionOptions?.sidebar_recent_posts_title ? sectionOptions?.sidebar_recent_posts_title : 'RECENT POSTS'}</h4>
                            <div class='recent-posts'>
                                {data.mvk_item_content.recent_posts.map((post, index) => (
                                    <Clicker key={index} class='post-wrap' type="anchor" url={post.url} ariaLabel={`link to ${decode(post.title)}`}>
                                        {(sectionOptions?.sidebar_recent_posts_thumbnail && post.feat_image) &&
                                            <div class='featured-image' dangerouslySetInnerHTML={{ __html: post.feat_image }} />
                                        }
                                        <div class='content-wrap'>
                                            {(sectionOptions?.sidebar_recent_posts_category && post.category) &&
                                                <div class='category'>{decode(post.category)}</div>
                                            }
                                            <div class='post-title primary-txt'>{decode(post.title)}</div>
                                            {sectionOptions?.sidebar_recent_posts_date &&
                                                <div class='post-date'>{post.post_date}</div>
                                            }
                                        </div>
                                    </Clicker>
                                ))}
                            </div>
                        </div>
                    }
                    {(!!sectionOptions?.enable_tags && data?.mvk_item_tax.tags) &&
                        <div class='tags cell'>
                            <h4>{sectionOptions?.tags_label ? sectionOptions?.tags_label : 'TAGS'}</h4>
                            {data.mvk_item_tax.tags.map((tag, index) => (
                                <span key={index} class='tag'>{tag.name}<span class='comma'>, </span></span>
                            ))
                            }
                        </div>
                    }
                    {sectionOptions?.enable_bottom_cta &&
                        <SidebarCTA data={sectionOptions?.sidebar_bottom_cta} settings={settings} />
                    }
                </div>
                : null}
        </div>
    );
}


const SidebarCTA = ({ data, settings }) => {

    return (
        <S.SidebarCTAWrapper
            className='sidebar-cta cell'
            backgroundColor={Coloring(data.background_color, settings)}
            textColor={data.background_value === 'dark' ? '#fff' : Coloring('body_copy_color', settings)}
        >
            <div className="content-wrapper">
                {data.image &&
                    <Imaging data={data.image} />
                }
                <div className='content'>
                    <HtmlParser html={data.content} />
                </div>
                {data.button &&
                    <Button type='anchor' url={data.button.url} title={data.button.title} tone={data.background_value} target={data.button.target} />
                }
            </div>
        </S.SidebarCTAWrapper>
    )

}

const SidebarSearch = ({ sectionOptions }) => {
    const [principal, setPrincipal] = useContext(PrincipalContext);

    const navigate = useNavigate();

    const Submit = (e) => {
        e?.preventDefault();
        var term = document.getElementById('sidebar-search-input').value;
        if (term && term.length > 0) {
            navigate(principal.subsite ? `/${principal.subsite}/search?s=${term}` : `/search?s=${term}`);
        }
    }

    return (
        <S.SidebarSearchWrapper
            className="sidebar-search cell"
        >
            <h4 className='search-title'>{sectionOptions?.search_title}</h4>
            <div class="inner-wrapper">
                <form method="post" action="#" autocomplete="off" onSubmit={Submit} role="search" aria-label="posts">
                    <label for="sidebar-search-input" class="show-for-sr">{sectionOptions?.search_placeholder || 'Search'}</label>
                    <input id="sidebar-search-input" type="text" placeholder={sectionOptions?.search_placeholder || 'Search'} />
                </form>
                <Clicker class="icon" process={Submit}>
                    <FontAwesomeIcon icon={faSearch} />
                </Clicker>
            </div>
        </S.SidebarSearchWrapper>
    );
}

export default Start;