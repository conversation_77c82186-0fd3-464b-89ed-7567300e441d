import React, { useState, useEffect } from "react";
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faEnvelope, faLink } from '@fortawesome/free-solid-svg-icons';
import { faFacebookF, faLinkedin } from '@fortawesome/fontawesome-free-brands';
import { faXTwitter } from "@fortawesome/free-brands-svg-icons";
import { decode } from 'html-entities';
// Helpers
import { Coloring } from "src/helpers";
// Styles
import * as S from "../../styles";

const Start = ({ data, settings, sectionOptions }) => {
    const iconColor = Coloring(sectionOptions?.icon_color, settings);
    const iconBackground = sectionOptions?.icon_style === 'icon-background' ? Coloring(sectionOptions?.icon_background_color, settings) : 'transparent';
    const iconBorderRadius = sectionOptions?.icon_style !== 'icon-only' ? `${sectionOptions?.icon_border_radius}` : '0';


    return (
        <S.ShareButtonWrapper
            className={`share-buttons ${sectionOptions?.settings_style || (sectionOptions.sidebar ? 'sidebar' : 'default')} cell ${sectionOptions?.icon_style}`}
            iconColor={iconColor}
            iconBackground={iconBackground}
            iconBorderRadius={iconBorderRadius}
        >
            {(sectionOptions?.share_buttons_label && (!sectionOptions.sidebar && sectionOptions?.settings_style !== 'sidebar')) &&

                <p className='label'>{sectionOptions?.share_buttons_label}</p>
            }
            {(sectionOptions?.share_buttons_label && (sectionOptions.sidebar || sectionOptions?.settings_style === 'sidebar')) &&

                <h4 className='label'>{sectionOptions?.share_buttons_label}</h4>
            }
            {sectionOptions?.facebook &&
                <ShareButton url={`https://www.facebook.com/sharer/sharer.php?u=${location.href}`} icon={faFacebookF} ariaLabel={"share to Facebook"} />
            }
            {sectionOptions?.linkedin &&
                <ShareButton url={`https://www.linkedin.com/shareArticle?url=${location.href}`} icon={faLinkedin} ariaLabel={"share to LinkedIn"} />
            }
            {sectionOptions?.x &&
                <ShareButton url={`https://twitter.com/intent/tweet?url=${location.href}`} icon={faXTwitter} ariaLabel={"share to X"} />
            }
            {sectionOptions?.email &&
                <ShareButton url={`mailto:?subject=${decode(data?.mvk_item_content?.title)}&body=${location.href}`} icon={faEnvelope} ariaLabel={"send in email"} />
            }
            {sectionOptions?.link_share &&
                <ShareButton url={false} icon={faLink} ariaLabel={"copy to clipboard"} />
            }
        </S.ShareButtonWrapper>
    )
}

const ShareButton = ({ url, icon, ariaLabel }) => {
    const [copySuccess, setCopySuccess] = useState("")

    useEffect(() => {
        if (copySuccess) {
            setTimeout(() => {
                setCopySuccess("")
            }, 3000)
        }
    }, [copySuccess]);

    function copy() {
        const el = document.createElement("input");
        el.value = window.location.href;
        document.body.appendChild(el);
        el.select();
        document.execCommand("copy");
        document.body.removeChild(el);
        setCopySuccess("Link copied!");
    }

    return (
        <>
            {url ?
                <a className={`share-button`} href={url} target='_blank' aria-label={ariaLabel}>
                    <FontAwesomeIcon icon={icon} />
                </a>
                :
                <div className={`share-button`} onClick={copy} aria-label={ariaLabel}>
                    <FontAwesomeIcon icon={icon} />
                    <span className="copy-success body-copy-txt">{copySuccess}</span>
                </div>

            }
        </>
    )
}

export default Start;