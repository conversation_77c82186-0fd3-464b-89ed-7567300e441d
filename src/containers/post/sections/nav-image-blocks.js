import React from "react";
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faArrowLeftLong, faArrowRightLong } from '@fortawesome/free-solid-svg-icons';
import { decode } from 'html-entities';
import { useInView } from 'react-intersection-observer';
// Helpers
const Clicker = React.lazy(() => import('src/helpers/clicker'));

const Start = ({ data }) => {
    const { ref, inView } = useInView({
        triggerOnce: true,
        fallbackInView: true
    });

    return (
        <div ref={ref} className={`post-navigation ${!data.mvk_item_content.prev_post ? 'no-prev' : ''}`}>
            {inView ? <>
                {data.mvk_item_content.prev_post &&
                    <Clicker class='nav-block prev' type='anchor' url={data.mvk_item_content.prev_post.url}>
                        <div class='overlay'></div>
                        {data.mvk_item_content.prev_post.feat_image &&
                            <div class='featured-image' dangerouslySetInnerHTML={{ __html: data.mvk_item_content.prev_post.feat_image }} />
                        }
                        <FontAwesomeIcon class='arrow prev' icon={faArrowLeftLong} />
                        <div className="content-wrap">
                            <div class='label'>Previous Post</div>
                            <div class='post-title'>{decode(data.mvk_item_content.prev_post.title)}</div>
                        </div>
                    </Clicker>
                }
                {data.mvk_item_content.next_post &&
                    <Clicker class='nav-block next' type='anchor' url={data.mvk_item_content.next_post.url}>
                        <div class='overlay'></div>
                        {data.mvk_item_content.next_post.feat_image &&
                            <div class='featured-image' dangerouslySetInnerHTML={{ __html: data.mvk_item_content.next_post.feat_image }} />
                        }
                        <div className="content-wrap">
                            <div class='label'>Next Post</div>
                            <div class='post-title'>{decode(data.mvk_item_content.next_post.title)}</div>
                        </div>
                        <FontAwesomeIcon class='arrow next' icon={faArrowRightLong} />
                    </Clicker>
                }
            </> : null}
        </div>
    );
}

export default Start;