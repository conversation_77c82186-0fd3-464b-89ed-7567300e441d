import React from "react";
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faChevronRight, faChevronLeft } from '@fortawesome/free-solid-svg-icons';
import { useInView } from 'react-intersection-observer';
// Helpers
const Clicker = React.lazy(() => import('src/helpers/clicker'));

const Start = ({ data, settings, template }) => {
    const sectionOptions = template ? template : settings.post;

    const { ref, inView } = useInView({
        triggerOnce: true,
        fallbackInView: true
    });

    return (
        <div ref={ref} className={`post-navigation-${sectionOptions.navigation_style} grid-x ${!data.mvk_item_content.prev_post ? 'no-prev' : ''}`}>
            {inView ? <>
                <div className="cell small-6">
                    {data.mvk_item_content.prev_post &&
                        <Clicker class='nav-link prev' type='anchor' url={data.mvk_item_content.prev_post.url} ariaLabel={`link to ${data.mvk_item_content?.prev_post?.title}`}>
                            <FontAwesomeIcon class='arrow prev' icon={faChevronLeft} /> Previous
                        </Clicker>
                    }
                </div>
                <div className="cell small-6">
                    {data.mvk_item_content.next_post &&
                        <Clicker class='nav-link next' type='anchor' url={data.mvk_item_content.next_post.url} ariaLabel={`link to ${data.mvk_item_content?.next_post?.title}`}>
                            Next <FontAwesomeIcon class='arrow next' icon={faChevronRight} />
                        </Clicker>
                    }
                </div>
            </> : null}
        </div>
    );
}

export default Start;