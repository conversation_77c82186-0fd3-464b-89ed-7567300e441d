@import "src/scss/variables.scss";

#app {
    #post {
        &.absolute {
            padding-top: 0;
        }
    }
    .post-navigation {
        margin-top: 5rem;
        .nav-block {
            height: 250px;
            display: flex;
            justify-content: center;
            align-items: center;
            position: relative;
            overflow: hidden;
            .overlay {
                position: absolute;
                content: "";
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background-color: rgba(0, 0, 0, 0.2);
                transition: 0.3s;
                z-index: 1;
            }
            .featured-image img {
                position: absolute;
                top: 0;
                left: 0;
                min-width: 100%;
                min-height: 100%;
                object-fit: cover;
                transition: 0.3s;
            }
            .content-wrap {
                z-index: 1;
                color: #fff;
                text-align: center;
                width: 100%;
                padding: 2rem;
                .label {
                    margin-bottom: 1rem;
                }
                .post-title {
                    font-size: 1.5rem;
                }
            }
            .arrow {
                display: none;
            }
            &:hover {
                .overlay {
                    background-color: rgba(0, 0, 0, 0.8);
                }
                .featured-image img {
                    transform: scale(1.1);
                }
            }
        }
        @media (min-width: $break-mobile) {
            display: flex;
            &.no-prev {
                justify-content: flex-end;
            }
            .nav-block {
                width: 50%;
                &.prev {
                    .content-wrap {
                        text-align: right;
                    }
                }
                &.next {
                    .content-wrap {
                        text-align: left;
                    }
                }
            }
        }
        @media (min-width: $break-large) {
            .nav-block {
                height: 300px;
                .content-wrap {
                    padding: 4rem;
                    width: 50%;
                    transition: 0.3s;
                }
                .arrow {
                    position: absolute;
                    display: block;
                    color: #fff;
                    z-index: 1;
                    opacity: 0;
                    transition: 0.3s;
                    width: 50px;
                    &.prev {
                        left: 6rem;
                    }
                    &.next {
                        right: 6rem;
                    }
                }
                &:hover {
                    .content-wrap {
                        width: 100%;
                    }
                    .arrow {
                        opacity: 1;
                        &.prev {
                            left: 4rem;
                        }
                        &.next {
                            right: 4rem;
                        }
                    }
                }
            }
        }
        @media (min-width: $break-desktop) {
            margin-top: 8rem;
            .nav-block {
                height: 375px;
                .content-wrap {
                    .post-title {
                        font-size: 2.25rem;
                    }
                }
            }
        }
    }
}
