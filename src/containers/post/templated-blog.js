import React, { useContext, useState, useEffect } from "react";
import { decode } from 'html-entities';
// Context
import { BlogTemplatesContext } from "src/context";
// Components
const Modules = React.lazy(() => import('src/modules'));
const Sidebar = React.lazy(() => import('./sections/sidebar'));
const NavLinks = React.lazy(() => import('./sections/nav-links'));
const NavImageBlocks = React.lazy(() => import('./sections/nav-image-blocks'));
const ShareButtons = React.lazy(() => import('./sections/share-buttons'));
import Schema from 'src/partials/schema/post';
// Styles
import * as S from "../styles";

const Start = ({ data, settings }) => {
    const [blogTemplates, setBlogTemplates] = useContext(BlogTemplatesContext);
    const [template, setTemplate] = useState(null)

    useEffect(() => {
        setTemplate((!blogTemplates.loading && blogTemplates && data?.mvk_template) ? blogTemplates?.find(template => template?.mvk_slug === data?.mvk_template) : null)
    }, [data.mvk_template, blogTemplates])

    return (
        <S.PostContainer
            id='post'
            className={`${template?.navigation ? 'with-nav' : ''} ${template?.title_style !== 'none' ? ' block-title' : ''} ${settings?.mvk_theme_config?.header?.header_type} templated`}
            maxWidth={settings?.mvk_theme_config?.layout?.site_max_width ? parseInt(settings?.mvk_theme_config?.layout?.site_max_width) + 1.875 : '76.875'}
        >
            <Schema data={data} settings={settings} />
            {(template && template?.title_style !== 'none') &&
                <S.TitleBlock
                    className={`title-color-block secondary-bg ${template?.title_style}`}
                    mobileHero={template?.mobile_hero ? template?.mobile_hero : template?.desktop_hero}
                    desktopHero={template?.desktop_hero ? template?.desktop_hero : ''}
                >
                    <div class='grid-container'>
                        <h1 class='white-txt'>{decode(data?.mvk_item_content?.title)}</h1>
                    </div>
                </S.TitleBlock>
            }
            <div class={`post-body${template?.sidebar ? ` sidebar sidebar-${template?.sidebar_position}` : ''}`}>
                <div className="default-post">
                    {template && <Modules page={data} layout={template?.layout} />}
                    {(template?.navigation && template?.navigation_style === 'links') &&
                        <NavLinks data={data} settings={settings} template={template} />
                    }
                    {(template?.enable_share_buttons && !template.sidebar) &&
                        <div className="grid-container">
                            <ShareButtons data={data} settings={settings} sectionOptions={template} />
                        </div>
                    }
                </div>
                {template?.sidebar &&
                    <Sidebar data={data} settings={settings} template={template} />
                }
            </div>
            {(template?.navigation && template?.navigation_style === 'image-blocks') &&
                <NavImageBlocks data={data} />
            }
        </S.PostContainer>
    )
}

export default Start;