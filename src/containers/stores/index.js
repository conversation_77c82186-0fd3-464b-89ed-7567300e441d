/*******************************************************************************************************
   Copyright 2021 - Calise Partners LLC d/b/a Imaginuity. All rights are reserved. 
   Reproduction or transmission in whole or in part, in any form or by any means, electronic, mechanical 
or otherwise, is prohibited without the prior written consent of Calise Partners LLC d/b/a Imaginuity.
********************************************************************************************************
   Project : 10618 Maverick Platform Development
   Author : Imaginuity Developers (<PERSON>, <PERSON>)
   Description : controller component that decides what type of store to render
   Creation Date : Tue May 18 2021
   Documentation : [documentation url - point to default wiki for project if no specific documentation for script]
 ********************************************************************************************************/

import React, { useEffect, useContext, Suspense } from 'react';
import { useNavigate, useLocation, useSearchParams } from "react-router-dom";

// HELPERS.
const Imaging = React.lazy(() => import('src/helpers/imaging'));

// CONTEXT.
import { SettingsContext } from 'src/context';
import { JobsProvider } from 'src/context';

// Partials
import Map from 'src/partials/map';
import MappedIn from 'src/partials/map/mapped-in';
import MappedInFull from 'src/partials/map/mapped-in-full';
import MapAcquireDigital from 'src/partials/map/acquire-digital';
import StoreCTA from 'src/partials/store-cta';

const Default = React.lazy(() => import('./default'));
const Lifestyle = React.lazy(() => import('./lifestyle'));
const Special = React.lazy(() => import('./special'));

// SCSS.
import 'src/containers/stores/index.scss';

const Start = ({ page }) => {
    const [settings, setSettings] = useContext(SettingsContext);
    const navigate = useNavigate();

    const type = settings?.store ? settings.store.type : '';
    const style = settings?.store ? settings.store.style : '';

    useEffect(() => {
        const landmarkId = (settings.store?.store_map_type === 'mapplic' && page?.mvk_item_content?.custom_fields?.enable_interactive_map && page.mvk_item_content.custom_fields.landmark_id) ? `?location=${page.mvk_item_content.custom_fields.landmark_id}` : '';
        if (landmarkId) {
            navigate({ search: landmarkId });
        }

    }, []);

    return (
        <Suspense fallback={<div />}>
            {(settings?.store && settings.store?.store_hero) &&
                <div class='store-hero'>
                    <Imaging data={settings.store.store_hero_image} />
                </div>
            }
            <JobsProvider>
                {style === 'default' && <Default page={page} settings={settings} />}
                {style === 'lifestyle' && <Lifestyle page={page} settings={settings} />}
                {style === 'special' && <Special page={page} settings={settings} />}
            </JobsProvider>
            {(settings.store?.store_map_type === 'mapplic' && page.mvk_item_content.custom_fields.enable_interactive_map && settings.mapplic_id) &&
                <div className='grid-container'>
                    <Map id={settings.mapplic_id} html={settings.mapplic_html} />
                </div>
            }
            {(settings.store?.store_map_type === 'mappedin' && page.mvk_item_content.custom_fields.enable_interactive_map && page.mvk_item_content.custom_fields.location_id) &&
                <MappedIn settings={settings} locationId={page.mvk_item_content.custom_fields.location_id} />
            }
            {(settings.store?.store_map_type === 'mappedin' && page.mvk_item_content.custom_fields.enable_interactive_map && !page.mvk_item_content.custom_fields.location_id) &&
                <div class='mappedin-container'><MappedInFull settings={settings} /></div>
            }
            {settings.store?.store_map_type === 'acquire-digital' &&
                <MapAcquireDigital locationID={page.mvk_item_content.custom_fields.ad_location} />
            }
            {(settings.store?.store_map_type === 'iframe' && settings.store?.iframe_url && page.mvk_item_content.custom_fields.landmark_id) &&
                <div class='store-iframe-container'>
                    <iframe title={'store map'} src={`${settings.store?.iframe_url}&lid=${page.mvk_item_content?.custom_fields?.landmark_id}`} height={`750px`} width="100%" />
                </div>
            }
            {settings.store?.enable_gift_card_cta &&
                <StoreCTA type='gift-card' data={settings.store.gift_card_cta} />
            }
        </Suspense>
    );
};

export default Start;