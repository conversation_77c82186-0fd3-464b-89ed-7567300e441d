.store-details__lifestyle {
    padding-top: 2rem;
    padding-bottom: 2rem;

    @media screen and (min-width: 768px) {
        padding-top: 4rem;
    }

    .main-content {
        display: flex;
        flex-flow: column nowrap;
        width: 100%;
        padding-bottom: 2rem;

        @media screen and (min-width: 768px) {
            flex-flow: row nowrap;
            padding-bottom: 4rem;
        }
    }

    .text-wrapper {
        width: 100%;

        @media screen and (min-width: 768px) {
            width: 50%;
            padding-right: 0.5rem;
        }
    }

    .directory-breadcrumb {
        text-decoration: underline;
        display: block;
        width: fit-content;
    }

    .store-heading {
    }

    .link-wrapper {
        margin-bottom: 1.5rem;
    }

    .website-link {
        text-decoration: underline;
    }

    .image-wrapper-mobile {
        display: block;
        width: 100%;
        margin-bottom: 0.5rem;

        @media screen and (min-width: 768px) {
            display: none;
        }
    }

    .store-excerpt {
        margin-bottom: 1.5rem;
    }

    .social-media-container {
        margin-bottom: 1.5rem;
    }

    .social-icons-wrapper {
        display: flex;
        flex-flow: row wrap;

        .icon {
            width: 30px;
            height: 30px;
            display: flex;
            justify-content: center;
            align-items: center;
            border-radius: 50%;
            margin: 0 0.5rem 0 0;
            transition: 0.2s;

            &:hover {
                box-shadow: 1px 3px 6px rgba(0, 0, 0, 0.5);
                transform: translateY(-1px);
            }
        }

        svg {
            color: #fff !important;
        }
    }

    .link-wrapper.gift-cards {
        a {
            display: flex;
            align-items: center;
            margin: 1rem 0;
            svg {
                width: 1.5rem;
                margin-right: 0.5rem;
            }
        }
    }

    .phone-link {
        text-decoration: underline;
    }

    .hours-module {
        padding: 1.5rem 0;

        .list {
            padding: unset;
        }
    }

    .lifestyle-override {
        padding-bottom: 0.5rem;
    }

    .image-wrapper-desktop {
        display: none;

        @media screen and (min-width: 768px) {
            display: block;
            width: 50%;
            padding-left: 0.5rem;
        }
    }

    .logo-container {
        width: 100%;

        img {
            box-shadow: 10px 10px 20px rgba(0, 0, 0, 0.16);
        }
    }

    .sub-content {
    }

    .best-entrance {
        margin-bottom: 1.5rem;
    }

    .store-copy {
    }
}
