import React, { useEffect, useState } from 'react';
import { decode } from 'html-entities';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faFacebookF, faInstagram, faPinterest, faYoutube } from '@fortawesome/fontawesome-free-brands';
import { faXTwitter, faTiktok } from "@fortawesome/free-brands-svg-icons";
import { faGift } from "@fortawesome/free-solid-svg-icons";

// Partials
import Table from 'src/partials/hours/table';
const Reduced = React.lazy(() => import('src/partials/hours/reduced'));
const List = React.lazy(() => import('src/partials/hours/list'));
import CondensedList from 'src/partials/hours/condensed-list';
import Special from 'src/partials/hours/special';
import StoreCTA from 'src/partials/store-cta';

// Helpers
const Imaging = React.lazy(() => import('src/helpers/imaging'));
import { WeeksHours } from 'src/helpers/hours';
const Clicker = React.lazy(() => import('src/helpers/clicker'));
const HtmlParser = React.lazy(() => import('src/helpers/html-parser'));

// SCSS.
import 'src/containers/stores/lifestyle.scss';

const Start = ({ page, settings }) => {

    useEffect(() => {
        var StyleSheet = document.createElement('style');
        var StyleStrings = ``;

        StyleStrings += `.store-details__lifestyle .hours-module .list-row {
            color: ${settings.design?.colors?.primary_color};
        }`;

        StyleSheet.innerHTML = StyleStrings;
        document.getElementsByTagName('head')[0].appendChild(StyleSheet);
    }, [])

    const socialItems = [];
    socialItems.push(
        (!!page.mvk_item_content.custom_fields.facebook),
        (!!page.mvk_item_content.custom_fields.twitter),
        (!!page.mvk_item_content.custom_fields.instagram),
        (!!page.mvk_item_content.custom_fields.pinterest),
        (!!page.mvk_item_content.custom_fields.youtube),
        (!!page?.mvk_item_content?.custom_fields?.tiktok)
    );

    let websiteStr = page.mvk_item_content.custom_fields.website;
    let strippedStr = websiteStr?.replace(/https:\/\//g, '').replaceAll(/\/+$/g, '');

    var socialStyle = {
        color: `${settings.design?.colors?.primary_color}`,
        fill: `${settings.design?.colors?.primary_color}`,
        backgroundColor: `${settings.design?.colors?.primary_color}`,
    };
    const [directoryLink, setDirectoryLink] = useState(settings?.store?.directory_link ? settings?.store?.directory_link?.url : settings?.directory_link?.url)
    const [directoryLinkTitle] = useState(settings?.store?.directory_link ? settings?.store?.directory_link?.title : settings?.directory_link?.title)
    useEffect(() => {
        setDirectoryLink(settings.current_location && page.post_type_parent_slug ? `/${settings.current_location}/${page.post_type_parent_slug}/` : (settings?.store?.directory_link ? settings?.store?.directory_link?.url : settings?.directory_link?.url))
    }, [settings.current_location])

    return (
        <>
            <main class='store-details__lifestyle grid-container'>
                {(settings?.store?.enable_back_to_directory_button || settings?.enable_back_to_directory_button) &&
                    <Clicker type='anchor' class='directory-breadcrumb primary-txt' url={directoryLink} title={directoryLinkTitle}>{decode(directoryLinkTitle)}</Clicker>
                }
                <div class='main-content'>
                    <div class='text-wrapper'>
                        {page.mvk_item_content.title &&
                            <h1 class='store-heading primary-txt'>{decode(page.mvk_item_content.title)}</h1>
                        }

                        {page.mvk_item_content.custom_fields.website &&
                            <div class='link-wrapper'><a class='website-link primary-txt' href={page.mvk_item_content.custom_fields.website} target='_blank'>{strippedStr}</a></div>
                        }

                        <div class='image-wrapper-mobile'>
                            {page.mvk_item_content.custom_fields.logo_color &&
                                <div class='logo-container'>
                                    <Imaging class='logo-color' data={page.mvk_item_content.custom_fields.logo_color} />
                                </div>
                            }
                            {(!page.mvk_item_content.custom_fields.logo_color && page.mvk_item_content.custom_fields.logo_monochrome) &&
                                <div class='logo-container'>
                                    <Imaging class='logo-monochrome' data={page.mvk_item_content.custom_fields.logo_monochrome} />
                                </div>
                            }
                        </div>

                        {page.mvk_item_content.excerpt &&
                            <div class='store-excerpt primary-txt'>
                                <HtmlParser html={page.mvk_item_content.excerpt} />
                            </div>
                        }

                        {socialItems.includes(true) &&
                            <div class='social-media-container'>
                                <div class='social-icons-wrapper'>
                                    {page.mvk_item_content.custom_fields.facebook &&
                                        <a class='icon facebook' href={page.mvk_item_content.custom_fields.facebook} rel="noopener" target='_blank' title='Facebook' style={socialStyle} >
                                            <FontAwesomeIcon icon={faFacebookF} />
                                        </a>
                                    }
                                    {page.mvk_item_content.custom_fields.twitter &&
                                        <a class='icon twitter' href={page.mvk_item_content.custom_fields.twitter} rel="noopener" target='_blank' title='Twitter' style={socialStyle} >
                                            <FontAwesomeIcon icon={faXTwitter} />
                                        </a>
                                    }
                                    {page.mvk_item_content.custom_fields.instagram &&
                                        <a class='icon instagram' href={page.mvk_item_content.custom_fields.instagram} rel="noopener" target='_blank' title='Instagram' style={socialStyle} >
                                            <FontAwesomeIcon icon={faInstagram} />
                                        </a>
                                    }
                                    {page.mvk_item_content.custom_fields.pinterest &&
                                        <a class='icon pinterest' href={page.mvk_item_content.custom_fields.pinterest} rel="noopener" target='_blank' title='Pinterest' style={socialStyle} >
                                            <FontAwesomeIcon icon={faPinterest} />
                                        </a>
                                    }
                                    {page.mvk_item_content.custom_fields.youtube &&
                                        <a class='icon youtube' href={page.mvk_item_content.custom_fields.youtube} rel="noopener" target='_blank' title='YouTube' style={socialStyle} >
                                            <FontAwesomeIcon icon={faYoutube} />
                                        </a>
                                    }
                                    {page?.mvk_item_content?.custom_fields?.tiktok &&
                                        <a class='icon tiktok' href={page?.mvk_item_content?.custom_fields?.tiktok} rel="noopener" target='_blank' title='YouTube' style={socialStyle} >
                                            <FontAwesomeIcon icon={faTiktok} />
                                        </a>
                                    }
                                </div>
                            </div>
                        }

                        {page.mvk_item_content.custom_fields.phone_number &&
                            <a class='phone-link primary-txt' href={`tel: ${page.mvk_item_content.custom_fields.phone_number}`}>
                                {page.mvk_item_content.custom_fields.phone_number}
                            </a>
                        }
                        {(page.mvk_item_content.custom_fields.accepts_gift_cards && settings.store?.gift_card_cta?.button) &&
                            <div class='link-wrapper gift-cards'>
                                <Clicker type='anchor' className='primary-txt' url={settings.store?.gift_card_cta?.button.url} target={settings.store?.gift_card_cta?.button.target}>
                                    <FontAwesomeIcon icon={faGift} class='gift-card icon' />
                                    {settings.store?.gift_card_cta?.button.title ? settings.store?.gift_card_cta?.button.title : 'Accepts Gift Cards'}
                                </Clicker>
                            </div>
                        }
                        {(settings?.store?.store_hours_style && settings.store.store_hours_style !== 'none') &&
                            <Hours store={page.mvk_item_content.custom_fields} style={settings.store.store_hours_style} settings={settings} page={page} />
                        }
                    </div>

                    <div class='image-wrapper-desktop'>
                        {page.mvk_item_content.custom_fields.logo_color &&
                            <div class='logo-container'>
                                <Imaging class='logo-color' data={page.mvk_item_content.custom_fields.logo_color} />
                            </div>
                        }

                        {(!page.mvk_item_content.custom_fields.logo_color && page.mvk_item_content.custom_fields.logo_monochrome) &&
                            <div class='logo-container'>
                                <Imaging class='logo-monochrome' data={page.mvk_item_content.custom_fields.logo_monochrome} />
                            </div>
                        }
                    </div>
                </div>

                <div class='sub-content'>
                    {page.mvk_item_content.custom_fields.best_entrance &&
                        <div class='best-entrance primary-txt'>{`Convenient Parking: ${page.mvk_item_content.custom_fields.best_entrance}`}</div>
                    }

                    {page.mvk_item_content.custom_fields.store_copy &&
                        <div class='store-copy primary-txt'>
                            <HtmlParser html={page.mvk_item_content.custom_fields.store_copy} />
                        </div>
                    }
                </div>
            </main>
            {settings.store?.enable_form_cta &&
                <StoreCTA type='form' data={settings.store.form_cta} />
            }
        </>
    );
};

const Hours = ({ style, store, settings, page }) => {
    const data = WeeksHours(style, store.hours);
    const specialInstructions = (store.hours && store.hours.standard_hours) ? store.hours.standard_hours[0].special_instructions : '';

    if (store?.hours?.custom_hours_message) {
        return (<div>{store.hours.custom_hours_message}</div>);
    } else if (store.hours.alternate_hours || store.hours.seasonal_hours || (store.hours.standard_hours && (store.hours.standard_hours[0].monday_open || store.hours.standard_hours[0].monday_closed))) {
        switch (style) {
            case 'table':
                return (<Table title={settings?.store?.when_to_shop_label ? settings?.store?.when_to_shop_label : 'When to Shop'} hours={data} special={specialInstructions} store={true} isLifestyle={true} />);
            case 'reduced':
                return (<Reduced title={specialInstructions} hours={data} store={true} isLifestyle={true} />);
            case 'list':
                return (<List title={settings?.store?.when_to_shop_label ? settings?.store?.when_to_shop_label : 'When to Shop'} hours={data} special={specialInstructions} store={true} isLifestyle={true} />);
            case 'special':
                return (<Special title={settings?.store?.when_to_shop_label ? settings?.store?.when_to_shop_label : 'When to Shop:'} hours={data} special={specialInstructions} store={true} isLifestyle={true} />);
            case 'condensed_list':
                return (<CondensedList title={settings?.store?.when_to_shop_label ? settings?.store?.when_to_shop_label : ''} hours={data} special={specialInstructions} store={true} />);
            default:
                return null;
        };
    } else if (settings?.hours?.custom_hours_message) {
        return (<div>{settings?.hours?.custom_hours_message}</div>);
    } else {
        switch (style) {
            case 'table':
                return (<Table title={settings?.store?.when_to_shop_label ? settings?.store?.when_to_shop_label : 'When to Shop'} hours={data} special={specialInstructions} store={true} isLifestyle={true} />);
            case 'reduced':
                return (<Reduced title={specialInstructions} hours={data} store={true} isLifestyle={true} />);
            case 'list':
                return (<List title={settings?.store?.when_to_shop_label ? settings?.store?.when_to_shop_label : 'When to Shop'} hours={data} special={specialInstructions} store={true} isLifestyle={true} />);
            case 'special':
                return (<Special title={settings?.store?.when_to_shop_label ? settings?.store?.when_to_shop_label : 'When to Shop:'} hours={data} special={specialInstructions} store={true} isLifestyle={true} />);
            case 'condensed_list':
                return (<CondensedList title={settings?.store?.when_to_shop_label ? settings?.store?.when_to_shop_label : ''} hours={data} special={specialInstructions} store={true} />);
            default:
                return null;
        };
    }
};

export default Start;
