import React from 'react';

const Start = ({ type, style }) => {

    let fill = {
        fill: style['color']
    }
    switch (type) {
        case 'phone':
            return (<svg id="phone-icon" style={style} aria-label="telephone"><circle class="cls-1 border-color-setter" cx="82.5" cy="83.06" r="79.8"></circle><path class="cls-2 background-color-setter" d="M63.18,74.78a108.56,108.56,0,0,0,27.91,27.65c3-3,5.88-5.74,8.6-8.6,2.34-2.46,4.83-2.79,7.52-.82,3.62,2.66,7.26,5.3,10.7,8.18,3.93,3.29,4.28,6.5,1.57,11-4.32,7.17-13.46,11.29-21.62,7.9a107.09,107.09,0,0,1-23.62-13.38c-11.74-8.92-21.38-20-27.59-33.64-3.07-6.73-4.49-13.82-.29-20.53A26.72,26.72,0,0,1,54,45c3.21-2.17,6.76-1.64,9.35,1.58,3,3.76,6.24,7.44,8.63,11.58.94,1.63.61,4.89-.46,6.59C69.36,68.25,66.28,71.17,63.18,74.78Z"></path></svg>);
        case 'location':
            return (<svg id="location-icon" style={style} aria-label="location pin"><circle class="cls-1 border-color-setter" cx="82.5" cy="83.06" r="79.8"></circle><path class="cls-2 border-color-setter background-color-setter" d="M82.7,127.31c-11.58-17.7-23.21-33.95-29.06-53.21-4.57-15.06,5.55-31.42,21-35.41a30.18,30.18,0,0,1,36.75,20.8,27.88,27.88,0,0,1-1.63,19.43c-2.84,6.35-5.65,12.75-9.09,18.78C95.2,107.33,89.19,116.69,82.7,127.31Zm14-59.74A13.83,13.83,0,0,0,82.53,53.44c-8.32,0-14.38,6.18-14.25,14.56a14.22,14.22,0,0,0,28.44-.43Z"></path></svg>);
        case 'best-entrance':
            return (<svg id="best-entrance-icon" style={style} viewBox="0 0 165 165" class="border-color-setter font-color-setter" alt="best entrance" aria-label="best entrance icon"><circle class="cls-1" cx="82.5" cy="83.06" r="79.8"></circle><text class="cls-2 background-color-setter" style={fill} transform="translate(53.75 72.39)">BEST<tspan class="cls-3 font-color-setter"><tspan x="-35.65" y="29.83">ENT</tspan><tspan class="cls-4" x="11.24" y="29.83">R</tspan><tspan class="cls-5" x="27.07" y="29.83">ANCE</tspan></tspan></text></svg>);
        case 'restaurant':
            return (<svg id="menu-icon" style={style} aria-label="restaurant icon"><circle class="cls-1 border-color-setter" cx="82.5" cy="83.06" r="79.8"></circle><path class="cls-2 background-color-setter" style={fill} d="M88.78,88.83l5.35-5.54a1.69,1.69,0,0,1,1.74-.41,13.1,13.1,0,0,0,4.41.72c6.07,0,13.23-3.53,19.15-9.45,9.31-9.31,12-21.82,5.89-27.88A12,12,0,0,0,116.6,43c-6.25,0-13.41,3.43-19.15,9.17-7.55,7.55-11.06,17-8.72,23.56a1.66,1.66,0,0,1-.41,1.74L82.61,83l-5.15-4.89a3.48,3.48,0,0,1-.91-3.59,14.4,14.4,0,0,0-3.76-14.44L50.44,37.71A1,1,0,0,0,49,39.14L71.36,61.48a12.37,12.37,0,0,1,3.27,12.39,5.51,5.51,0,0,0,1.44,5.67l5.09,4.83-2.65,2.55-4.79-5a5.51,5.51,0,0,0-5.67-1.44,12.37,12.37,0,0,1-12.39-3.27L33.32,54.83a1,1,0,0,0-1.42,1.42L54.24,78.6a14.4,14.4,0,0,0,14.44,3.76,3.48,3.48,0,0,1,3.59.91l4.8,5.05L47.14,117.19a5.14,5.14,0,0,0,3.54,8.87h.07a5.1,5.1,0,0,0,3.66-1.6L83.12,94.7l28.26,29.76a5.1,5.1,0,0,0,3.66,1.6h.07a5.14,5.14,0,0,0,3.54-8.87ZM53,123.07a3.11,3.11,0,0,1-2.23,1h0a3.13,3.13,0,0,1-2.15-5.4L89.71,78.92A3.67,3.67,0,0,0,90.62,75c-2.08-5.82,1.24-14.45,8.25-21.46,5.37-5.37,12-8.58,17.73-8.58a10,10,0,0,1,7.3,2.69c5.28,5.28,2.63,16.51-5.89,25-5.55,5.55-12.17,8.86-17.72,8.86A11.1,11.1,0,0,1,96.55,81a3.63,3.63,0,0,0-1.22-.21,3.7,3.7,0,0,0-2.65,1.12Zm64.37.06a3.13,3.13,0,0,1-4.48-.06L84.52,93.25l2.86-3,29.88,28.37a3.13,3.13,0,0,1,.06,4.48Z"></path><path class="cls-2 background-color-setter" d="M65,66.4A1,1,0,1,0,66.44,65L44.81,43.35a1,1,0,1,0-1.42,1.42Z"></path><path class="cls-2 background-color-setter" d="M59.16,72.25a1,1,0,1,0,1.42-1.42L39,49.2a1,1,0,0,0-1.42,1.42Z"></path></svg>);
        default:
            return (<div />)
    }
};

export default Start;