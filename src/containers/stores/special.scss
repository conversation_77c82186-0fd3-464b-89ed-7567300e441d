:root {
    --heading-line-height: 1.2;
    /* capital letters - used in combo with the lhCrop mixin */
    --font-primary-capital-letter: 0.75;
}

/* lhCrop mixin - crop top space on text elements */
@mixin lhCrop($line-height, $capital-letter: 1) {
    &::before {
        content: "";
        display: block;
        height: 0;
        width: 0;
        margin-top: calc((#{$capital-letter} - #{$line-height}) * 0.5em);
    }
}

.store-details__special {
    padding-top: 2rem;
    padding-bottom: 2rem;

    @media screen and (min-width: 768px) {
        padding-top: 2rem;
        padding-bottom: unset;
    }

    @media screen and (min-width: 1200px) {
        padding-top: 5rem;
    }

    .main-content-wrapper {
        // @media screen and (min-width: 768px) {
        @media screen and (min-width: 992px) {
            display: flex;
            flex-flow: row nowrap;
            padding-bottom: 3rem;
        }
    }

    .left-column-container {
        margin-bottom: 1rem;

        // @media screen and (min-width: 768px) {
        @media screen and (min-width: 992px) {
            width: 33%;
            // margin-right: 1rem;
            margin-right: 2rem;
            margin-bottom: unset;

            & > * {
                margin-bottom: 1rem;
            }
        }

        @media screen and (min-width: 1100px) {
            margin-right: unset;
        }
    }

    .logo-container {
        margin-bottom: 2.5rem;

        img {
            border: 1px solid #d3d3d3;
        }
        
        // @media screen and (min-width: 768px) {
            @media screen and (min-width: 992px) {
                height: 215px;
                width: 325px;
            
            img {
                height: 100%;
                width: 100%;
                object-fit: contain;
            }
        }

    }

    .social-media-container {
        svg {
            height: 1em;
            width: 1em;
        }
    }

    .social-icons-wrapper {
        display: flex;
        align-items: center;

        & > * {
            margin-right: 0.5rem;
        }
    }

    .link-wrapper {
        display: flex;
        align-items: center;

        svg {
            height: 1em;
            width: 1em;
        }
    }

    .website-link-icon,
    .phone-link-icon,
    .location-link-icon,
    .gift-card.icon {
        margin-right: 0.5rem;
    }

    .website-link,
    .phone-link {
        text-decoration: underline;
    }

    .right-column-container {
        @media screen and (min-width: 768px) {
            padding-bottom: 2rem;
        }
        @media screen and (min-width: 992px) {
            padding-bottom: 0;
            width: 66%;
        }
    }

    .store-flag {
        position: relative;
        text-align: center;
        width: fit-content;
        margin-bottom: 1rem;

        @media screen and (min-width: 768px) {
            margin-bottom: 2rem;
        }

        // &:after {
        //     right: -2em;
        //     border-left-width: 1.5em;
        //     border-right-color: transparent !important;
        //     content: "";
        //     position: absolute;
        //     display: block;
        //     top: 50%;
        //     transform: translateY(-50%);
        //     border: 1.03rem solid black;
        //     z-index: -1;
        // }

        .custom {
            color: #fff;
            padding: 0.5rem;
        }
    }

    .store-heading {
        @media screen and (min-width: 768px) {
            margin-bottom: 1rem;
            line-height: var(--heading-line-height);
            /* lhCrop mixin - crop top space on text elements */
            @include lhCrop(
                var(--heading-line-height),
                var(--font-primary-capital-letter)
            );

        }
    }

    .store-copy {
        @media screen and (min-width: 768px) {
            margin-bottom: 3rem;
        }
    }

    .hours-special {
        h2 {
            font-size: 1.5rem;
        }
    }
    .happenings-title {
        text-align: center;
        padding-top: 2rem;
        margin-bottom: 2rem;
        font-size: 2.5rem;
    }

    .happenings-posts-wrapper {
        @media screen and (min-width: 768px) {
            display: grid;
            grid-auto-flow: row;
            grid-template-columns: repeat(3, 1fr);
            width: 100%;
        }
    }

    .post {
        @media (max-width: 767px) {
            border-bottom: 1px solid black;
            padding-bottom: 1.5rem;
            margin-bottom: 1.5rem;

            &:last-child {
                border-bottom: unset;
            }
        }

        .post-type-label {
            font-weight: bolder;
            text-decoration: underline;
            margin-bottom: 1rem;
        }

        .post-title {
            width: fit-content;
            margin-bottom: 1rem;
        }

        .post-excerpt {
            margin-bottom: 1rem;
        }

        .post-button {
            width: fit-content;
            cursor: pointer;
            text-decoration: underline;
        }

        @media screen and (min-width: 768px) {
            margin-bottom: 4rem;
            padding: 0 3rem;
            border-right: 1px solid black;

            &:nth-child(3n) {
                border-right: unset;
            }
        }
    }
}
