/*******************************************************************************************************
   Copyright 2021 - Calise Partners LLC d/b/a Imaginuity. All rights are reserved.
   Reproduction or transmission in whole or in part, in any form or by any means, electronic, mechanical
or otherwise, is prohibited without the prior written consent of Calise Partners LLC d/b/a Imaginuity.
********************************************************************************************************
   Project : 10618 Maverick Platform Development
   Author : Imaginuity Developers (<PERSON>, <PERSON>)
   Description : store details page
   Creation Date : Tue May 18 2021
   Documentation : [documentation url - point to default wiki for project if no specific documentation for script]
 ********************************************************************************************************/

//TODO: find open table icon to use

import React, { useState, useEffect } from 'react';
import { decode } from 'html-entities';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faFacebookF, faInstagram, faPinterest, faYoutube } from '@fortawesome/fontawesome-free-brands';
import { faXTwitter, faTiktok } from "@fortawesome/free-brands-svg-icons";

// Partials
import Table from 'src/partials/hours/table';
const Reduced = React.lazy(() => import('src/partials/hours/reduced'));
const List = React.lazy(() => import('src/partials/hours/list'));
import Special from 'src/partials/hours/special';
import CondensedList from 'src/partials/hours/condensed-list';

import Icon from './icon';

// Helpers
const Imaging = React.lazy(() => import('src/helpers/imaging'));
const Clicker = React.lazy(() => import('src/helpers/clicker'));
import { BackgroundClass, TextClass, BackgroundColor } from 'src/helpers/theme';
import { WeeksHours } from 'src/helpers/hours';
const HtmlParser = React.lazy(() => import('src/helpers/html-parser'));

// SCSS.
import 'src/containers/stores/default.scss';

const Start = ({ page, settings }) => {
    return (<Container page={page} settings={settings} />);
};

const Container = ({ page, settings }) => {

    const socialItems = [];
    // check if not negative
    socialItems.push(
        (!!page.mvk_item_content.custom_fields.facebook),
        (!!page.mvk_item_content.custom_fields.twitter),
        (!!page.mvk_item_content.custom_fields.instagram),
        (!!page.mvk_item_content.custom_fields.pinterest),
        (!!page.mvk_item_content.custom_fields.youtube),
        (!!page?.mvk_item_content?.custom_fields?.tiktok)
    );

    let ctaColor = settings?.store?.cta_background_color ? BackgroundColor(settings.store?.cta_background_color) : 'grey';
    let iconStyle = {
        color: ctaColor,
        stroke: ctaColor
    }
    let socialStyle = {
        borderColor: ctaColor,
        color: ctaColor
    }

    return (
        <main class='store-details__page grid-container'>
            <div class='main-content-wrapper'>
                {page.mvk_item_content.custom_fields.logo_color &&
                    <div class='logo-container'>
                        <Imaging class='logo-color' data={page.mvk_item_content.custom_fields.logo_color} />
                    </div>
                }
                {(!page.mvk_item_content.custom_fields.logo_color && page.mvk_item_content.custom_fields.logo_monochrome) &&
                    <div class='logo-container'>
                        <Imaging class='logo-monochrome' data={page.mvk_item_content.custom_fields.logo_monochrome} />
                    </div>
                }

                {page.mvk_item_content.title &&
                    <div class='title-container'>
                        <h1>{decode(page.mvk_item_content.title)}</h1>
                    </div>
                }

                {page.mvk_item_content.custom_fields.website &&
                    <div class='website-link-container'>
                        <a href={page.mvk_item_content.custom_fields.website} target='_blank' aria-label={`Visit Website For ${page.mvk_item_content.title}`}>{settings.store?.visit_website_label ? settings.store?.visit_website_label : 'Visit Website'}</a>
                    </div>
                }
                {page.mvk_item_content.custom_fields.store_copy &&
                    <div class='store-copy-container'>
                        <HtmlParser html={page.mvk_item_content.custom_fields.store_copy} />
                    </div>
                }

                <div class='related-container'>
                    {page.mvk_item_content.custom_fields.related_jobs && <Modal page={page} type={'Jobs'} items={page.mvk_item_content.custom_fields.related_jobs} settings={settings} />}
                    {page.mvk_item_content.custom_fields.related_sales && <Modal page={page} type={'Sales'} items={page.mvk_item_content.custom_fields.related_sales} settings={settings} />}
                </div>

                <div class='store-info-container'>
                    {page.mvk_item_content.custom_fields.phone_number &&
                        <div class='phone-wrapper'>
                            <a class='phone-icon-link' href={`tel: ${page.mvk_item_content.custom_fields.phone_number}`} aria-label='phone icon'>
                                {settings.store?.phone_number_icon &&
                                    <Imaging data={settings.store?.phone_number_icon} />
                                }
                                {!settings.store?.phone_number_icon &&
                                    <Icon type='phone' style={iconStyle} />
                                }
                            </a>

                            <a class='phone-link body-copy-txt' href={`tel: ${page.mvk_item_content.custom_fields.phone_number}`}>
                                {page.mvk_item_content.custom_fields.phone_number}
                            </a>
                        </div>
                    }

                    {page.mvk_item_content.custom_fields.location &&
                        <Location page={page} settings={settings} style={iconStyle} />
                    }

                    {page.mvk_item_content.custom_fields.best_entrance &&
                        <div class='best-entrance-wrapper'>
                            <div>
                                {settings.store?.best_entrance_icon &&
                                    <Imaging data={settings.store?.best_entrance_icon} />
                                }
                                {!settings.store?.best_entrance_icon &&
                                    <Icon type='best-entrance' style={iconStyle} />
                                }
                            </div>
                            <span>{page.mvk_item_content.custom_fields.best_entrance}</span>
                        </div>
                    }

                    {page.mvk_item_content.custom_fields.restaurant_menu &&
                        <div class='restaurant-menu-wrapper'>
                            <a class='menu-link' href={page.mvk_item_content.custom_fields.restaurant_menu.url} target='_blank'>
                                <Icon type='restaurant' style={iconStyle} />
                            </a>
                            <a class='menu-link body-copy-txt' href={page.mvk_item_content.custom_fields.restaurant_menu.url} target='_blank'>
                                Restaurant Menu
                            </a>
                        </div>
                    }
                </div>
                {(settings?.store?.store_hours_style && settings.store?.store_hours_style !== 'none') &&
                    <Hours store={page.mvk_item_content.custom_fields} style={settings.store?.store_hours_style} page={page} settings={settings} />
                }

                {socialItems.includes(true) &&
                    <div class='social-media-container'>
                        <h2>The Social Scene</h2>
                        <div class='social-icons-wrapper'>
                            {page.mvk_item_content.custom_fields.facebook &&
                                <a class='facebook-icon' href={page.mvk_item_content.custom_fields.facebook} rel="noopener" target='_blank' title='Facebook' style={socialStyle} >
                                    <FontAwesomeIcon icon={faFacebookF} />
                                </a>
                            }
                            {page.mvk_item_content.custom_fields.twitter &&
                                <a class='twitter-icon' href={page.mvk_item_content.custom_fields.twitter} rel="noopener" target='_blank' title='Twitter' style={socialStyle} >
                                    <FontAwesomeIcon icon={faXTwitter} />
                                </a>
                            }
                            {page.mvk_item_content.custom_fields.instagram &&
                                <a class='instagram-icon' href={page.mvk_item_content.custom_fields.instagram} rel="noopener" target='_blank' title='Instagram' style={socialStyle} >
                                    <FontAwesomeIcon icon={faInstagram} />
                                </a>
                            }
                            {page.mvk_item_content.custom_fields.pinterest &&
                                <a class='pinterest-icon' href={page.mvk_item_content.custom_fields.pinterest} rel="noopener" target='_blank' title='Pinterest' style={socialStyle} >
                                    <FontAwesomeIcon icon={faPinterest} />
                                </a>
                            }
                            {page.mvk_item_content.custom_fields.youtube &&
                                <a class='youtube-icon' href={page.mvk_item_content.custom_fields.youtube} rel="noopener" target='_blank' title='YouTube' style={socialStyle} >
                                    <FontAwesomeIcon icon={faYoutube} />
                                </a>
                            }
                            {page?.mvk_item_content?.custom_fields?.tiktok &&
                                <a class='icon tiktok' href={page?.mvk_item_content?.custom_fields?.tiktok} rel="noopener" target='_blank' title='YouTube' style={socialStyle} >
                                    <FontAwesomeIcon icon={faTiktok} />
                                </a>
                            }
                        </div>
                    </div>
                }
            </div>

            <div class='featured-video-container' dangerouslySetInnerHTML={{ __html: page.mvk_item_content.custom_fields.featured_video_type === 'youtube' ? page.mvk_item_content.custom_fields.featured_video_youtube : page.mvk_item_content.custom_fields.featured_video_vimeo }} />
        </main>
    );
};

const Hours = ({ style, store, page, settings }) => {
    const data = WeeksHours(style, store.hours);
    const specialInstructions = (store.hours && store.hours.standard_hours) ? store.hours.standard_hours[0].special_instructions : '';

    if (store?.hours?.custom_hours_message) {
        return (<div>{store.hours.custom_hours_message}</div>);
    } else if (store.hours.alternate_hours || store.hours.seasonal_hours || (store.hours.standard_hours && (store.hours.standard_hours[0].monday_open || store.hours.standard_hours[0].monday_closed))) {
        switch (style) {
            case 'table':
                return (<Table title={settings?.store?.when_to_shop_label ? settings?.store?.when_to_shop_label : 'When to Shop'} hours={data} special={specialInstructions} store={true} />);
            case 'reduced':
                return (<Reduced title={specialInstructions} hours={data} store={true} />);
            case 'list':
                return (<List title={settings?.store?.when_to_shop_label ? settings?.store?.when_to_shop_label : 'When to Shop'} hours={data} special={specialInstructions} store={true} />);
            case 'special':
                return (<Special title={settings?.store?.when_to_shop_label ? settings?.store?.when_to_shop_label : 'When to Shop:'} hours={data} special={specialInstructions} store={true} />);
            case 'condensed_list':
                return (<CondensedList title={settings?.store?.when_to_shop_label ? settings?.store?.when_to_shop_label : ''} hours={data} special={specialInstructions} store={true} />);
            default:
                return null;
        };
    } else if (settings?.hours?.custom_hours_message) {
        return (<div>{settings?.hours?.custom_hours_message}</div>);
    } else {
        switch (style) {
            case 'table':
                return (<Table title={settings?.store?.when_to_shop_label ? settings?.store?.when_to_shop_label : 'When to Shop'} hours={data} special={specialInstructions} store={true} />);
            case 'reduced':
                return (<Reduced title={specialInstructions} hours={data} store={true} />);
            case 'list':
                return (<List title={settings?.store?.when_to_shop_label ? settings?.store?.when_to_shop_label : 'When to Shop'} hours={data} special={specialInstructions} store={true} />);
            case 'special':
                return (<Special title={settings?.store?.when_to_shop_label ? settings?.store?.when_to_shop_label : 'When to Shop:'} hours={data} special={specialInstructions} store={true} />);
            case 'condensed_list':
                return (<CondensedList title={settings?.store?.when_to_shop_label ? settings?.store?.when_to_shop_label : ''} hours={data} special={specialInstructions} store={true} />);
            default:
                return null;
        };
    }
}

const Modal = ({ page, type, items, settings }) => {
    const [isOpen, setIsOpen] = useState(false);
    let backgroundClass = BackgroundClass(settings.store?.cta_background_color);
    let textClass = TextClass(settings.store?.cta_background_value);

    // NOTE: USE THE MODAL HELPER INSTEAD.
    return (
        <div class='related-cta-container'>
            <button class={`modal-trigger ${backgroundClass} ${textClass}`} onClick={() => setIsOpen(true)}><h2>{type}</h2></button>
            {!isOpen ? null :
                <>
                    <div class='modal-overlay' onClick={() => setIsOpen(false)}></div>
                    <div class='related-modal'>
                        <div class='modal-content'>
                            <div class='modal-header'>
                                <h4 class='modal-title body-copy-txt'>
                                    {`${page.mvk_item_content.title} - ${type}`}
                                </h4>
                                <button type='button' class='close-button' aria-label='Close Modal' onClick={() => setIsOpen(false)}>
                                    <span aria-hidden='true'>&times;</span>
                                </button>
                            </div>

                            <div class='modal-body'>
                                {items?.map((item, index) => (
                                    <h4 key={index}>
                                        <Clicker class='body-copy-txt' type='anchor' target={item.external_link} url={item.url} title={item.title}>
                                            {decode(item.title)}
                                        </Clicker>
                                    </h4>
                                ))}
                            </div>

                            <div class='modal-footer'>
                                <button type='button' class='close-button' aria-label='Close Modal' onClick={() => setIsOpen(false)}>Close</button>
                            </div>
                        </div>
                    </div>
                </>
            }
        </div>
    );
};

const Location = ({ page, settings, style }) => {

    let location_link = settings?.store?.location_link === 'google-maps' ? `https://www.google.com/maps/place/${page.mvk_item_content.custom_fields.location.replace(/ /g, '+')}` : settings.center_info.printable_directory;

    return (
        <div class='location-wrapper'>
            {(settings?.store?.location_link === 'no-link' && page.mvk_item_content?.custom_fields?.location) &&
                <>
                    <div class='location-icon-link' href={location_link} target='_blank'>
                        {settings.store?.location_icon &&
                            <Imaging data={settings.store?.location_icon} />
                        }
                        {!settings.store?.location_icon &&
                            <Icon type='location' style={style} />
                        }
                    </div>
                    <div class='location-link body-copy-txt' href={location_link} target='_blank'>
                        {page.mvk_item_content.custom_fields.location}
                    </div>
                </>
            }
            {(settings?.store?.location_link === 'google-maps' || settings?.store?.location_link === 'printable-directory') &&
                <>
                    <a class='location-icon-link' href={location_link} target='_blank'>
                        {settings.store?.location_icon &&
                            <Imaging data={settings.store?.location_icon} />
                        }
                        {!settings.store?.location_icon &&
                            <Icon type='location' style={style} />
                        }
                    </a>
                    <a class='location-link body-copy-txt' href={location_link} target='_blank'>
                        {page.mvk_item_content.custom_fields.location}
                    </a>
                </>
            }
            {(settings?.store?.location_link === 'interactive-map' && settings?.store?.interactive_map_page) &&
                <>
                    <Clicker type='anchor' url={settings?.store?.interactive_map_page?.url} class='location-icon-link'>
                        {settings.store?.location_icon &&
                            <Imaging data={settings.store?.location_icon} />
                        }
                        {!settings.store?.location_icon &&
                            <Icon type='location' style={style} />
                        }
                    </Clicker>
                    <Clicker type='anchor' url={settings?.store?.interactive_map_page?.url} class='location-link body-copy-txt'>
                        {settings?.store?.interactive_map_page.title ? settings?.store?.interactive_map_page.title : 'View on Mall Map'}
                    </Clicker>
                </>
            }
        </div>
    );
}


export default Start;
