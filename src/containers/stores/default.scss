/*******************************************************************************************************
   Copyright 2021 - Calise Partners LLC d/b/a Imaginuity. All rights are reserved. 
   Reproduction or transmission in whole or in part, in any form or by any means, electronic, mechanical 
or otherwise, is prohibited without the prior written consent of Calise Partners LLC d/b/a Imaginuity.
********************************************************************************************************
   Project : 10618 Maverick Platform Development
   Author : Imaginuity Developers (<PERSON>, <PERSON>)
   Description : styling for the store details page
   Creation Date : Tue May 18 2021
   Documentation : [documentation url - point to default wiki for project if no specific documentation for script]
 ********************************************************************************************************/
@import "src/scss/variables.scss";

.store-details__page {
    margin-top: 3rem;

    & > div {
        margin-bottom: 1rem;
    }

    .main-content-wrapper {
        display: flex;
        flex-flow: column nowrap;
        align-items: center;

        & > div {
            margin-bottom: 1rem;
        }
    }

    .hours-module {
        .hours-list {
            display: flex;
            justify-content: center;
        }
        .condensed-list {
            display: flex;
            justify-content: center;
            text-align: center;
        }
    }

    .title-container {
        border-bottom: 0.0625rem solid #eee;
        width: 100%;
        text-align: center;
        padding-bottom: 1rem;
    }

    .website-link-container {
        a {
            text-decoration: underline;
            font-size: 1rem;
        }
    }

    .store-copy-container {
        text-align: center;
    }

    .related-container {
        display: flex;
        flex-wrap: wrap;
        width: 100%;
        justify-content: center;
        .related-cta-container {
            width: 100%;
            max-width: 28.125rem;
            margin: 0.5rem;
            .modal-trigger {
                font-family: inherit;
                width: 100%;
                border: none;
                padding: 18px 50px;
            }
        }
    }

    .modal-overlay {
        position: fixed;
        top: 0;
        left: 0;
        bottom: 0;
        right: 0;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: 1000;
    }

    @keyframes modalSlide {
        0% {
            transform: translate(-50%, -100%);
        }
        100% {
            transform: translate(-50%, -50%);
        }
    }
    .related-modal {
        position: fixed;
        width: 90%;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background-color: #fff;
        z-index: 1000;
        border: 0.0625rem solid rgba(0, 0, 0, 0.2);
        border-radius: 0.375rem;
        outline: 0;
        animation: modalSlide 0.3s ease-out;
        @media screen and (min-width: 768px) {
            width: 37.5rem;
        }

        .modal-content {
            padding: 1rem;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 0.0625rem solid #e5e5e5;
            padding-bottom: 1rem;

            .modal-title {
                color: #5a5c66;
            }

            .close-button {
                background: none;
                border: none;
                outline: none;
                width: unset;
                padding: unset;
            }

            span {
                font-size: 1.5rem;
                opacity: 0.2;

                &:hover {
                    color: #000;
                    opacity: 0.5;
                }
            }
        }

        .modal-body {
            padding-top: 1rem;
            border-bottom: 0.0625rem solid #e5e5e5;

            & > * {
                text-decoration: underline;
                padding-bottom: 1rem;

                // a {
                //     color: #337ab7 !important;

                //     &:hover {
                //         color: #23527c !important;
                //     }
                // }
            }
        }

        .modal-footer {
            padding-top: 1rem;
            text-align: right;

            .close-button {
                width: unset;
                padding: 0.375rem 0.75rem;
                color: #333;
                background-color: #fff;
                outline: none;
                border: 0.0625rem solid #adadad;
                border-radius: 0.25rem;
                font-weight: 400;
                font-size: 0.875rem;

                &:hover {
                    background-color: #e6e6e6;
                }
            }
        }
    }

    .store-info-container {
        font-size: 1rem;

        & > div {
            margin: 1rem;
            text-align: center;
            max-width: 300px;
            & > :first-child {
                display: flex;
                justify-content: center;
                // flex-flow: column nowrap;
                text-align: center;

                img {
                    margin-bottom: 0.5rem;
                }
            }
        }

        @media screen and (min-width: 768px) {
            display: flex;
            flex-flow: row wrap;
            justify-content: center;
        }
        svg {
            width: 165px;
            height: 165px;
            margin-bottom: 0.5rem;

            &#best-entrance-icon {
                .cls-1 {
                    fill: none;
                    stroke-miterlimit: 10;
                    stroke-width: 3px;
                }
                .cls-2 {
                    font-size: 24.86px;
                    font-family: AzoSans-Bold, Azo Sans, sans-serif;
                    font-weight: 700;
                }
                .cls-2,
                .cls-5 {
                    letter-spacing: -0.04em;
                }
                .cls-3 {
                    letter-spacing: -0.04em;
                }
                .cls-4 {
                    letter-spacing: -0.02em;
                }
            }

            &#menu-icon {
                width: 165px;
                height: 165px;
                .cls-1 {
                    fill: none;
                    stroke-miterlimit: 10;
                    stroke-width: 3px;
                }
            }

            &#location-icon {
                width: 165px;
                height: 165px;
                .cls-1,
                .cls-2 {
                    fill: none;
                    stroke-miterlimit: 10;
                    stroke-width: 3px;
                }
            }

            &#phone-icon {
                width: 165px;
                height: 165px;
                .cls-1,
                .cls-2,
                path {
                    fill: none;
                    stroke-miterlimit: 10;
                    stroke-width: 3px;
                }
            }
        }
    }

    .phone-link,
    .menu-link,
    .location-link {
        text-decoration: underline;
    }

    .social-media-container {
        display: flex;
        flex-flow: column nowrap;
        align-items: center;

        h2 {
            margin-bottom: 1rem;
        }
    }

    .social-icons-wrapper {
        @media screen and (min-width: 768px) {
            display: flex;
            flex-flow: row wrap;
            justify-content: center;
        }

        & > a {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 1.5rem;
            height: 1.5rem;
            padding: 2.5rem;
            border: 0.1875rem solid;
            border-radius: 50%;
            margin: 0.5rem;

            svg {
                color: inherit;
                height: 3.125rem;
                width: 3.125rem;
            }
        }
    }

    .featured-video-container {
        @media screen and (min-width: 768px) {
            width: 66%;
            margin: 0 auto;
        }
    }
}

@keyframes fadeIn {
    0% {
        opacity: 0;
    }
    100% {
        opacity: 1;
    }
}
@keyframes modalSlide {
    0% {
        transform: translate(-50%, -70%);
    }
    100% {
        transform: translate(-50%, -50%);
    }
}
