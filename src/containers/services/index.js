/*******************************************************************************************************
   Copyright 2020 - Calise Partners LLC d/b/a Imaginuity. All rights are reserved.
   Reproduction or transmission in whole or in part, in any form or by any means, electronic, mechanical
or otherwise, is prohibited without the prior written consent of Calise Partners LLC d/b/a Imaginuity.
********************************************************************************************************
   Project : 10618 Maverick Platform Development
   Author : Imaginuity Developers (<PERSON>)
   Description : basic services component
   Creation Date : Wed Oct 16 2024
   Documentation : [documentation url - point to default wiki for project if no specific documentation for script]
 ********************************************************************************************************/

import React from "react";

// Templated Service
const TemplatedService = React.lazy(() => import('./templated-service'));

const Start = ({ data, settings }) => {
    if (settings.services?.service_templates && data?.mvk_template) {
        return (
            <TemplatedService data={data} settings={settings} />
        )
    } else {
        return null;
    }
};

export default Start;