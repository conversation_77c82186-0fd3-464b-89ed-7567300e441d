import React, { useContext, useState, useEffect } from "react";
// Context
import { ServiceTemplatesContext } from "src/context";
// Components
const Modules = React.lazy(() => import('src/modules'));
import Schema from 'src/partials/schema/service';

const Start = ({ data, settings }) => {
    const [serviceTemplates, setServiceTemplates] = useContext(ServiceTemplatesContext);
    const [template, setTemplate] = useState(null)

    useEffect(() => {
        setTemplate((!serviceTemplates.loading && serviceTemplates && data?.mvk_template) ? serviceTemplates?.find(template => template?.mvk_slug === data?.mvk_template) : null)
    }, [data.mvk_template, serviceTemplates])
   
    return (
        <div
            id='service-post'
            className={`service-post`}
        >
            <Schema data={data} settings={settings} />
            {template && <Modules page={data} layout={template?.layout} />}
        </div>
    )
}

export default Start;