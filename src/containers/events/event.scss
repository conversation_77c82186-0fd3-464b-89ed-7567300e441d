// maybe we could add these to a global sass variables file?
$sm_breakpoint: 768px;
$md_breakpoint: 1200px;

$change_layout_breakpoint: $md_breakpoint; // this is where it will cahnge to a row instead of column

.event-detail__module {
    // margin-top: 150px; // !IMPORTANT! -- this will need to get deleted ( used to avoid the weird nav overflow )
    // these general padding / container styles should likely make it into globals at some point
    // padding: 30px; // matching to nav / footer container
    padding: 30px 0;
    // @media (min-width: 768px) {
    //     // matching to nav / footer container
    //     padding: 30px 40px;
    // }

    // @media (min-width: 992px) {
    //     // matching to nav / footer container
    //     padding: 30px 55px;
    // }

    // & > .flexbox {
    //     & > * {
    //         margin: 0 15px;

    //         &:first-child {
    //             margin-left: 0;
    //         }

    //         &:last-child {
    //             margin-right: 0;
    //         }
    //     }
    // }

    .content-container {
        @media (max-width: 1199px) {
            flex-direction: column-reverse;
        }
    }

    .events-breadcrumb {
        text-decoration: underline;
        display: block;
        width: fit-content;
        margin-bottom: 2rem;
    }

    .event-detail-text-content {
        width: 100%;

        @media (min-width: $change_layout_breakpoint) {
            width: 60%;
            // padding-right: 40px;
        }
    }

    .event-detail-title {
        font-size: 1.5rem;
        font-weight: 700;
        margin-bottom: 1rem;

        @media (min-width: $sm_breakpoint) {
            font-size: 2.75rem;
        }

        @media (min-width: $md_breakpoint) {
            // font-size: 3.25rem;
            font-size: 3rem;
        }
    }

    .event-detail-dates {
        font-size: 1.125rem;
        margin: 0;

        @media (min-width: $sm_breakpoint) {
            font-size: 1.75rem;
        }

        @media (min-width: $md_breakpoint) {
            font-size: 2.25rem;
        }
    }

    .event-detail-subtitle {
        font-size: 0.875rem;
        margin: 1rem 0;

        @media (min-width: $sm_breakpoint) {
            font-size: 1.125rem;
        }

        @media (min-width: $md_breakpoint) {
            font-size: 1.25rem;
        }
    }

    .related-store {
        display: block;
        margin: 1rem 0;
        text-decoration: underline;
        font-size: 1.5rem;
    }

    .event-detail-description {
        font-size: 0.75rem;
        margin: 20px 0;

        @media (min-width: $sm_breakpoint) {
            font-size: 1rem;
            margin-bottom: 30px;
        }

        @media (min-width: $md_breakpoint) {
            font-size: 1.125rem;
            margin-top: 1.875rem;
            max-width: 700px;
        }

        p {
            font-size: 1.25rem;
        }
    }

    .event-detail-media-items {
        width: 100%;

        @media (min-width: $change_layout_breakpoint) {
            flex: 1;
        }

        @media (max-width: 1199px) {
            margin-bottom: 1rem;
        }
    }

    .event-detail-image img {
        width: 100%;
    }

    .event-detail-socials-container {
        display: flex;
        align-items: center;
        margin-top: 20px;
        add-to-calendar-button {
            margin-right: 1rem;
        }
    }

    .event-detail-social-button {
        display: flex;
        align-items: center;
        margin-right: 25px;
        transition: 0.3s opacity;
        cursor: pointer;

        @media (min-width: $sm_breakpoint) {
            margin-right: 35px;
        }

        @media (min-width: $md_breakpoint) {
            margin-right: 45px;
        }
    }

    .event-detail-social-icon {
        height: 20px;
        width: auto;
        margin-right: 10px;

        @media (min-width: $sm_breakpoint) {
            height: 25px;
        }

        @media (min-width: $md_breakpoint) {
            height: 30px;
        }
    }

    &.full-width {
        .event-detail-text-content {
            width: 100%;
            .event-detail-description {
                max-width: none;
            }
        }
        .event-detail-media-items {
            display: none;
        }
    }
    @media (min-width: $md_breakpoint) {
        &.image-left-content-right .event-detail-text-content {
            order: 2;
            margin-left: 2rem;
        }
    }
}

.event-hero img {
    width: 100%;
}
