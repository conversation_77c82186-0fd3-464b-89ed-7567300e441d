import React, { useState, useEffect, useContext } from 'react';

// Helpers
import { Coloring } from 'src/helpers';
import HtmlParser from 'src/helpers/html-parser';
const Button = React.lazy(() => import('src/partials/button'));

// Styles
import { MultiLocationEventLinks } from '../styles';

const Start = ({ data, settings }) => {

    return (
        <MultiLocationEventLinks
            className={`multilocation-event-links ${settings.event?.ml_background_type}`}
            bgColor={settings.event?.ml_background_color ? Coloring(settings.event.ml_background_color, settings) : ''}
            bgImage={settings.event?.ml_background_image ? settings.event?.ml_background_image.url : ''}
            textColor={settings.event?.ml_background_value === 'dark' ? '#fff' : Coloring('body_copy_color', settings)}
        >
            <div className='grid-container'>
                <div className='grid-x'>
                    <div className='cell'>
                        {settings.event?.ml_copy && <HtmlParser html={settings.event?.ml_copy} />}
                        <div className='button-container'>
                            {data.multilocation_related_events && data.multilocation_related_events.map((item) => <Button key={`${item.label}-link`} className="ml-button" title={item.label} url={item.url} type={settings.event?.ml_button_style} tone={settings.event?.ml_background_value} aria-label={`link to ${item.label}`} />)}
                        </div>
                    </div>
                </div>
            </div>
        </MultiLocationEventLinks>
    )

}

export default Start;