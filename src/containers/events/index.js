import React, { useState, useEffect, useContext } from 'react';
import { decode } from 'html-entities';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faShare } from '@fortawesome/free-solid-svg-icons';
import { faCalendarPlus } from '@fortawesome/free-solid-svg-icons';
import { AddToCalendarButton } from 'add-to-calendar-button-react';

// Helpers
const Imaging = React.lazy(() => import('src/helpers/imaging'));
const Clicker = React.lazy(() => import('src/helpers/clicker'));
import { FormattedDate } from 'src/helpers/date';
import { convertToFrench } from 'src/helpers/hours';
import HtmlParser from 'src/helpers/html-parser';
import { Coloring } from "src/helpers";

import './event.scss'
import SocialShareModal from 'src/partials/social-share-modal/social-share-modal';
import MultilocationEventLinks from './multilocation-event-links';
// Context
import { SettingsContext, PrincipalContext } from "src/context";
import { EventContainer } from '../styles';

const Start = ({ page }) => {
    const [settings, setSettings] = useContext(SettingsContext);
    let detailImage = page?.mvk_item_content?.custom_fields?.event_details_image ? page?.mvk_item_content?.custom_fields?.event_details_image : page?.mvk_item_content?.feat_image;
    const bgColor = Coloring(settings.event?.background_color, settings);
    const [eventsLink, setEventsLink] = useState(settings?.event?.events_link ? settings?.event?.events_link?.url : settings?.events_link?.url)
    const [eventsLinkTitle] = useState(settings?.event?.events_link ? settings?.event?.events_link?.title : settings?.events_link?.title)
    useEffect(() => {
        setEventsLink(settings.current_location && page.post_type_parent_slug ? `/${settings.current_location}/${page.post_type_parent_slug}/` : (settings?.event?.events_link ? settings?.event?.events_link?.url : settings?.events_link?.url))
    }, [settings.current_location])

    return (
        <EventContainer
            className={`event-container ${settings?.event?.background_type}`}
            bgColor={bgColor}
            bgImage={settings?.event?.background_type === 'image' ? settings?.event?.background_image?.url : ''}
            textColor={settings?.event?.background_value === 'dark' ? '#fff' : Coloring('body_copy_color', settings)}
            topPaddingSmall={settings?.event?.top_padding_small ? settings?.event?.top_padding_small : '30'}
            topPaddingLarge={settings?.event?.top_padding_large ? settings?.event?.top_padding_large : '30'}
        >
            {(settings?.event && settings?.event.hero) &&
                <div class='event-hero'>
                    <Imaging data={settings?.event.hero_image} />
                </div>
            }
            <div class={`event-detail__module ${settings?.event?.style}`}>
                {(settings?.event?.enable_back_to_events_button || settings?.enable_back_to_events_button) &&
                    <div className='grid-container'>
                        <Clicker type='anchor' class={`events-breadcrumb ${settings?.event?.background_value === 'dark' ? 'white-txt' : 'primary-txt'}`} url={eventsLink} title={eventsLinkTitle}>{decode(eventsLinkTitle)}</Clicker>
                    </div>
                }
                <div class="content-container grid-container flexbox wrap">
                    <TextContent page={page} />
                    <MediaItems page={page} detailImage={detailImage} />
                </div>
            </div>
            {(page?.mvk_item_content?.custom_fields?.multilocation_event && page?.mvk_item_content?.custom_fields?.multilocation_related_events) && <MultilocationEventLinks data={page?.mvk_item_content?.custom_fields} settings={settings} />}
        </EventContainer>
    );
}

const TextContent = ({ page }) => {
    const [principal, setPrincipal] = useContext(PrincipalContext);
    const [settings, setSettings] = useContext(SettingsContext);

    // IS THIS ONE DAY OR MORE?
    var spanType = (page?.mvk_event_data?.next_occurrence?.start?.event_date == page?.mvk_event_data?.next_occurrence?.end?.event_date) ? 'single' : 'multiple';

    // TIMES
    let eventMeta = page.mvk_event_data?.meta;
    if (eventMeta.start_time_minutes == "0") { eventMeta.start_time_minutes = "00"; }
    if (eventMeta.end_time_minutes == "0") { eventMeta.end_time_minutes = "00"; }
    let startTime = `${eventMeta.start_time_hour}:${eventMeta.start_time_minutes ?? "00"} ${eventMeta.start_time_ampm}`;
    let endTime = `${eventMeta.end_time_hour}:${eventMeta.end_time_minutes ?? "00"} ${eventMeta.end_time_ampm}`;
   
    if (principal.activeTranslation === 'fr') {
        startTime = convertToFrench(startTime);
        endTime = convertToFrench(endTime);
    }
    if (eventMeta?.hide_time != "1" && eventMeta?.allday != "1") {
        var times = startTime;
        if (eventMeta?.hide_end_time != "1") {
            times += ` - ${endTime}`
        }
    } else {
        var times = ``;
    }

    return (
        <div className="event-detail-text-content">
            <h1 className={`event-detail-title ${settings?.event?.background_value === 'dark' ? 'white-txt' : 'primary-txt'}`} dangerouslySetInnerHTML={{ __html: page.mvk_item_content.title }} />
            {spanType == 'single' && <SingleDay page={page} />}
            {spanType == 'multiple' && <MultipleDays page={page} />}
            {(eventMeta?.hide_time != "1" && eventMeta?.allday != "1") &&
                <div className="event-detail-dates">{times}</div>
            }
            {eventMeta?.comment &&
                <h2 className="event-detail-subtitle" dangerouslySetInnerHTML={{ __html: eventMeta.comment }} />
            }
            {page.mvk_item_content.custom_fields.related_store && page.mvk_item_content.custom_fields.related_store.map((store) => <p><Clicker class='related-store' type='anchor' url={store.url}>{decode(store.title)}</Clicker></p>)}
            <div className="event-detail-description"><HtmlParser html={page.mvk_item_content.content} /></div>
        </div>
    )
};

const SingleDay = ({ page }) => {
    var date = page?.mvk_event_data?.next_occurrence?.start?.event_date;
    if (date) {
        const dateString = FormattedDate(date, 'day-month-date');
        return (
            <div className="event-detail-dates">{dateString}</div>
        );
    } else {
        return null;
    }
};

const MultipleDays = ({ page }) => {
    var startDate = page?.mvk_event_data?.next_occurrence?.start?.event_date;
    var endDate = page?.mvk_event_data?.next_occurrence?.end?.event_date;
    if (startDate && endDate) {
        const dateString = `${FormattedDate(startDate, 'day-month-date')} - ${FormattedDate(endDate, 'day-month-date')}`;
        return (<div className="event-detail-dates">{dateString}</div>);
    } else {
        return null;
    }
};

const MediaItems = ({ page, detailImage }) => (

    <div className="event-detail-media-items">
        <div className="event-detail-image"><Imaging data={detailImage} /></div>
        <EventSocials page={page} />
    </div>
);

const EventSocials = ({ page }) => {
    const [settings, setSettings] = useContext(SettingsContext);
    const [isShareModalVisible, setIsShareModalVisible] = useState(false);
    function checkTime(i) {
        return (i < 10) ? "0" + i : i;
    }
    const openShareModal = () => setIsShareModalVisible(true);
    const closeShareModal = () => setIsShareModalVisible(false);
    // Dates
    const startDate = page?.mvk_event_data?.next_occurrence?.start?.event_date;
    const endDate = page?.mvk_event_data?.next_occurrence?.end?.event_date;
    // Times
    let startHour = page?.mvk_event_data?.next_occurrence?.start?.hour ? checkTime(parseInt(page?.mvk_event_data?.next_occurrence?.start?.hour)) : '00';
    let startMinutes = page?.mvk_event_data?.next_occurrence?.start?.minutes ? checkTime(parseInt(page?.mvk_event_data?.next_occurrence?.start?.minutes)) : '00';
    const startTime = `${startHour}:${startMinutes} ${page?.mvk_event_data?.next_occurrence?.start?.ampm}`;
    let endHour = page?.mvk_event_data?.next_occurrence?.end?.hour ? checkTime(parseInt(page?.mvk_event_data?.next_occurrence?.end?.hour)) : '00';
    let endMinutes = page?.mvk_event_data?.next_occurrence?.end?.minutes ? checkTime(parseInt(page?.mvk_event_data?.next_occurrence?.end?.minutes)) : '00';
    const endTime = `${endHour}:${endMinutes} ${page?.mvk_event_data?.next_occurrence?.end?.ampm}`;
    
    return (
        <div className="event-detail-socials-container">
            <AddToCalendarButton
                name={decode(page?.mvk_item_content?.title)}
                description={page?.mvk_item_content?.excerpt}
                options={['Apple','Google','iCal','Outlook.com','Yahoo']}
                startDate={startDate}
                endDate={endDate}
                startTime={startTime}
                endTime={endTime}
                trigger="click"
                label={settings?.event?.add_to_calendar_label ? settings?.event?.add_to_calendar_label : 'Add to Calendar'}
                hideBackground
            ></AddToCalendarButton>
            <a onClick={openShareModal} className={`event-detail-social-button  ${settings?.event?.background_value === 'dark' ? 'white-txt' : 'body-copy-txt'}`}>
                <FontAwesomeIcon className="event-detail-social-icon" icon={faShare} size='lg' />
                {settings?.event?.share_label ? settings?.event?.share_label : 'Share'}
            </a>
            <SocialShareModal isVisible={isShareModalVisible} closeModal={closeShareModal} />
        </div>
    )
};

export default Start
