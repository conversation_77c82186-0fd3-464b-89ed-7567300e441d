import styled from 'styled-components';

export const PostContainer = styled.div`
    &:not(.templated) {
        padding: 40px 0;
    }
    &.with-nav {
        padding-bottom: 0;
    }
    &.block-title {
        padding-top: 0;
        .title {
            border-bottom: none;
        }
    }
    .title {
        border-bottom: 1px solid #ddd;
        padding-bottom: 1rem;
    }
    .message {
        font-size: 0.875rem;
        @media (min-width: 768px) {
            font-size: 1rem;
        }
    }
    .post-data {
        font-size: 0.875rem;
        & > span {
            margin: 0.25rem 0;
            display: inline-block;
            &:not(.cats)::after {
                content: "|";
                margin: 0 0.5rem;
                color: #ccc;
            }
        }
        .cats .category:last-of-type .comma {
            display: none;
        }
    }
    .post-body {
        margin-bottom: 4rem;
        .featured-image {
            width: 100%;
            margin-bottom: 1rem;
        }
    }
    .sidebar {
        margin-top: 2rem;
        .recent-posts {
            margin: 1rem 0 2rem;
            .post-wrap {
                display: flex;
                align-items: center;
                margin-bottom: 1.5rem;
                .featured-image {
                    width: 80px;
                    height: 80px;
                    position: relative;
                    overflow: hidden;
                    border-radius: 8px;
                    margin: 0 1rem 0 0;
                    img {
                        height: 100%;
                        width: 100%;
                        object-fit: cover;
                    }
                }
                .content-wrap {
                    width: 75%;
                    .category {
                        margin-bottom: .5rem;
                    }
                    .post-title {
                        font-weight: bolder;
                        font-size: 1.25rem;
                        margin-bottom: 0.5rem;
                        transition: 0.3s;
                    }
                    .post-date {
                        font-size: 0.875rem;
                    }
                }
                &:not(:hover) .content-wrap .post-title {
                    color: inherit;
                }
            }
        }
        .tags {
            margin-bottom: 2rem;
            h4 {
                margin-bottom: 1rem;
            }
            .tag:last-of-type .comma {
                display: none;
            }
        }
        @media (min-width: 1024px) {
            margin-top: 0;
            & > .grid-x {
                position: sticky;
                top: 15%;
            }
        }
    }
    .post-navigation-links {
        margin: 3rem 0;
        .nav-link {
            display: flex;
            align-items: center;
            font-size: 1.5rem !important;
            &.prev {
                svg {
                    height: 1.5rem;
                    margin-right; .5rem;
                }
            }
            &.next {
                justify-content: flex-end;
                svg {
                    height: 1.5rem;
                    margin-left: .5rem;
                }
            }
        }
    }
    @media (min-width: 1024px) {
        .post-body.sidebar {
            max-width: ${props => props.maxWidth}rem;
            display: flex;
            margin: auto;
            & > .default-post,
            & > #modules-container {
                width: 66%;
            }
            & > .sidebar {
                width: 34%;
                margin-top: 0;
            }
            &.sidebar-left {
                & > .default-post,
                & > #modules-container {
                    order: 2;
                }
            }
        }
    }
`;

export const TitleBlock = styled.div`
    padding: 4rem 0;
    margin-bottom: 1.5rem;
    h1 {
        font-size: 1.5rem;
        margin-bottom: 0;
    }
    &.hero-image {
        display: flex;
        align-items: center;
        padding: 0;
        min-height: ${props => props.mobileHero?.height}px;
        background-image: url(${props => props.mobileHero?.url});
        background-size: cover;
        background-position: center center;
        .grid-container {
            width: 100%;
        }
        @media (min-width: 768px) {
            min-height: ${props => props.desktopHero?.height}px;
            background-image: url(${props => props.desktopHero?.url});
        }
    }
`

export const SidebarCTAWrapper = styled.div`
    background-color: ${props => props.backgroundColor};
    color: ${props => props.textColor};
    margin-bottom: 2rem;
    .content-wrapper {
        padding: 2rem;
        text-align: center;
        .content {
            margin: 1.5rem auto;
        }
    }
`;

export const SidebarSearchWrapper = styled.div`
    margin-bottom: 1rem;
    .inner-wrapper {
        margin: 1rem 0;
        position: relative;
        form {
            input {
                border: 2px solid #ccc;
                min-height: 40px;
            }
        }
        .icon {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            right: 0.5rem;
            cursor: pointer;
            font-size: 1.25rem;
            color: #999;
        }
    }
`;

export const CompanySummaryWrapper = styled.div`
    margin: 1rem 0;
    .inner-wrapper {
        display: flex;
        .logo {
            margin-right: 1rem;
            max-width: 100px;
        }
        .company-title {
            margin-bottom: .5rem;
        }
    }
`;

export const RecentPostsWrapper = styled.div`
    margin: 2rem auto;
    h2 {
        margin-bottom: 2rem;
        text-align: center;
    }
    .featured-image {
        height: 220px;
        margin-bottom: 1rem;
        img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
    }
    .content-wrap {
        .post-title {
            text-decoration: underline;
        }
    }
`;

export const ShareButtonWrapper = styled.div`
    margin: 2rem 0;
    h4.label {
        margin-bottom: 1rem;
    }
    .share-button {
        display: inline-flex;
        margin: 0 1.25rem 0 0;
        cursor: pointer;
        transition: .3s;
        padding: 0.675rem;
        box-sizing: border-box;
        width: 40px;
        height: 40px;
        border-radius: ${props => props.iconBorderRadius}%;
        text-align: center;
        svg {
            font-size: 1.5rem;
            width: 100%;
            height: 100%;
            object-fit: contain;
        }
        .copy-success {
            margin-left: .5rem;
        }
    }
    &.icon-only {
        .share-button {
            color: ${props => props.iconColor};
            &:hover {
                color: ${props => props.textColor};
            }
        }
    }
    &.icon-outline {
        .share-button {
            color: ${props => props.iconColor};
            border: 2px solid ${props => props.iconColor};
            &:hover {
                background-color: ${props => props.iconColor};
                color: #fff;
            }
        }
    }
    &.icon-background {
        .share-button {
            color: ${props => props.iconColor};
            background-color: ${props => props.iconBackground};
            &:hover {
                background-color: ${props => props.iconColor};
                color: ${props => props.iconBackground};
            }
        }
    }
`;

export const ImageWrapper = styled.div`
    border: 1px solid #ddd;
    position: relative;
    min-height: 200px;
    overflow: hidden;
    margin-bottom: 1rem;
    &.site-logo {
        background-color: ${props => props.backgroundColor};
        @media (min-width: 640px) {
            min-height: 100%;
        }
    }
    img {
        object-fit: contain;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
    }
    &.featured {
        img {
            min-width: 100%;
            min-height: 100%;
        }
    }
`;

export const Basic = styled.div`
    .post-body {
        padding-bottom: 2rem;
        .title h1 {
            margin: 1rem 0;
        }
        .featured-image {
            float: left;
            margin: 0 1rem 1rem 0;
        }
    }
`

export const MultiLocationEventLinks = styled.div`
    color: ${props => props.textColor};
    &.color {
        background-color: ${props => props.bgColor};
    }
    &.image {
        background-image: url(${props => props.bgImage});
        background-position: center center;
        background-size: cover;
        background-repeat: no-repeat;
    }
    padding: 3rem 0;
    @media (min-width: 768px) {
        padding: 4rem 0 3rem;
    }
    .button-container {
        .ml-button {
            margin: .75rem 0;
        }
        @media (min-width: 768px) {
            display: flex;
            justify-content: center;
            flex-wrap: wrap;
            .ml-button {
                margin: .75rem .5rem;
            }
        }
    }
`

export const EventContainer = styled.div`
    color: ${props => props.textColor};
    &.color {
        background-color: ${props => props.bgColor};
    }
    &.image {
        background-image: url(${props => props.bgImage});
        background-position: center center;
        background-size: cover;
        background-repeat: no-repeat;
    }
    .event-detail__module {
        padding-top: ${props => props.topPaddingSmall}px;
        @media (min-width: 1200px) {
            padding-top: ${props => props.topPaddingLarge}px;
        }
    }
`