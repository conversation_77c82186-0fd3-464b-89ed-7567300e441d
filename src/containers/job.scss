@import "src/scss/variables.scss";

.job-detail__module {
    margin: 30px 0;
    @media (min-width: $break-medium) {
        .job-detail-text-content {
            width: 60%;
        }
        .with-image {
            &.image-left-content-right .job-detail-text-content {
                order: 2;
                margin-left: 2rem;
            }
        }
    }
    .job-detail-title,
    .job-detail-subtitle,
    .job-date {
        margin-bottom: 1rem;
    }

    .job-detail-description {
        margin: 0 0 20px 0;
        max-width: 700px;
        .custom-image {
            float: left;
            margin-right: 0.5rem;
        }
        @media (min-width: $break-medium) {
            margin-bottom: 30px;
        }
    }

    .related-store {
        text-decoration: underline;
    }

    .contact-info {
        display: flex;
        flex-wrap: wrap;
        font-size: 1rem;
        strong {
            font-weight: bold;
            margin-right: 0.5rem;
        }
    }

    .job-detail-media-items {
        width: 100%;
        .job-detail-image {
            width: 100%;
        }
        @media (min-width: $break-medium) {
            flex: 1;
        }
    }
}
.job-hero img {
    width: 100%;
}
