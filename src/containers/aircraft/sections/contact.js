import React from "react";
import * as S from '../styles';
//helpers 
import Imaging from 'src/helpers/imaging';
import Clicker from 'src/helpers/clicker';
//partials
const ShareButtons = React.lazy(() => import('src/partials/share-buttons'));
const Button = React.lazy(() => import('src/partials/button'));

const Start = ({ data, backgroundValue, settings }) => {

    const imageData = {
        url: data.contact?.headshot ? data.contact?.headshot : false,
        alt: `headshot for ${data.contact?.first_name} ${data.contact?.last_name}`,
        height: 120
    }
    sessionStorage.setItem('utm_data', JSON.stringify({sales_rep: data.contact?.email, aircraft_details: `${data.category?.manufacturer} ${data.category?.model} ${data.serial_number}`}));

    return (
        <S.Contact
            className="contact"
        >
            {(data.contact?.url && imageData.url) &&
                <Clicker type='anchor' url={data.contact?.url} className="headshot" ariaLabel={`link to ${data.contact?.first_name} ${data.contact?.last_name} detail page`}>
                    <Imaging data={imageData} />
                </Clicker>
            }
            <div className="contact-details">
                {`Contact ${data.contact?.first_name} ${data.contact?.last_name}`}<br />
                {data.contact?.phone &&
                    <>
                        at <Clicker type='anchor' url={`tel:${data.contact?.phone}`} ariaLabel={`call ${data.contact?.first_name} ${data.contact?.last_name}`}>{data.contact?.phone}</Clicker> or<br />
                    </>
                }
                {data.contact?.email &&
                    <Clicker type='anchor' url={`mailto:${data.contact?.email}`} ariaLabel={`email ${data.contact?.first_name} ${data.contact?.last_name}`}>{data.contact?.email}</Clicker>
                }
                <div className="button-wrapper"><Button className={'inquire-button'} title={'INQUIRE'} url={`#inquireForm`} tone={backgroundValue} /></div>
                {settings?.aircraft?.share_options && <div className="share-buttons"><span>SHARE</span><ShareButtons title={data.title} settings={settings?.aircraft?.share_options} /></div>}
            </div>
        </S.Contact>
    );
};

export default Start;