import React from "react";
// Helpers
import HtmlParser from 'src/helpers/html-parser';
import * as S from '../styles';

const Start = ({ data }) => {

    return (
        <S.Body
            className="grid-container"
        >
            <div className="grid-x">
                {data.reasons_buy_aircraft &&
                    <div className="cell">
                        <h3>Reasons To Buy This Aircraft</h3>
                        <HtmlParser html={data.reasons_buy_aircraft} />
                    </div>
                }
                {data.airframe_details &&
                    <div className="cell">
                        <h3>Airframe Details</h3>
                        <HtmlParser html={data.airframe_details} />
                    </div>
                }
                {data.engine_details &&
                    <div className="cell">
                        <h3>Engine Details</h3>
                        <HtmlParser html={data.engine_details} />
                    </div>
                }
                {data.maintenance &&
                    <div className="cell">
                        <h3>Maintenance</h3>
                        <HtmlParser html={data.maintenance} />
                    </div>
                }
                {data.avionics &&
                    <div className="cell">
                        <h3>Avionics</h3>
                        <HtmlParser html={data.avionics} />
                    </div>
                }
                {data.additional_equipment &&
                    <div className="cell">
                        <h3>Additional Equipment</h3>
                        <HtmlParser html={data.additional_equipment} />
                    </div>
                }
                {data.interior &&
                    <div className="cell">
                        <h3>Interior</h3>
                        <HtmlParser html={data.interior} />
                    </div>
                }
                {data.exterior &&
                    <div className="cell">
                        <h3>Exterior</h3>
                        <HtmlParser html={data.exterior} />
                    </div>
                }
            </div>
        </S.Body>
    );
}

export default Start;