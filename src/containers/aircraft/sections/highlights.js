import React from 'react';
import { useInView } from 'react-intersection-observer';
// Helpers
import { Coloring } from 'src/helpers';
import Imaging from 'src/helpers/imaging';
// Styles
import * as S from '../styles';

const Start = ({ data, settings }) => {
    const { ref, inView } = useInView({
        triggerOnce: true,
        fallbackInView: true,
        threshold: 0
    });

    return (
        <S.Highlights
            ref={ref}
            className={`aircraft-highlights${inView && settings.aircraft?.highlights?.background_type === 'image' ? ' add-bg-image' : ''}`}
            bgColor={Coloring(settings.aircraft?.highlights?.background_color, settings)}
            bgImage={settings.aircraft?.highlights?.background_image}
            textColor={settings.aircraft?.highlights?.background_value === 'dark' ? '#fff' : settings.design?.colors?.body_copy_color}
        >
            <div className='grid-container'>
                <div className='grid-x'>
                    <div className='cell'>
                        {settings.aircraft?.highlights?.heading &&
                            <h2 className='title'>{`${settings.aircraft?.highlights?.heading}${data.category.manufacturer ? ` ${data.category.manufacturer}` : ''}${data.category.model ? ` ${data.category.model}` : ''}`}</h2>
                        }
                        {Array.isArray(data.aircraft_detail_highlights) && data.aircraft_detail_highlights.map((highlight, i) => <Highlight data={highlight} index={i} />)}
                    </div>
                </div>
            </div>
        </S.Highlights>
    )
}

const Highlight = ({ data, index }) => {

    return (
        <div key={`highlight ${index}`} className='highlight'>
            {data.highlight_icon &&
                <Imaging data={data.highlight_icon} />
            }
            {data.copy &&
                <div className='copy' dangerouslySetInnerHTML={{ __html: data.copy }} />
            }
        </div>
    )
}

export default Start;