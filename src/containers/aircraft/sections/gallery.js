import React, { useState, useLayoutEffect } from 'react';
import Slider from 'react-slick';
import "slick-carousel/slick/slick.scss";
import "slick-carousel/slick/slick-theme.scss";
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faChevronLeft, faChevronRight } from '@fortawesome/free-solid-svg-icons'
// Helpers
import Imaging from 'src/helpers/imaging';

import * as S from '../styles';

const Start = ({ data, screenSize }) => {
    const [feature, setFeature] = useState(data.image_gallery ? data.image_gallery[0] : false);
    const [video, setVideo] = useState(false);
    const [walkthrough, setWalkthrough] = useState(false);
    // const [screenSize, setSize] = useState({ width: window.innerWidth });

    const sliderSettings = {
        dots: false,
        draggable: true,
        infinite: true,
        slidesToShow: 1,
        adaptiveHeight: true,
        slidesToScroll: 1,
        arrows: true,
        prevArrow: <FontAwesomeIcon icon={faChevronLeft} />,
        nextArrow: <FontAwesomeIcon icon={faChevronRight} />,
        responsive: [
            {
                breakpoint: 1023,
            },
            {
                breakpoint: 10000,
                settings: 'unslick'
            }
        ]
    };

    const featuredImg = (item) => {
        setFeature(item);
        setVideo(false);
        setWalkthrough(false);
    }

    return (
        <S.Gallery>
            <div className='feature-area show-for-large'>
                {feature ? <Imaging data={feature} /> : null}
                {video ? <div dangerouslySetInnerHTML={{ __html: data.video_type === 'youtube' ? data.youtube_video : data.vimeo_video }} /> : null}
                {walkthrough &&
                    <div class='video-container'>
                        <iframe class='walkthrough' src={data.walkthrough_link} />
                    </div>
                }
            </div>
            <Slider ref={(a) => a} {...sliderSettings}>
                {data.image_gallery && data.image_gallery.map((item, index) => <DisplayItem item={item} index={index} featuredImg={featuredImg} feature={feature} screenSize={screenSize} />)}
                {(data.youtube_video || data.vimeo_video) &&
                    <div className='video'>
                        {data.video_thumbnail && !video &&
                            <div className='thumbnail show-for-large' onClick={() => { setVideo(true), setFeature(false), setWalkthrough(false) }}>
                                <svg xmlns="http://www.w3.org/2000/svg" width="37.608" height="47.996" viewBox="0 0 37.608 47.996">
                                    <path id="play_arrow_FILL0_wght400_GRAD0_opsz24" d="M334.327-709.857Zm0,24,37.608-24-37.608-24Z" transform="translate(-334.326 733.854)" fill="#fff" />
                                </svg>
                                <Imaging data={data.video_thumbnail} />
                            </div>
                        }
                        {screenSize < 1024 ? <div dangerouslySetInnerHTML={{ __html: data.video_type === 'youtube' ? data.youtube_video : data.vimeo_video }} /> : null}
                        {(video && screenSize > 1023) ? <div className='placeholder background-bg'>Currently Viewing</div> : null}
                    </div>
                }
                {(data.walkthrough_thumbnail && !walkthrough && screenSize > 1023) &&
                    <div className='video show-for-large'>
                        <div className='thumbnail' onClick={() => { setWalkthrough(true), setFeature(false), setVideo(false) }}>
                            <svg xmlns="http://www.w3.org/2000/svg" width="58.992" height="58.992" viewBox="0 0 58.992 58.992">
                                <path id="_3d_rotation_FILL0_wght400_GRAD0_opsz24" data-name="3d_rotation_FILL0_wght400_GRAD0_opsz24" d="M109.5-821.008a28.721,28.721,0,0,1-11.5-2.323,29.788,29.788,0,0,1-9.365-6.3,29.783,29.783,0,0,1-6.3-9.365A28.722,28.722,0,0,1,80-850.5h5.9a22.86,22.86,0,0,0,5.346,14.969,23.209,23.209,0,0,0,13.679,8.185l-4.277-4.277,4.129-4.129,13.421,13.42a22.431,22.431,0,0,1-4.314,1.032A32.634,32.634,0,0,1,109.5-821.008Zm1.475-20.647v-17.7h8.849a2.852,2.852,0,0,1,2.1.849,2.853,2.853,0,0,1,.848,2.1v11.8a2.854,2.854,0,0,1-.848,2.1,2.854,2.854,0,0,1-2.1.848Zm-14.748,0v-4.425H103.6v-2.95H99.172v-2.95H103.6v-2.949H96.223v-4.425h8.849a2.852,2.852,0,0,1,2.1.849,2.853,2.853,0,0,1,.848,2.1v11.8a2.854,2.854,0,0,1-.848,2.1,2.854,2.854,0,0,1-2.1.848Zm19.172-4.425h2.95v-8.849H115.4Zm17.7-4.425a22.859,22.859,0,0,0-5.346-14.969,23.205,23.205,0,0,0-13.679-8.185l4.277,4.277-4.129,4.129-13.421-13.421a22.472,22.472,0,0,1,4.314-1.032,32.635,32.635,0,0,1,4.388-.3,28.721,28.721,0,0,1,11.5,2.323,29.785,29.785,0,0,1,9.365,6.3,29.78,29.78,0,0,1,6.3,9.365,28.723,28.723,0,0,1,2.323,11.5Z" transform="translate(-80 880)" fill="#fff" />
                            </svg>
                            <Imaging data={data.walkthrough_thumbnail} />
                        </div>
                    </div>
                }
                {(walkthrough && screenSize > 1023) ? <div className='placeholder background-bg'>Currently Viewing</div> : null}
                {(screenSize < 1024 && data.walkthrough_link) &&
                    <div class='video-container mvk-responsive-video standard'>
                        <iframe class='walkthrough' src={data.walkthrough_link} />
                    </div>
                }
            </Slider>
        </S.Gallery>
    )
}

const DisplayItem = ({ item, index, featuredImg, feature, screenSize }) => {

    const setImage = () => {
        featuredImg(item);
    }
    return (
        <div index={index} className='gallery-image' onClick={screenSize > 1023 ? () => setImage() : null}>
            {(screenSize < 1024 || feature.url !== item.url) ? <Imaging data={item} /> : null}
            {(feature.url === item.url && screenSize > 1023) ? <div className='placeholder background-bg'>Currently Viewing</div> : null}
        </div>
    )
}

export default Start;