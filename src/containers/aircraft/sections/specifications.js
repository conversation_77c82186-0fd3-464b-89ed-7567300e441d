import React from 'react';
import { decode } from 'html-entities';
import { useInView } from 'react-intersection-observer';
// Helpers
import { Coloring } from 'src/helpers';
import Imaging from 'src/helpers/imaging';
// Partials
const Button = React.lazy(() => import('src/partials/button'));
// Styles
import * as S from '../styles';

const Start = ({ data, settings }) => {
    const { ref, inView } = useInView({
        triggerOnce: true,
        fallbackInView: true,
        threshold: 0
    });

    return (
        <S.Specifications
            ref={ref}
            className={`aircraft-specifications${inView && settings.aircraft?.stats_bar?.background_type === 'image' ? ' add-bg-image' : ''}`}
            bgColor={Coloring(settings.aircraft?.stats_bar?.background_color, settings)}
            bgImage={settings.aircraft?.stats_bar?.background_image}
            textColor={settings.aircraft?.stats_bar?.background_value === 'dark' ? '#fff' : settings.design?.colors?.body_copy_color}
        >
            <div className='grid-container center'>
                <div className='grid-x'>
                    <div className='cell'>
                        <h1>{decode(data.title)}</h1>
                    </div>
                    <div className='cell'>
                        {data.custom_fields?.category?.pilots &&
                            <div className='stat-wrap'>
                                {settings.aircraft?.pilot_icon && <Imaging data={settings.aircraft?.pilot_icon} />}
                                {data.custom_fields?.category?.pilots}
                            </div>
                        }
                        {data.custom_fields?.category?.passengers &&
                            <div className='stat-wrap'>
                                {settings.aircraft?.passenger_icon && <Imaging data={settings.aircraft?.passenger_icon} />}
                                {data.custom_fields?.category?.passengers}
                            </div>
                        }
                        {data.custom_fields?.category?.baggage_capacity &&
                            <div className='stat-wrap'>
                                {settings.aircraft?.baggage_capacity_icon && <Imaging data={settings.aircraft?.baggage_capacity_icon} />}
                                {data.custom_fields?.category?.baggage_capacity} cu ft
                            </div>
                        }
                        {data.custom_fields?.category?.distance &&
                            <div className='stat-wrap'>
                                {settings.aircraft?.distance_icon && <Imaging data={settings.aircraft?.distance_icon} />}
                                {data.custom_fields?.category?.distance} nm
                            </div>
                        }
                        {data.custom_fields?.category?.speed &&
                            <div className='stat-wrap'>
                                {settings.aircraft?.speed_icon && <Imaging data={settings.aircraft?.speed_icon} />}
                                {data.custom_fields?.category?.speed} kts
                            </div>
                        }
                    </div>
                    <div className='cell'>
                        {data.custom_fields?.specifications && <Button title={'DOWNLOAD SPECIFICATIONS'} url={data.custom_fields?.specifications.url} target="_blank" />}
                        {data.custom_fields?.contact && <Button title={'INQUIRE'} url={`#inquireForm`} />}
                    </div>
                </div>
            </div>
        </S.Specifications>
    );
}

export default Start;