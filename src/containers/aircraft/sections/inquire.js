import React from 'react';
import { useInView } from 'react-intersection-observer';
// Helpers
import { Coloring } from 'src/helpers';
// Sections / Partials
import { Form } from 'src/modules/gravity-forms';
const Contact = React.lazy(() => import('./contact'));

// Styles
import * as S from '../styles';

const Start = ({ data, settings }) => {
    const { ref, inView } = useInView({
        triggerOnce: true,
        fallbackInView: true,
        threshold: 0
    });

    return (
        <S.Inquire
            ref={ref}
            id={'inquireForm'}
            className={`inquire-section${inView && settings.aircraft?.inquire?.background_type === 'image' ? ' add-bg-image' : ''}`}
            bgColor={Coloring(settings.aircraft?.inquire?.background_color, settings)}
            bgImage={settings.aircraft?.inquire?.background_image}
            textColor={settings.aircraft?.inquire?.background_value === 'dark' ? '#fff' : settings.design?.colors?.body_copy_color}
        >
            <div className='grid-container'>
                <div className='grid-x grid-margin-x'>
                    {settings.aircraft?.inquire?.heading &&
                        <div className='cell center'>
                            <h2 className='title'>{settings.aircraft?.inquire?.heading}</h2>
                        </div>
                    }
                    <div className='contact-wrapper cell medium-6'>
                        <Contact data={data} backgroundValue={settings.aircraft?.inquire?.background_value} />
                    </div>
                    {settings.aircraft?.inquire?.inquire_form &&
                        <div class='form-module cell medium-6'>
                            <Form module={settings.aircraft?.inquire} data={settings.aircraft?.inquire?.inquire_form} footerTextColor={settings.aircraft?.inquire?.background_value === 'dark' ? '#fff' : settings.design?.colors?.body_copy_color} settings={settings} />
                        </div>
                    }
                </div>
            </div>
        </S.Inquire>
    );
};

export default Start;