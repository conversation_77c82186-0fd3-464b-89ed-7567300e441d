import styled from 'styled-components';

export const AircraftDetail = styled.div`

`
export const Specifications = styled.div`
    padding: 1rem 0;
    background-color: ${props => props.bgColor};
    color: ${props => props.textColor};
    box-shadow: 3px 3px 6px rgba(0,0,0,.4);
    &.add-bg-image {
        background-image: url('${props => props.bgImage?.url}');
        background-position: center center;
        background-size: cover;
    }
    @media (max-width: 639px) {
        h1 {
            font-size: 1.5rem !important;
        }
    }
    .cell {
        &:has(.stat-wrap) {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            .stat-wrap {
                width: 33%;
                margin-bottom: .5rem;
                display: flex;
                align-items: center;
                justify-content: center;
                img {
                    margin-right: .25rem;
                    max-height: 25px;
                    max-width: 25px;
                    object-fit: contain;
                }
            }
        }
        a[role='button'] {
            margin: .5rem auto;
        }
    }

    @media (min-width: 768px) {
        padding: 1.5rem 0;
        // position: fixed;
        // width: 100%;
        z-index: 1;
        .cell {
            &:has(.stat-wrap) {
                flex-wrap: nowrap;
                justify-content: space-around;
                max-width: 540px;
                margin: auto;
                .stat-wrap {
                    width: auto;
                    img {
                        max-height: 30px;
                        max-width: 30px;
                    }
                }
            }
            a[role='button'] {
                margin: 1rem .5rem 0;
            }
        }
     }
`

export const Details = styled.div`
    margin-top: 1rem;
    margin-bottom: 2rem;
    > * {
        margin-bottom: .5rem;
        span {
            margin-right: .5rem;
            font-weight: bold;
        }
    }
`

export const Contact = styled.div`
    margin: 1rem 0 2rem;
    .contact-details {
        font-weight: bold;

        a {
            text-decoration: underline;
        }
    }
    .inquire-button {
        margin: 1rem 0;
    }
    .share-buttons {
        display: flex;
        align-items: center;
        span {
            margin-right: 8px;
        }
        a {
            font-size: 1.25rem !important;
            margin: 0 0.25rem;
        }
    }
    a {
        color: inherit;
    }
    @media (max-width: 639px) {
        display: grid;
        grid-auto-flow: column;
        grid-template-columns: 1fr 1fr;
        column-gap: 0.5rem;
        font-size: .75rem;
        .headshot img {
            height: 100%;
            object-fit: cover;
        }
        a {
            font-size: .75rem !important;
        }
    }
`
export const Gallery = styled.div`
    @media (min-width: 640px) {
        margin-top: 1rem;
    }
    .slick-slider {
        .slick-arrow {
            color: #000;
            z-index: 1;
            &.slick-prev {
                left: 0;
            } 
            &.slick-next {
                right: 0;
            }
          
        }
        .slick-list {
            margin: 1rem;
        }
        .video {

        }
    }
    @media (min-width: 1024px) {
        .regular.slider {
            display: grid;
            grid-auto-flow: row;
            grid-template-columns: repeat(5, 1fr);
            column-gap: 1rem;
            row-gap: 1rem;
            margin: 1rem 0;
            .placeholder {
                height: 100px;
                display: flex;
                align-items: center;
                justify-content: center;
            }
            .gallery-image, .thumbnail {
                position: relative;
                overflow: hidden;
                cursor: pointer;
                min-height: 100px;
                svg {
                    position: absolute;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    z-index: 1;
                }
                img {
                    position: absolute;
                    height: 100%;
                    width: 100%;
                    object-fit: cover;
                    transition: .3s;
                }
                &:hover {
                    img {
                        transform: scale(1.1);
                    }
                }
            }
        }

        .feature-area {
            height: 500px;
            
            .mvk-responsive-video.standard {
                height: 500px;
                padding-bottom: unset;
            }

            img {
                width: 100%;
                height: 500px;
                object-fit: cover;
            }

            iframe {
                width: 100%;
                height: 500px;
                border: none;
            }
        }
    }
`

export const Highlights = styled.div`
    background-color: ${props => props.bgColor};
    color: ${props => props.textColor};
    padding: 2rem 0 1rem;
    text-align: center;
    &.add-bg-image {
        background-image: url('${props => props.bgImage?.url}');
        background-position: center center;
        background-size: cover;
    }
    .highlight {
        position: relative;
        padding: 1rem 0;
        &:after {
            content: '';
            position: absolute;
            width: 50%;
            height: 1px;
            background: ${props => props.textColor};
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
        }
        &:last-child:after {
            display: none;
        }
        img {
            max-height: 64px;
            margin-bottom: 1rem;
        }
    }
    @media (min-width: 768px) {
        .grid-x > .cell {
            display: flex;
            align-items: center;
        }
    }
    @media (min-width: 1024px) {
        .grid-x > .cell {
            & > * {
                width: 25%;
                padding: 0 2rem;
            }
            .title {
                padding: 0 4rem 0 0;
                text-align: left;
            }
            .highlight:after {
                width: 1px;
                height: 140px;
                left: unset;
                right: 0;
                top: 50%;
                transform: translateY(-50%); 
            }
        }
       
    }
`

export const Body = styled.div`
    .cell {
        border-bottom: 1px solid #9e9e9e;
        padding: 1.5rem 0 1rem;
        &:last-child {
            border: none;
        }
    }
`

export const Inquire = styled.div`
    padding: 2rem 0;
    background-color: ${props => props.bgColor};
    color: ${props => props.textColor};
    .grid-container {
        max-width: 800px;
    }
    &.add-bg-image {
        background-image: url('${props => props.bgImage?.url}');
        background-position: center center;
        background-size: cover;
    }
    .contact-wrapper {
        display: flex;
        justify-content: center;
        text-align: center;
        .contact {
            display: block;
            margin: 1rem 0;
            .contact-details, .contact-details a {
                font-size: initial !important;
            }
            > a {
                display: block;
            }
            .button-wrapper {
                display: none;
            }
        }
    }
    .form-module {
        padding: 0;
        button[type='submit'] {
            text-transform: uppercase;
        }
        .type-textarea {
            .floater {
                margin-bottom: 4px;
            }
        }
    }
`