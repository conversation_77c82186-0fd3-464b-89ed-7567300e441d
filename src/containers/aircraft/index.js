import React, { useState, useLayoutEffect } from "react";
import * as S from './styles';

// Sections
const Specifications = React.lazy(() => import('./sections/specifications'));
const Details = React.lazy(() => import('./sections/details'));
const Contact = React.lazy(() => import('./sections/contact'));
const Gallery = React.lazy(() => import('./sections/gallery'));
const Highlights = React.lazy(() => import('./sections/highlights'));
const Body = React.lazy(() => import('./sections/body'));
const Inquire = React.lazy(() => import('./sections/inquire'));

// Partials



const Start = ({ data, settings }) => {

    const [screenSize, setSize] = useState({ width: window.innerWidth });

    useLayoutEffect(() => {
        function updateSize() {
            setSize(window.innerWidth);
        }
        window.addEventListener('resize', updateSize);
        updateSize();
        return () => window.removeEventListener('resize', updateSize);
    }, []);

    return (
        <S.AircraftDetail
            className="aircraft-detail-page"
        >
            <Specifications data={data.mvk_item_content} settings={settings} />
            <div className="grid-container">
                <div className="grid-x grid-margin-x">
                    <div className="aircraft-details medium-6 large-3 cell">
                        <Details data={data.mvk_item_content?.custom_fields} />
                        {(screenSize > 639 && data.mvk_item_content?.custom_fields?.contact) && <Contact data={data.mvk_item_content?.custom_fields} backgroundValue={'light'} settings={settings} />}
                    </div>
                    <div className="image-gallery medium-6 large-9 cell">
                        <Gallery data={data.mvk_item_content?.custom_fields} screenSize={screenSize} />
                    </div>
                    {(screenSize < 640 && data.mvk_item_content?.custom_fields?.contact) && <div className="cell"><Contact data={data.mvk_item_content?.custom_fields} backgroundValue={'light'} settings={settings} /></div>}
                </div>
            </div>
            {data.mvk_item_content?.custom_fields?.aircraft_detail_highlights &&
                <Highlights data={data.mvk_item_content?.custom_fields} settings={settings} />
            }
            <Body data={data.mvk_item_content?.custom_fields} />
            {data.mvk_item_content?.custom_fields?.contact &&
                <Inquire data={data.mvk_item_content?.custom_fields} settings={settings} />
            }
        </S.AircraftDetail>
    );
}

export default Start;