import React, { useContext } from "react";
import * as S from './styles';
import { AppContext } from 'src/contexts/app';

// Sections
const Hero = React.lazy(() => import('./sections/hero'));
const AircraftSelect = React.lazy(() => import('./sections/aircraft-select'));
const Description = React.lazy(() => import('./sections/description'));
const Highlights = React.lazy(() => import('./sections/highlights'));
const Details = React.lazy(() => import('./sections/details'));
const Report = React.lazy(() => import('./sections/report'));
const FeaturedImage = React.lazy(() => import('./sections/featured-image'));
const Inquire = React.lazy(() => import('./sections/inquire'));

const Start = ({ data, settings }) => {
    const appContext = useContext(AppContext);

    return (
        <S.MarketReportDetail className='market-report-details-page'>
            <Hero data={data.mvk_item_content} settings={settings} />
            <div class={`${appContext.width >= 768 ? 'select-description-container grid-container' : 'select-description-container'}`}>
                <AircraftSelect data={data.mvk_item_content} settings={settings} />
                <Description data={data.mvk_item_content} settings={settings} />
            </div>
            <Highlights data={data.mvk_item_content} settings={settings} />
            <div class={`${appContext.width >= 1024 ? 'details-highlights-image-container grid-container' : 'details-highlights-image-container'}`}>
                <Details data={data.mvk_item_content} settings={settings} />
                <FeaturedImage data={data.mvk_item_content} settings={settings} />
                <Report data={data.mvk_item_content} settings={settings} />
            </div>
            <Inquire data={data.mvk_item_content} settings={settings} />
        </S.MarketReportDetail>
    );
};

export default Start;