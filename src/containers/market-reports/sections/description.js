import React, { useContext } from "react";
// Styles
import * as S from '../styles';
import { AppContext } from 'src/contexts/app';

const Start = ({ data, settings }) => {
    const appContext = useContext(AppContext);

    return (
        <S.Description className='mr-description-section'>
            <div class={`${appContext.width < 768 ? 'grid-container' : 'no-gc'}`}>
                <h1 class='mr-title'>{data.title}</h1>
                <hr />
                {data.custom_fields.description && <div class='mr-description' dangerouslySetInnerHTML={{ __html: data.custom_fields.description }} />}
            </div>
        </S.Description>
    );
};

export default Start;