import React, { useState, useEffect, useContext } from "react";
// Styles
import * as S from '../styles';
// Helpers
import { Coloring } from 'src/helpers';
import Clicker from 'src/helpers/clicker';
// Sections / Partials
import { Form } from 'src/modules/gravity-forms';
// CONTEXT.
import { PrincipalContext } from "src/context";
import { AppContext } from 'src/contexts/app';

const Start = ({ data, settings }) => {
    const [principal, setPrincipal] = useContext(PrincipalContext);
    const appContext = useContext(AppContext);
    const [isDownloadVisible, setIsDownloadVisible] = useState(false);
    
    sessionStorage.setItem('utm_data', JSON.stringify({aircraft_details: `${data.custom_fields.category?.manufacturer} ${data.custom_fields.category?.model}`}));

    useEffect(() => {
        principal.isFormSubmitted && setIsDownloadVisible(true);
    }, [principal.isFormSubmitted])

    return (
        <S.Report 
            id={`reportForm`} 
            className={`mr-report-section ${settings.market_reports?.report.background_value === 'image' ? ' add-bg-image' : ''}`}
            bgColor={Coloring(settings.market_reports?.report.background_value.background_color, settings)}
            bgImage={settings.market_reports?.report.background_value.background_image}
            textColor={settings.market_reports?.report.background_value.background_value === 'dark' ? '#fff' : settings.design?.colors?.body_copy_color}
        >
            <div class={`${appContext.width < 1024 ? 'grid-container' : 'no-gc'}`}>
                {settings.market_reports?.report.mr_report_copy &&
                    <div class='mr-report-copy' dangerouslySetInnerHTML={{ __html: settings.market_reports?.report.mr_report_copy }} />
                }
                {settings.market_reports?.report.mr_full_report_download_form &&
                    <div class='form-module'>
                        <Form 
                            module={settings.market_reports?.report} 
                            data={settings.market_reports?.report.mr_full_report_download_form}
                            footerTextColor={settings.market_reports?.report.background_value === 'dark' ? '#fff' : settings.design?.colors?.body_copy_color}
                            settings={settings} 
                        />
                        {isDownloadVisible && data.custom_fields.full_report.url &&
                            <div class='download-container'>
                                <Clicker class='full-report-download' type='anchor' url={data.custom_fields.full_report.url} target='_blank' aria-label={`download market report for ${data.title} aircraft`}>DOWNLOAD REPORT</Clicker>
                            </div>
                        }
                    </div>
                }
            </div>
        </S.Report>
    );
};

export default Start;