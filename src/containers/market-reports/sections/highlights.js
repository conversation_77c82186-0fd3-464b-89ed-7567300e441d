import React from "react";
// Styles
import * as S from '../styles';
// Helpers
import { Coloring } from 'src/helpers';
import Imaging from 'src/helpers/imaging';

const Start = ({ data, settings }) => {
    return (
        <S.Highlights
            className={`mr-highlights-section ${settings.market_reports?.details?.background_type === 'image' ? ' add-bg-image' : ''}`}
            bgColor={Coloring(settings.market_reports?.details?.background_color, settings)}
            bgImage={settings.market_reports?.details?.background_image}
            textColor={settings.market_reports?.details?.background_value === 'dark' ? '#fff' : settings.design?.colors?.body_copy_color}
        >
            <div class='grid-container'>
                <div class='highlights-container'>
                    {data.custom_fields.mr_detail_highlights.average_price?.length > 0 &&
                        <div class='mr-highlight average-price'>
                            {settings.market_reports.icons.average_price_icon && <Imaging data={settings.market_reports.icons.average_price_icon} />}
                            <div class='highlight-title'>AVERAGE<br/>PRICE</div>
                            <h4 class='highlight-info'>{`$${data.custom_fields.mr_detail_highlights.average_price}`}</h4>
                        </div>
                    }
                    {data.custom_fields.mr_detail_highlights.active_fleet?.length > 0 &&
                        <div class='mr-highlight average-price'>
                            {settings.market_reports.icons.active_fleet_icon && <Imaging data={settings.market_reports.icons.active_fleet_icon} />}
                            <div class='highlight-title'>ACTIVE<br/>FLEET</div>
                            <h4 class='highlight-info'>{data.custom_fields.mr_detail_highlights.active_fleet}</h4>
                        </div>
                    }
                    {data.custom_fields.mr_detail_highlights.currently_for_sale?.length > 0 &&
                        <div class='mr-highlight average-price'>
                            {settings.market_reports.icons.currently_for_sale_icon && <Imaging data={settings.market_reports.icons.currently_for_sale_icon} />}
                            <div class='highlight-title'>CURRENTLY<br/>FOR SALE</div>
                            <h4 class='highlight-info'>{data.custom_fields.mr_detail_highlights.currently_for_sale}</h4>
                        </div>
                    }
                    {data.custom_fields.mr_detail_highlights.average_time_on_market?.length > 0 &&
                        <div class='mr-highlight average-price'>
                            {settings.market_reports.icons.average_time_on_market_icon && <Imaging data={settings.market_reports.icons.average_time_on_market_icon} />}
                            <div class='highlight-title'>AVERAGE TIME<br/>ON MARKET</div>
                            <h4 class='highlight-info'>{data.custom_fields.mr_detail_highlights.average_time_on_market}</h4>
                        </div>
                    }
                    {data.custom_fields.mr_detail_highlights.absorption_rate?.length > 0 &&
                        <div class='mr-highlight average-price'>
                            {settings.market_reports.icons.absorption_rate_icon && <Imaging data={settings.market_reports.icons.absorption_rate_icon} />}
                            <div class='highlight-title'>ABSORPTION<br/>RATE</div>
                            <h4 class='highlight-info'>{data.custom_fields.mr_detail_highlights.absorption_rate}</h4>
                        </div>
                    }
                </div>
                <div class='asterisk'><small>*Based on a 6-month market analysis</small></div>
            </div>
        </S.Highlights>
    );
};

export default Start;