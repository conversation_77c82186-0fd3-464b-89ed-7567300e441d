import React from "react";
// Styles
import * as S from '../styles';
// Helpers
import { Coloring } from 'src/helpers';
// Sections / Partials
import { Form } from 'src/modules/gravity-forms';

const Start = ({ data, settings }) => {

    sessionStorage.setItem('utm_data', JSON.stringify({aircraft_details: `${data.custom_fields.category?.manufacturer} ${data.custom_fields.category?.model}`}));

    return (
        <S.Inquire
            id={'marketReportForm'}
            className={`mr-inquire-section ${settings.market_reports?.inquire?.background_type === 'image' ? ' add-bg-image' : ''}`}
            bgColor={Coloring(settings.market_reports?.inquire?.background_color, settings)}
            bgImage={settings.market_reports?.inquire?.background_image}
            textColor={settings.market_reports?.inquire?.background_value === 'dark' ? '#fff' : settings.design?.colors?.body_copy_color}
        >
            <div class='grid-container'>
                <div class='mr-inquire-container'>
                    {settings.market_reports.inquire.mr_inquire_copy &&
                        <div class='mr-inquire-copy' dangerouslySetInnerHTML={{ __html: settings.market_reports.inquire.mr_inquire_copy }} />
                    }
                    {settings.market_reports?.inquire?.mr_inquire_form &&
                        <div class='form-module'>
                            <Form 
                                module={settings.market_reports?.inquire} 
                                data={settings.market_reports?.inquire?.mr_inquire_form} 
                                footerTextColor={settings.market_reports?.inquire?.background_value === 'dark' ? '#fff' : settings.design?.colors?.body_copy_color} settings={settings} 
                            />
                        </div>
                    }
                </div>
            </div>
        </S.Inquire>
    );
};

export default Start;