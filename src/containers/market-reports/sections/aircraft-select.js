import React, { useContext } from "react";
import Select from 'react-select';
import { useNavigate } from 'react-router-dom';
// Styles
import * as S from '../styles';
// Context
import { MarketReportsContext } from "src/context";

const Start = ({ data, settings }) => {
    const [marketReports, setMarketReports] = useContext(MarketReportsContext);
    const navigate = useNavigate();

    let options = [];
    for (const [key, value] of Object.entries(marketReports)) {
        const path = new URL(value.mvk_item_meta.url).pathname
        options.push({ value: key, label: value.mvk_item_content.title, url: path })
    }

    const Styles = {
        control: (styles, state) => ({ 
            ...styles, 
            backgroundColor: settings.design.colors.primary_color,
            borderRadius: 0,
            border: 'none',
            cursor: 'pointer'
        }),
        menu:(styles, state) => ({ ...styles, border: `2px solid ${settings.design.colors.primary_color}`}),
        option: (styles, state) => ({
            ...styles, 
            color: settings.design.colors.body_copy_color,
            backgroundColor: '#fff',
            cursor: 'pointer'
        }),
        placeholder: (styles, state) => ({
            ...styles,
            color: '#fff'
        }),
        indicatorSeparator: (styles, state) => ({ ...styles, color: 'transparent', backgroundColor: 'transparent'}),
        dropdownIndicator: (styles, state) => ({
            ...styles,
            color: '#fff',
        }),
        singleValue: (styles, state) => ({
            ...styles,
            color: '#fff'
        })
    }

    return (
        <S.AircraftSelect className='mr-aircraft-select-section'>
            <Select 
                name='select aircraft' 
                onChange={e => navigate(e.url)} 
                aria-label='select an aircraft to view the market report'
                options={options}
                placeholder={'SELECT AN AIRCRAFT'}
                styles={Styles}
            />
        </S.AircraftSelect>
    );
};

export default Start;