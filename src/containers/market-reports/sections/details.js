import React, { useContext } from "react";
// Styles
import * as S from '../styles';
import { AppContext } from 'src/contexts/app';

const Start = ({ data, settings }) => {
    const appContext = useContext(AppContext);

    return (
        <S.Details className='mr-details-section' borderColor={settings.design.colors.body_copy_color}>
            <div class={`${appContext.width < 1024 ? 'grid-container' : 'no-gc'}`}>
                {data.custom_fields.highest_total_time &&
                    <div class='mr-detail'>
                        <div>HIGHEST TOTAL TIME</div>
                        <h4>{data.custom_fields.highest_total_time}</h4>
                    </div>
                }
                {data.custom_fields.lowest_total_time &&
                    <div class='mr-detail'>
                        <div>LOWEST TOTAL TIME</div>
                        <h4>{data.custom_fields.lowest_total_time}</h4>
                    </div>
                }
                {data.custom_fields.average_total_time &&
                    <div class='mr-detail'>
                        <div>AVERAGE TOTAL TIME</div>
                        <h4>{data.custom_fields.average_total_time}</h4>
                    </div>
                }
            </div>
        </S.Details>
    );
};

export default Start;