import styled from 'styled-components';

export const MarketReportDetail = styled.div`
    .select-description-container {
        @media (min-width: 1024px) {
            display: grid;
            grid-template-columns: 1fr 3fr;
            column-gap: 3rem;
        }

        @media (min-width: 1200px) {
            grid-template-columns: 1fr 4fr;
        }
    }

    .details-highlights-image-container {
        @media (min-width: 1024px) {
            display: grid;
            grid-template-columns: 1fr 1fr;
            grid-template-rows: auto auto;
            column-gap: 5rem;
            padding-top: 3rem;
            padding-bottom: 8rem;

            .mr-details-section {
                grid-column: 1 / 2;
                grid-row-start: 1;
            }

            .mr-feat-image-section {
                grid-column: 2 / 3;
                grid-row: 1 / 4;
                justify-self: flex-end;
            }

            .mr-report-section {
                grid-column: 1 / 2;
                grid-row-start: 2;
            }
        }
    }
`

export const Hero = styled.div`
    img {
        display: block;
        width: 100%;
    }
`

export const AircraftSelect = styled.div`

    @media (min-width: 1024px) {
        padding-top: 3rem;
        > div {
            cursor: pointer;
            .css-62w049-control .css-1dimb5e-singleValue {
                color: #fff;
            }
        }
    }
`

export const Description = styled.div`
    padding: 2rem 0;

    hr {
        height: 2px !important;
        margin-bottom: 1rem;
    }
    @media (min-width: 768px) {
        .mr-description {
            column-count: 2;
        }
    }
`

export const Highlights = styled.div`
    background-color: ${props => props.bgColor};
    color: ${props => props.textColor};
    padding: 2rem 0;

    @media (min-width: 1024px) {
        padding-top: 3rem;
        padding-bottom: unset;
    }
    
    &.add-bg-image {
        background-image: url('${props => props.bgImage?.url}');
        background-position: center center;
        background-size: cover;
    }

    .highlights-container {
        .highlight-title {
            font-size: 1.375rem;
            font-weight: bold;
        }
        .highlight-info {
            font-size: 2.25rem !important;
        }
        @media (min-width: 1024px) {
            display: grid;
            grid-template-columns: repeat(5, 1fr);
            column-gap: 2rem;
        }
    }

    .mr-highlight {
        text-align: center;
        margin-bottom: 2.5rem;
        &:last-child {
            margin-bottom: 0;
        }
        @media (min-width: 1024px) {
            margin-bottom: unset;
        }

        img {
            margin-bottom: .5rem;
        }

        .highlight-title {
            margin-bottom: .5rem;
        }

        .highlight-info {}
    }

    .asterisk {
        padding-top: 3rem;

        @media (min-width: 1024px) {
            padding-bottom: .75rem;
        }
    }
`

export const Details = styled.div`
    padding: 1.5rem 0;

    @media (min-width: 1024px) {
        padding-top: unset;
    }

    .mr-detail {
        text-align: center;
        border-bottom: 1px solid ${props => props.borderColor};
        padding-bottom: .75rem;

        div {
            margin-bottom: .25rem;
            font-size: 1.375rem;
        }
        h4 {
            font-size: 1.375rem !important;
            margin-bottom: .25rem;
        }
        &:nth-child(2) {
            margin-top: .75rem;
        }

        &:nth-child(3) {
            margin-top: .75rem;
        }

        @media (min-width: 1024px) {
            text-align: unset;
            display: flex;
            flex-flow: row nowrap;
            justify-content: space-between;
            align-items: center;
            margin-bottom: .75rem;
            padding-bottom: unset;

            &:nth-child(2), &:nth-child(3) {
                margin-top: unset;
            }
        }
    }
`

export const Report = styled.div`
    color: ${props => props.textColor};
    padding: 1rem 0;

    .form-module {
        padding-top: unset;
        padding-bottom: unset;
    }

    .download-container {
        margin-top: 2rem;
        a {
            text-decoration: underline;
        }
    }
`

export const FeaturedImage = styled.div`
    img {
        width: 100%;
    }
`

export const Inquire = styled.div`
    padding: 2rem 0;
    background-color: ${props => props.bgColor};
    color: ${props => props.textColor};
    
    &.add-bg-image {
        background-image: url('${props => props.bgImage?.url}');
        background-position: center center;
        background-size: cover;
    }

    .mr-inquire-container {
        @media (min-width: 1024px) {
            display: grid;
            grid-template-columns: 1fr 1fr;
            column-gap: 5rem;
        }
    }

    .mr-inquire-copy {
        @media (min-width: 1024px) {
            padding: 2rem 0;
        }
    }
`