import React, { useState, useEffect, useContext, useLayoutEffect } from "react";
import CookieConsent from "react-cookie-consent";
const HtmlParser = React.lazy(() => import('src/helpers/html-parser'));

const Helmet = React.lazy(() => import('src/helmet'));
const Header = React.lazy(() => import('src/partials/header'));
const Footer = React.lazy(() => import('src/partials/footer'));
const BackToTop = React.lazy(() => import('src/partials/back-to-top'));
const Modules = React.lazy(() => import('src/modules'));
const Templates = React.lazy(() => import('src/templates'));
const BreadCrumbs = React.lazy(() => import('src/partials/breadcrumbs'));
const Modal = React.lazy(() => import('src/partials/modal'));
const BottomMobileBar = React.lazy(() => import('src/partials/bottom-mobile-bar'));
const Search = React.lazy(() => import('./search'));
const Event = React.lazy(() => import('./events'));
const Job = React.lazy(() => import('./job'));
const Post = React.lazy(() => import('./post'));
const Service = React.lazy(() => import('./services'));
const Property = React.lazy(() => import('./properties'));
const Aircraft = React.lazy(() => import('./aircraft'));
const MarketReports = React.lazy(() => import('./market-reports'));
const Floorplan = React.lazy(() => import('./floorplans'));
const Sale = React.lazy(() => import('./sale'));
const Store = React.lazy(() => import('./stores'));
const Spaces = React.lazy(() => import('./spaces'));
const Team = React.lazy(() => import('./team'));
const NotFound = React.lazy(() => import('./notfound'));
const Sales = React.lazy(() => import('./sales'));

// LAYOUTS.
import ChassisLayout from 'src/layouts/chassis';

// HELPERS.
import { triggerActiveCampaign } from "src/hooks";
import { Coloring } from 'src/helpers';
import { addScript } from 'src/hooks';
import { withinDateRange } from "src/helpers/date";
import { handleContentLoaded } from "src/helpers/calculateMarginTop";
// HOOKS
import { useQuery } from 'src/hooks/query';
// CONTEXT.
import { SettingsContext, CustomTemplatesContext } from "src/context";

const Start = ({ type, data }) => {
    const [settings, setSettings] = useContext(SettingsContext);

    if (settings.store?.store_map_type === 'acquire-digital' && settings?.store?.acquire_digital?.wayfinder_js_api_url) {
        addScript(settings?.store?.acquire_digital?.wayfinder_js_api_url);
    }
    // GTM virtual pageview
    useEffect(() => {
        setSettings({ ...settings, ['hide_nav']: data?.mvk_item_meta?.template === 'template-landing-page' });
    }, [data?.mvk_item_meta?.template]);

    // activeCampaignCheck
    if (settings.active_campaign_id) {
        triggerActiveCampaign(settings.active_campaign_id);
    }

    switch (type) {
        case 'search':
            return (<BuildSearch />);
        case 'event':
            return (<BuildEvent data={data} />);
        case 'location':
            return (<BuildLocation data={data} />);
        case 'job':
            return (<BuildJob data={data} settings={settings} />);
        case 'post':
            return (<BuildPost data={data} settings={settings} />);
        case 'service':
            return (<BuildService data={data} settings={settings} />);
        case 'page':
            return (<BuildPage data={data} />);
        case 'property':
            return (<BuildProperty data={data} settings={settings} />);
        case 'floorplan':
            return (<BuildFloorplan data={data} settings={settings} />)
        case 'sale':
            return (<BuildSale data={data} settings={settings} />);
        case 'store':
            return (<BuildStore data={data} />);
        case 'space':
            return (<BuildSpace data={data} />);
        case 'team':
            return (<BuildTeam data={data} />);
        case 'testimonial':
            return (<BuildTestimonial data={data} />);
        case 'aircraft':
            return (<BuildAircraft data={data} />);
        case 'market_reports':
            return (<BuildMarketReports data={data} />);
        case '404':
        default:
            return (<BuildNotFound data={data} />);
    }
};

const BuildSearch = ({ data }) => {
    const [settings, setSettings] = useContext(SettingsContext);

    var urlParams = new URLSearchParams(window.location.search);
    var searchText = urlParams.get('s');

    return (
        <ChassisLayout>
            <Helmet />
            <Header />
            <Search searchText={searchText} />
            <Footer />
            {settings.cookie_consent && <CookieConsentModal />}
            {settings.mvk_theme_config?.other?.enable_back_to_top && <BackToTop />}
        </ChassisLayout>
    );
};

const BuildPage = ({ data }) => {
    const [settings, setSettings] = useContext(SettingsContext);
    const [bgImage, setBgImage] = useState(false);
    const [bgImageHoliday, setBgImageHoliday] = useState(false);
    const breadcrumbs = data?.mvk_item_content?.custom_fields?.breadcrumbs;
    const template = data?.mvk_item_meta?.template;
    const query = useQuery();
    const simulateDate = query.get('simulate-date');
    const isActive = settings.holiday?.enable_holiday ? withinDateRange(false, false, true, false, simulateDate) : false;

    handleContentLoaded(settings, 'page', data);

    // GET SALES DATA FROM POST_TYPE_LIST MODULE TO BUILD SCHEMA
    const salesData = Array.isArray(data?.mvk_item_content?.custom_fields?.layout)
        ? data.mvk_item_content.custom_fields.layout
            .filter(object => object.mvk_mod_layout === 'post_type_list' && object.post_list_post_type === 'sales')
            .map(object => ({ ...object, url: data?.mvk_item_meta?.url }))
        : [];

    useEffect(() => {
        const backgroundPages = settings?.mvk_theme_config?.other?.background_pages ? settings?.mvk_theme_config?.other?.background_pages : [];
        const holidayBackgroundPages = settings?.holiday?.styles?.background_pages ? settings?.holiday?.styles?.background_pages : [];
        const checkPageMatch = (enabled, pages) => {
            if (enabled && pages.length > 0) {
                const compare = item => item ? new URL(item)?.pathname?.replace(/\//g, '') === location.pathname.replace(/\//g, '') : false;
                let match = pages.some(compare);
                return match;
            }
            return false;
        }
        if (settings?.mvk_theme_config?.other?.enable_page_background) {
            setBgImage(checkPageMatch(settings?.mvk_theme_config?.other?.enable_page_background, backgroundPages));
        }
        if (settings?.holiday?.enable_holiday) {
            setBgImageHoliday(checkPageMatch(settings?.holiday?.styles?.enable_page_background, holidayBackgroundPages));
        }
    }, [location.pathname]);

    let pageStyle = null;

    if (bgImageHoliday && isActive) {
        pageStyle = {
            background: `url(${settings.holiday?.styles?.page_background?.url}) repeat-y top`,
            backgroundSize: '100%'
        }
    } else if (bgImage) {
        pageStyle = {
            background: `url(${settings.mvk_theme_config?.other?.page_background?.url}) repeat-y top`,
            backgroundSize: '100%'
        }
    }

    if (template == 'template-movie-listing' || template == 'template-market') {
        return (
            <ChassisLayout style={bgImage || bgImageHoliday ? pageStyle : null}>
                <Helmet page={data} />
                <Header />
                {breadcrumbs && <BreadCrumbs data={breadcrumbs} settings={settings} />}
                <Modules page={data} />
                <Templates data={data} settings={settings} />
                <Footer />
                {settings.cookie_consent && <CookieConsentModal />}
                {settings.mvk_theme_config?.other?.enable_back_to_top && <BackToTop />}
                {settings.mvk_theme_config?.nav?.enable_bottom_mobile_bar && <BottomMobileBar />}
            </ChassisLayout>
        );
    } else if (template === 'template-mappedin') {
        return (
            <ChassisLayout style={bgImage || bgImageHoliday ? pageStyle : null}>
                <Helmet page={data} />
                <Header />
                {breadcrumbs && <BreadCrumbs data={breadcrumbs} settings={settings} />}
                <Modules page={data} />
                <Templates data={data} settings={settings} />
                {settings.cookie_consent && <CookieConsentModal />}
                {settings.mvk_theme_config?.other?.enable_back_to_top && <BackToTop />}
            </ChassisLayout>
        );
    } else if (template === 'template-landing-page') {

        return (
            <ChassisLayout style={bgImage || bgImageHoliday ? pageStyle : null} className={template}>
                <Helmet page={data} />
                <Header />
                <Modules page={data} />
                {!data.mvk_item_content?.custom_fields?.hide_footer && <Footer />}
                {settings.cookie_consent && <CookieConsentModal />}
                {settings.mvk_theme_config?.other?.enable_back_to_top && <BackToTop />}
            </ChassisLayout>
        );
    } else if (template === 'template-axial-scrape') {
        // Options to hide header or footer
        const hideHeader = query.get('mvk_hide_header');
        const hideFooter = query.get('mvk_hide_footer');

        return (
            <ChassisLayout style={bgImage || bgImageHoliday ? pageStyle : null}>
                <Helmet page={data} />
                {!hideHeader && <Header />}
                <Templates data={data} settings={settings} />
                {!hideFooter && <Footer />}
            </ChassisLayout>
        );
    } else if (settings?.page?.custom_templates && data.mvk_template) {
        return <CustomTemplates data={data} settings={settings} breadcrumbs={breadcrumbs} bgImage={bgImage} bgImageHoliday={bgImageHoliday} pageStyle={pageStyle} />
    } else {
        return (
            <ChassisLayout style={bgImage || bgImageHoliday ? pageStyle : null}>
                <Helmet page={data} />
                <Header />
                {breadcrumbs && <BreadCrumbs data={breadcrumbs} settings={settings} />}
                {settings.modal && <Modal settings={settings} />}
                {salesData && <BuildSalesSchema data={salesData} />}
                {data?.mvk_item_meta?.template && <Templates data={data} settings={settings} />}
                <Modules page={data} />
                <Footer />
                {settings.cookie_consent && <CookieConsentModal />}
                {settings.mvk_theme_config?.other?.enable_back_to_top && <BackToTop />}
                {settings.mvk_theme_config?.nav?.enable_bottom_mobile_bar && <BottomMobileBar />}
            </ChassisLayout>
        );
    }
};

const CustomTemplates = ({ data, settings, breadcrumbs, bgImage, bgImageHoliday, pageStyle }) => {
    const [customTemplates] = useContext(CustomTemplatesContext);
    const [customTemplate, setCustomTemplate] = useState(null);

    useEffect(() => {
        setCustomTemplate((!customTemplates.loading && Array.isArray(customTemplates) && data?.mvk_template) ? customTemplates?.find(template => template?.mvk_slug === data?.mvk_template) : null)
    }, [data.mvk_template, customTemplates])

    const salesData = Array.isArray(customTemplate?.layout)
        ? customTemplate?.layout
            .filter(object => object.mvk_mod_layout === 'sale_banner')
            .map(object => ({ ...object.sale.mvk_sales_schema, url: object?.sale?.url }))
        : [];

    return (
        <ChassisLayout style={bgImage || bgImageHoliday ? pageStyle : null}>
            <Helmet page={data} />
            <Header />
            {breadcrumbs && <BreadCrumbs data={breadcrumbs} settings={settings} />}
            {settings.modal && <Modal settings={settings} />}
            {salesData && <BuildSalesBannerSchema data={salesData} />}
            <Modules page={data} layout={customTemplate?.layout} />
            <Footer />
            {settings.cookie_consent && <CookieConsentModal />}
            {settings.mvk_theme_config?.other?.enable_back_to_top && <BackToTop />}
            {settings.mvk_theme_config?.nav?.enable_bottom_mobile_bar && <BottomMobileBar />}
        </ChassisLayout>
    )
}

const BuildPost = ({ data }) => {
    const [settings, setSettings] = useContext(SettingsContext);
    handleContentLoaded(settings, 'post');

    return (
        <ChassisLayout>
            <Helmet page={data} />
            <Header />
            <Post data={data} settings={settings} />
            <Footer />
            {settings.cookie_consent && <CookieConsentModal />}
            {settings.mvk_theme_config?.other?.enable_back_to_top && <BackToTop />}
            {settings.mvk_theme_config?.nav?.enable_bottom_mobile_bar && <BottomMobileBar />}
        </ChassisLayout>
    );
};


const BuildService = ({ data }) => {
    const [settings, setSettings] = useContext(SettingsContext);
    handleContentLoaded(settings, 'service');

    return (
        <ChassisLayout>
            <Helmet page={data} />
            <Header />
            <Service data={data} settings={settings} />
            <Footer />
            {settings.cookie_consent && <CookieConsentModal />}
            {settings.mvk_theme_config?.other?.enable_back_to_top && <BackToTop />}
            {settings.mvk_theme_config?.nav?.enable_bottom_mobile_bar && <BottomMobileBar />}
        </ChassisLayout>
    );
};

const BuildProperty = ({ data }) => {
    const [settings, setSettings] = useContext(SettingsContext);
    handleContentLoaded(settings, 'property');

    return (
        <ChassisLayout>
            <Helmet page={data} />
            <Header />
            <Property data={data} />
            <Modules page={data} />
            <Footer />
            {settings.cookie_consent && <CookieConsentModal />}
            {settings.mvk_theme_config?.other?.enable_back_to_top && <BackToTop />}
            {settings.mvk_theme_config?.nav?.enable_bottom_mobile_bar && <BottomMobileBar />}
        </ChassisLayout>
    );
};

const BuildFloorplan = ({ data }) => {
    const [settings, setSettings] = useContext(SettingsContext);
    handleContentLoaded(settings, 'floorplan');

    return (
        <ChassisLayout>
            <Helmet page={data} />
            <Header />
            <Floorplan data={data} settings={settings} />
            <Footer />
            {settings.cookie_consent && <CookieConsentModal />}
            {settings.mvk_theme_config?.other?.enable_back_to_top && <BackToTop />}
            {settings.mvk_theme_config?.nav?.enable_bottom_mobile_bar && <BottomMobileBar />}
        </ChassisLayout>
    );
};

const BuildLocation = ({ data }) => {
    const [settings, setSettings] = useContext(SettingsContext);
    handleContentLoaded(settings, 'location');

    return (
        <ChassisLayout>
            <Helmet page={data} />
            <Header />
            <Modules page={data} />
            <Footer />
            {settings.cookie_consent && <CookieConsentModal />}
            {settings.mvk_theme_config?.other?.enable_back_to_top && <BackToTop />}
            {settings.mvk_theme_config?.nav?.enable_bottom_mobile_bar && <BottomMobileBar />}
        </ChassisLayout>
    );
};

const BuildAircraft = ({ data }) => {
    const [settings, setSettings] = useContext(SettingsContext);
    handleContentLoaded(settings, 'aircraft');

    return (
        <ChassisLayout>
            <Helmet page={data} />
            <Header />
            <Aircraft data={data} settings={settings} />
            <Footer />
            {settings.cookie_consent && <CookieConsentModal />}
            {settings.mvk_theme_config?.other?.enable_back_to_top && <BackToTop />}
        </ChassisLayout>
    );
};

const BuildMarketReports = ({ data }) => {
    const [settings, setSettings] = useContext(SettingsContext);
    handleContentLoaded(settings, 'market_reports');

    return (
        <ChassisLayout>
            <Helmet page={data} />
            <Header />
            <MarketReports data={data} settings={settings} />
            <Footer />
            {settings.cookie_consent && <CookieConsentModal />}
            {settings.mvk_theme_config?.other?.enable_back_to_top && <BackToTop />}
        </ChassisLayout>
    );
};

const BuildEvent = ({ data }) => {
    const [settings, setSettings] = useContext(SettingsContext);
    handleContentLoaded(settings, 'event');

    return (
        <ChassisLayout>
            <Helmet page={data} />
            <Header />
            <Event page={data} settings={settings} />
            <Modules page={data} />
            <Footer />
            {settings.cookie_consent && <CookieConsentModal />}
            {settings.mvk_theme_config?.other?.enable_back_to_top && <BackToTop />}
            {settings.mvk_theme_config?.nav?.enable_bottom_mobile_bar && <BottomMobileBar />}
        </ChassisLayout>
    );
};

const BuildJob = ({ data }) => {
    const [settings, setSettings] = useContext(SettingsContext);
    handleContentLoaded(settings, 'job');

    return (
        <ChassisLayout>
            <Helmet page={data} />
            <Header />
            <Job page={data} settings={settings} />
            <Modules page={data} />
            <Footer />
            {settings.cookie_consent && <CookieConsentModal />}
            {settings.mvk_theme_config?.other?.enable_back_to_top && <BackToTop />}
            {settings.mvk_theme_config?.nav?.enable_bottom_mobile_bar && <BottomMobileBar />}
        </ChassisLayout>
    );
};

const BuildSale = ({ data }) => {
    const [settings, setSettings] = useContext(SettingsContext);
    handleContentLoaded(settings, 'sale');

    // WE NEED TO BUILD THE SALES SCHEMA HERE
    const { mvk_sales_schema, mvk_item_meta } = data || {};


    return (
        <ChassisLayout>
            <Helmet page={data} />
            <Header />
            <Sales data={{
                ...mvk_sales_schema,
                url: mvk_item_meta?.url,
            }} />
            <Sale page={data} settings={settings} />
            <Modules page={data} />
            <Footer />
            {settings.cookie_consent && <CookieConsentModal />}
            {settings.mvk_theme_config?.other?.enable_back_to_top && <BackToTop />}
            {settings.mvk_theme_config?.nav?.enable_bottom_mobile_bar && <BottomMobileBar />}
        </ChassisLayout>
    );
};

const BuildSalesSchema = ({ data }) => {
    const salesData = data.flatMap(item =>
        item.posts.flatMap(post => {
            const { start_date, end_date, url } = post || {};
            return { ...post.mvk_sales_schema, start_date, end_date, url: item.url, slug: url };
        })
    );

    return <Sales data={salesData} />;
};

const BuildSalesBannerSchema = ({ data }) => {
    return <Sales data={data} />;
};

const BuildStore = ({ data }) => {
    const [settings, setSettings] = useContext(SettingsContext);
    handleContentLoaded(settings, 'store');

    return (
        <ChassisLayout>
            <Helmet page={data} />
            <Header />
            <Store page={data} settings={settings} />
            <Modules page={data} />
            <Footer />
            {settings.cookie_consent && <CookieConsentModal />}
            {settings.mvk_theme_config?.other?.enable_back_to_top && <BackToTop />}
            {settings.mvk_theme_config?.nav?.enable_bottom_mobile_bar && <BottomMobileBar />}
        </ChassisLayout>
    );
};

const BuildSpace = ({ data }) => {
    const [settings, setSettings] = useContext(SettingsContext);
    handleContentLoaded(settings, 'space');

    return (
        <ChassisLayout>
            <Helmet page={data} />
            <Header />
            <Spaces page={data} settings={settings} />
            <Modules page={data} />
            <Footer />
            {settings.cookie_consent && <CookieConsentModal />}
            {settings.mvk_theme_config?.other?.enable_back_to_top && <BackToTop />}
            {settings.mvk_theme_config?.nav?.enable_bottom_mobile_bar && <BottomMobileBar />}
        </ChassisLayout>
    );
};

const BuildTeam = ({ data }) => {
    const [settings, setSettings] = useContext(SettingsContext);
    handleContentLoaded(settings, 'team');

    return (
        <ChassisLayout>
            <Helmet page={data} />
            <Header />
            <Team page={data} settings={settings} />
            <Modules page={data} />
            <Footer />
            {settings.cookie_consent && <CookieConsentModal />}
            {settings.mvk_theme_config?.other?.enable_back_to_top && <BackToTop />}
            {settings.mvk_theme_config?.nav?.enable_bottom_mobile_bar && <BottomMobileBar />}
        </ChassisLayout>
    );
};

const BuildTestimonial = ({ data }) => {
    const [settings, setSettings] = useContext(SettingsContext);
    handleContentLoaded(settings, 'testimonial');

    return (
        <ChassisLayout>
            <Helmet page={data} />
            <Header />
            <Modules page={data} />
            <Footer />
            {settings.cookie_consent && <CookieConsentModal />}
            {settings.mvk_theme_config?.other?.enable_back_to_top && <BackToTop />}
            {settings.mvk_theme_config?.nav?.enable_bottom_mobile_bar && <BottomMobileBar />}
        </ChassisLayout>
    );
};

const BuildNotFound = ({ data }) => {
    const [settings, setSettings] = useContext(SettingsContext);
    handleContentLoaded(settings, '');

    var myHeaders = new Headers();
    myHeaders.set('Status', '404');

    return (
        <ChassisLayout>
            <Header />
            <NotFound data={data} />
            <Footer />
            {settings.cookie_consent && <CookieConsentModal />}
            {settings.mvk_theme_config?.other?.enable_back_to_top && <BackToTop />}
            {settings.mvk_theme_config?.nav?.enable_bottom_mobile_bar && <BottomMobileBar />}
        </ChassisLayout>
    );
};

const CookieConsentModal = (props) => {
    const [settings, setSettings] = useContext(SettingsContext);
    const bgColor = settings.cookie_consent?.background_color ? Coloring(settings.cookie_consent?.background_color, settings) : settings.design?.colors?.secondary_color;
    const color = settings.cookie_consent?.background_value === 'light' ? Coloring('body_copy_color', settings) : '#fff';
    const buttonBgColor = settings.cookie_consent?.button_background_color ? Coloring(settings.cookie_consent?.button_background_color, settings) : settings.design?.colors?.primary_color;
    const buttonColor = settings.cookie_consent?.button_background_value && settings.cookie_consent?.button_background_value === 'light' ? Coloring('body_copy_color', settings) : '#fff';

    if (settings.cookie_consent?.enable_cookie_consent) {
        return (
            <CookieConsent location="bottom" buttonText={settings.cookie_consent?.button_text || settings.cookie_consent?.cookie_consent_button_text} cookieName="CookieConsent" style={{ background: bgColor, color: color }} buttonStyle={{ background: buttonBgColor, color: buttonColor, fontSize: "1rem", padding: "10px 20px" }} expires={150} ariaAcceptLabel={`${settings.cookie_consent?.button_text}, Accept cookies`}>
                <div style={{ fontSize: "15px" }}>
                    <HtmlParser html={settings.cookie_consent?.message || settings.cookie_consent?.cookie_consent_message} />
                </div>
            </CookieConsent>
        );
    } else {
        return null;
    }
};

export default Start;
