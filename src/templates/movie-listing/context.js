import React, { useState, useEffect, createContext } from 'react';

export const TheatersContext = createContext();
export const TheatersProvider = ({ data, children }) => {
    const [ loading, setLoading ]   = useState(true);
    const [ error, setError ]       = useState(false);
    const [ theaters, setTheaters ] = useState(false);
    const [ selected, setSelected ] = useState(0);

    useEffect(() => {
        setTheaters(data);
        setLoading(false);
    },[]);

    return (
        <TheatersContext.Provider value={{ loading, theaters, selected, setSelected }}>
            {children}
        </TheatersContext.Provider>
    );
};