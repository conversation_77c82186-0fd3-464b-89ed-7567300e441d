import React, { useContext, useState, useEffect, useCallback } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faPlayCircle, faTimes } from '@fortawesome/free-solid-svg-icons'

import { decode } from 'html-entities';


// SCSS.
import './trailer.scss';

// HELPERS.
const Clicker = React.lazy(() => import('src/helpers/clicker'));


const Start = ({ movie }) => {
    const [ modal, setModal ] = useState(false);

    const ShowTrailer = (e) => {
        setModal(true);
    };

    const CloseTrailer = (e) => {
        setModal(false);
    };

    return (
        <div class="trailer">
            <Clicker process={ShowTrailer} class="icon-text">
                <FontAwesomeIcon icon={faPlayCircle} aria-label="play button"/> Trailer
            </Clicker>
            {modal && <Modal title={movie.title} trailer={movie.trailer} display={modal} close={CloseTrailer} />}
        </div>
    );
};

const Modal = ({ title, trailer, display, close }) => {
    var url = new URL(trailer);
    var id  = url.searchParams.get('v');
    var src = `https://www.youtube.com/embed/${id}`;

    const CloseModal = useCallback(e => {
        close(e);
    }, [close]);

    return (
        <Clicker id="movie-trailer-modal" class={(display) ? 'expanded' : 'hidden'} process={CloseModal}>
            <div class="movie-trailer-container">
                <div class="movie-trailer-header flexbox gap">
                    <div class="movie-trailer-title flex1">
                        <h3>{title}</h3>
                    </div>
                    <Clicker class="movie-trailer-close flex" process={CloseModal}>
                        <FontAwesomeIcon icon={faTimes} aria-label="close icon" />
                    </Clicker>
                </div>
                <div class="movie-trailer-iframe">
                    <iframe src={src} title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowfullscreen></iframe>
                </div>
            </div>
        </Clicker>
    ); // width="560" height="315"
};

export default Start;