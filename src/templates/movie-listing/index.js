import React, { useContext, useState, useEffect } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faPlusSquare, faMapMarkerAlt } from '@fortawesome/free-solid-svg-icons'

import { decode } from 'html-entities';


// HELPERS.
const Clicker = React.lazy(() => import('src/helpers/clicker'));
import Errors from 'src/helpers/errors';
const Imaging = React.lazy(() => import('src/helpers/imaging'));

// CONTEXT.
import { TheatersProvider, TheatersContext } from 'src/templates/movie-listing/context';

// PARTIALS.
import Trailer from 'src/templates/movie-listing/trailer';
import ShowTimes from 'src/templates/movie-listing/showtimes';

// SCSS.
import 'src/templates/movie-listing/index.scss';

const Start = ({ data }) => {
    return (
        <TheatersProvider data={data?.mvk_item_content?.custom_fields?.theaters}>
            <Container data={data} />
        </TheatersProvider>
    );
};

const Container = ({ data }) => {
    const theatersContext = useContext(TheatersContext);

    if (theatersContext.theaters) {
        return (
            <div id="template-movie-listing">
                <div class="flexbox column gap">
                    <Theaters theaters={theatersContext.theaters} />
                    <Theater theater={theatersContext.theaters[theatersContext.selected]} />
                </div>
            </div>
        );
    } else {
        return (<div />);
    }
};

const Theaters = ({ theaters }) => {
    const theatersContext = useContext(TheatersContext);

    function changed (e) {
        theatersContext.setSelected(e.target.value);
    };

    return (
        <div id="theaters-choices">
            <div class="flexbox gap align">
                <div class="flex">
                    <select onChange={changed}>
                        {theaters && theaters.map((theater, i) => <option value={i} class="flex1">{theater.name}</option>)}
                    </select>
                </div>
                <div class="flex1" />
            </div>
        </div>
    );
};

const Theater = ({ theater }) => {
    return (
        <div id="theater">
            <div class="theater-header flexbox gap">
                <div class="theater-title flex1">
                    <h2>{theater.name}</h2>
                </div>
                <div class="theater-address flex">
                    <Clicker type="anchor" url={`https://www.google.com/maps/place/${theater.address}`} target="_blank" ariaLabel={`directions to ${theater.name}`}>
                        <FontAwesomeIcon icon={faMapMarkerAlt} aria-label="location pin" /> {theater.address}
                    </Clicker>
                </div>
            </div>
            <Movies theater={theater} />
        </div>
    );
};

const Movies = ({ theater }) => {
    if (theater.movies) {
        return (
            <div id="movies" class="flexbox gap wrap">
                {theater && theater.movies.map((movie, i) => <Movie theater={theater} key={i} movie={movie} />)}
            </div>
        );
    } else {
        return (
            <div id="movies" class="flexbox gap wrap">
                <div class="center">No Movies Provided For This Theater.</div>
            </div>
        );
    }
};

const Movie = ({ key, theater, movie }) => {
    return (
        <div data-theater={theater.name} data-movie={movie.title} class="movie flex1" >
            <div class="flexbox gap">
                {movie.poster_image_thumbnail && <Thumbnail movie={movie} />}
                <Content movie={movie} />
            </div>
        </div>
    );
};

const Content = ({ movie }) => {
    return (
        <div class="flex1 content">
            <div class="title">
                <h3>{movie.title}</h3>
            </div>
            <div class="details">
                <div class="rating">Rating: {movie.rating ? movie.rating : 'NA'}</div>
                <div class="duration">Runtime: {movie.runtime ? `${movie.runtime} minutes` : 'NA'}</div>
                {movie.genres && <Genres genres={movie.genres} />}
            </div>
            {movie.trailer && <Trailer movie={movie} />}
            {movie.synopsis && <Summary synopsis={movie.synopsis} />}
            {movie.showtimes && <ShowTimes movie={movie} />}
        </div>
    );
};

const Genres = ({ genres }) => {
    var formatted = genres.map((genre, k) => genre.name).join(', ');
    return (<div class="genre">Genres: {formatted}</div>);
};

const Thumbnail = ({ movie }) => {
    let imageData = {
        url: movie.poster_image_thumbnail,
        alt: `${movie.title} poster`,
        width: 130,
        height: 200
    }
    return (
        <div class="flex poster">
            <Imaging data={imageData} class="img-responsive" />
        </div>
    );
};

const Summary = ({ synopsis }) => {
    const [ display, setDisplay ] = useState(false);

    function Expand (e) {
        setDisplay(!display);
    };

    return (
        <div class="summary">
            <Clicker process={Expand} class="icon-text">
                <FontAwesomeIcon icon={faPlusSquare} aria-label="plus icon" /> Summary
            </Clicker>
            {display && <div>{synopsis}</div>}
        </div>
    );
};

export default Start;
