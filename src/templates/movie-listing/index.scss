@import 'src/scss/variables.scss';

#template-movie-listing {
    margin: 50px auto;
    max-width: 1250px;

    @media screen and (max-width:1300px) {
        padding: 10px;
    }

    #theater {

        .theater-header {
            margin-bottom: 30px;
            display: flex;
            flex-flow: column nowrap;

            @media screen and (min-width:$break-small) {
                margin-bottom: 60px;
            }

            .theater-title {
                font-size: 1.25rem;
            }

            .theater-address {
                font-size: 0.875rem;

                @media screen and (min-width:$break-small) {
                    font-size: 1rem;
                }
            }
        }

        #movies {
            .movie {
                width: 300px;
                max-width: 300px;
                min-width: 300px;

                .poster {
                    max-width: 130px;

                    img {
                        border-radius: 5px;
                    }
                }

                .content {
                    font-size: 0.813rem;

                    .details {
                        margin: 10px 0px;
                        line-height: 20px;
                    }

                    .trailer {
                        margin-bottom: 8px;
                        cursor: pointer;

                        .icon-text {
                            svg {
                                width: 15px;
                                height: 15px;
                            }
                        }
                    }

                    .summary {
                        margin-bottom: 8px;
                        cursor: pointer;

                        .icon-text {
                            svg {
                                width: 15px;
                                height: 15px;
                            }
                        }
                    }
                }
            }
        }
    }
}