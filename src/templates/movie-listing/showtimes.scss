.movie-showtimes {
    cursor: pointer;

    .time {
        white-space: nowrap;
    }
}

#movie-showtimes-modal {
    position: fixed;
    overflow-y: scroll;
    z-index: 5000;
    top: 0px;
    bottom: 0px;
    left: 0px;
    right: 0px;
    background-color: rgba(0, 0, 0, 0.8);
    cursor: pointer;
    padding: 120px 0 40px;
    @media (min-width: 1200px) {
        padding: 180px 0 40px;
    }
    .movie-showtimes-container {
        margin: 50px auto 0px;
        max-width: 600px;
        background-color: white;
        box-shadow: 5px 5px rgba(0, 0, 0, 0.3);
        border-radius: 10px;
        cursor: default;

        .movie-showtimes-header {
            padding: 20px;

            .movie-showtimes-title {
                text-align: left;
                // color: black;
                font-size: 1.25rem;
            }

            svg {
                fill: black;
                color: black;
                width: 30px;
                height: 30px;
                cursor: pointer;
            }
        }

        .movie-showtimes {
            padding: 20px;

            .date {
                margin: 15px 0px 10px;
                font-size: 0.875rem;
                color: black;
            }

            .time {
                display: inline-block;
                margin: 5px;
                padding: 5px;
                background-color: black;
                color: white;
                border-radius: 3px;
                line-height: 13px;
            }
        }
    }
}
