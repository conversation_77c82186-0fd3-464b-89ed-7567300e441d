import React, { useContext, useState, useEffect, useCallback } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faTicketAlt, faTimes } from '@fortawesome/free-solid-svg-icons'

import { decode } from 'html-entities';


// SCSS.
import './showtimes.scss';

// HELPERS.
const Clicker = React.lazy(() => import('src/helpers/clicker'));
import { getDayName } from 'src/helpers/date';

const Start = ({ movie }) => {
    const [ modal, setModal ] = useState(false);

    const OpenModal = (e) => {
        setModal(true);
        document.body.classList.add('modal-open');
    };

    const CloseModal = (e) => {
        setModal(false);
        document.body.classList.remove('modal-open');
    };

    return (
        <div>
            <Clicker process={OpenModal} class="movie-showtimes">
                <FontAwesomeIcon icon={faTicketAlt} aria-label="ticket icon" /> Show Times
            </Clicker>
            {modal && <Modal movie={movie} display={modal} close={CloseModal} />}
        </div>
    );
};



const Modal = ({ movie, display, close }) => {
    const CloseModal = useCallback(e => close(e), [close]);

    return (
        <div id="movie-showtimes-modal" class={(display) ? 'expanded' : 'hidden'}>
            <div class="movie-showtimes-container">
                <div class="movie-showtimes-header flexbox gap">
                    <div class="movie-showtimes-title flex1">
                        <h3>{movie.title}</h3>
                    </div>
                    <Clicker class="movie-showtimes-close flex" process={CloseModal}>
                        <FontAwesomeIcon icon={faTimes} aria-label="close icon" />
                    </Clicker>
                </div>
                <div class="movie-showtimes-iframe">
                    <ShowTimes movie={movie} />
                </div>
            </div>
        </div>
    );
};

const ShowTimes = ({ movie }) => {
    var currently = new Date();
    var previous  = false;

    var formatted = movie.showtimes.map((showtime, k) => {
        var start = new Date(showtime.start_at);

        if (start < currently) {
            return;
        }

        if (previous != showtime.formatted_start_date) {
            previous = showtime.formatted_start_date;
            var num = start.getDay();
            var day = getDayName(num, 'long');

            return (
                <>
                    <div class="date">{day} / {showtime.formatted_start_date}</div>
                    <Clicker class="time" url={showtime.booking_link} target="_blank">{showtime.formatted_start_time}</Clicker>
                </>
            );
        } else {
            return (<Clicker class="time" url={showtime.booking_link} target="_blank">{showtime.formatted_start_time}</Clicker>);
        }
    });

    return (
        <div class="movie-showtimes">
            {formatted}
        </div>
    );
};

export default Start;
