/*******************************************************************************************************
   Copyright 2021 - Calise Partners LLC d/b/a Imaginuity. All rights are reserved. 
   Reproduction or transmission in whole or in part, in any form or by any means, electronic, mechanical 
or otherwise, is prohibited without the prior written consent of Calise Partners LLC d/b/a Imaginuity.
********************************************************************************************************
   Project : 10618 Maverick Platform Development
   Author : Imaginuity Developers (<PERSON>, <PERSON>)
   Description : Controller component that checks for type of template and displays it
   Creation Date : Fri Apr 16 2021
   Documentation : [documentation url - point to default wiki for project if no specific documentation for script]
 ********************************************************************************************************/

   import React, { Suspense } from 'react';

const Default     = React.lazy(() => import('src/templates/default'));
const AxialScrape = React.lazy(() => import('src/templates/axial-scrape'));
const MovieList   = React.lazy(() => import('src/templates/movie-listing'));
const Mappedin    = React.lazy(() => import('src/templates/mappedin'));

const Start = ({ data, settings }) => {
    return (
        <Suspense fallback={<div />}>
            <Pathway data={data} settings={settings} />
        </Suspense>
    );
};

const Pathway = ({ data, settings }) => {
    const templateType = data.mvk_item_meta.template;
    switch (templateType) {   
        case 'template-movie-listing':
            return (<MovieList data={data} settings={settings} />);             
        case 'template-axial-scrape':
            return (<AxialScrape data={data} settings={settings} />);
        case 'template-mappedin':
            return (<Mappedin data={data} settings={settings} />);
        case 'default':
            return (<Default data={data} settings={settings} />);
        default:
            return null;
    }
};

export default Start;
