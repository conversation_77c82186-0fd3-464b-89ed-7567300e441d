import React, { useState, useEffect, useMemo } from 'react';

// SCSS.
import 'src/templates/axial-scrape/index.scss';

const Start = ({ data, settings }) => {
    const comments = useMemo(() => data?.mvk_item_content?.custom_fields || false, []);

    // PLACEMENT
    useEffect(() => {
        // option for aboslule urls
        if (data.mvk_item_content.custom_fields.absolute_urls) {
            setTimeout(() => {
                let anchortags = document.body.getElementsByTagName("a");
                for (let tag of anchortags) {
                    tag.href = tag.href;
                }
            }, 700);
        }

        if (comments) {
            var updated = [];
            var observers = {};

            // CREATE COMMENTS.
            Object.keys(comments).map(key => {
                if (key.includes('axial')) {
                    var order = ordering(key);
                    updated[order] = {
                        key: key,
                        order: order,
                        text: comments[key],
                        comment: document.createComment(comments[key]),
                        selector: selection(key, settings),
                        type: type(key),
                        completed: false
                    };
                }
            });
            // console.log(updated);
            updated.map(item => {
                console.log(item);
                var targetElement = document.getElementById(item.selector);
                var parentElement = document.getElementById(item.selector)?.parentNode;
                if (targetElement) {
                    switch (item.type) {
                        case 'prepend':
                            document.getElementById(item.selector)?.prepend(item.comment);
                            console.log('FOUND', item.selector);
                            break;
                        case 'append':
                            document.getElementById(item.selector)?.append(item.comment);
                            console.log('FOUND', item.selector);
                            break;
                        case 'before':
                            parentElement?.insertBefore(item.comment, targetElement);
                            console.log('FOUND', item.selector);
                            break;
                        case 'after':
                            parentElement?.insertBefore(item.comment, targetElement.nextSibling);
                            console.log('FOUND', item.selector);
                            break;
                        default:
                        console.log('NOT FOUND / UPFRONT', item);
                    };

                } else {
                    observers[item.selector] = new MutationObserver((mutations, obs) => {
                        var targetElement = document.getElementById(item.selector);
                        var parentElement = document.getElementById(item.selector)?.parentNode;
                        if (targetElement && parentElement) {
                            switch (item.type) {
                                case 'prepend':
                                    targetElement?.prepend(item.comment);
                                    console.log('FOUND', item.selector);
                                    break;
                                case 'append':
                                    targetElement?.append(item.comment);
                                    console.log('FOUND', item.selector);
                                    break;
                                case 'before':
                                    parentElement?.insertBefore(item.comment, targetElement);
                                    console.log('FOUND', item.selector);
                                    break;
                                case 'after':
                                    parentElement?.insertBefore(item.comment, targetElement.nextSibling);
                                    console.log('FOUND', item.selector);
                                    break;
                                default:
                                console.log('NOT FOUND / MUTATION', item);
                            };
                            obs.disconnect();
                        }
                    });
                    observers[item.selector].observe(document, { childList: true, subtree: true });
                }
            });
        }
    }, [ comments ]);

    return (<div id="content" />);
};

export default Start;

const selection = (key, settings) => {
    switch (key) {
        case 'axial_body_start_comment':
            return 'content';
        case 'axial_body_end_comment':
            return 'content';
        case 'axial_language_start_comment':
            return 'translations-container';
        case 'axial_language_end_comment':
            return 'translations-container';
        case 'axial_account_link_comment':
            return (settings?.mvk_theme_config?.header?.search && settings?.mvk_theme_config?.header?.search_type == 'menu-utility') ? 'search-box' : 'translations-container';
        case 'axial_account_link_mobile_comment':
            return (settings?.mvk_theme_config?.header?.search && settings?.mvk_theme_config?.header?.search_type == 'menu-utility') ? 'search-box' : 'translations-container';
        case 'axial_header_cart_start_comment':
            return (settings?.mvk_theme_config?.header?.search && settings?.mvk_theme_config?.header?.search_type == 'menu-utility') ? 'search-box' : 'translations-container';
        case 'axial_header_cart_end_comment':
            return (settings?.mvk_theme_config?.header?.search && settings?.mvk_theme_config?.header?.search_type == 'menu-utility') ? 'search-box' : 'translations-container';
    };
};

// ORDERING PLAYS A ROLE.
const ordering = (key) => {
    // RETURNS MUST BE UNIQUE.
    switch (key) {
        case 'axial_body_end_comment':
            return 0;
        case 'axial_body_start_comment':
            return 1;
        case 'axial_language_end_comment':
            return 7;
        case 'axial_language_start_comment':
            return 2;
        case 'axial_header_cart_start_comment':
            return 6;
        case 'axial_header_cart_end_comment':
            return 5;
        case 'axial_account_link_comment':
            return 4;
        case 'axial_account_link_mobile_comment':
            return 3;
    };
};

// TYPE OF INSERTION.
const type = (key) => {
    switch (key) {
        case 'axial_body_start_comment':
            return 'prepend';
        case 'axial_body_end_comment':
            return 'append';
        case 'axial_language_start_comment':
            return 'before';
        case 'axial_language_end_comment':
            return 'after';
        case 'axial_account_link_comment':
            return 'after';
        case 'axial_account_link_mobile_comment':
            return 'after';
        case 'axial_header_cart_start_comment':
            return 'after';
        case 'axial_header_cart_end_comment':
            return 'after';
    };
};