// .mappedin-container {
//     height: calc(100vh - 200px);
//     width: 100%;

// }

.mappedin-container {
    position: relative;
    min-height: 550px;

    @media (min-width: 768px) {
        min-height: 70vh;
    }
}

// #mappedin-map {
//     position: relative !important;
//     width: 100%;
//     // height: 100%;
//     height: calc(100vh - 200px);
//     margin: 0px;
//     touch-action: none;
// }