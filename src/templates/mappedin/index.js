/*******************************************************************************************************
   Copyright 2021 - Calise Partners LLC d/b/a Imaginuity. All rights are reserved. 
   Reproduction or transmission in whole or in part, in any form or by any means, electronic, mechanical 
or otherwise, is prohibited without the prior written consent of Calise Partners LLC d/b/a Imaginuity.
********************************************************************************************************
   Project : 10618 Maverick Platform Development
   Author : Imaginuity Developers (<PERSON>, <PERSON>, <PERSON>)
   Description : module that inserts mappedin html and window variables before calling the external mappedin script
   Creation Date : Wed June 22 2022
   Documentation : [documentation url - point to default wiki for project if no specific documentation for script]
 ********************************************************************************************************/

import React, { useEffect, useContext, useState } from 'react';
import { addScript, addStylesheet } from 'src/hooks';
import { PrincipalContext } from "src/context";

import './index.scss';

const Start = ({ data, settings }) => {
    const [principal, setPrincipal] = useContext(PrincipalContext);
    const [mapLang] = useState(principal?.activeTranslation === 'zh-hans' ? 'zh-cn' : principal?.activeTranslation);

    addScript(data?.mvk_item_content?.mappedin?.script);
    addStylesheet('https://d1p5cqqchvbqmy.cloudfront.net/web2/release/mappedin-web.css');

    useEffect(() => {
        window.mappedin = {
            miKey: {
                id: data?.mvk_item_content?.mappedin?.miKey?.id,
                key: data?.mvk_item_content?.mappedin?.miKey?.key
            },
            searchKey: {
                id: data?.mvk_item_content?.mappedin?.searchKey?.id,
                secret: data?.mvk_item_content?.mappedin?.searchKey?.key
            },
            venue: settings?.store?.mappedin_venue,
            vertical: "mall",
            language: mapLang || 'en',
        };
    }, [principal?.activeTranslation]);

    return (
        <div class='mappedin-container'>
            <div data-key="externalId" id="mappedin-map" class='mappedin-template'></div>
        </div>
    );
};

export default Start;