/*******************************************************************************************************
   Copyright 2021 - Calise Partners LLC d/b/a Imaginuity. All rights are reserved. 
   Reproduction or transmission in whole or in part, in any form or by any means, electronic, mechanical 
or otherwise, is prohibited without the prior written consent of Calise Partners LLC d/b/a Imaginuity.
********************************************************************************************************
   Project : 10618 Maverick Platform Development
   Author : Imaginuity Developers (<PERSON>, <PERSON>)
   Description : default template markup
   Creation Date : Fri Apr 16 2021
   Documentation : [documentation url - point to default wiki for project if no specific documentation for script]
 ********************************************************************************************************/

import React, { useState, useEffect } from 'react';

import './index.scss';

import { decode } from 'html-entities';


const Start = ({ data, settings }) => {
    const [table, setTable] = useState(false);

    let titleStyles = {
        color: settings.design?.colors?.primary_color
    };

    let contentStyles = {
        color: settings.design?.colors?.body_copy_color
    };

    let tableEl = document.getElementsByTagName('table');

    useEffect(() => {
        if (tableEl.length > 0) {
            setTable(true)
        }
    }, [tableEl]);

    return (
        <section id='template'>
            <div class='template-container grid-container'>
                <div class='title-container'>
                    <h1 style={titleStyles}>{decode(data.mvk_item_content.title)}</h1>
                </div>
                <div class='content-container'>
                    <div class={`${table ? 'table-container' : ''} content`} style={contentStyles} dangerouslySetInnerHTML={{ __html: data.mvk_item_content.content }} />
                </div>
            </div>
        </section>
    );
};

export default Start;