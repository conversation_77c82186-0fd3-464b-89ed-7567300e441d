import React, { useContext, useState, useEffect, Suspense } from "react";
import { useLocation, useParams } from "react-router-dom";

import config from 'src/config';

// PARTIALS.
import Loading from "src/partials/loading";

// LAZY.
const Containers = React.lazy(() => import("src/containers"));

// CONTEXT.
import { PrincipalContext } from 'src/context';
import { SettingsProvider, SettingsContext } from 'src/context';
import { PagesProvider, PagesContext } from 'src/context';
import { PostsContext, PostsProvider } from 'src/context';
import { BlogTemplatesContext, BlogTemplatesProvider } from "src/context";
import { CustomTemplatesContext, CustomTemplatesProvider } from "src/context";
import { ServicesContext, ServicesProvider } from "src/context";
import { ServiceTemplatesContext, ServiceTemplatesProvider } from "src/context";
import { EventsContext, EventsProvider } from "src/context";
import { JobsContext, JobsProvider } from "src/context";
import { PropertiesContext, PropertiesProvider } from "src/context";
import { SalesContext, SalesProvider } from "src/context";
import { GlobalSalesContext, GlobalSalesProvider } from "src/context";
import { FloorplansContext, FloorplansProvider } from "src/context";
import { AircraftContext, AircraftProvider } from "src/context";
import { MarketReportsContext, MarketReportsProvider } from "src/context";
import { StoresContext, StoresProvider } from "src/context";
import { SpacesContext, SpacesProvider } from "src/context";
import { TeamContext, TeamProvider } from "./context";
import { TestimonialsContext, TestimonialsProvider } from "./context";
import { LocationsContext, LocationsProvider } from "./context";
import { PageTemplatesContext, PageTemplatesProvider } from "./context";

const Start = ({ subsite, activeTranslation, settings, pages }) => {

    return (
        <SettingsProvider settings={settings}>
            <PagesProvider pages={pages}>
                <LocationsCheck subsite={subsite} activeTranslation={activeTranslation} settings={settings} pages={pages} />
            </PagesProvider>
        </SettingsProvider>
    );

};

const LocationsCheck = ({ subsite, activeTranslation, settings, pages }) => {
    if (settings.locations) {
        return (
            <LocationsProvider subsite={subsite} translation={activeTranslation || ''}>
                <PartTwo subsite={subsite} activeTranslation={activeTranslation} settings={settings} pages={pages} />
            </LocationsProvider>
        )
    } else {
        return <PartTwo subsite={subsite} activeTranslation={activeTranslation} settings={settings} pages={pages} />
    }
}

const PartTwo = ({ subsite, activeTranslation, settings, pages }) => {
    const [postOptions] = useState(settings?.post?.enable_custom_subdirectory ? (Array.isArray(settings?.post?.custom_subdirectory) ? settings?.post?.custom_subdirectory : Object.values(settings?.post?.custom_subdirectory)) : ['blog']);
    const [propertyOptions] = useState(settings?.property?.enable_custom_subdirectory ? (Array.isArray(settings?.property?.custom_subdirectory) ? settings?.property?.custom_subdirectory : Object.values(settings?.property?.custom_subdirectory)) : ['properties']);
    const [floorplanOptions] = useState(settings?.floorplans?.enable_custom_subdirectory ? (Array.isArray(settings?.floorplans?.custom_subdirectory) ? settings?.floorplans?.custom_subdirectory : Object.values(settings?.floorplans?.custom_subdirectory)) : ['floorplans']);
    const [spaceOptions] = useState(settings?.space?.enable_custom_subdirectory ? (Array.isArray(settings?.space?.custom_subdirectory) ? settings?.space?.custom_subdirectory : Object.values(settings?.space?.custom_subdirectory)) : ['spaces']);
    const [teamOptions] = useState(settings?.team?.enable_custom_subdirectory ? (Array.isArray(settings?.team?.custom_subdirectory) ? settings?.team?.custom_subdirectory : Object.values(settings?.team?.custom_subdirectory)) : ['team']);
    const [saleOptions] = useState(settings?.sale?.enable_custom_subdirectory ? (Array.isArray(settings?.sale?.custom_subdirectory) ? settings?.sale?.custom_subdirectory : Object.values(settings?.sale?.custom_subdirectory)) : ['sales']);
    const [eventOptions] = useState(settings?.event?.enable_custom_subdirectory ? (Array.isArray(settings?.event?.custom_subdirectory) ? settings?.event?.custom_subdirectory : Object.values(settings?.event?.custom_subdirectory)) : ['events']);
    const [resourceOptions] = useState(settings?.resources?.enable_custom_subdirectory ? (Array.isArray(settings?.resources?.custom_subdirectory) ? settings?.resources?.custom_subdirectory : Object.values(settings?.resources?.custom_subdirectory)) : ['resources']);
    const [jobOptions] = useState(settings?.job?.enable_custom_subdirectory ? (Array.isArray(settings?.job?.custom_subdirectory) ? settings?.job?.custom_subdirectory : Object.values(settings?.job?.custom_subdirectory)) : ['jobs']);
    const [storeOptions] = useState(settings?.store?.enable_custom_subdirectory ? (Array.isArray(settings?.store?.custom_subdirectory) ? settings?.store?.custom_subdirectory : Object.values(settings?.store?.custom_subdirectory)) : ['stores']);
    const [locationOptions] = useState(settings?.locations?.enable_custom_subdirectory ? (Array.isArray(settings?.locations?.custom_subdirectory) ? settings?.locations?.custom_subdirectory : Object.values(settings?.locations?.custom_subdirectory)) : ['locations']);
    const [locationsList] = useState(settings?.locations?.no_subdirectory ? settings?.locations?.list : []);
    const locationsNoSubdirectory = settings?.locations?.no_subdirectory;
    const [serviceOptions] = useState(['services']);
    const [servicesList] = useState(settings?.services?.no_subdirectory ? settings?.services?.list : []);
    const servicesNoSubdirectory = settings?.services?.no_subdirectory;
    const params = useParams();

    var data = {
        slug: params.slug || false,
        first: params.first || false,
        second: params.second || false,
        third: params.third || false,
        fourth: params.fourth || false,
        fifth: params.fifth || false
    };

    if (Object.keys(data).some((key) => data[key] == 'search')) {
        return (<Containers type='search' />);
    } else if (Object.keys(data).some((key) => spaceOptions.includes(data[key]) && !spaceOptions.includes(params.slug))) {
        return (
            <SpacesProvider subsite={subsite} translation={activeTranslation || ''}>
                <WaitSpaces />
            </SpacesProvider>
        );
    } else if (Object.keys(data).some((key) => postOptions.includes(data[key]) && !postOptions.includes(params.slug))) {
        if (settings.post?.blog_templates) {
            return (
                <BlogTemplatesProvider subsite={subsite} translation={activeTranslation || ''}>
                    <PostsProvider subsite={subsite} translation={activeTranslation || ''}>
                        <WaitPosts />
                    </PostsProvider>
                </BlogTemplatesProvider>
            );
        } else {
            return (
                <PostsProvider subsite={subsite} translation={activeTranslation || ''}>
                    <WaitPosts />
                </PostsProvider>
            );
        }
    } else if (settings.services && (!servicesNoSubdirectory && Object.keys(data).some((key) => serviceOptions.includes(data[key]) && !serviceOptions.includes(params.slug))) || (servicesNoSubdirectory && Object.keys(data).some((key) => servicesList.includes(data[key])))) {
        if (settings.services?.service_templates) {
            return (
                <ServiceTemplatesProvider subsite={subsite} translation={activeTranslation || ''}>
                    <ServicesProvider subsite={subsite} translation={activeTranslation || ''}>
                        <WaitServices />
                    </ServicesProvider>
                </ServiceTemplatesProvider>
            );
        } else {
            return (
                <ServicesProvider subsite={subsite} translation={activeTranslation || ''}>
                    <WaitServices />
                </ServicesProvider>
            );
        }
    } else if (Object.keys(data).some((key) => eventOptions.includes(data[key]) && !eventOptions.includes(params.slug))) {
        return (
            <EventsProvider subsite={subsite} translation={activeTranslation || ''}>
                <WaitEvents />
            </EventsProvider>
        );
    } else if (Object.keys(data).some((key) => jobOptions.includes(data[key]) && !jobOptions.includes(params.slug))) {
        return (
            <JobsProvider subsite={subsite} translation={activeTranslation || ''}>
                <WaitJobs />
            </JobsProvider>
        );
    } else if (Object.keys(data).some((key) => propertyOptions.includes(data[key]) && !propertyOptions.includes(params.slug))) {
        return (
            <PropertiesProvider subsite={subsite} translation={activeTranslation || ''}>
                <WaitProperties />
            </PropertiesProvider>
        );
    } else if (Object.keys(data).some((key) => floorplanOptions.includes(data[key]) && !floorplanOptions.includes(params.slug))) {
        return (
            <FloorplansProvider subsite={subsite} translation={activeTranslation || ''}>
                <WaitFloorplans />
            </FloorplansProvider>
        );
    } else if (Object.keys(data).some((key) => storeOptions.includes(data[key]) && !storeOptions.includes(params.slug))) {
        return (
            <StoresProvider subsite={subsite} translation={activeTranslation || ''}>
                <WaitStores />
            </StoresProvider>
        );
    } else if (Object.keys(data).some((key) => saleOptions.includes(data[key]) && !saleOptions.includes(params.slug))) {
        return (
            <SalesProvider subsite={subsite} translation={activeTranslation || ''}>
                <WaitSales />
            </SalesProvider>
        );
    } else if (Object.keys(data).some((key) => data[key] == 'national-sales' && params.slug != 'national-sales')) {
        return (
            <GlobalSalesProvider subsite={subsite} translation={activeTranslation || ''}>
                <WaitGlobalSales />
            </GlobalSalesProvider>
        );
    } else if (Object.keys(data).some((key) => teamOptions.includes(data[key]) && !teamOptions.includes(params.slug))) {
        return (
            <TeamProvider subsite={subsite} translation={activeTranslation || ''}>
                <WaitTeam />
            </TeamProvider>
        );
    } else if (Object.keys(data).some((key) => data[key] == 'testimonials' && params.slug != 'testimonials')) {
        return (
            <TestimonialsProvider subsite={subsite} translation={activeTranslation || ''}>
                <WaitTestimonials />
            </TestimonialsProvider>
        );
    } else if (Object.keys(data).some((key) => data[key] == 'aircraft' && params.slug != 'aircraft')) {
        return (
            <AircraftProvider subsite={subsite} translation={activeTranslation || ''}>
                <WaitAircraft />
            </AircraftProvider>
        );
    } else if (Object.keys(data).some((key) => data[key] == 'market-reports' && params.slug != 'market-reports')) {
        return (
            <MarketReportsProvider subsite={subsite} translation={activeTranslation || ''}>
                <WaitMarketReports />
            </MarketReportsProvider>
        );
    } else if (settings.locations && (!locationsNoSubdirectory && Object.keys(data).some((key) => locationOptions.includes(data[key]) && !locationOptions.includes(params.slug))) || (locationsNoSubdirectory && Object.keys(data).some((key) => locationsList.includes(data[key])))) {
        return (
            <PageTemplatesProvider subsite={subsite} translation={activeTranslation || ''}>
                <WaitLocations />
            </PageTemplatesProvider>
        )
    } else {
        if (settings.page?.custom_templates) {
            return (
                <CustomTemplatesProvider subsite={subsite} translation={activeTranslation || ''}>
                    <Ascertain content="page" context={pages} />
                </CustomTemplatesProvider>
            )
        } else {
            return (<Ascertain content="page" context={pages} />);
        }
    }
};

const WaitPosts = ({ }) => {
    const [posts, setPosts] = useContext(PostsContext);
    if (posts.loading) return (<Loading />);
    else return (<Ascertain content="post" context={posts} />);
};

const WaitServices = ({ }) => {
    const [services, setServices] = useContext(ServicesContext);
    if (services.loading) return (<Loading />);
    else return (<Ascertain content="service" context={services} />);
};

const WaitEvents = ({ }) => {
    const [events, setEvents] = useContext(EventsContext);
    if (events.loading) return (<Loading />);
    else return (<Ascertain content="event" context={events} />);
};

const WaitJobs = ({ }) => {
    const [jobs, setJobs] = useContext(JobsContext);
    if (jobs.loading) return (<Loading />);
    else return (<Ascertain content="job" context={jobs} />);
};

const WaitProperties = ({ }) => {
    const [properties, setProperties] = useContext(PropertiesContext);
    if (properties.loading) return (<Loading />);
    else return (<Ascertain content="property" context={properties} />);
};

const WaitFloorplans = ({ }) => {
    const [floorplans, setFloorplans] = useContext(FloorplansContext);
    if (floorplans.loading) return (<Loading />);
    else return (<Ascertain content="floorplan" context={floorplans} />);
};

const WaitLocations = ({ pages }) => {
    const [pageTemplates, setPageTemplates] = useContext(PageTemplatesContext);
    if (pageTemplates.loading) return (<Loading />);
    else return (<Ascertain content="location" context={pageTemplates} />);
};

const WaitSales = ({ }) => {
    const [sales, setSales] = useContext(SalesContext);
    if (sales.loading) return (<Loading />);
    else return (<Ascertain content="sale" context={sales} />);
};

const WaitGlobalSales = ({ }) => {
    const [globalSales, setGlobalSales] = useContext(GlobalSalesContext);
    if (globalSales.loading) return (<Loading />);
    else return (<Ascertain content="sale" context={globalSales} />);
};

const WaitStores = ({ }) => {
    const [stores, setStores] = useContext(StoresContext);
    if (stores.loading) return (<Loading />);
    else return (<Ascertain content="store" context={stores} />);
};

const WaitSpaces = ({ }) => {
    const [spaces, setSpaces] = useContext(SpacesContext);
    if (spaces.loading) return (<Loading />);
    else return (<Ascertain content="space" context={spaces} />);
};

const WaitTeam = ({ }) => {
    const [team, setTeam] = useContext(TeamContext);
    if (team.loading) return (<Loading />);
    else return (<Ascertain content="team" context={team} />);
};

const WaitTestimonials = ({ }) => {
    const [testimonials, setTestimonials] = useContext(TestimonialsContext);
    if (testimonials?.loading) return (<Loading />);
    else return (<Ascertain content="testimonial" context={testimonials} />);
};
const WaitAircraft = ({ }) => {
    const [aircraft, setAircraft] = useContext(AircraftContext);
    if (aircraft?.loading) return (<Loading />);
    else return (<Ascertain content="aircraft" context={aircraft} />);
};

const WaitMarketReports = ({ }) => {
    const [marketReports, setMarketReports] = useContext(MarketReportsContext);
    if (marketReports?.loading) return (<Loading />);
    else return (<Ascertain content="market_reports" context={marketReports} />);
};

const Ascertain = ({ content, context }) => {
    const [loading, setLoading] = useState(true);
    const [principal, setPrincipal] = useContext(PrincipalContext);
    const [settings, setSettings] = useContext(SettingsContext);
    const [pages, setPages] = useContext(PagesContext);
    const [currentLocation, setCurrentLocation] = useState('');
    const location = useLocation();
    const params = useParams();
    const [found, setFound] = useState(false);

    useEffect(() => {
        setLoading(true);
        const slug = params?.slug?.toLowerCase();
        let stuff;
        let locationData;
        if (content === 'location') {
            const locationParam = settings?.locations?.no_subdirectory ? params.first : params.second;
            locationData = settings?.locations?.template_slugs[slug] ? settings?.locations?.template_slugs[slug] : settings?.locations?.template_slugs[locationParam] ? settings?.locations?.template_slugs[locationParam] : false;
            if (!locationData) setFound(false);
            const endpointSlug = locationData[slug] ? locationData[slug] : locationData[""];
            stuff = (context) ? context[endpointSlug] : false;
            setCurrentLocation(locationData['slug']);
        } else {
            const first = params?.first?.toLowerCase();
            const page = (first) ? slug : (principal.subsite && slug && principal.subsite != slug) ? slug : (!principal.subsite && slug) ? slug : settings.front_page;
            stuff = (context && config.preview.enabled) ? context.find((item) => item.mvk_slug == page) : (context) ? context[page] : false;
            if (settings?.locations && settings.locations?.enable_custom_subdirectory && settings.locations?.custom_subdirectory?.includes(params.first)) {
                setCurrentLocation(params.second);
            } else if (settings?.locations && settings.locations?.no_subdirectory && settings?.locations?.list.includes(params.first)) {
                setCurrentLocation(params.first);
            }
        }
        if (stuff) {
            const templateSlug = content === 'location' && stuff.mvk_item_content?.custom_fields?.template_slug ? stuff.mvk_item_content?.custom_fields?.template_slug : '';

            try {
                var webaddress = content === 'location' ? new URL(`${locationData['url']}${templateSlug}`) : new URL(stuff.mvk_item_meta.url);
            } catch (error) {
                setLoading(false);
                setCurrentLocation(false);
                setFound(false);
            }
            if (params.slug && (webaddress.pathname.replace(/\/$/, '').toLowerCase() == location.pathname.replace(/\/$/, '').toLowerCase() || params.slug === settings.front_page)) {
                setLoading(false);
                setFound(stuff);
            } else if (!(params.slug) && stuff) {
                setLoading(false);
                setCurrentLocation(false);
                setFound(stuff);
            } else {
                setLoading(false);
                setCurrentLocation(false);
                setFound(false);
            }
        } else {
            setLoading(false);
            setCurrentLocation(false);
            setFound(false);
        }
    }, [params, location, content, context]);

    useEffect(() => {
        setSettings({ ...settings, ...{ current_location: currentLocation } })
    }, [currentLocation])

    // custom 404 page content
    let override404 = false;
    if (settings?.not_found?.set_custom_404_page) {
        override404 = settings?.not_found?.custom_404_page
    }
    const data404 = (pages && override404) ? pages[override404] : context[override404];

    if (found) {
        return (
            <Suspense fallback={<div />}>
                <Containers type={content} data={found} />
            </Suspense>
        );
    } else if (!found && !loading) {
        return (
            <Suspense fallback={<div />}>
                <Containers
                    type={override404 && data404 ? "page" : "404"}
                    data={data404 ? data404 : false}
                    settings={settings}
                />
            </Suspense>
        );
    } else {
        return null;
    }

};

export default Start;