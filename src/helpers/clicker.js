/*******************************************************************************************************
   Copyright 2020-21 - Calise Partners LLC d/b/a Imaginuity. All rights are reserved.
   Reproduction or transmission in whole or in part, in any form or by any means, electronic, mechanical
or otherwise, is prohibited without the prior written consent of Calise Partners LLC d/b/a Imaginuity.
********************************************************************************************************
   Project : 10618 Maverick Platform Development
   Author : Imaginuity Developers (<PERSON>, <PERSON>)
   Description : Helper for managing a single clickable element.
   Creation Date : Thu Nov 05 2020
   Update Date : Jun 11, 2021
********************************************************************************************************/

import React, { useState } from 'react'
import { useNavigate } from 'react-router-dom';
import { decode } from 'html-entities';
// CONFIG.
import config from 'src/config';

// HELPERS
import { scrollIntoViewWithOffset } from 'src/helpers/scrollIntoViewWithOffset';

const Start = ({ process, path, url, type, target, isExternal, children, ariaLabel, ...otherProps }) => {
    // The touching and swiping was somehow screwing up mobile clicks from trasmitting to GTM thus we were weren't collecting mobile click events. 
    // const [swiping, setSwiping] = useState(false);
    // const Touching = (e) => { setSwiping(false); };
    // const Swiping  = (e) => { setSwiping(true); };
    const navigate = useNavigate();

    var hash = false;
    var search = false;

    // ABSOLUTE URL MISSING HTTPS. (but ignore urls that are paths or hashes)
    // Idiot proofing cause some peeps forget to provide the protocol with abosolute URLs.
    if (url && url.charAt(0) != '/' && url.charAt(0) != '#' && url.substr(0, 4) != 'tel:' && url.substr(0, 7) != 'mailto:') {
        url = (url?.includes('http')) ? url : `https://${url}`;
    }

    // FILE CHECK (convert path to absolute url so it jumps out of SPA).
    else if (url && isFile(url) && (url.charAt(0) == '/' || url.charAt(0) == '#')) {
        url = `https://${config.domain}${url}`;
    }

    // URL: ABSOLUTE VS. RELATIVE?
    if (url && (url.charAt(0) == '/' || url.charAt(0) == '#') && path == undefined && !otherProps?.class?.toLowerCase().includes('mvk-absolute')) {
        path = url;
    }

    // IF URL CONTAINS CURRENT DOMAIN & NOT A FILE, CONVERT TO PATH.
    // Idiot proofing: Some people will enter the absolute url when it should be just the path.
    if (url && !isFile(url) && url?.includes('http') && url?.includes(config.domain) && !otherProps?.class?.toLowerCase()?.includes('mvk-absolute')) {
        // UNCOMMENT WHEN THE FLEET BRANCH IS MERGED TO RELEASE.
        try {
            var parsed = new URL(url)
            path = parsed.pathname;
            hash = parsed.hash || false;
            search = parsed.search || false;
        } catch (err) {
            console.error(err);
        }
    }
    // add class of mvk-absolute to force an absolute path, originally used for adeptmind /shop links
    if (otherProps?.class?.toLowerCase().includes('mvk-absolute')) {
        url = (url.includes('http')) ? url : `https://${config.domain}${url}`
    }
    // allow internal links to open in new tab if set in admin
    if (target === '_blank') {
        isExternal = 'true';
    }

    // if link is external it should automatically open in new tab and include aria label
    // also if link is a nav item containing only a relative url, don't open in new tab
    if (url && !url?.includes('mypylot') && url?.includes('http') && !url?.includes(config.domain.replace('www.', ''))) {
        isExternal = 'true';
        let label = Array.isArray(children) ? typeof children[0] == 'string' ? children[0] : children[0]?.props?.children : '';
        otherProps['aria-label'] = `${label ? label : ''} opens in a new tab`;
    }
    if (ariaLabel) {
        otherProps['aria-label'] = ariaLabel;
    }

    // JUST IN CASE THIS IS PROVIDED & TARGET IS NOT.
    if (isExternal) {
        target = (isExternal || isExternal == 'true') ? '_blank' : '_self';
    }

    const Processing = (e) => {
        // console.log(url, path);
        e.target.focus();
        e.stopPropagation();
        e.preventDefault();
        // RUN OTHER FUNCTION IF INCLUDED.
        if (process) process.call(e);

        if (path && isExternal) {
            var webaddress = `https://${config.domain}${path}`.replace(/\/?$/, '/');
            window.open(webaddress, target);
        } else if (path && path.charAt(0) == '#') {
            scrollIntoViewWithOffset(path, 70, 'smooth');
        } else if (path) {
            navigate(`${path.replace(/\/?$/, '/')}${hash ? hash : ''}${search ? search : ''}`);
            document.body.scrollTop = 0;
            document.documentElement.scrollTop = 0;
        } else if (url && target) {
            var webaddress = (url?.includes('http')) ? decode(url) : `http://${decode(url)}`;
            window.open(decode(webaddress), target).focus();
        } else if (url) {
            var webaddress = (url?.includes('http') || url?.includes('#')) ? url : `http://${url}`;
            window.location.href = decode(url);
        } else {
            return;
        }
        // close modal
        if (otherProps.closeModal) {
            otherProps.closeModal();
        }
        if (otherProps.closeBanner) {
            otherProps.closeBanner();
        }
    };

    if (type == 'button' || type == 'submit' || type == 'reset') {
        return (
            <button type={type} onClick={Processing} {...otherProps}>
                {children}
            </button>
        );
    } else if (url && url.substr(0, 7) == 'mailto:') {
        return (
            <a href={url} target={target} {...otherProps}>
                {children}
            </a>
        );
    } else if (type == 'anchor') {
        return (
            <a href={decode(url)} target={target} onClick={Processing} {...otherProps}>
                {children}
            </a>
        );
    } else {
        return (
            <div onClick={Processing} {...otherProps}>
                {children}
            </div>
        );
    }
}

export default Start;

function isFile(pathname) {
    return (pathname) ? pathname?.split('/')?.pop()?.indexOf('.') > -1 : false;
};
