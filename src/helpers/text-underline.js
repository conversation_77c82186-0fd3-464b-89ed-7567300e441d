import styled, { css } from 'styled-components';
import { TranslationDropdownItem } from '../partials/header/translations/styles';

export const TextUnderline = styled.span`
    display: inline-block;
    position: relative;
    overflow: hidden;

    &::before {
        content: '';
        display: block;
        position: absolute;
        bottom: 0px;
        left: 0;
        border-bottom: 1px solid;
        width: 100%;
        transform: translateX(-105%);
        transition: transform .2s cubic-bezier(.645,.045,.355,1);
    }

    ${TranslationDropdownItem}:hover &::before,
    &:hover::before {
        transform: translateX(0);
    }

    ${props => props.isActive && css`
        &::before {
            transform: translateX(0);
        }
    `}
`