import React, { useContext } from 'react';
import styled from 'styled-components';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faChevronLeft, faChevronRight, faCircle } from '@fortawesome/free-solid-svg-icons';
import { SettingsContext } from 'src/context';
// HELPERS.
import Imaging from 'src/helpers/imaging';
import Clicker from 'src/helpers/clicker';

export const PrevArrow = (props) => {
    const [settings, setSettings] = useContext(SettingsContext);
    const { className, style, onClick, ariaLabel } = props;
    const custom = (settings.mvk_theme_config?.other?.enable_custom_slider_arrows && settings.mvk_theme_config?.other?.custom_slider_arrow);

    return (
        <Arrow className={`${className} site-arrow-prev${custom ? ' custom' : ''}`} process={onClick} style={{ ...style }} type='button' role='button' tab-index="0" ariaLabel={ariaLabel ? ariaLabel : "previous slide"}>
            {custom ?
                <Imaging data={settings.mvk_theme_config?.other?.custom_slider_arrow} />
                :
                <FontAwesomeIcon icon={faChevronLeft} aria-label="chevron left" />
            }
        </Arrow>
    );
};

export const NextArrow = (props) => {
    const [settings, setSettings] = useContext(SettingsContext);
    const { className, style, onClick, ariaLabel } = props;
    const custom = (settings.mvk_theme_config?.other?.enable_custom_slider_arrows && settings.mvk_theme_config?.other?.custom_slider_arrow);

    return (
        <Arrow className={`${className} site-arrow-next${custom ? ' custom' : ''}`} process={onClick} style={{ ...style }} type='button' role='button' tab-index="0" ariaLabel={ariaLabel ? ariaLabel : "next slide"}>
            {custom ?
                <Imaging data={settings.mvk_theme_config?.other?.custom_slider_arrow} />
                :
                <FontAwesomeIcon icon={faChevronRight} aria-label="chevron right" />
            }
        </Arrow>
    );
};

const Arrow = styled(Clicker)`
    &.site-arrow-prev, &.site-arrow-next {
        color: #fff;
        filter: drop-shadow(1px 1px 2px black);
        font-size: 2rem !important;
        background: none;
        border: none;
        padding: .5rem;
        cursor: pointer;
        width: auto;
        max-width: 60px;
        height: auto;
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        z-index: 10;
        &::before {
            display: none;
        }
        &:hover, &:focus {
            color: #fff;
        }
    }
    &.site-arrow-prev {
        left: 0;
    }
    &.site-arrow-next {
        right: 0;
        &.custom img {
            transform: scaleX(-1);
        }
    }
`

export function Dots(props) {
    const { className, dots } = props;

    const styles = {
        color: props?.data?.background_value === 'light' ? props.settings.design.colors.primary_color : '#fff'
    };

    return (
        <StyledDots className="slick-dots">
            <ul class="dots-list" style={styles} children={dots} />
        </StyledDots>
    );
};

export function Dot(props) {
    const { dot, onClick } = props;
    return (
        <div onClick={onClick}>
            <FontAwesomeIcon icon={faCircle} className="icon" />
        </div>
    );
};

const StyledDots = styled.div`
    position: absolute;
    z-index: +1;
    bottom: 20px;
    margin: 0px auto;

    left: 50%;
    -webkit-transform: translate(-50%, 0);
    -ms-transform: translate(-50%, 0);
    transform: translate(-50%, 0);

    ul.dots-list {
        margin: 0px auto;
        padding: 7px 0px 7px;
        text-align: center;
        height: 15px;

        // display: block;
        flex: 1 0 100%;
        list-style-type: disc;
        margin-block-start: 0px;
        margin-block-end: 0px;
        margin-inline-start: 0px;
        margin-inline-end: 0px;
        padding-inline-start: 0px;

        li {
            position: relative;
            display: inline-block;
            width: 15px;
            height: 15px;
            margin: 0px 10px;
            padding: 0px;
            cursor: pointer;
            opacity: 0.5;
            font-family: unset;
            filter: drop-shadow(1px 1px 2px rgba(0, 0, 0, 0.4));
            border-radius: 50%;
            &::before {
                content: none;
            }

            &.slick-active {
                opacity: 1;
            }

            svg {
                position: relative;
                height: 15px;
                width: 15px;
                vertical-align: top;
                font-family: unset;
                box-sizing: border-box;
            }
        }
    }
`