import React, { useState, useEffect } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faTimes } from '@fortawesome/free-solid-svg-icons';

// CONFIG.
import config from 'src/config';

// SCSS.
import 'src/helpers/modal.scss';

// REQUIRED PROPS: close, children.
const Start = (props) => {
    const [active, setActive] = useState(false);

    useEffect(() => {
        setActive(props.active);
    }, [props.active]);

    function clicked(e) {
        e.stopPropagation();
        e.preventDefault();
        if (props.close) {
            props.close.call(e);
        }
    };

    if (active) {
        return (
            <div id="modal" class={props.class} onClick={(!props.closeIcon || props.clickDarkness) ? clicked : null} aria-modal='true'>
                {props.closeIcon && <FontAwesomeIcon role='button' aria-label='close modal' className={'close-modal'} icon={faTimes} onClick={clicked} />}
                {React.cloneElement(props.children, { close: props.close })}
            </div>
        );
    } else {
        return null;
    }
};

export default Start;