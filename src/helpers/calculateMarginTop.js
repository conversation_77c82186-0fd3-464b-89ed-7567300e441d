import { useEffect, useState } from "react";

export const handleContentLoaded = (settings, type, data = false) => {
    const [bottomMargin, setBottomMargin] = useState(null);
    const detectHero = settings.mvk_theme_config.header?.header_type === 'semi-absolute' || settings.mvk_theme_config.header?.header_type === 'gradient';
    const appEl = document.getElementById('app');
    const headerEl = document.getElementById('header');
    const layout = data?.mvk_item_content?.custom_fields?.layout ? data?.mvk_item_content?.custom_fields?.layout[0] : null;
    let withHero = false;

    useEffect(() => {
        switch (type) {
            case 'event':
                withHero = Boolean(settings?.event && settings?.event.hero);
                break;
            case 'job':
                withHero = Boolean(settings?.job && settings.job.hero);
                break;
            case 'sale':
                withHero = Boolean(settings?.sale && settings.sale.hero);
                break;
            case 'property':
                withHero = Boolean(settings?.property && settings.property.hero);
                break;
            case 'store':
                withHero = Boolean(settings?.store && settings.store?.store_hero);
                break;
            case 'space':
                withHero = Boolean(settings?.space && settings.space?.hero)
                break;
            case 'floorplan':
            case 'testimonial':
            case 'team':
            case 'aircraft':
                withHero = false;
                break;
            case 'page':
            default:
                withHero = layout ? (layout.mvk_mod_layout === 'hero' && layout.type === 'image-carousel' && layout.module_toggle) || (layout.mvk_mod_layout === 'hero' && layout.type === 'video' && layout.module_toggle) || (layout.mvk_mod_layout === 'image_carousel_hero' && layout.module_toggle) || (layout.mvk_mod_layout === 'hero_animated_layers' && layout.module_toggle) : false;
                break
        }
        const marginTop = settings.mvk_theme_config.header?.header_type == 'fixed' || settings.mvk_theme_config.header?.header_type == 'semi-absolute' || (settings.mvk_theme_config.header?.header_type == 'gradient' && !withHero);

        if (headerEl && detectHero) {
            if (withHero) {
                headerEl?.classList.add('has-hero')
                headerEl?.classList.remove('no-hero')
            } else {
                headerEl?.classList.remove('has-hero')
                headerEl?.classList.add('no-hero')
            }
            if (marginTop) {
                appEl.classList.add('margin-mobile');
                appEl.classList.remove('no-margin');
            } else {
                appEl.classList.add('no-margin');
                appEl.classList.remove('margin-mobile');
            }
        }
        if (appEl && marginTop) {
            appEl.style.marginTop = `${settings.header_height}px`;
        } else if (appEl) {
            appEl.style.marginTop = '0';
        }
    
        if (!bottomMargin && window.innerWidth < 1200) {
            const bottomMobileNav = settings?.mvk_theme_config?.nav?.enable_bottom_mobile_navigation ? 66 : 0;
            const locationSelector = settings.locations?.enable_location_selector ? 56 : 0;
            appEl.style.marginBottom = `${bottomMobileNav + locationSelector}px`
            setBottomMargin(true);
        }

    }, [headerEl, type, layout])

}