import { GetYYMMDD, FormattedDate } from "./date";

// If current date is equal or past given date return given string, else return date in requested format
export const checkAvailability = (date, activeString, format) => {
    const currentDate = new Date();
    const givenDate = date.replace(/(\d{4})(\d{2})(\d{2})/g, '$1-$2-$3');

    return GetYYMMDD(currentDate) >= GetYYMMDD(givenDate) ? activeString : `${FormattedDate(givenDate, format)}`;
}