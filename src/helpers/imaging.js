import React, { useState, useEffect } from 'react'
// Hooks
import { useInView } from 'react-intersection-observer';

// CONFIG.
import config from 'src/config';

const Start = ({ data, imageWidth, imageHeight, forceLoad, ...otherProps }) => {
    if (!data) return null;

    const { ref, inView } = useInView({
        triggerOnce: true,
        fallbackInView: true
    });

    if (typeof data === 'number' || Array.isArray(data) || typeof data === 'string') {
        // This can happen if a store is imported from centers
        // => Saving each store should fix this
        console.log('image data is in id, or array format', data)
        return null;
    }

    // ADDS DOMAIN TO MISSING URL VALUE INCASE IT'S ONLY THE PATH.
    if (!data.url?.includes('http')) {
        data.url = `https://${config.domain}${data.url}`;
    }

    if (config.preview.enabled) {
        data.url = data.url.replace(`/${config.preview.decodeContent}/uploads/sites/`, '/media/v1/');
    }

    // Use passed in width / height if none in data and at very least set to something to ensure attribute is there
    let width = data.width ? data.width : imageWidth ? imageWidth : 'auto';
    let height = data.height ? data.height : imageHeight ? imageHeight : 100;

    return (<img ref={ref} loading={!inView ? "lazy" : null} src={inView || forceLoad ? data.url : null} alt={data.alt ? data.alt : ""} role={!data.alt ? 'presentation' : null} title={!data.alt ? null : data.title} srcset={(data.subtype !== 'svg+xml' && inView) || forceLoad ? createSrcSet(data.sizes) : ''} width={width} height={height} {...otherProps} />);
};

const createSrcSet = (sizes) => {
    var string = null;
    var log = [];
    if (typeof sizes === 'object' && sizes !== null) {
        for (let key in sizes) {
            if (!key.includes('width') && !key.includes('height')) {
                var width = sizes[`${key}-width`];
                var height = sizes[`${key}-height`];

                // PREVENTS ANY DUPLICATES FROM SHOWING TWICE.
                if (log.includes(`${width}x${height}`)) {
                    continue;
                } else {
                    log.push(`${width}x${height}`);
                }

                // ADDS DOMAIN TO MISSING URL VALUE INCASE IT'S ONLY THE PATH.
                if (!sizes[key].includes('http')) {
                    sizes[key] = `https://${config.domain}${sizes[key]}`;
                }

                if (config.preview.enabled) {
                    sizes[key] = sizes[key].replace(`/${config.preview.decodeContent}/uploads/sites/`, '/media/v1/');
                }

                // APPEND TO OR CREATE SRCSET STRING TO BE RETURNED.
                var portion = `${sizes[key]} ${width}w`
                string = (string) ? string.concat(', ', portion) : portion;
            }
        }
    }
    return string;
}

export default Start;