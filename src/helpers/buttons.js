/*******************************************************************************************************
   Copyright 2020-21 - Calise Partners LLC d/b/a Imaginuity. All rights are reserved.
   Reproduction or transmission in whole or in part, in any form or by any means, electronic, mechanical
or otherwise, is prohibited without the prior written consent of Calise Partners LLC d/b/a Imaginuity.
********************************************************************************************************
   Project : 10618 Maverick Platform Development
   Author : Imaginuity Developers (<PERSON>, <PERSON>)
   Description : Helper for managing multiple clickable elements.
   Creation Date : Thu Nov 05 2020
   Update Date : Jun 11, 2021
 ********************************************************************************************************/

import React, { useContext } from 'react'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faAngleRight } from '@fortawesome/free-solid-svg-icons';

import { decode } from 'html-entities';


const Clicker = React.lazy(() => import('src/helpers/clicker'));

// CONTEXT.
import { SettingsContext } from "src/context";
import { Background, Border, Color } from './theme';

const Start = ({ items, tone, ...otherProps }) => {
    return (
        <div class="buttons" {...otherProps}>
            {items && items.map((button, index) => {

                if (button.button_icon) {
                    var buttonIcon = ''; //(button.button_icon === 'icon-one') ? <FontAwesomeIcon icon={faAngleRight} size='lg' /> : null;
                }

                const style = Style(tone, button);

                return (
                    <div class="button" key={index}>
                        <Clicker type='anchor' role="button" url={button.button.url} target={button.button.target} style={style} icon={button.button_icon}>
                            {decode(button.button.title)} {buttonIcon}
                        </Clicker>
                    </div>
                );
            })}
        </div>
    );
}

const Style = (tone, button) => {
    const [settings, setSettings] = useContext(SettingsContext);
    const buttonStyle = (button.button_style === 'secondary')
        ? settings.mvk_theme_config.other?.secondary_button_style
        : settings.mvk_theme_config.other?.primary_button_style;

    // Override styles for gradient options
    // Outline Gradient
    if (buttonStyle === 'outline-gradient' || buttonStyle === 'outline-gradient-persistent') {
        return {
            fontFamily: cache.fonts.body,
            backgroundColor: 'transparent',
            color: tone === 'dark' ? '#fff' : settings.design?.colors?.outline_gradient_text_color,
            border: tone === 'dark' && buttonStyle === 'outline-gradient' ? '3px solid #fff': '3px solid transparent',
            borderRadius: (settings.mvk_theme_config.other?.enable_border_radius) ? `${settings.mvk_theme_config.other?.border_radius_size}px` : "0px",
            borderImageSource: Border(tone, buttonStyle),
            borderImageSlice: 4
        }
    } else if ((buttonStyle === 'solid-fill-gradient' && tone !== 'dark') || buttonStyle === 'solid-fill-gradient-persistent') {
        return {
            backgroundImage: Background(tone, buttonStyle),
            color: tone === 'dark' ? '#fff' : settings.design?.colors?.solid_fill_gradient_text_color
        }
    } else {
        return {
            fontFamily: cache.fonts.body,
            backgroundColor: Background(tone, buttonStyle),
            color: Color(tone, buttonStyle),
            borderRadius: (settings.mvk_theme_config.other?.enable_border_radius) ? `${settings.mvk_theme_config.other?.border_radius_size}px` : "0px",
            border: Border(tone, buttonStyle)
        };
    }
};

export default Start;