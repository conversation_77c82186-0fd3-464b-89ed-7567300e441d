export function set (name, value, days) {
    var d = new Date();
    d.setTime(d.getTime() + (days * 86400000))
    var expires = d.toUTCString();
    document.cookie = `${name}=${value}; Path=/; Expires=${expires}; Domain=${window.location.hostname.toLowerCase()}`;
};

export function get (name) {
    var decoded = decodeURIComponent(document.cookie);
    var array   = decoded.split(';');
    for (var i = 0; i < array.length; i++) {  
        var [ key, value ] = array[i].split('=');
        if (name == key.trim()) return value;
    }
    return false;
};

export function getall () {
    var cookies = {};
    var decoded = decodeURIComponent(document.cookie);
    var array   = decoded.split(';');
    for (var i = 0; i < array.length; i++) {  
        var [ key, value ] = array[i].split('=');
        cookies[key.trim()] = value;
    }
    return cookies;
};