import React, { useContext } from "react";
import { SettingsContext, PrincipalContext } from "src/context";
import { convertToFrench } from 'src/helpers/hours';

// return date as yy-mm-dd
export const GetYYMMDD = (date) => {
    date = new Date(date);
    let year = date.getFullYear(),
        month = date.getMonth() + 1,
        day = date.getDate()
    if (day < 10) {
        day = '0' + day;
    }
    if (month < 10) {
        month = '0' + month;
    }
    return [year, month, day].join('-');
}

// return long form date
export const FormattedDate = (datePassed, type) => {
    const [principal, setPrincipal] = useContext(PrincipalContext);

    const date = new Date(`${datePassed}T00:00:00`);
    switch (type) {
        case 'date':
            return (`${date.getDate()}${nth(date.getDate())}`);
        case 'month-date':
            if (principal.activeTranslation === 'fr') {
                return (`${date.getDate()}${nth(date.getDate())} ${getMonthName(date.getMonth())}`);
            } else {
                return (`${getMonthName(date.getMonth())} ${date.getDate()}${nth(date.getDate())}`);
            }
        case 'day-month-date':
            if (principal.activeTranslation === 'fr') {
                return (`${getDayName(date.getDay())} le ${date.getDate()}${nth(date.getDate())} ${getMonthName(date.getMonth())}`);
            } else {
                return (`${getDayName(date.getDay())}, ${getMonthName(date.getMonth())} ${date.getDate()}${nth(date.getDate())}`);
            }
        case 'month-date-year':
            if (principal.activeTranslation === 'fr') {
                return (`${date.getDate()}${nth(date.getDate())} ${getMonthName(date.getMonth())} ${date.getFullYear()}`);
            } else {
                return (`${getMonthName(date.getMonth())} ${date.getDate()}${nth(date.getDate())}, ${date.getFullYear()}`);
            }
        case 'block':
            return (`${date.getMonth() + 1}.${date.getDate()}.${date.getFullYear().toString().slice(-2)}`)
        default:
            return '';
    }
};

// return multiple dates, formatted by function above
export const DateRange = (firstDate, secondDate) => {
    const [principal, setPrincipal] = useContext(PrincipalContext);

    const date1 = new Date(`${firstDate}T00:00:00`);
    const date2 = new Date(`${secondDate}T00:00:00`);
    if (principal.activeTranslation === 'fr') {
        if (date1.getMonth() !== date2.getMonth()) {
            return (`${FormattedDate(firstDate, 'month-date')} - ${FormattedDate(secondDate, 'month-date')}`);
        } else {
            return (`${FormattedDate(firstDate, 'date')} - ${FormattedDate(secondDate, 'month-date')}`);
        }
    } else {
        return (`${FormattedDate(firstDate, 'month-date')} - ${FormattedDate(secondDate, (date1.getMonth() == date2.getMonth() ? 'date' : 'month-date'))}`);
    }
};

// get day of week name
export const getDayName = (day, style) => {
    const [settings, setSettings] = useContext(SettingsContext);
    let days = [];

    if (style === 'short') {
        days = [
            settings.mvk_theme_config?.labels?.day_labels?.sunday ? settings.mvk_theme_config?.labels?.day_labels?.sunday : 'sun',
            settings.mvk_theme_config?.labels?.day_labels?.monday ? settings.mvk_theme_config?.labels?.day_labels?.monday : 'mon',
            settings.mvk_theme_config?.labels?.day_labels?.tuesday ? settings.mvk_theme_config?.labels?.day_labels?.tuesday : 'tue',
            settings.mvk_theme_config?.labels?.day_labels?.wednesday ? settings.mvk_theme_config?.labels?.day_labels?.wednesday : 'wed',
            settings.mvk_theme_config?.labels?.day_labels?.thursday ? settings.mvk_theme_config?.labels?.day_labels?.thursday : 'thur',
            settings.mvk_theme_config?.labels?.day_labels?.friday ? settings.mvk_theme_config?.labels?.day_labels?.friday : 'fri',
            settings.mvk_theme_config?.labels?.day_labels?.saturday ? settings.mvk_theme_config?.labels?.day_labels?.saturday : 'sat'
        ];
    } else {
        days = [
            settings.mvk_theme_config?.labels?.day_labels?.sunday ? settings.mvk_theme_config?.labels?.day_labels?.sunday : 'Sunday',
            settings.mvk_theme_config?.labels?.day_labels?.monday ? settings.mvk_theme_config?.labels?.day_labels?.monday : 'Monday',
            settings.mvk_theme_config?.labels?.day_labels?.tuesday ? settings.mvk_theme_config?.labels?.day_labels?.tuesday : 'Tuesday',
            settings.mvk_theme_config?.labels?.day_labels?.wednesday ? settings.mvk_theme_config?.labels?.day_labels?.wednesday : 'Wednesday',
            settings.mvk_theme_config?.labels?.day_labels?.thursday ? settings.mvk_theme_config?.labels?.day_labels?.thursday : 'Thursday',
            settings.mvk_theme_config?.labels?.day_labels?.friday ? settings.mvk_theme_config?.labels?.day_labels?.friday : 'Friday',
            settings.mvk_theme_config?.labels?.day_labels?.saturday ? settings.mvk_theme_config?.labels?.day_labels?.saturday : 'Saturday'
        ];
    }
    return days[day];
}

// get month name
const getMonthName = (month) => {
    const [settings, setSettings] = useContext(SettingsContext);

    const months = [
        settings?.mvk_theme_config?.labels?.month_labels?.month_1 ? settings?.mvk_theme_config?.labels?.month_labels?.month_1 : "January",
        settings?.mvk_theme_config?.labels?.month_labels?.month_2 ? settings?.mvk_theme_config?.labels?.month_labels?.month_2 : "February",
        settings?.mvk_theme_config?.labels?.month_labels?.month_3 ? settings?.mvk_theme_config?.labels?.month_labels?.month_3 : "March",
        settings?.mvk_theme_config?.labels?.month_labels?.month_4 ? settings?.mvk_theme_config?.labels?.month_labels?.month_4 : "April",
        settings?.mvk_theme_config?.labels?.month_labels?.month_5 ? settings?.mvk_theme_config?.labels?.month_labels?.month_5 : "May",
        settings?.mvk_theme_config?.labels?.month_labels?.month_6 ? settings?.mvk_theme_config?.labels?.month_labels?.month_6 : "June",
        settings?.mvk_theme_config?.labels?.month_labels?.month_7 ? settings?.mvk_theme_config?.labels?.month_labels?.month_7 : "July",
        settings?.mvk_theme_config?.labels?.month_labels?.month_8 ? settings?.mvk_theme_config?.labels?.month_labels?.month_8 : "August",
        settings?.mvk_theme_config?.labels?.month_labels?.month_9 ? settings?.mvk_theme_config?.labels?.month_labels?.month_9 : "September",
        settings?.mvk_theme_config?.labels?.month_labels?.month_10 ? settings?.mvk_theme_config?.labels?.month_labels?.month_10 : "October",
        settings?.mvk_theme_config?.labels?.month_labels?.month_11 ? settings?.mvk_theme_config?.labels?.month_labels?.month_11 : "November",
        settings?.mvk_theme_config?.labels?.month_labels?.month_12 ? settings?.mvk_theme_config?.labels?.month_labels?.month_12 : "December"
    ];
    return months[month];
}

// ordinal indicator
const nth = function (day) {
    const [principal, setPrincipal] = useContext(PrincipalContext);

    if (principal.activeTranslation === 'fr') {
        if (day == 1) {
            return 'er'
        } else {
            return '';
        }
    } else {
        if (day > 3 && day < 21) return 'th';
        switch (day % 10) {
            case 1: return "st";
            case 2: return "nd";
            case 3: return "rd";
            default: return "th";
        };
    }
}

/**
 * Check is current date is within range of holiday dates or passed start and end dates.
 * @param {string} startDate 
 * @param {string} endDate 
 * @param {boolean} useDateFromSettings 
 * @returns boolean
 */
export const withinDateRange = (startDate, endDate, useDateFromSettings, moduleShowHide = false, simulateDate) => {
    const [settings, setSettings] = useContext(SettingsContext);
    let currentDate = simulateDate ? new Date(simulateDate) : new Date();
    let isActive;
    // override dates with holiday dates if holiday scheduled and either enabled for module or useDateFromSettings passed as true
    if (settings.holiday?.enable_holiday && useDateFromSettings && !moduleShowHide) {
        currentDate = currentDate.setHours(0, 0, 0, 0);
        startDate = settings.holiday?.start ? new Date(settings.holiday?.start).setHours(0, 0, 0, 0) : false;
        endDate = settings.holiday?.end ? new Date(settings.holiday?.end).setHours(0, 0, 0, 0) : false;
    }

    if (startDate && endDate) {
        isActive = (currentDate >= startDate && currentDate < endDate);
    } else if (startDate && !endDate) {
        isActive = (currentDate >= startDate);
    } else if (!startDate && endDate) {
        isActive = (currentDate < endDate);
    }

    return isActive;
}

export const DateTime = ({ data, post, type }) => {
    const [principal, setPrincipal] = useContext(PrincipalContext);

    switch (data.post_type) {
        case 'post':
            return (
                <p>{post.post_date ? post.post_date : null}</p>
            )
        case 'event':
            const startDate = post.next_occurrence ? post.next_occurrence?.start?.event_date : post.start?.event_date;
            const endDate = post.next_occurrence ? post.next_occurrence?.end?.event_date : post.end?.event_date;
            if (type === 'full') {

                let dateString;
                if (startDate !== endDate) {
                    dateString = DateRange(startDate, endDate)
                } else {
                    dateString = FormattedDate(startDate, 'day-month-date');
                }

                if (post.start.minutes == "0") { post.start.minutes = "00"; }
                if (post.end.minutes == "0") { post.end.minutes = "00"; }

                // TIMES
                let startTime = `${post.start?.hour}:${post.start?.minutes} ${post.start?.ampm}`;
                let endTime = `${post.end?.hour}:${post.end?.minutes} ${post.end?.ampm}`;
                if (principal.activeTranslation === 'fr') {
                    startTime = convertToFrench(startTime);
                    endTime = convertToFrench(endTime);
                }
                let times = '';
                if ((data.source === 'api' && !post.all_day) || (data.source === 'pylot' && post.meta.mec_hide_time != "1" && post.meta.mec_allday != "1")) {
                    times = ` ${startTime}`;
                    if (data.source === 'api' || post.meta.mec_hide_end_time != "1") {
                        times += ` - ${endTime}`
                    }
                }
                return (
                    <p>{dateString} {times}</p>
                )
            } else if (type === 'block') {
                return (
                    <div className='date-block primary-bg white-txt'>{FormattedDate(startDate, 'block')}</div>
                )
            }
    }
}

// Robot provided this function
export const addOneDay = (dateString) => {
    // Step 1: Split the input string into components
    const [year, month, day] = dateString.split('-').map(Number);

    // Step 2: Create a Date object with the components
    let date = new Date(year, month - 1, day); // Month is zero-based, so subtract 1

    // Step 3: Add one day to the Date object
    date.setDate(date.getDate() + 1);

    // Step 4: Format the date back to yyyy-mm-dd
    const newYear = date.getFullYear();
    const newMonth = String(date.getMonth() + 1).padStart(2, '0'); // Add 1 to the zero-based month
    const newDay = String(date.getDate()).padStart(2, '0');

    return `${newYear}-${newMonth}-${newDay}`;
}