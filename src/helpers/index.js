import { split } from 'postcss/lib/list';
import { useParams } from 'react-router-dom';
import config from 'src/config';
import Base64 from 'base-64';

// HELPERS.
import * as Cookies from 'src/helpers/cookies';

export function Linking(url) {
    const params = useParams();
    return (params.subsite) ? `/${params.subsite}${url}` : url;
}

export function Coloring(option, settings) {
    switch (option) {
        case 'background_color':
            return settings?.design.colors.background_color;
        case 'primary_color':
        case 'light':
            return settings?.design.colors.primary_color;
        case 'secondary_color':
            return settings?.design.colors.secondary_color;
        case 'tertiary_color':
            return settings?.design.colors.tertiary_color;
        case 'body_copy_color':
            return settings?.design.colors.body_copy_color;
        case 'gradient':
            return `linear-gradient(to right, ${settings.design?.colors?.gradient_color_1}, ${settings.design?.colors?.gradient_color_2})`;
        case 'none':
            return 'transparent';
        case 'dark':
        default:
            return 'white';
    }
}

export function AddressString(address, city, state, zip, country) {
    return [address, city, state, zip, country].filter(x => typeof x === 'string' && x.length > 0).join(", ");
}

export function ScrollToHash(hash) {
    if (hash.length) {
        document.querySelector(hash).scrollIntoView();
        ConsoleLog('Scroll To', hash);
    }
}

export function getUniqueArray(arr, keyProps) {
    return Object.values(
        arr.reduce((uniqueMap, entry) => {
            const key = keyProps.map((k) => entry[k]).join('|')
            if (!(key in uniqueMap)) uniqueMap[key] = entry
            return uniqueMap
        }, {}),
    )
};

export function pxToRem(value) {
    let rem = (value / 16);
    return rem;
}

export function fontPercentage(fontSize, percentage) {
    let pecentSize = (parseInt(fontSize) * parseInt(percentage)) / 100;
    return pxToRem(pecentSize); 
}