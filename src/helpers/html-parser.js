import React, { useContext, useState } from 'react';
import styled from 'styled-components';
import parse from 'html-react-parser';
import { decode } from 'html-entities';
import { useParams } from "react-router-dom";
// Context
import { PrincipalContext, SettingsContext, PostsContext, ServicesContext, LocationsContext } from "src/context";
// Helpers
import { Coloring } from 'src/helpers';
import Imaging from 'src/helpers/imaging';
// Components
import { Form } from 'src/modules/gravity-forms';
import HolidayHours from 'src/partials/hours/holiday-hours';
import Hours from 'src/partials/hours';
const Button = React.lazy(() => import('src/partials/button'));
import Bullet from 'src/partials/bullet';
import Bullets from 'src/partials/bullets';
import CountUpAnimation from 'src/partials/count-up-animation';
import { HtmlCode } from 'src/modules/raw-html';
const EventBrite = React.lazy(() => import('src/partials/eventbrite'));
const LocationHours = React.lazy(() => import('src/partials/location-shortcodes/location-hours'));
const LocationAddress = React.lazy(() => import('src/partials/location-shortcodes/location-address'));
const LocationEmail = React.lazy(() => import('src/partials/location-shortcodes/location-email'));
const LocationPhone = React.lazy(() => import('src/partials/location-shortcodes/location-phone'));
const LocationSelectorShortcode = React.lazy(() => import('src/partials/location-shortcodes/location-selector'));
const ServiceTitanWidget = React.lazy(() => import('src/partials/service-titan-widget'));
const StaticDirectory = React.lazy(() => import('src/partials/static-directory'));

const HtmlParser = ({ html, data, placeholders }) => {
    const [settings, setSettings] = useContext(SettingsContext);
    const [principal, setPrincipal] = useContext(PrincipalContext);
    const [locations] = settings.current_location ? useContext(LocationsContext) : [];
    const params = useParams();
    var urlData = {
        slug: params.slug || false,
        first: params.first || false,
        second: params.second || false,
        third: params.third || false,
        fourth: params.fourth || false,
        fifth: params.fifth || false
    };
    const [postOptions] = useState(settings?.post?.enable_custom_subdirectory ? (Array.isArray(settings?.post?.custom_subdirectory) ? settings?.post?.custom_subdirectory : Object.values(settings?.post?.custom_subdirectory)) : ['blog']);
    const [posts, setPosts] = (Object.keys(urlData).some((key) => postOptions.includes(urlData[key]) && !postOptions.includes(params.slug))) ? useContext(PostsContext) : [];
    const [serviceOptions] = useState(['services']);
    const [servicesList] = useState(settings?.services?.no_subdirectory ? settings?.services?.list : []);
    const servicesNoSubdirectory = settings?.services?.no_subdirectory;
    const [services, setServices] = settings.services && (!servicesNoSubdirectory && Object.keys(urlData).some((key) => serviceOptions.includes(urlData[key]) && !serviceOptions.includes(params.slug))) || (servicesNoSubdirectory && Object.keys(urlData).some((key) => servicesList.includes(urlData[key]))) ? useContext(ServicesContext) : [];

    // Replace shortcodes with their react component counterpart
    const replace = (domNode) => {
        // make sure image src has domain
        if (domNode && domNode.name === 'img') {
            if (!domNode.attribs.src?.includes('http')) {
                domNode.attribs.src = `https://${config.domain}${domNode.attribs.src}`;
            }
        }
        // If we find the '<sc>' tag then we need to replace it.
        if (domNode && domNode.name === `sc` && domNode.attribs) {
            if (domNode.attribs.comp === `gravity-form`) {
                const formData = JSON.parse(domNode.attribs.data);
                const bgType = domNode.attribs.background_type ? domNode.attribs.background_type : '';
                const bgColor = domNode.attribs.background_color ? Coloring(domNode.attribs.background_color, settings) : '';
                const bgImage = domNode.attribs.background_image ? domNode.attribs.background_image : '';
                const bgValue = domNode.attribs.background_value ? domNode.attribs.background_value : data?.background_value ? data?.background_value : 'light';
                const borderRadius = domNode.attribs.border_radius ? domNode.attribs.border_radius : '0';
                const buttonStyle = domNode.attribs.button_style ? domNode.attribs.button_style : '';
                return (
                    <GravityForm
                        className={`form-module ${bgType}`}
                        bgColor={bgColor}
                        bgImage={bgImage}
                        borderRadius={borderRadius}
                    >
                        <Form data={formData} settings={settings} bgValue={bgValue} styleOverride={domNode.attribs.style ? domNode.attribs.style : ''} buttonStyleOverride={buttonStyle} />
                    </GravityForm>
                )
            } else if (domNode.attribs.comp === `holiday-hours`) {
                return (
                    <HolidayHours settings={settings} />
                )
            } else if (domNode.attribs.comp === `location-hours`) {
                return (
                    <LocationHours />
                )
            } else if (domNode.attribs.comp === `location-address`) {
                return (
                    <LocationAddress />
                )
            } else if (domNode.attribs.comp === `location-phone`) {
                return (
                    <LocationPhone />
                )
            } else if (domNode.attribs.comp === `location-email`) {
                return (
                    <LocationEmail />
                )
            } else if (domNode.attribs.comp === `hours`) {
                return (
                    <Hours data={{ 'display_options': domNode.attribs.hours_style ? domNode.attribs.hours_style : 'daily' }} />
                )
            } else if (domNode.attribs.comp === `button`) {
                return (
                    <Button className='shortcode-button' title={decode(domNode.attribs.title)} url={domNode.attribs.url} target={(domNode.attribs.external && domNode.attribs.external === 'true') ? '_blank' : null} tone={domNode.attribs.bg_value.toLowerCase()} type={domNode.attribs.button_style.toLowerCase()} noIcon={domNode.attribs.no_icon === 'true' ? true : false} />
                )
            } else if (domNode.attribs.comp === 'bullet') {
                return (
                    <Bullet bulletStyle={domNode.attribs.bullet_style.toLowerCase()} fillColor={domNode.attribs.fill_color} content={domNode.attribs.content} imgOverride={domNode.attribs.image_override} />
                )
            } else if (domNode.attribs.comp === 'bullets') {
                let bullets = [];
                let el = document.createElement('html');
                el.innerHTML = domNode.attribs.content;
                const listItems = el.getElementsByTagName('li');
                for (let bullet of listItems) {
                    const match = bullet.innerHTML.match(/\[placeholder key="([^"]+)" type="([^"]+)"\]/);
                    if (match && placeholders) {
                        const key = match[1];
                        const type = match[2];
                        const placeholderContent = placeholders ? (placeholders[type][key] ?? '') : '';
                        if (placeholderContent) bullets.push(placeholderContent);
                    } else {
                        bullets.push(bullet.innerHTML);
                    }
                }
                return (<Bullets bulletStyle={domNode.attribs.bullet_style.toLowerCase()} fillColor={domNode.attribs.fill_color} bullets={bullets} imgOverride={domNode.attribs.image_override} />);
            } else if (domNode.attribs.comp === 'count-up') {
                return (
                    <CountUpAnimation number={domNode.attribs.number} fontFamily={domNode.attribs.font_family} fontSizeMobile={domNode.attribs.font_size_mobile} fontSizeDesktop={domNode.attribs.font_size_desktop} color={domNode.attribs.color} />
                )
            } else if (domNode.attribs.comp === 'raw-html') {
                const rawHtml = settings?.raw_html?.html_blocks;
                const htmlCode = rawHtml?.find((block) => block.raw_id === domNode.attribs.id);

                if (htmlCode?.raw_html_code) {
                    return (
                        <HtmlCode data={htmlCode.raw_html_code} />
                    )
                }
                return null
            } else if (domNode.attribs.comp === 'eventbrite-button') {
                return (<EventBrite eventID={domNode.attribs.event_id} buttonStyle={domNode.attribs.button_style.toLowerCase()} buttonText={domNode.attribs.button_text ? domNode.attribs.button_text : 'Buy Tickets'} />)
            } else if (domNode.attribs.comp === 'service-titan-button') {
                const stSettings = {
                    link_text: domNode.attribs.title ? domNode.attribs.title : 'Schedule Service',
                    button_style: domNode.attribs.button_style ? domNode.attribs.button_style : 'primary',
                    background_value: domNode.attribs.bg_value ? domNode.attribs.bg_value : 'light',
                    autoopen: domNode.attribs.autoopen ? domNode.attribs.autoopen : 0
                }
                if (settings.service_titan?.api_key || (settings.current_location && locations[settings.current_location]?.mvk_item_content?.custom_fields?.location_st_api_key)) {
                    return (<ServiceTitanWidget apiKey={settings.current_location ? locations[settings.current_location]?.mvk_item_content?.custom_fields?.location_st_api_key : settings.service_titan?.api_key} settings={stSettings} type='main-navigation' />)
                }
                return null;
            } else if (domNode.attribs.comp === 'location-selector') {
                return (<LocationSelectorShortcode placeholder={domNode.attribs.placeholder} locations={JSON.parse(domNode.attribs.locations)} bgColor={domNode.attribs.bg_color} bgValue={domNode.attribs.bg_value} />)
            } else if (domNode.attribs.comp === 'store-directory') {
                const directory = JSON.parse(domNode.attribs.directory);
                const headingBg = domNode.attribs.heading_bg ? Coloring(domNode.attribs.heading_bg, settings) : Coloring('primary_color', settings);
                const headingTxt = domNode.attribs.heading_value === 'dark' ? '#fff' : Coloring('body_copy_color', settings);
                const listBg = domNode.attribs.list_bg ? Coloring(domNode.attribs.list_bg, settings) : 'white';
                const listTxt = domNode.attribs.list_value === 'dark' ? '#fff' : Coloring('body_copy_color', settings);
                const tabletColumns = domNode.attribs.tablet_columns ? domNode.attribs.tablet_columns : '2';
                const desktopColumns = domNode.attribs.desktop_columns ? domNode.attribs.desktop_columns : '2';
                return (
                    <StaticDirectory directory={decode(directory)} headingBg={headingBg} headingTxt={headingTxt} listBg={listBg} listTxt={listTxt} tabletColumns={tabletColumns} desktopColumns={desktopColumns} />
                )
            } else if (domNode.attribs.comp === 'placeholder') {
                let placeholderContent = placeholders ? (placeholders[domNode.attribs.type][domNode.attribs.key.toLowerCase()] ?? '') : '';
                if (!placeholderContent && posts) {
                    placeholderContent = posts[params.slug]?.mvk_placeholders ? posts[params.slug]?.mvk_placeholders[domNode.attribs.key.toLowerCase()] : '';
                }
                if (!placeholderContent && services) {
                    placeholderContent = services[params.slug]?.mvk_placeholders ? services[params.slug]?.mvk_placeholders[domNode.attribs.key.toLowerCase()] : '';
                }
                if (!placeholderContent && principal.pages[params.slug ? params.slug : settings?.front_page]) {
                    placeholderContent = principal.pages[params.slug ? params.slug : settings?.front_page]?.mvk_placeholders ? principal.pages[params.slug ? params.slug : settings?.front_page]?.mvk_placeholders[domNode.attribs.key.toLowerCase()] : '';
                }
                if (!placeholderContent && settings?.global?.mvk_placeholders) {
                    placeholderContent = settings?.global?.mvk_placeholders ? settings?.global?.mvk_placeholders[domNode.attribs.key.toLowerCase()] : '';
                }
                if (!placeholderContent && settings?.faq?.mvk_placeholders) {
                    placeholderContent = settings?.faq?.mvk_placeholders ? settings?.faq?.mvk_placeholders[domNode.attribs.key.toLowerCase()] : '';
                }
                if (placeholderContent) {
                    if (domNode.attribs.type === 'button_link') {
                        return <Button className='shortcode-button' title={decode(placeholderContent.title)} url={placeholderContent.url} target={(placeholderContent.external && placeholderContent.external === 'true') ? '_blank' : null} tone={domNode.attribs.bg_value ? domNode.attribs.bg_value.toLowerCase() : 'light'} type={domNode.attribs.button_style ? domNode.attribs.button_style.toLowerCase() : 'primary'} noIcon={domNode.attribs.no_icon === 'true' ? true : false} />
                    }
                    if (domNode.attribs.type === 'image') {
                        return <Imaging data={placeholderContent} />
                    }
                    if (typeof placeholderContent === 'string') {
                        return (<HtmlParser html={placeholderContent} placeholders={placeholders} />);
                    }
                }
                return ""
            }
        }

        return domNode;
    }

    return (
        parse(html, { replace })
    )
}

export default HtmlParser

const GravityForm = styled.div`
    border-radius: ${props => props.borderRadius}px;
    &.color {
        background-color: ${props => props.bgColor};
    }
    &.image {
        background-position: center center;
        background-size: cover;
        background-repeat: no-repeat;
        background-image: url(${props => props.bgImage});
    }
    &.color, &.image {
        padding: 1rem;
    }
`

export const replaceScTagsWithPlaceholders = (inputString, mvkPlaceholders) => {
     const regex = /<sc\s+comp='([^']+)'[^>]*key='([^']+)'[^>]*>.*?<\/sc>/g;

     return inputString.replace(regex, (match, comp, key) => {
         if (mvkPlaceholders.hasOwnProperty(key)) {
             return mvkPlaceholders[key];
         }
         return match;
     });
}
