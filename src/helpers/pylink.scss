// $default: 255, 255, 255;
// $primary: 66, 120, 182;
// $success: 99, 187, 93;
// $danger: 174, 2, 47;

// button {
//     // padding: 8px 11px;
//     padding: 18px 50px;
//     font-size: 1.125rem;
//     letter-spacing: 1.1px;
//     font-weight: 600;
//     outline: none;
//     cursor: pointer;
//     vertical-align: middle;
//     // display: inline-block;
//     display: flex;
//     flex-flow: row nowrap;
//     align-items: center;

//     @media screen and (max-width: 800px) {
//         font-size: 1.125rem;
//     }

//     &:focus {
//         outline: 0;
//     }

//     &.icon {
//         padding: 0px 11px;
//         border: none !important;
//         background: none !important;
//         font-size: 0.813rem;
//     }

//     &.center {
//         margin: auto;
//         text-align: center;
//     }

//     &.right {
//         margin-left: auto !important;
//         margin-right: 0px !important;
//     }

//     &.black {
//         background-color: rgba(30, 30, 30, 1);
//         color: white;
//         border: none;
//     }

//     &.warning {
//         background-color: goldenrod;
//         color: white;
//         border: 1px solid rgba(255, 255, 255, 0.2);
//     }

//     &.plain {
//         background-color: white;
//         color: rgba(0, 0, 0, 0.9);
//         border: 1px solid rgba(0, 0, 0, 0.2);
//     }

//     &.default {
//         background-color: rgba(var($default), 1);
//         color: rgba(0, 0, 0, 0.9);
//         border: 1px solid rgba(0, 0, 0, 0.2);

//         &:hover {
//             background-color: rgba(var($default), 0.9);
//         }
//     }

//     &.primary {
//         background-color: rgba(var($primary), 0.7);
//         color: white;
//         border: 1px solid rgba(255, 255, 255, 0.3);

//         &:hover {
//             background-color: rgba(var($primary), 1);
//             color: white;
//             border: 1px solid rgba(255, 255, 255, 0.2);
//         }
//     }

//     &.success {
//         background-color: rgba(var($success), 0.7);
//         color: white;
//         border: 1px solid rgba(255, 255, 255, 0.2);

//         &:hover {
//             background-color: rgba(var($success), 1);
//         }
//     }

//     &.danger {
//         background-color: rgba(var($danger), 0.7);
//         color: white;
//         border: 1px solid rgba(255, 255, 255, 0.2);

//         &:hover {
//             background-color: rgba(var($danger), 1);
//         }
//     }

//     &.shadow {
//         box-shadow: 2px 2px 0px rgba(0, 0, 0, 0.1) !important;
//     }

//     &[disabled] {
//         opacity: 0.4;
//         cursor: not-allowed;
//     }

//     svg {
//         margin-left: 15px;
//         transition: transform .3s;
//         transform: translateX(0);
//     }

//     &:hover {

//         svg { // may need to adjust as more icon's are added
//             transform: translateX(5px);
//         }
//     }
// }
