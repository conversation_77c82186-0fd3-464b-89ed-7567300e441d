import React, { useContext } from 'react';

import { SettingsContext } from "src/context";

export const Border = (tone, style) => {
    switch (tone) {
        case 'dark':
            return DarkBorder(style);
        case 'light':
        default:
            return LightBorder(style);
    }
}


const LightBorder = (style) => {
    const [settings, setSettings] = useContext(SettingsContext);
    switch (style) {
        case 'solid-fill-primary':
        case 'outline-primary':
        case 'solid-fill-monochrome-primary':
        case 'outline-monochrome-primary':
            return `${settings.mvk_theme_config.other?.button_outline_width ? settings.mvk_theme_config.other?.button_outline_width : '3'}px solid ${settings.design?.colors?.primary_color}`;
        case 'solid-fill-secondary':
        case 'outline-secondary':
        case 'solid-fill-monochrome-secondary':
        case 'outline-monochrome-secondary':
            return `${settings.mvk_theme_config.other?.button_outline_width ? settings.mvk_theme_config.other?.button_outline_width : '3'}px solid ${settings.design?.colors?.secondary_color}`;
        case 'solid-fill-tertiary':
        case 'outline-tertiary':
            return `${settings.mvk_theme_config.other?.button_outline_width ? settings.mvk_theme_config.other?.button_outline_width : '3'}px solid ${settings.design?.colors?.tertiary_color}`;
        case 'solid-fill-gradient':
            return `${settings.mvk_theme_config.other?.button_outline_width ? settings.mvk_theme_config.other?.button_outline_width : '3'}px solid transparent`;
        case 'outline-gradient':
        case 'outline-gradient-persistent':
            return `linear-gradient(90deg, ${settings.design?.colors?.gradient_color_1}, ${settings.design?.colors?.gradient_color_2})`;
        default:
            return 'none';
    }
};

const DarkBorder = (style) => {
    const [settings, setSettings] = useContext(SettingsContext);
    switch (style) {
        case 'solid-fill-primary':
        case 'solid-fill-secondary':
        case 'solid-fill-tertiary':
        case 'solid-fill-monochrome-primary':
        case 'solid-fill-monochrome-secondary':
        case 'outline-primary':
        case 'outline-secondary':
        case 'outline-tertiary':
        case 'outline-gradient':
        case 'outline-monochrome-primary':
        case 'outline-monochrome-secondary':
            return `${settings.mvk_theme_config.other?.button_outline_width ? settings.mvk_theme_config.other?.button_outline_width : '3'}px solid white`;
        // case 'outline-primary':
        //     return `${settings.mvk_theme_config.other?.button_outline_width ? settings.mvk_theme_config.other?.button_outline_width : '3'}px solid ${settings.design?.colors?.primary_color}`;
        // case 'outline-secondary':
        //     return `${settings.mvk_theme_config.other?.button_outline_width ? settings.mvk_theme_config.other?.button_outline_width : '3'}px solid ${settings.design?.colors?.secondary_color}`;
        // case 'outline-tertiary':
        //     return `${settings.mvk_theme_config.other?.button_outline_width ? settings.mvk_theme_config.other?.button_outline_width : '3'}px solid ${settings.design?.colors?.tertiary_color}`;
        case 'outline-gradient-persistent':
            return `linear-gradient(90deg, ${settings.design?.colors?.gradient_color_1}, ${settings.design?.colors?.gradient_color_2})`;
        default:
            return 'none';
    }
};

export const Background = (tone, style) => {
    switch (tone) {
        case 'dark':
            return DarkBackground(style);
        case 'light':
        default:
            return LightBackground(style);
    }
}

const LightBackground = (style) => {
    const [settings, setSettings] = useContext(SettingsContext);
    switch (style) {
        case 'solid-fill-primary':
        case 'solid-fill-monochrome-primary':
            return `${settings.design?.colors?.primary_color}`;
        case 'solid-fill-secondary':
        case 'solid-fill-monochrome-secondary':
            return `${settings.design?.colors?.secondary_color}`;
        case 'solid-fill-tertiary':
            return `${settings.design?.colors?.tertiary_color}`;
        case 'solid-fill-gradient':
        case 'solid-fill-gradient-persistent':
            return `linear-gradient(to right, ${settings.design?.colors?.gradient_color_1}, ${settings.design?.colors?.gradient_color_2})`;
        case 'outline-primary':
        case 'outline-secondary':
        case 'outline-tertiary':
        case 'outline-gradient':
        case 'outline-gradient-persistent':
        case 'outline-monochrome-primary':
        case 'outline-monochrome-secondary':
            return 'transparent';
        default:
            return 'white';
    }
};

const DarkBackground = (style) => {
    const [settings, setSettings] = useContext(SettingsContext);
    switch (style) {
        case 'solid-fill-primary':
        case 'solid-fill-secondary':
        case 'solid-fill-tertiary':
        case 'solid-fill-gradient':
        case 'solid-fill-monochrome-primary':
        case 'solid-fill-monochrome-secondary':
            return 'white';
        case 'solid-fill-gradient-persistent':
            return `linear-gradient(to right, ${settings.design?.colors?.gradient_color_1}, ${settings.design?.colors?.gradient_color_2})`;
        case 'outline-primary':
        case 'outline-secondary':
        case 'outline-tertiary':
        case 'outline-gradient':
        case 'outline-gradient-persistent':
        case 'outline-monochrome-primary':
        case 'outline-monochrome-secondary':
            return 'transparent';
        default:
            return 'white';
    }
};

export const Color = (tone, style) => {
    switch (tone) {
        case 'dark':
            return DarkColor(style);
        case 'light':
        default:
            return LightColor(style);
    }
}

const LightColor = (style) => {
    const [settings, setSettings] = useContext(SettingsContext);
    switch (style) {
        case 'solid-fill-primary':
        case 'solid-fill-secondary':
        case 'solid-fill-monochrome-primary':
        case 'solid-fill-monochrome-secondary':
            return 'white';
        case 'solid-fill-tertiary':
        case 'outline-primary':
        case 'outline-secondary':
        case 'outline-tertiary':
        case 'outline-gradient':
        case 'outline-gradient-persistent':
            return `${settings.design?.colors?.body_copy_color}`;
        case 'outline-monochrome-primary':
            return `${settings.design?.colors?.primary_color}`;
        case 'outline-monochrome-secondary':
            return `${settings.design?.colors?.secondary_color}`;
        default:
            return 'white';
    }
};

const DarkColor = (style) => {
    const [settings, setSettings] = useContext(SettingsContext);
    switch (style) {
        case 'solid-fill-primary':
        case 'solid-fill-secondary':
        case 'solid-fill-tertiary':
        case 'solid-fill-gradient':
            return `${settings.design?.colors?.body_copy_color}`;
        case 'solid-fill-monochrome-primary':
            return `${settings.design?.colors?.primary_color}`;
        case 'solid-fill-monochrome-secondary':
            return `${settings.design?.colors?.secondary_color}`;
        case 'outline-primary':
        case 'outline-secondary':
        case 'outline-tertiary':
        case 'outline-gradient':
        case 'outline-monochrome-primary':
        case 'outline-monochrome-secondary':
            return 'white';
        default:
            return 'white';
    }
};

export const BackgroundColor = (value) => {
    const [ settings, setSettings ] = useContext(SettingsContext);
    switch (value) {
        case 'background_color':
            return `${settings.design?.colors?.background_color}`;
        case 'primary_color':
            return `${settings.design?.colors?.primary_color}`;
        case 'secondary_color':
            return `${settings.design?.colors?.secondary_color}`;
        case 'tertiary_color':
            return `${settings.design?.colors?.tertiary_color}`;
        case 'body_copy_color':
            return `${settings.design?.colors?.body_copy_color}`;
        default:
            return '';
    }
}

export const BackgroundClass = (value) => {
    switch (value) {
        case 'background_color':
            return 'background-bg';
        case 'primary_color':
            return 'primary-bg';
        case 'secondary_color':
            return 'secondary-bg';
        case 'tertiary_color':
            return 'tertiary-bg';
        case 'body_copy_color':
            return 'body-copy-bg';
        case 'white':
            return 'white-bg';
        default:
            return '';
    }
}

export const TextClass = (value) => {
    switch (value) {
        case 'dark':
            return 'white-txt';
        case 'light':
            return 'body-copy-txt';
        default:
            return '';
    } 
}

export const ColorSplash = (csColor) => {
    const [settings, setSettings] = useContext(SettingsContext);

    switch (csColor) {
        case 'primary-bg':
            return `${settings.design?.colors?.primary_color}`;
        case 'secondary-bg':
            return `${settings.design?.colors?.secondary_color}`;
        case 'tertiary-bg':
            return `${settings.design?.colors?.tertiary_color}`;
        case 'custom':
            return `${settings.mvk_theme_config.other?.cs_custom_color}`;
        default:
            return `${settings.design?.colors?.body_copy_color}`;
    }
}

export const OppositeStyle = (value) => {

    switch (value) {
        case 'solid-fill-primary':
            return 'outline-primary';
        case 'solid-fill-secondary':
            return 'outline-secondary';
        case 'solid-fill-tertiary':
            return 'outline-tertiary';
        case 'solid-fill-gradient':
            return 'outline-gradient'
        case 'solid-fill-gradient-persistent':
            return 'outline-gradient-persistent';
        case 'solid-fill-monochrome-primary':
            return 'outline-monochrome-primary';
        case 'solid-fill-monochrome-secondary':
            return 'outline-monochrome-secondary';
        case 'outline-primary':
            return 'solid-fill-primary';
        case 'outline-secondary':
            return 'solid-fill-secondary';
        case 'outline-tertiary':
            return 'solid-fill-tertiary';
        case 'outline-gradient':
            return 'solid-fill-gradient';
        case 'outline-gradient-persistent':
            return 'solid-fill-gradient-persistent';
        case 'outline-monochrome-primary':
            return 'solid-fill-monochrome-primary';
        case 'outline-monochrome-secondary':
            return 'solid-fill-monochrome-secondary';
    }
}