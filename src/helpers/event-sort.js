export const eventSort = (sortOption, listings) => {
    let events = listings?.sort((a, b) => {
        switch (sortOption) {
            case 'start-date-desc':
                var dateA = new Date((a.next_occurrence && a.next_occurrence.start) ? a.next_occurrence.start.event_date : a.start.event_date);
                var dateB = new Date((b.next_occurrence && b.next_occurrence.start) ? b.next_occurrence.start.event_date : b.start.event_date);
                return dateB - dateA;
            case 'end-date-asc':
                var dateA = new Date((a.next_occurrence && a.next_occurrence.end) ? a.next_occurrence.end.event_date : a.end.event_date);
                var dateB = new Date((b.next_occurrence && b.next_occurrence.end) ? b.next_occurrence.end.event_date : b.end.event_date);
                return dateA - dateB;
            case 'end-date-desc':
                var dateA = new Date((a.next_occurrence && a.next_occurrence.end) ? a.next_occurrence.end.event_date : a.end.event_date);
                var dateB = new Date((b.next_occurrence && b.next_occurrence.end) ? b.next_occurrence.end.event_date : b.end.event_date);
                return dateB - dateA;
            case 'start-date-asc':
            default:
                var dateA = new Date((a.next_occurrence && a.next_occurrence.start) ? a.next_occurrence.start.event_date : a.start.event_date);
                var dateB = new Date((b.next_occurrence && b.next_occurrence.start) ? b.next_occurrence.start.event_date : b.start.event_date);
                return dateA - dateB;
        }
    });

    return events
}