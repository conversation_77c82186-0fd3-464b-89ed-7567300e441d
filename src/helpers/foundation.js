export function ColumnCalculator (number) {
    switch (number) {
        case 5:
            return 'col5-unit';
        case 4:
            return 'large-3 medium-3 small-12';
        case 3:
            return 'large-4 medium-4 small-12';
        case 2:
            return 'large-6 medium-6 small-12';
        default:
            return 'small-12';
    }
};

export function ColumnClass(option) {
    switch (option) {
        case 'two-column':
        case '2':
            return 'cell medium-6';
        case 'three-column':
        case '3':
            return 'cell medium-4';
        case 'four-column':
        case '4':
            return 'cell medium-6 large-3';
        default:
            return 'cell';
    }
}

export const PTLColumns = (options, number) => {
    switch (options) {
        case 'half-half':
            return 'cell medium-6';
        case 'two-third-one-third':
            return `cell medium-6 ${number === 1 ? 'large-8' : 'large-4'}`;
        case 'one-third-two-third':
            return `cell medium-6 ${number === 1 ? 'large-4' : 'large-8'}`;
        default:
            return 'cell';
    };
};

export const PTLPosts = (options, number) => {
    switch (options) {
        case 'fullwidth':
            return 'cell large-4'
        // case 'half-half':
        //     return 'cell large-6';
        case 'two-third-one-third':
            return `cell ${number === 1 ? 'large-6' : ''}`;
        case 'one-third-two-third':
            return `cell ${number === 1 ? '' : 'large-6'}`;
        default:
            return 'cell';
    };
}