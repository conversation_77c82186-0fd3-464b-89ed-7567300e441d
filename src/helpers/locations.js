// Multilocation Functionality Helper Functions

// export const noGeocode = async () => {
//     let latitude = '';
//     let longitude = '';
//     let response = await new Promise(resolve => {
//         var xhr = new XMLHttpRequest();
//         xhr.open("GET", 'https://freeipapi.com/api/json', true);
//         xhr.onload = function (e) {
//             resolve(xhr.response);
//         };
//         xhr.onerror = function () {
//             console.error("** An error occurred during the XMLHttpRequest");
//         };
//         xhr.send();
//     }).then(() => {
//         const data = JSON.parse(response);
//         let response = new Promise(resolve => {
//             var xhr = new XMLHttpRequest();
//             xhr.open("GET", `https://api.findip.net/${data.ipAddress}/?token=bdbb7eb5500e4582bdaad9d084050ff8`, true);
//             xhr.onload = function (e) {
//                 resolve(xhr.response);
//             };
//             xhr.onerror = function () {
//                 console.error("** An error occurred during the XMLHttpRequest");
//             };
//             xhr.send();
//         }).catch((error) => {
//             console.log(error);
//         })
//         const findIP = JSON.parse(response);
//         latitude = findIP.location?.latitude
//         longitude = findIP.location?.longitude
//     }).catch((error) => {
//         console.log(error);
//     })
   
//     // const latitude = data.latitude;
//     // const longitude = data.longitude;

//     return { latitude, longitude }
// }
export const noGeocode = async () => {
    let latitude = '';
    let longitude = '';

    try {
        // Fetch the user's geolocation data based on their IP address
        let response = await fetch('https://ipapi.co/json/');
        if (!response.ok) throw new Error(`IP API error: ${response.statusText}`);

        let data = await response.json();

        // Extract latitude and longitude from the response
        latitude = data.latitude;
        longitude = data.longitude;

        console.log(`Latitude: ${latitude}, Longitude: ${longitude}`);
    } catch (error) {
        console.error("An error occurred:", error);
    }

    return { latitude, longitude };
};
// export const noGeocode = async () => {
//     let latitude = '';
//     let longitude = '';

//     try {
//         // Fetch the user's IP
//         let response = await fetch('https://freeipapi.com/api/json');
//         let data = await response.json();
        
//         // Fetch lat and long based on IP
//         let geoResponse = await fetch(`https://api.findip.net/${data.ipAddress}/?token=bdbb7eb5500e4582bdaad9d084050ff8`);
//         let findIP = await geoResponse.json();

//         latitude = findIP.location?.latitude || '';
//         longitude = findIP.location?.longitude || '';
//     } catch (error) {
//         console.error("An error occurred:", error);
//     }

//     return { latitude, longitude };
// };
export const getUserLocation = () => {
    let userLocation = false;
    if (navigator.geolocation) {
        navigator.geolocation.getCurrentPosition(
            (position) => {
                const { latitude, longitude } = position.coords;
                userLocation = { latitude, longitude }
            },
            (error) => {
                userLocation = {latitude: 39.8283, longitude: 98.5795 }
                console.log('Error getting user location:', error.message);
            }
        );
    } else {
        userLocation = {latitude: 39.8283, longitude: 98.5795 }
    }
 
    return userLocation;
}

export const sortLocations = (userLocation, locations, zipSearch = false) => {
    if (!userLocation) {
        var sortedAlpha = locations.sort((a, b) => {
            return a.name.localeCompare(b.name);
        });
        return sortedAlpha;
    }
    function toRad(v) { return v * Math.PI / 180; }
    function kmToMiles(km) { return (km * 0.62137).toFixed(2); }
    function haversine(l1, l2) {
        var R = 6371; // km 
        var x1 = l2.lat - l1.latitude;
        var dLat = toRad(x1);
        var x2 = l2.lng - l1.longitude;
        var dLon = toRad(x2);
        var a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
            Math.cos(toRad(l1.latitude)) * Math.cos(toRad(l2.lat)) *
            Math.sin(dLon / 2) * Math.sin(dLon / 2);
        var c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
        var d = R * c;
        return d;
    }
    // Filter locations based on zipSearch
    let filteredLocations = locations.filter(location => {
        if (!zipSearch) {
            return true;
        }
        if (location.zip_search) {
            const zipList = location.zip_search.split(',').map(zip => zip.trim());
            return zipList.includes(zipSearch);
        }
        return true;
    });
    for (let i = 0; i < filteredLocations.length; i++) {
        let distance = kmToMiles(haversine(userLocation, filteredLocations[i]))
        //Attaching returned distance from function to array elements
        filteredLocations[i].distance = distance;
    }
    var sorted = filteredLocations.sort((a, b) => {
        return parseInt(a.distance) - parseInt(b.distance)
    })
    return sorted;
}

export const locationListScroll = (querySelector) => {
    const container = document.querySelector(querySelector);
    const listItems = container.querySelectorAll('.single-location');
    // const listItems = document.getElementsByClassName('single-location');
    for (let i = 0; i < listItems.length; i++) {
        if (listItems[i].classList.contains('focused')) {
            const top = listItems[i].offsetTop;
            container.querySelector('.list-inner').scrollTop = top;
        }
    }
}

export const isValidZipCode = (value) => {
    const zipCodeRegex = /^\d{5}(-\d{4})?$/; // Matches 12345 or 12345-6789
    return zipCodeRegex.test(value);
};