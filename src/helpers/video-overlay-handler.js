import { useEffect } from 'react';

const VideoOverlayHandler = () => {

    useEffect(() => {
        const handleOverlayClick = (event) => {
            const overlay = event.target;
            if (overlay.classList.contains('overlay')) {
                overlay.style.display = 'none';
            }
        };

        const observer = new MutationObserver((mutations) => {
            const videoContainers = document.querySelectorAll('.mvk-responsive-video');

            videoContainers.forEach((container) => {
                const overlay = container.querySelector('.overlay');
                if (overlay) {
                    overlay.removeEventListener('click', handleOverlayClick);
                    overlay.addEventListener('click', handleOverlayClick);
                }
            });
        });

        observer.observe(document.body, { childList: true, subtree: true });

        return () => {
            observer.disconnect();
            const videoContainers = document.querySelectorAll('.mvk-responsive-video');
            videoContainers.forEach((container) => {
                const overlay = container.querySelector('.overlay');
                if (overlay) {
                    overlay.removeEventListener('click', handleOverlayClick);
                }
            });
        };
    }, []); 

    return null;
};

export default VideoOverlayHandler;