import React, { useState } from 'react'
import { Link, useNavigate } from 'react-router-dom'
import { isMobile } from 'react-device-detect';

// DO NOT CONTINUE TO USE THIS HELPER.
// REPLACED BY SRC/HELPERS/CLICKER.

export default function PyLink({ url, isExternal = false, children, ...otherProps }) {
    const [swiping, setSwiping] = useState(false);

    const navigate = useNavigate();
    const Touching = (e) => { setSwiping(false); };
    const Swiping = (e) => { setSwiping(true); };

    try {
        var { pathname } = (url && !isExternal) ? new URL(url) : '/';
        var webaddress = pathname;
    } catch (err) {
        var webaddress = url;
    }

    const Click = (e) => {
        e.preventDefault();
        e.stopPropagation();
        if ((e.type == "click" && isMobile) || swiping) {
            return;
        }
        if (isExternal) {
            window.open(webaddress, '_blank');
        } else {
            navigate(webaddress);
        }
    };

    // if (!otherProps.id) {
    //     otherProps.id = "py-link";
    // }

    if (!isExternal) {
        return (
            <a href={url} onTouchStart={Touching} onTouchMove={Swiping} onClick={Click} onTouchEnd={Click} {...otherProps}>
                {children}
            </a>
        );
    } else if (isExternal) {
        return (
            <a href={url} rel="noopener nofollow" target="_blank" {...otherProps}>
                {children}
            </a>
        );
    } else {
        return null;
    }
}
