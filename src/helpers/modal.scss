@import "src/scss/variables.scss";

#modal {
    &.fullpage,
    &.iframe,
    &.slider,
    &.review-listing {
        position: fixed;
        z-index: 999999;
        top: 0px;
        bottom: 0px;
        left: 0px;
        right: 0px;
        background-color: rgba(0, 0, 0, 0.9);
        overflow-y: scroll;
    }
    &.review-listing {
        .inner-wrapper {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: #fff;
            padding: 1rem;
            border-radius: 10px;
            max-height: 80%;
            overflow: auto;
            width: 90%;
            max-width: 600px;
            .author {
                display: flex;
                align-items: center;
                margin-bottom: 1rem; 
                img {
                    width: 60px;
                    margin-right: 1rem;
                }
                .name {
                    margin: 0;
                }
            }
            .content-container {
                p {
                    margin: 0;
                }
            }
            .post-on-google {
                margin-top: 1.5rem;
            }
        }
    }
    &.iframe {
        .iframe-container {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 85%;
            max-width: 1280px;
            &.widget {
                width: auto;
            }
        }
    }
    &.slider {
        .slider-container {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 85%;
            max-width: 1280px;
            .slick-slider {
                top: unset;
                transform: none;
            }
            .slick-track {
                display: flex;
                align-items: center;
            }
            .slide-wrapper {
                display: flex !important;
                justify-content: center;
                .caption {
                    margin-top: .5rem;
                    color: #fff;
                }
            }
        }
    }
    .close-modal {
        position: absolute;
        top: 5%;
        right: 5%;
        color: rgba(255, 255, 255, 0.9);
        font-size: 2rem;
        transition: 0.2s;
        cursor: pointer;
        &:hover {
            color: #fff;
            transform: scale(1.1);
        }
    }
    .container {
        &.dynamic {
            padding: 50px;
        }
    }
}
