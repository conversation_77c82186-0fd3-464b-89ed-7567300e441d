import React, { useEffect, useState } from "react";

export const ScrollHandler = (offset) => {
	const [scroll, setScroll] = useState(false);

	useEffect(() => {
		const onScroll = () => {
			const scrollCheck = window.scrollY > (offset ?? 100);
			setScroll(scrollCheck);
		};

		document.addEventListener("scroll", onScroll);
		return () => {
			document.removeEventListener("scroll", onScroll);
		};
	}, [scroll, setScroll]);

	return scroll;
};

export const elementInView = (id, amountVisible) => {
	const [inView, setInView] = useState(false);

	const isInViewport = (element) => {
		const height = window.innerHeight || document.documentElement.clientHeight;
		const rect = element.getBoundingClientRect();

		return (
			rect.top <= height - amountVisible
		);
	}

	useEffect(() => {
		setTimeout(() => {
			let el = document.getElementById(id)
			// first check if it's already in view
			if (el && isInViewport(el)) {
				setInView(true);
			}
			// on scroll find the element and check if it's in viewport
			const onScroll = () => {
				if (el && isInViewport(el)) {
					setInView(true);
				}
			}
			//scroll event listener
			document.addEventListener('scroll', onScroll, { passive: true });
			return () => {
				document.removeEventListener("scroll", onScroll);
			};
		}, 900)

	}, [id, inView, setInView]);

	return inView;
}