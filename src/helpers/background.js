import config from 'src/config';

export default function Background (data, settings) {

    if (data.background_type == 'image' && data.background_image) {
        if (!data.background_image.url.includes('http')) {
            data.background_image.url = `https://${config.domain}${data.background_image.url}`;
        }
        return { backgroundImage:`url(${data.background_image.url})`, backgroundRepeat:'no-repeat', backgroundPosition:'center', backgroundSize:'cover' };
    }
    
    else if (data.background_type == 'color' && data.background_color == 'primary_color') {
        return { backgroundColor:settings?.design?.colors?.primary_color };
    }    
    
    else {
        return null;
    }
};