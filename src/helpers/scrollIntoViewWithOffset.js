// scroll with an offset
export const scrollIntoViewWithOffset = (selector, offset, behavior) => {
    // if string get element by id first
    if (typeof selector === 'string') {
        selector = selector.charAt() === '#' ? selector.split('#')[1] : selector;
        selector = selector.endsWith('/') ? selector.split('/')[0] : selector;
        selector = document.getElementById(selector);
    }
    if (selector) {
        window.scrollTo({
            behavior: behavior,
            top:
                selector.getBoundingClientRect().top -
                document.body.getBoundingClientRect().top -
                offset,
        })
    }
}