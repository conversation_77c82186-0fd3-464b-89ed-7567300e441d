import React, { Suspense, useContext, useState, useEffect, useLayoutEffect } from "react";
import { useParams } from "react-router-dom";

import { ThemeProvider } from "styled-components";
var AmazonCognitoIdentity = require('amazon-cognito-identity-js');
import { getSecrets } from 'src/api/requests';

// CONTEXT.
import { AppProvider } from 'src/contexts/app';
import { PrincipalProvider, PrincipalContext, TranslationsProvider } from 'src/context';

// PARTIALS.
import Loading from "src/partials/loading";

// HELPERS.
import Styles from "src/styles";

// LAYOUTS.
import AppLayout from 'src/layouts/app';

const Content = React.lazy(() => import("src/content"));
const Cognito = React.lazy(() => import("src/cognito"));
// const Styles  = React.lazy(() => import("src/styles"));

const Start = (props) => {
    return (
        <AppProvider>
            <AppLayout>
                <TranslationsProvider>     
                    <PrincipalProvider>
                        <CheckData />
                    </PrincipalProvider>
                </TranslationsProvider>
            </AppLayout>
        </AppProvider>
    );
};

const CheckData = (props) => {
    const [ principal ] = useContext(PrincipalContext);

    switch (true) {
        case principal.loading:
            return (<Loading type="loading-start" />);
        case Object.keys(principal?.settings)?.length <= 0:
            return (<NoSettings />);
        case Object.keys(principal?.pages)?.length <= 0:
            return (<NoPages />);
        default:
            return (<CheckCognito principal={principal} />);
    };
};

const NoSettings = (props) => {
    return (
        <div style={{ textAlign: 'center', padding: '50px', fontFamily: 'Verdana' }}>
            <h2>Error: PLT417</h2>
            <h4>No settings were provided for this website to load properly.</h4>
            <p>Please contact your Pylot representative.</p>
        </div>
    );
};

const NoPages = (props) => {
    return (
        <div style={{ textAlign: 'center', padding: '50px', fontFamily: 'Verdana' }}>
            <h2>Error: PLT417</h2>
            <h4>No pages were provided for this website to load properly.</h4>
            <p>Please contact your Pylot representative.</p>
        </div>
    );
};



const CheckCognito = (props) => {
    const [ principal ]         = useContext(PrincipalContext);
    const [ locked, setLocked ] = useState(false);

    useLayoutEffect(() => {
        if (principal?.settings?.cognito) {
            var userPool = new AmazonCognitoIdentity.CognitoUserPool({ UserPoolId: principal.settings?.cognito?.user_pool_id, ClientId: principal.settings?.cognito?.app_client_id });
            var cognitoUser = userPool.getCurrentUser();
            setLocked(cognitoUser ? false : true);
        }
    }, []);

    switch (true) {
        case locked:
            // SEND THEM TO COGNITO.
            return (
                <Suspense fallback={<div />}>
                    <Cognito settings={principal.settings} />
                </Suspense>
            );
        default:
            // SEND THEM TO ROUTING.
            return (<LetsParty subsite={false} activeTranslation={principal?.activeTranslation || ''} settings={principal.settings} pages={principal.pages} />);
    };
};

// const FleetCheck = (props) => {
//     const [ principal, setPrincipal ] = useContext(PrincipalContext);
//     const params = useParams();

//     switch (true) {
//         case principal.fleet:
//             return (<Ascertain slug={params.slug} first={params.first} />);
//         default:
//             return (<LetsParty subsite={false} activeTranslation={principal?.activeTranslation || ''} settings={principal.settings} pages={principal.pages} />);
//     };
// };

// const Ascertain = ({ slug, first = false }) => {
//     const [ principal, setPrincipal ] = useContext(PrincipalContext);
//     const [ direction, setDirection ] = useState(false);

//     useLayoutEffect(() => {
//         setDirection(false);
//         var page = (slug) ? slug : principal.settings.front_page;

//         // first param provided and page exists for it, auto principal.
//         if (first && principal.pages[first]) {
//             setDirection('principal');
//         } 
        
//         // first param provided, but no page exists for it, auto subsite.
//         else if (first && principal.pages[first] == undefined) {
//             setDirection('probable');
//         }

//         // a page or redirect exists for the slug/page provided.
//         else if (principal.pages[page]) {
//             setDirection('principal');
//         }
        
//         // a slug was provided, but no page or redirect exists for it.
//         else {
//             setDirection('probable');
//         }

//     }, [ slug, first ]);

//     if (direction == 'principal') {
//         return (<LetsParty subsite={false} activeTranslation={principal?.activeTranslation || ''} settings={principal.settings} pages={principal.pages} />);
//     } else if (direction == 'probable') {
//         return (<Verification subsite={first ? first : slug} translation={principal?.activeTranslation || ''}/>);
//     } else {
//         return (<Loading type="loading-start" />);
//     }
// };

// VERIFY THAT IT'S A FLEET SUBSITE.
// const Verification = ({ subsite, activeTranslation }) => {
//     return (
//         <FleetProvider subsite={subsite} translation={activeTranslation || ''}>
//             <Confirmation subsite={subsite} />
//         </FleetProvider>
//     );
// };

// VERIFY THAT IT'S A FLEET SUBSITE.
// const Confirmation = ({ subsite }) => {
//     const [ principal, setPrincipal ] = useContext(PrincipalContext);
//     const [ fleet, setFleet ] = useContext(FleetContext);

//     if (fleet.loading) {
//         return (<Loading type="loading-start" />);
//     } else if (fleet.settings) {
//         // FLEET SUBSITE SETTINGS FOUND.
//         return (<LetsParty subsite={subsite} activeTranslation={principal?.activeTranslation || ''} settings={fleet.settings} pages={fleet.pages} />);
//     } else {
//         // FLEET SUBSITE SETTINGS NOT FOUND.
//         return (<LetsParty subsite={false} activeTranslation={principal?.activeTranslation || ''} settings={principal.settings} pages={principal.pages} />);
//     }
// };

const LetsParty = ({ subsite = false, activeTranslation, settings, pages }) => {
    const [ principal, setPrincipal ] = useContext(PrincipalContext);
    
    useEffect(() => {
        getSecrets();
    }, [])

    useEffect(() => {
        setPrincipal({ ...principal, ...{ subsite:subsite }});
    }, [ subsite, activeTranslation, settings, pages ]);

    return (
        <Suspense fallback={<div />}> 
            <ThemeProvider theme={Styles({ subsite: subsite, principal: principal, settings: settings })}>
                <Content subsite={subsite} activeTranslation={activeTranslation || ''} settings={settings} pages={pages} />
            </ThemeProvider>
        </Suspense>
    );
};

export default Start;