import React, { useState, useEffect, useRef } from "react";
import { faChevronDown, faChevronUp } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';

// HELPERS.
const Clicker = React.lazy(() => import('src/helpers/clicker'));
import { CenterHours, WeeksHours } from 'src/helpers/hours';
import { Coloring } from 'src/helpers';

import { HoursDropdownContainer } from './styles';
import './hours-dropdown.scss';

const Start = (props) => {
    const [dropdown, setDropdown] = useState(false);
    const btnRef = useRef();
    const dropdownRef = useRef();

    useEffect(() => {
        const closeDropdown = (event) => {
            const dropdownButtonClosest = event.target.closest('[data-dropdown-button]');
            const dropdownClosest = event.target.closest('[data-dropdown]');

            if (dropdownClosest === dropdownRef.current) {
                return;
            } else if (dropdownButtonClosest === btnRef.current) {
                setDropdown(prev => !prev);
            } else {
                setDropdown(false);
            }
        };

        document.addEventListener('click', closeDropdown);

        return () => document.removeEventListener('click', closeDropdown);
    }, []);


    var date = new Date;
    let hoursString;
    let hours = CenterHours(date, true);
    if (props?.settings?.hours && props?.settings?.hours?.custom_hours_message) {
        return props?.settings?.hours?.custom_hours_message;
    } else if (props?.settings?.hours) {
        if (hours) {
            hoursString = `${props?.settings?.hours?.standard_hours[0]?.todays_hours_label ? props?.settings?.hours?.standard_hours[0]?.todays_hours_label : "Today's Hours"}${props?.delimiter ? props?.delimiter : ' /'} ${hours}`;
        } else {
            hoursString = "Contact Retailer For Hours";
        }
        return (
            <>
                {props?.settings?.mvk_theme_config?.header?.hours_link &&
                    <Clicker className={props?.settings?.mvk_theme_config?.header?.hours_color} type="anchor" url={props?.settings?.mvk_theme_config?.header?.hours_link?.url} target={props?.settings?.mvk_theme_config?.header?.hours_link?.target ? item?.target : null} dangerouslySetInnerHTML={{ __html: hoursString }} />
                }
                {!props?.settings?.mvk_theme_config?.header?.hours_link &&
                    <span class={props?.settings?.mvk_theme_config?.header?.hours_color} dangerouslySetInnerHTML={{ __html: hoursString }} />
                }
                {props?.settings?.mvk_theme_config?.header?.expand_hours &&
                    <>
                        <button ref={btnRef} class='dropdown-button' aria-label='chevron down' data-dropdown-button>
                            <FontAwesomeIcon icon={dropdown ? faChevronUp : faChevronDown} />
                        </button>
                        <HoursDropdown dropdown={dropdown} settings={props?.settings} dropdownRef={dropdownRef} />
                    </>
                }
            </>
        );
    } else {
        return (<span />);
    }
};

const HoursDropdown = ({ dropdown, settings, dropdownRef }) => {
    var date = new Date;
    const hoursData = WeeksHours('dropdown', null, date);
    let currentDay = date.getDay();
    if (currentDay === 0) {
        currentDay = 7;
    }
    var hoursBgColor = settings.design?.colors?.util_nav?.utility_nav_custom_background ? settings.design?.colors?.util_nav?.ut_nav_bg_color : settings.design?.colors?.main_nav?.nav_bg_color;

    return (
        <HoursDropdownContainer
            ref={dropdownRef}
            className={`hours-dropdown-container ${dropdown}`}
            hoursBgColor={hoursBgColor}
            data-dropdown>
            <div class='hours-list'>
                {hoursData?.hours && hoursData?.hours?.map(function (day, index) {
                    return (
                        <p class={`list-row ${index + 1 === currentDay ? 'current-day' : ''}`} key={index} dangerouslySetInnerHTML={{ __html: day }}></p>
                    );
                })}
            </div>
            {settings?.hours?.alternate_hours &&
                <div class='blurb-container'>
                    <p class='holiday-blurb'>{settings.hours?.holiday_hours_message ? settings.hours?.holiday_hours_message : 'We have special hours for holidays.'}</p>
                </div>
            }
        </HoursDropdownContainer>
    );
};
export default Start;