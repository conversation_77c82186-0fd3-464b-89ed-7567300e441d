import React, { useContext, useMemo } from "react";
import Slider from "react-slick";
import { SettingsContext } from "src/context";
// Helpers
import { PrevArrow, NextArrow } from 'src/helpers/slick';
// Partials
import Table from 'src/partials/hours/table';
// Styles
import "slick-carousel/slick/slick.scss";
import "slick-carousel/slick/slick-theme.scss";

const Start = ({ title, hours, type, customMessage }) => {

    switch (type) {
        case 'table':
            return (<TableSlides title={title} hours={hours} customMessage={customMessage} />);
        case 'card':
            return (<CardSlides title={title} hours={hours} customMessage={customMessage} />);
        default:
            return (<div />)
    }
};

const TableSlides = ({ title, hours, customMessage }) => {
    const sliderSettings = {
        arrows: true,
        infinite: false,
        slidesToShow: 1,
        nextArrow: <NextArrow />,
        prevArrow: <PrevArrow />
    }
    return (
        <div class='hours-module'>
            <div class='carousel grid-container table'>
                <Slider ref={(a) => a} {...sliderSettings}>
                    {hours?.map((week, index) =>
                        <div class='slide'>
                            <Table key={index} title={title} hours={week} customMessage={customMessage} />
                        </div>
                    )}
                </Slider>
            </div>
        </div>
    )
};

const CardSlides = ({ title, hours, customMessage }) => {
    const [settings, setSettings] = useContext(SettingsContext);
    
    let sliderSettings = {
        infinite: false,
        slidesToShow: 3,
        slidesToScroll: 1,
        slickGoTo: 0,
        arrows: true,
        centerMode: true,
        nextArrow: <NextArrow />,
        prevArrow: <PrevArrow />,
        responsive: [
            {
                breakpoint: 768,
                settings: {
                    slidesToShow: 1,
                    slidesToScroll: 1,
                    centerMode: true,
                }
            },
        ]
    }
    return (
        <div class='hours-module'>
            <div class='carousel card'>
                <div class='grid-container'>
                    <div class='grid-x'>
                        <div class='cell'>
                            {title &&
                                <h2>{title}</h2>
                            }
                            {settings.hours?.standard_hours[0]?.special_instructions &&
                                <p class='special-instructions'>{settings.hours?.standard_hours[0]?.special_instructions}</p>
                            }
                            {customMessage &&
                                <h3 className='center'>{customMessage}</h3>
                            }
                        </div>
                    </div>
                </div>
                {!customMessage &&
                    <Slider ref={(a) => a} {...sliderSettings}>
                        {hours?.map((week, index) =>
                            <Slide week={week} index={index} settings={settings} />
                        )}
                    </Slider>
                }
            </div>
        </div>
    )
};

const Slide = ({ week, index, settings }) => {
    return (
        <div key={index} class='card-slide'>
            <div class='hours-set'>
                {week.currentWeek &&
                    <p class='current-week'>{`< ${settings?.hours?.standard_hours[0]?.week_of_hours_label ? settings?.hours?.standard_hours[0]?.week_of_hours_label : 'Week of'} ${week.currentWeek} >`}</p>
                }
                <div class='hours'>
                    {week.hours && week.hours.map((day, index) =>
                        <p class='hours-row' key={index} dangerouslySetInnerHTML={{ __html: day }}></p>
                    )}
                </div>
            </div>
        </div>
    )
}

export default Start;