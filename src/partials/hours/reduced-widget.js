// import React from "react";
import React, { useContext } from "react";
import { decode } from 'html-entities';
import { SettingsContext } from "src/context";

const Start = ({ data, title, hours, customMessage }) => {
    const [settings, setSettings] = useContext(SettingsContext);

    return (
        <div class={`hours-module mobile-${data.enable_mobile_alignment ? data.mobile_alignment : 'false'} desktop-${data.widget_alignment}`}>
            <div class={`reduced-widget`}>
                {title &&
                    <h3 className="title">{decode(title)}</h3>
                }
                {(customMessage) &&
                    <p class='hours'>{customMessage}</p>
                }
                {(!customMessage) &&
                    <p class='hours' dangerouslySetInnerHTML={{ __html: hours }} />
                }
            </div>
        </div>
    );
}

export default Start;