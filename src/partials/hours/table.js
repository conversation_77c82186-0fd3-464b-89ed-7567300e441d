import React, {useContext} from "react";
// CONTEXT.
import { SettingsContext } from "src/context";

const Start = ({ title, hours, special, background, customMessage, store, isLifestyle }) => {
    const [settings, setSettings] = useContext(SettingsContext);
    let classes = (background && background === 'dark') ? 'primary-bg white-txt' : '';
    const lifestyle = (isLifestyle) ? 'primary-txt left lifestyle-override' : '';

    return (
        <div class={`hours-module ${classes}`}>
            <div class='grid-container'>
                <div class='grid-x'>
                    <div class='cell'>
                        {title &&
                            <h2 class={`${lifestyle}`}>{title}</h2>
                        }
                        {special &&
                            <p class='special-instructions'>{special}</p>
                        }
                        {(!store && settings.hours?.standard_hours[0]?.special_instructions) &&
                            <p class='special-instructions'>{settings.hours?.standard_hours[0]?.special_instructions}</p>
                        }
                        {(hours.currentWeek && !customMessage) &&
                            <p class='current-week'>{settings?.hours?.standard_hours[0]?.week_of_hours_label ? settings?.hours?.standard_hours[0]?.week_of_hours_label : 'Week of'} {hours.currentWeek}</p>
                        }
                        {(customMessage && !store) &&
                            <h3 class='custom-hours-message'>{customMessage}</h3>
                        }
                        {(!customMessage || store) &&
                            <div class='hours-container'>
                                {hours.hours && hours.hours.map(function (day, index) {
                                    return (
                                        <p class='hours-row' key={index} dangerouslySetInnerHTML={{ __html: day }}></p>
                                    )
                                })
                                }
                            </div>
                        }
                    </div>
                </div>
            </div>
        </div>
    );
}

export default Start;