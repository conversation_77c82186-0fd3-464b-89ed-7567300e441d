// import React from "react";
import React, { useContext } from "react";
import { SettingsContext } from "src/context";

const Start = ({ title, hours, background, customMessage, store, isLifestyle }) => {
    const [settings, setSettings] = useContext(SettingsContext);

    let classes = (background && background === 'dark') ? 'primary-bg white-txt' : '';
    const lifestyle = (isLifestyle) ? 'primary-txt left lifestyle-override' : '';

    return (
        <div class='hours-module'>
            <div class={`condensed-list ${classes}`}>
                <div class={store ? '' : 'grid-container'}>
                    <div class='grid-x'>
                        <div class='cell'>
                            {title &&
                                <p class={`${lifestyle}`}>{title}</p>
                            }
                            {(customMessage && !store) &&
                                <p class='hours'>{customMessage}</p>
                            }
                            {(!store && settings.hours?.standard_hours[0]?.special_instructions) &&
                                <p class='special-instructions'>{settings.hours?.standard_hours[0]?.special_instructions}</p>
                            }
                            {(!customMessage || store) &&
                                <p class='hours' dangerouslySetInnerHTML={{ __html: hours }} />
                            }
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
}

export default Start;