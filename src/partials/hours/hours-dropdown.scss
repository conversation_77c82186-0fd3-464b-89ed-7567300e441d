.hours-today {
    align-items: center;
}

.dropdown-button {
    background: transparent;
    border: transparent;
    padding: 0;
    width: min-content;
    height: min-content;
    margin-left: 0.5rem;
    color: inherit;
    svg {
        width: 1em;
        height: 1em;
        margin: 0;
        transition: unset !important;
        transform: unset !important;
    }
}

.hours-dropdown-container {
    transition: transform 0.2s ease-in-out;
    position: absolute;
    background-color: #fff;
    width: 100%;
    height: auto;
    padding: 0.5rem 1rem 0 1rem;
    top: 40px;
    left: 0;
    &.false {
        opacity: 0;
        z-index: -1;
        transform: translateY(-10rem);
        pointer-events: none;
    }

    &.true {
        opacity: 1;
        z-index: 2000;
        transform: translateY(0);
        pointer-events: all;
    }

    @media (min-width: 768px) {
        left: unset;
        right: 0;
    }

    @media (min-width: 992px) {
        width: max-content;
    }
    
    .blurb-container {
        max-width: fit-content;
    }
}

.hours-list {
    line-height: 1.5;

    p.current-day {
        font-weight: bold;
    }
}

.list-row {
    font-size: 0.813rem;
    display: grid;
    grid-auto-flow: row;
    grid-template-columns: 1fr 1fr;

    @media (min-width: 768px) {
        column-gap: 1rem;
    }
}


.hours-vary-blurb {
    font-size: 0.813rem;
    line-height: 1.5;
}

.holiday-blurb {
    font-size: 0.813rem;
}
