import React, { useContext, useMemo } from "react";

// CONTEXT.
import { SettingsContext } from "src/context";

// HELPERS.
import { WeeksHours } from 'src/helpers/hours';

// SUB PARTIALS.
const Table = React.lazy(() => import('./table'));
const Reduced = React.lazy(() => import('./reduced'));
const ReducedWidget = React.lazy(() => import('./reduced-widget'));
const Carousel = React.lazy(() => import('./carousel'));
const HoursToday = React.lazy(() => import('./hours-today'));
const Daily = React.lazy(() => import('./daily'))
const List = React.lazy(() => import('./list'));
const CondensedList = React.lazy(() => import('./condensed-list'));

const Start = ({ data }) => {
    const [settings, setSettings] = useContext(SettingsContext);

    const hoursData = useMemo(() => {
        var newArray = [];
        if (data.display_options == 'carousel') {
            for (let i = 0; i <= 52; i++) {
                let now = new Date();
                let date = new Date(now.setDate(now.getDate() + i * 7));
                newArray.push(WeeksHours(data.display_options, null, date));
            }
        } else {
            newArray = WeeksHours(data.display_options);
        }
        return newArray
    }, []);

    switch (data.display_options) {
        case 'table':
            return (<Table title={data.title} hours={hoursData} background={data.background_value} customMessage={settings.hours?.custom_hours_message} />);
        case 'reduced':
            return (<Reduced title={data.title} hours={hoursData} background={data.background_value} customMessage={settings.hours?.custom_hours_message} />);
        case 'reduced-widget':
            return (<ReducedWidget data={data} title={data.hours_title} hours={hoursData} customMessage={settings.hours?.custom_hours_message} />);
        case 'carousel':
            return (<Carousel title={data.title} hours={hoursData} type={data.carousel_style} customMessage={settings.hours?.custom_hours_message} />);
        case 'hours-today':
            return (<HoursToday title={data.title} hours={hoursData} type={data.carousel_style} />);
        case 'daily':
            return (<Daily title={data.title} hours={hoursData} type={data.carousel_style} customMessage={settings.hours?.custom_hours_message} />);
        case 'list':
            return (<List title={data.title} hours={hoursData} background={data.background_value} customMessage={settings.hours?.custom_hours_message} />);
        case 'condensed_list':
            return (<CondensedList title={data.title} hours={hoursData} background={data.background_value} customMessage={settings.hours?.custom_hours_message} />);
        default:
            return (<div />)
    };
};

export default Start;