import React, { useContext } from "react";
import { SettingsContext } from "src/context";

const Start = ({ title, hours, customMessage }) => {
    const [settings, setSettings] = useContext(SettingsContext);

    return (
        <div class={`hours-daily`}>

            {title &&
                <h2 className="title">{title}</h2>
            }

            <div class='hours-container'>
                {customMessage ?
                    customMessage :
                    <>
                        {hours.hours && hours.hours.map(function (day, index) {
                            return (
                                <div class='hours-block' key={index} dangerouslySetInnerHTML={{ __html: day }}></div>
                            )
                        })
                        }
                    </>
                }
            </div>

        </div>
    );
}

export default Start;