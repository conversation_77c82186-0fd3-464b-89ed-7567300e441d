import React, { useContext } from "react";
import { SettingsContext } from "src/context";

const Start = ({ title, hours, special, isLifestyle }) => {
    const [settings, setSettings] = useContext(SettingsContext);
    const lifestyle = (isLifestyle) ? 'primary-txt left lifestyle-override' : '';

    return (
        <div class={`hours-special`}>

            {title &&
                <h2 class={`${lifestyle}`}>{title}</h2>
            }
            {special &&
                <p class='special-instructions'>{special}</p>
            }
            {(!special && settings.hours?.standard_hours[0]?.special_instructions) &&
                <p class='special-instructions'>{settings.hours?.standard_hours[0]?.special_instructions}</p>
            }
            <div class='hours-container'>
                {(hours.currentWeek) &&
                    <p class='current-week'>{settings?.hours?.standard_hours[0]?.week_of_hours_label ? settings?.hours?.standard_hours[0]?.week_of_hours_label : 'Week of'} {hours.currentWeek}</p>
                }
                {hours.hours && hours.hours.map(function (day, index) {
                    return (<p class='hours-row' key={index} dangerouslySetInnerHTML={{ __html: day }}></p>);
                })
                }
            </div>
        </div>
    );
};

export default Start;
