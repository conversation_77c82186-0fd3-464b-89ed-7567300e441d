import React, { useContext } from "react";
import { SettingsContext } from "src/context";

const Start = ({ title, hours, special, store, isLifestyle }) => {
    const [settings, setSettings] = useContext(SettingsContext);
    const d = new Date();
    let currentDay = d.getDay();
    if (currentDay === 0) {
        currentDay = 7;
    }

    const lifestyle = (isLifestyle) ? 'primary-txt left lifestyle-override' : '';

    return (
        <div class='hours-module'>
            <div class='grid-container list'>
                <div class='grid-x'>
                    <div class='cell'>
                        {title &&
                            <h2 class={`${lifestyle}`}>{title}</h2>
                        }
                        {special &&
                            <p class='special-instructions'>{special}</p>
                        }
                        {(!store && settings.hours?.standard_hours[0]?.special_instructions) &&
                            <p class='special-instructions'>{settings.hours?.standard_hours[0]?.special_instructions}</p>
                        }
                        <div class='hours-list'>
                            {(settings.hours?.custom_hours_message && !store) &&
                                <div class='inner-wrapper'>
                                    <p class='list-row'>{settings.hours?.custom_hours_message}</p>
                                </div>
                            }
                            {(!settings.hours?.custom_hours_message || store) &&
                                <div class='inner-wrapper'>
                                    {hours.hours && hours.hours.map(function (day, index) {
                                        return (
                                            <p class={`list-row ${index + 1 === currentDay ? 'current-day' : ''}`} key={index} dangerouslySetInnerHTML={{ __html: day }}></p>
                                        )
                                    })
                                    }
                                </div>
                            }
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
}

export default Start;