import React, { useContext } from "react";
import styled from 'styled-components';
import { decode } from 'html-entities';
import { FormattedDate } from 'src/helpers/date';
import { convertToFrench, getMonthName } from 'src/helpers/hours';
import { PrincipalContext } from "src/context";

const Start = ({ settings }) => {
    const [principal, setPrincipal] = useContext(PrincipalContext);

    let hoursRow = settings?.hours?.alternate_hours?.sort((a, b) => {
        return new Date(a.date_formatted) - new Date(b.date_formatted);
    });

    return (
        <div class='holiday-hours'>
            {principal.primaryHours?.alternate_hours && principal.primaryHours?.alternate_hours.map((row, index) => <HoursRow data={row} settings={settings} index={index} />)}
        </div>
    );
}

const HoursRow = ({ data, settings, index }) => {
    const [principal, setPrincipal] = useContext(PrincipalContext);
    const hideFromShorcode = data.hide_from_shortcode;
    let now = new Date();
    let theEnd = new Date(data.date_formatted);
    const dateString = FormattedDate(data.shortcode, 'month-date-year');
    let startTime = data.start_time;
    let endTime = data.end_time;

    if (principal.activeTranslation === 'fr' && !data.closed) {
        startTime = convertToFrench(startTime);
        endTime = convertToFrench(endTime);
    }

    if ((theEnd > now || theEnd.toDateString() === now.toDateString()) && !hideFromShorcode) {
        return (
            <HolidayRow key={index} className='holiday-row'>
                <div className='label-date'>
                    {settings.hours?.alternate_hours[index] && settings.hours?.alternate_hours[index].alternate_hours_label &&
                        <div className="strong">{decode(settings.hours?.alternate_hours[index].alternate_hours_label)}</div>
                    }
                    {dateString &&
                        <div>{dateString}: </div>
                    }
                </div>
                <div className='time'>
                    {data.closed ?
                        <span>{settings?.hours?.standard_hours[0]?.closed_label ? settings?.hours?.standard_hours[0]?.closed_label : 'Closed'}</span>
                        :
                        <span>{`${startTime} - ${endTime}`}</span>
                    }
                </div>
            </HolidayRow>
        )
    } else {
        return null;
    }
}

const HolidayRow = styled.div`
    display: flex;
    justify-content: space-between;
    align-items: end;
    max-width: 550px;
    margin: 0 auto 1rem;
    font-size: .875rem;
    .label-date {
        text-align: left;
        width: 60%;
    }
    .time {
        text-align: right;
    }
    @media (min-width: 768px) {
        font-size: 1rem;
    }
`;

export default Start;