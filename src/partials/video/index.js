import React, { useEffect, useState } from "react";
import { decode } from 'html-entities';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faCirclePlay } from '@fortawesome/free-regular-svg-icons';
import { faXmark } from '@fortawesome/free-solid-svg-icons';

import { VideoLightbox } from './styles';

export const Video = ({ data, closeVideo }) => {

    return (
        <VideoLightbox className='video-lightbox' onClick={closeVideo}>
            <FontAwesomeIcon className='close-lightbox' icon={faXmark} onClick={closeVideo} aria-label="close video" />
            {data.video?.youtube_video &&
                <div class="video" dangerouslySetInnerHTML={{ __html: data.video?.youtube_video }} />
            }
            {data.video?.vimeo_video &&
                <div class="video" dangerouslySetInnerHTML={{ __html: data.video?.vimeo_video }} />
            }
            {data.video?.title &&
                <h2 className='white-txt title'>{decode(data.video.title)}</h2>
            }
            {data.video?.blurb &&
                <p className='white-txt blurb'>{data.video.blurb}</p>
            }
        </VideoLightbox>
    )
}
