import React, { useState, useEffect } from 'react';
import { decode } from 'html-entities';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faEnvelope } from '@fortawesome/free-solid-svg-icons';
import { faFacebookF, faLinkedin } from '@fortawesome/fontawesome-free-brands';
import { faXTwitter } from "@fortawesome/free-brands-svg-icons";

const Start = ({ title, settings }) => {


    return (
        <>
            {settings?.facebook &&
                <ShareButton url={`https://www.facebook.com/sharer/sharer.php?u=${location.href}`} icon={faFacebookF} ariaLabel={"share to Facebook"} />
            }
            {settings?.linkedin &&
                <ShareButton url={`https://www.linkedin.com/shareArticle?url=${location.href}`} icon={faLinkedin} ariaLabel={"share to LinkedIn"} />
            }
            {settings?.x &&
                <ShareButton url={`https://twitter.com/intent/tweet?url=${location.href}`} icon={faXTwitter} ariaLabel={"share to X"} />
            }
            {settings?.email &&
                <ShareButton url={`mailto:?subject=${decode(title)}&body=${location.href}`} icon={faEnvelope} ariaLabel={"send in email"} />
            }
        </>
    )
}

const ShareButton = ({ url, icon, ariaLabel }) => {
    const [copySuccess, setCopySuccess] = useState("")

    useEffect(() => {
        if (copySuccess) {
            setTimeout(() => {
                setCopySuccess("")
            }, 3000)
        }
    }, [copySuccess]);

    function copy() {
        const el = document.createElement("input");
        el.value = window.location.href;
        document.body.appendChild(el);
        el.select();
        document.execCommand("copy");
        document.body.removeChild(el);
        setCopySuccess("Link copied!");
    }

    return (
        <>
            {url ?
                <a className={`share-button`} href={url} target='_blank' aria-label={ariaLabel}>
                    <FontAwesomeIcon icon={icon} />
                </a>
                :
                <div className={`share-button`} onClick={copy} aria-label={ariaLabel}>
                    <FontAwesomeIcon icon={icon} />
                    <span className="copy-success body-copy-txt">{copySuccess}</span>
                </div>

            }
        </>
    )
}
export default Start;