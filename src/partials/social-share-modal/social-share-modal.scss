
$sm_breakpoint: 768px;
$md_breakpoint: 1280px;

$overlay_color: rgba(0, 0, 0, 0.397);

$transition_offset: .15s;
$transition_base_time: .3s;

.social-share-modal__partial {
    display: flex;
    justify-content: center;
    align-items: center;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1501;
    opacity: 0;
    visibility: hidden;
    transition: visibility #{$transition_base_time + $transition_offset}, opacity $transition_base_time;

    .social-share-modal-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: $overlay_color;
        z-index: -1;
    }

    .social-share-modal-content {
        width: 90%;
        padding: 30px 20px 40px 20px;
        max-width: 400px;
        background-color: white;
        border-radius: 5px;
        transform: translateY(15px);
        opacity: 0;
        transition: transform $transition_base_time, opacity $transition_base_time;

        @media (min-width: $sm_breakpoint) {
            padding: 35px 25px 50px 25px;
        }
    }
    
    .social-share-modal-title {
        font-size: 1.5rem;
        margin-bottom: 15px;

        @media (min-width: $sm_breakpoint) {
            font-size: 1.7rem;
        }

        @media (min-width: $md_breakpoint) {
            font-size: 1.875rem;
        }
    }

    .social-share-modal-icons-container {
        display: flex;
        padding: 0 10px;
    }

    .social-share-modal-icon {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 5px;
        border-radius: 50%;
        width: 30px;
        height: 30px;
        margin-right: 12px;

        @media (min-width: $sm_breakpoint) {
            width: 35px;
            height: 35px;
        }

        &:last-of-type {
            margin-right: 0;
        }

        svg {
            width: auto;
            height: 15px;
            z-index: 1;
        }
    }

    .social-share-modal-bottom {
        padding: 0 10px;
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        max-width: 100%;
        margin-top: 20px;

        @media (min-width: $sm_breakpoint) {
            flex-direction: row;
            align-items: center;
            margin-top: 25px;
        }
    }

    .social-share-modal-url-input {
 
        @media (min-width: $sm_breakpoint) {
            margin: 0;
        }
    }

    .social-share-modal-btn {
        padding: 5px 20px 8px 20px;
        border-radius: 20px;
        margin-top: 5px;
        color: white;
        font-weight: 400;

        @media (min-width: $sm_breakpoint) {
            margin-left: 20px;
            margin-top: 0;
        }
    }

    &.is-active {
        opacity: 1;
        visibility: visible;
        transition-delay: $transition_offset;


        .social-share-modal-content {
            transform: translateY(0px);
            opacity: 1;
            transition-delay: $transition_offset;
        }
    }
}