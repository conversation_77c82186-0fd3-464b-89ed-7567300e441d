import React, { useState } from 'react'

import './social-share-modal.scss'

import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faFacebookF } from '@fortawesome/fontawesome-free-brands';
import { faXTwitter, faRedditAlien, faTumblr } from '@fortawesome/free-brands-svg-icons';
import { faEnvelope } from '@fortawesome/free-solid-svg-icons';

const socialIcons = [
    {
        icon: <FontAwesomeIcon className="primary-txt" icon={faFacebookF} size='lg' />,
        url: pylot.socialShareUrls.facebook(),
        ariaLabel: 'link to Facebook'
    },
    {
        icon: <FontAwesomeIcon className="primary-txt" icon={faXTwitter} size='lg' />,
        url: pylot.socialShareUrls.twitter(),
        ariaLabel: 'link to X'
    },
    {
        icon: <FontAwesomeIcon className="primary-txt" icon={faRedditAlien} size='lg' />,
        url: pylot.socialShareUrls.reddit(),
        ariaLabel: 'link to Reddit'
    },
    {
        icon: <FontAwesomeIcon className="primary-txt" icon={faTumblr} size='lg' />,
        url: pylot.socialShareUrls.tumblr(),
        ariaLabel: 'link to Tumblr'
    },
    {
        icon: <FontAwesomeIcon className="primary-txt" icon={faEnvelope} size='lg' />,
        url: pylot.socialShareUrls.email(),
        ariaLabel: 'link to Email'
    },
]

function copyToClipboard(text) {
    var dummy = document.createElement("textarea");
    document.body.appendChild(dummy);
    dummy.value = text;
    dummy.select();
    document.execCommand("copy");
    document?.body?.removeChild(dummy);
}

export default function SocialShareModal({ isVisible = false, closeModal, ...otherProps }) {
    
    const [urlInputText, setUrlInputText] = useState(location.href);

     return (
        <div className={`social-share-modal__partial${isVisible ? ' is-active' : ''}`}>
            <div className="social-share-modal-overlay" onClick={closeModal} />
            <div className="social-share-modal-content">
                <h3 className="social-share-modal-title primary-txt">Share</h3>
                <div className="social-share-modal-icons-container">
                    {socialIcons.map(({ icon, url, ariaLabel }, i) => (
                        <a 
                            className="social-share-modal-icon background-bg"
                            href={url} 
                            rel="noopener nofollow" 
                            target="_blank" 
                            key={url + 'social-icon-modal' + i}
                            aria-label={ariaLabel}
                        >
                            {icon}
                        </a>
                    ))}
                </div>
                
                <div className="social-share-modal-bottom">
                    <input 
                        onChange={(e) => setUrlInputText(e.target.value)}
                        className="social-share-modal-url-input" 
                        type="text" 
                        name="url-text" 
                        value={urlInputText} 
                    />
               
                    <button onClick={() => copyToClipboard(urlInputText)} className="social-share-modal-btn primary-bg">
                        copy
                    </button> 
                </div>
            </div> 
        </div>
    )
}

