import styled from 'styled-components';

const spacer = '4px';

export const BottomMobileBar = styled.div`
    background-color: ${props => props.bgColor};
    color: ${props => props.textColor};
    position: fixed;
    bottom: 0;
    right: 0;
    width: 100%;
    box-sizing: border-box;
    z-index: 1001;
    &:has(#modal) {
        z-index: 999999;
    }
    .nav-items {
        .nav-item {
            a {
                color: ${props => props.textColor};
            }
        }
    }
    .se-widget-button {
        display: flex;
        align-items: center;
        width: 100%;
        color: ${props => props.textColor};
        img {
            max-width: 30px;
            margin-right: .5rem;
        }
    }
`

export const NavItems = styled.div`
    display: flex;
    padding: .5rem calc(.625rem - ${spacer});
    box-sizing: border-box;
    &:has([role=button]) {
        height: 66px;
    }
    .nav-item {
        width: 100%;
        margin: 0 ${spacer};
        .nav-button {
            text-decoration: none;
            display: flex;
            padding: 10px 15px;
        }
        button {
            width: 100%;
        }
        &:has(.icon) a {
            display: flex;
            align-items: center;
            .icon {
                margin-right: 1rem;
            }
        }
    }
`