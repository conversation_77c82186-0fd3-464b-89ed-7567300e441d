import React, { useState, useEffect, useContext } from 'react';
import Base64 from 'base-64';
import GoogleMapReact from 'google-map-react';
import ReactDOMServer from "react-dom/server";
// CONTEXT.
import { SettingsContext } from "src/context";

const Start = ({ mapID }) => {
    return (
        <MapComponent mapID={mapID} />
    );
}

const MapComponent = ({ mapID }) => {
    const [settings, setSettings] = useContext(SettingsContext);
    const mapData = settings?.google_maps?.maps_repeater?.find((map) => map.google_map_id === mapID);
    const position = { lat: mapData.pins ? parseFloat(mapData.pins[0].position.latitude) : '0', lng: mapData.pins ? parseFloat(mapData.pins[0].position.longitude) : '0' };
    const [mapStyles, setMapStyles] = useState(mapData.custom_map_styles ? JSON.parse(mapData.custom_map_styles) : {});
    const [googleMap, setGoogleMap] = useState(null);
    const mapOptions = {
        styles: mapStyles
    }

    useEffect(() => {
        if (mapData.use_site_address) {
            const sitePin = {
                custom_pin: mapData.site_custom_pin ? mapData.site_custom_pin : false,
                position: {
                    latitude: parseFloat(settings.address?.lat),
                    longitude: parseFloat(settings.address?.long)
                },
                label: settings.branding?.site_title
            }
            if (!mapData.pins) {
                mapData.pins = [];
            }
            mapData.pins.push(sitePin);
        }
    }, [mapData])

    const handleApiLoaded = (google) => {
        if (!googleMap) setGoogleMap(google);
        const infowindow = new google.maps.InfoWindow({ content: '', pixelOffset: new google.maps.Size(0, 0) });

        if (mapData.pins?.length > 0) {
            const bounds = new google.maps.LatLngBounds();
            mapData.pins.map((pin, index) => {
                const lat = pin.position.latitude ? parseFloat(pin.position.latitude) : false;
                const lng = pin.position.longitude ? parseFloat(pin.position.longitude) : false;
                if (lat && lng) {
                    bounds.extend(new google.maps.LatLng(lat, lng));
                }
                let marker = new google.maps.Marker({
                    position: { lat: lat, lng: lng },
                    map: google.map,
                    title: pin.label,
                    icon: pin.custom_pin ? {
                        url: `https://${config.domain}${pin.custom_pin.url}`
                    } : ''
                });
                if (pin.label) {
                    marker.addListener('mouseover', () => {
                        console.log(pin.label)
                        const content = ReactDOMServer.renderToString(`${pin.label}`);
                        infowindow.setContent(content);
                        infowindow.open(googleMap, marker);
                    });
                    marker.addListener('mouseout', () => {
                        infowindow.close();
                    });
                }
                return marker;
            });
            google.map.fitBounds(bounds);

            if (mapData.pins.length === 1) {
                google.map.setZoom(14);
            }
        }
        if (mapData.custom_map_styles) {
            setMapStyles(JSON.parse(mapData.custom_map_styles));
        }
    };


    return (
        <GoogleMapReact
            id={mapID}
            mapId={mapID}
            bootstrapURLKeys={{ key: Base64.decode('QUl6YVN5QkhGMUdEQnZMeVcwaUxUbndablNiS1JjSWN4LUZNT3Rn') }}
            defaultCenter={position}
            defaultZoom={15}
            options={mapOptions}
            disableDefaultUI
            zoomControl={true}
            onGoogleApiLoaded={({ ...google }) => handleApiLoaded(google)}
        ></GoogleMapReact>
    )
}

export default Start;