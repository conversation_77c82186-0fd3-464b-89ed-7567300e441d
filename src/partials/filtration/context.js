import React, { useState, createContext, useLayoutEffect, useEffect } from 'react';

// HOOKS.
import { useQuery } from 'src/hooks/query';

export const FiltrationContext = createContext();
export const FiltrationProvider = (props) => {
    const query = useQuery();

    // USED FOR FILTERING, NOT STORING DATA.
    const [ filtration, setFiltration ] = useState({
        categories: query.get('c')?.split(',') || false,
        category: query.get('c') || false,
        term: false, 
        toggleCategories: props.toggleCategories, 
        toggleSearch: props.toggleSearch, 
        mapView: false
    });

    return (
        <FiltrationContext.Provider value={[ filtration, setFiltration ]}>
            {props.children}
        </FiltrationContext.Provider>
    );
}