import React, { Fragment, useCallback, useEffect, useContext, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faMap, faFilePdf } from "@fortawesome/free-regular-svg-icons";
import { decode } from 'html-entities';
import { useInView } from 'react-intersection-observer';

// CONTEXT.
import { SettingsContext } from 'src/context';
import { FiltrationProvider, FiltrationContext } from "./context";

// HELPERS
import Clicker from 'src/helpers/clicker';
// HOOKS 
import { addScriptDefer } from 'src/hooks';
// PARTIALS.
import Map from 'src/partials/map';
import MappedInFull from 'src/partials/map/mapped-in-full';
import MapAcquireDigital from 'src/partials/map/acquire-digital';

import './index.scss';
import { CategoryListItem, CategoryListItemText, Checkbox, CheckboxWrapper, Arrow, Toggles, PrintableButton, SearchInput, SearchSVG, MapPrintIconContainer, ApplyButton } from './styles';


const Start = (props) => {
    return (
        <FiltrationProvider toggleCategories={props?.toggleCategories} toggleSearch={props?.toggleSearch}>
            <Container props={props} />
        </FiltrationProvider>
    );
};

const Container = ({ props }) => {
    const [settings, setSettings] = useContext(SettingsContext);
    const [filtration, setFiltration] = useContext(FiltrationContext);
    const [number] = useState(Math.floor(Math.random() * 10000));
    const [loadMap, setLoadMap] = useState(false);
    const { ref, inView } = useInView({
        triggerOnce: true,
        fallbackInView: true
    });
    if (props?.addStoreMap && settings?.store?.store_map_type === 'mapplic' && settings?.mapplic_html) {
        addScriptDefer(`https://code.jquery.com/jquery-3.7.1.min.js`);
    }
    useEffect(() => {
        let tick = setInterval(function () {
            if (window.jQuery) {
                setLoadMap(true);
                clearInterval(tick);
            }
        }, 1000);
    }, [])

    const Reset = useCallback((e) => {
        document?.getElementById('filter-search')?.reset();
        setFiltration({ ...filtration, ...{ term: null } })
    }, []);

    return (
        <>
            <div ref={ref} class={`filtration grid-container fbox fcol ${props?.restrictModuleWidth ? 'restricted' : ''} ${props?.filterStyle ? props?.filterStyle : ''} ${props?.fullwidth ? 'full' : ''} ${props?.bleedBackgroundClass ? props?.bleedBackgroundClass : ''}`}>
                {inView ? <>
                    {(props?.moduleTitle && (props?.filterStyle === 'dropdown' || props?.filterStyle?.length === 0)) &&
                        <div class={`module-title one ${props.titleAlignment}`}>
                            <h2>{decode(props?.moduleTitle)}</h2>
                        </div>
                    }
                    {(props?.filterStyle === 'dropdown' && props?.blurb) &&
                        <div className='blurb' dangerouslySetInnerHTML={{ __html: props?.blurb }} />
                    }
                    <FilterBar categories={props?.categories} props={props} number={number} settings={settings} />

                    {!filtration?.mapView &&
                        <div class="bulk-container fbox fcol gap">
                            {props?.filterStyle == 'list' && <CategoryList props={props} categories={props?.categories} number={number} />}
                            {React?.Children?.map(props?.children, child => React?.cloneElement(child, { filtration, Reset }))}
                        </div>
                    }

                    {(loadMap && settings?.store?.store_map_type === 'mapplic' && filtration?.mapView && settings?.mapplic_html) &&
                        <Map id={settings?.mapplic_id} html={settings?.mapplic_html} />
                    }

                    {(settings?.store?.store_map_type === 'mappedin' && filtration?.mapView) &&
                        <div class='mappedin-container'>
                            <MappedInFull settings={settings} />
                        </div>
                    }
                    {(settings.store?.store_map_type === 'acquire-digital' && filtration?.mapView) &&
                        <div class='aquire-digital-map-module'>
                            <MapAcquireDigital />
                        </div>
                    }
                </> : null}
            </div>
            {(settings.store?.store_map_type === 'iframe' && settings.store?.iframe_url && filtration?.mapView) &&
                <div class='store-iframe-container'>
                    <iframe title={'store map'} src={settings.store?.iframe_url} height={`750px`} width="100%" />
                </div>
            }
        </>
    );
};

const FilterBar = ({ categories, props, number, settings }) => {
    const [filtration, setFiltration] = useContext(FiltrationContext);

    return (
        <div class={`filterbar grid-x ${props?.filterStyle ? props?.filterStyle : ''} ${(filtration?.mapView && settings?.store?.store_map_type === 'mappedin') ? 'hide-filters-mobile' : 'show-filters'}`}>
            {(props?.moduleTitle && props?.filterStyle === 'inline-title') &&
                <div class='module-title'><h2>{decode(props?.moduleTitle)}</h2></div>
            }
            {props?.filterStyle !== 'list' &&
                <CategoryDropdown props={props} categories={categories} number={number} />
            }
            {filtration?.toggleSearch && <Search props={props} number={number} />}
        </div>
    );
};

const CategoryDropdown = ({ categories, number }) => {
    const navigate = useNavigate();
    const [settings, setSettings] = useContext(SettingsContext);
    const [filtration, setFiltration] = useContext(FiltrationContext);

    const Filtering = (e) => {
        e?.preventDefault();
        var selected = document?.getElementById(`filter-categories-${number}`)?.value;
        if (selected == "viewall") {
            setFiltration({ ...filtration, ...{ category: false } });
            navigate({ search: '' });
        } else {
            setFiltration({ ...filtration, ...{ category: selected } });
            navigate({ search: `c=${selected}` });
        }
    };

    return (
        <div class="filterbar-categories cell small-12 medium-shrink large-3">
            {filtration?.toggleCategories &&
                <select id={`filter-categories-${number}`} onChange={Filtering} value={filtration.category || false}>
                    <option value="viewall" aria-label='view all categories'>{settings?.mvk_theme_config?.labels?.filter_view_all_dropdown_text ? settings?.mvk_theme_config?.labels?.filter_view_all_dropdown_text : 'View All Categories'}</option>
                    {categories && categories?.map(cat => cat?.slug ? <option value={cat?.slug} aria-label={`filter by ${decode(cat?.name)}`}>{decode(cat?.name)}</option> : null)}
                </select>
            }
        </div>
    );
}

const CategoryList = ({ props, categories, number }) => {
    // const navigate = useNavigate(); switching from useNavigate to history.pushState because it won't be caught in the scrollTop we have in app.js
    const [settings, setSettings] = useContext(SettingsContext);
    const [filtration, setFiltration] = useContext(FiltrationContext);

    // Filtering with "Categories" to enable multiple filters to be strung together
    const setListFilter = (e, selectedCategory) => {
        e?.preventDefault();

        if (selectedCategory === 'viewall') {
            setFiltration({ ...filtration, ...{ categories: [] } });
            var href = window.location.href;
            var url = href.split('?c=');
            window.history.pushState({}, '', url[0]);
            return;
        }

        // No categories currently selected
        if (!filtration?.categories?.length) {
            let selectedCategories = [];
            selectedCategories?.push(selectedCategory);
            let parent = categories?.find((cat) => cat.slug === selectedCategory);

            if (parent && parent.children?.length > 0) {
                parent?.children?.forEach((child) => {
                    selectedCategories?.push(child.slug);
                })
            }
            setFiltration({ ...filtration, ...{ categories: selectedCategories } });
            window.history.pushState({}, '', `?c=${selectedCategories}`);
            return;
        }

        // Clicked Category is already selected
        let isCategoryAlreadyPresent = filtration?.categories?.some((category) => category === selectedCategory);
        if (isCategoryAlreadyPresent) {
            let updatedCategories = filtration?.categories?.filter((category) => category !== selectedCategory);
            let parent = categories?.find((cat) => cat.slug === selectedCategory);

            if (parent && parent.children?.length > 0) {
                parent?.children?.forEach((child) => {
                    const index = updatedCategories?.indexOf(child.slug);
                    if (index > -1) {
                        updatedCategories?.splice(index, 1);
                    }
                })
            }
            setFiltration({ ...filtration, ...{ categories: updatedCategories } });
            window.history.pushState({}, '', `?c=${updatedCategories}`);
            return;
        }

        // New category clicked and not the first one
        let selectedCategories = filtration.categories;
        selectedCategories?.push(selectedCategory);
        let parent = categories?.find((cat) => cat.slug === selectedCategory);

        if (parent && parent.children?.length > 0) {
            parent?.children?.forEach((child) => {
                selectedCategories?.push(child.slug);
            })
        }

        setFiltration({ ...filtration, ...{ categories: selectedCategories } });
        window.history.pushState({}, '', `?c=${selectedCategories}`);
    }

    function closeCategories(e) {
        e?.preventDefault();
        const filterList = document?.getElementById(`filter-list-${number}`);
        filterList?.classList?.remove('active');
    }
    return (
        <div class="filterbar-categories" id={`filter-list-${number}`}>
            {(filtration?.toggleSearch && props?.filterStyle === 'list') &&
                <SearchForm props={props} number={number + 1} />
            }
            <div class='close' onClick={closeCategories}></div>
            {filtration?.toggleCategories &&
                <ul>
                    <CategoryListItem
                        onClick={(e) => setListFilter(e, 'viewall')}
                        className={!filtration?.categories?.length ? 'active' : null}
                        data-slug="viewall"
                        aria-label="view all categories"
                    >
                        <div className='group'>
                            <CheckboxWrapper>
                                <span class="sr-only">Category Filter</span>
                                <Checkbox type="checkbox" name={`filter-list-${number}-checkbox-0`} />
                            </CheckboxWrapper>

                            <CategoryListItemText>
                                {`${settings?.mvk_theme_config?.labels?.filter_view_all_list_text ? settings?.mvk_theme_config?.labels?.filter_view_all_list_text : 'All'} (${props.totalQuantity})`}
                            </CategoryListItemText>
                        </div>
                    </CategoryListItem>

                    {categories && categories.map(cat => cat.slug ? (
                        <Fragment>
                            <CategoryListItem onClick={(e) => setListFilter(e, cat.slug)} className={filtration?.categories && filtration.categories.includes(cat.slug) ? 'active' : null} data-slug={cat.slug} aria-label={`filter by ${decode(cat.name)}`}>
                                <div className='group'>
                                    <CheckboxWrapper className={cat.children?.length > 0 ? 'with-children' : ''}>
                                        <span class="sr-only">Category Filter</span>
                                        <Checkbox onClick={() => setListFilter(cat.slug)} type="checkbox" name={`filter-list-${number}-checkbox-${cat.slug}`} />
                                    </CheckboxWrapper>

                                    <CategoryListItemText>
                                        {`${decode(cat.name)} (${cat.count})`}
                                    </CategoryListItemText>
                                </div>
                                {cat.children?.length > 0 ? <Arrow className='arrow' /> : null}
                            </CategoryListItem>
                            {cat.children?.length > 0 &&
                                <div id={`${cat.slug}-children`} className={`children ${filtration?.categories && filtration.categories.includes(cat.slug) ? 'show' : 'hide'}`}>
                                    {cat.children.map(child => (
                                        <CategoryListItem onClick={(e) => setListFilter(e, child.slug)} className={filtration?.categories && filtration.categories.includes(cat.slug) && filtration.categories.includes(child.slug) ? 'active' : null} data-slug={child.slug} aria-label={`filter by ${decode(cat.name)}`}>
                                            <div className='group'>
                                                <CheckboxWrapper>
                                                    <span class="sr-only">Category Filter</span>
                                                    <Checkbox onClick={() => setListFilter(child.slug)} type="checkbox" name={`filter-list-${number}-checkbox-${child.slug}`} />
                                                </CheckboxWrapper>

                                                <CategoryListItemText>
                                                    {`${decode(child.name)} (${child.count})`}
                                                </CategoryListItemText>
                                            </div>
                                        </CategoryListItem>
                                    ))}
                                </div>
                            }
                        </Fragment>
                    ) : null)}
                </ul>
            }
            <ApplyButton
                className='apply-button'
                onClick={closeCategories}
                backgroundColor={settings?.design?.colors?.primary_color}
                borderRadius={(settings.mvk_theme_config.other?.enable_border_radius) ? `${settings.mvk_theme_config.other?.border_radius_size}px` : "0px"}
                aria-label="apply changes"
            >
                {settings?.mvk_theme_config?.labels?.filter_apply_button_text || 'Apply'}
            </ApplyButton>
        </div>
    );
}

const Search = ({ props, number }) => {
    const [settings, setSettings] = useContext(SettingsContext);
    const [filtration, setFiltration] = useContext(FiltrationContext);

    const setListView = () => {
        setFiltration({ ...filtration, ...{ mapView: false } })
    }
    const setMapView = () => {
        setFiltration({ ...filtration, ...{ mapView: true } })
    }

    function openCategories() {
        const filterList = document?.getElementById(`filter-list-${number}`);
        filterList?.classList?.add('active');
    }
    return (
        <div class={`filterbar-search-icons cell small-12 ${props?.filterStyle !== 'inline-title' ? 'medium-shrink' : ''} ${props?.addStoreMap ? 'with-toggles' : 'no-toggles'}`}>
            <div class='search-wrap'>
                {(props?.moduleTitle && props?.filterStyle === 'list') &&
                    <div class='module-title'><h2 class={props?.filterColor === '#fff' ? 'white-txt' : 'primary-txt'}>{decode(props?.moduleTitle)}</h2></div>
                }
                {(filtration?.toggleSearch || props.toggleSearch) &&
                    <SearchForm props={props} number={number} />
                }
                {(filtration?.toggleCategories && props?.filterStyle === 'list') &&
                    <div class='mobile-filter-trigger' onClick={openCategories}>{settings?.mvk_theme_config?.labels?.filter_view_all_dropdown_text ? settings?.mvk_theme_config?.labels?.filter_view_all_dropdown_text : 'View All Categories'}<div class='arrow'></div></div>
                }
                {!props?.addStoreMap &&
                    <Icons props={props} />
                }
            </div>
            {props?.addStoreMap &&
                <div className='toggle-container'>
                    <div className='inner-wrapper'>
                        <Toggles
                            className='toggles'
                            primaryColor={settings?.design?.colors?.primary_color}
                            bodyCopyColor={settings?.design?.colors?.body_copy_color}
                        >
                            <span className={`list-view ${!filtration?.mapView ? 'active' : ''}`} onClick={setListView} aria-label="show list view" aria-selected={!filtration.mapView}>{props?.listViewLabel ? props?.listViewLabel : 'List View'}</span>
                            <span className={`map-view ${filtration?.mapView ? 'active' : ''}`} onClick={setMapView} aria-label="show map view" aria-selected={filtration.mapView}>{props?.mapViewLabel ? props?.mapViewLabel : 'Map View'}</span>
                        </Toggles>
                        {props?.printableDirectory &&
                            <div class='printable-directory-wrapper'>
                                <PrintableButton
                                    type='anchor'
                                    url={props?.printableDirectory}
                                    target="_blank"
                                    className={'printable-directory'}
                                    primaryColor={settings?.design?.colors?.primary_color}
                                    bodyCopyColor={settings?.design?.colors?.body_copy_color}
                                    title={'printable-directory'}
                                    alt={'printable-directory'}
                                >
                                    {settings?.center_info?.printable_directory_label ? settings?.center_info?.printable_directory_label : 'Printable Directory'}
                                </PrintableButton>
                            </div>
                        }
                    </div>
                </div>
            }
        </div>

    );
}

const SearchForm = ({ props, number }) => {
    const navigate = useNavigate();
    const [settings, setSettings] = useContext(SettingsContext);
    const [filtration, setFiltration] = useContext(FiltrationContext);

    const Submit = (e) => {
        e?.preventDefault();
        var term = document?.getElementById(`filter-search-${number}`)?.value;
        if (term && term != null) {
            setFiltration({ ...filtration, ...{ term: term } });
        } else {
            setFiltration({ ...filtration, ...{ term: null } });
        }
    };

    return (
        <div class="search-form-container flex-container flex-dir-row">
            <form id="filter-search" method="post" action="#" autocomplete="off" onSubmit={Submit} onChange={Submit} class="flex-child-grow" role="search" aria-label="posts">
                <label htmlFor={`filter-search-${number}`}>
                    <SearchInput filterColor={props?.filterColor ? props?.filterColor : settings?.design?.colors?.primary_color} id={`filter-search-${number}`} type="text" className="search" value={filtration?.term ? filtration?.term : ''} placeholder={props?.searchPlaceholder ? props?.searchPlaceholder : 'Search Filter'} />
                </label>
            </form>
            <SearchSVG filterColor={props?.filterColor ? props?.filterColor : settings?.design?.colors?.primary_color} version="1.1" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" aria-label="search icon">
                <g id="info" />
                <g id="icons">
                    <path d="M22.4,19.6l-4.8-4.8c0.9-1.4,1.4-3,1.4-4.8c0-5-4-9-9-9s-9,4-9,9s4,9,9,9c1.8,0,3.4-0.5,4.8-1.4l4.8,4.8   c0.4,0.4,0.9,0.6,1.4,0.6c1.1,0,2-0.9,2-2C23,20.4,22.8,19.9,22.4,19.6z M5,10c0-2.8,2.2-5,5-5s5,2.2,5,5s-2.2,5-5,5S5,12.8,5,10z" id="icon-search" />
                </g>
            </SearchSVG>
        </div>
    )
}

const Icons = ({ props }) => {
    const [settings, setSettings] = useContext(SettingsContext);

    if (props?.interactiveMap || props?.printableDirectory) {
        return (
            <MapPrintIconContainer
                className='map-print-icons-container'
                filterColor={props?.filterColor ? props?.filterColor : settings?.design?.colors?.primary_color}
            >
                {props?.interactiveMap &&
                    <div class='interactive-map-wrapper'>
                        <Clicker type='anchor' url={props?.interactiveMap} target="_blank" class={'interactive-map'} title={'interactive-map'} alt={'interactive-map'}>
                            <FontAwesomeIcon icon={faMap} className="icon" aria-label='map icon' />
                        </Clicker>
                    </div>
                }
                {props?.printableDirectory &&
                    <div class='printable-directory-wrapper'>
                        <PrintableButton
                            type='anchor'
                            url={props?.printableDirectory}
                            target="_blank"
                            className={'printable-directory'}
                            primaryColor={settings?.design?.colors?.primary_color}
                            bodyCopyColor={settings?.design?.colors?.body_copy_color}
                            title={'printable-directory'}
                            alt={'printable-directory'}
                        >
                            {settings?.center_info?.printable_directory_label ? settings?.center_info?.printable_directory_label : 'Printable Directory'}
                        </PrintableButton>
                    </div>
                }
            </MapPrintIconContainer>
        );
    } else {
        return (
            null
        );
    }
};

export default Start;
