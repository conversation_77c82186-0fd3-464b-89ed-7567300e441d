/*******************************************************************************************************
   Copyright 2020 - Calise Partners LLC d/b/a Imaginuity. All rights are reserved. 
   Reproduction or transmission in whole or in part, in any form or by any means, electronic, mechanical 
or otherwise, is prohibited without the prior written consent of Calise Partners LLC d/b/a Imaginuity.
********************************************************************************************************
   Project : 10618 Maverick Platform Development
   Author : Imaginuity Developers (<PERSON>, <PERSON>)
   Description : 
   Creation Date : Wed, Jan 20, 2021
   Documentation : [documentation url - point to default wiki for project if no specific documentation for script]
 ********************************************************************************************************/

.filterbar {
    // margin: 0px auto;
    // max-width: 1100px;
    &.inline-title {
        align-items: center;
        justify-content: space-between;
        .filterbar-categories {
            width: auto;
            select {
                border: 0;
                background: none;
                width: auto;
                font-size: 1rem;
            }
        }
    }
    @media screen and (min-width: 640px) {
        justify-content: space-between;
    }

    &.hide-filters-mobile {
        .search-form-container,
        .mobile-filter-trigger {
            display: none !important;
        }
    }
}

.filterbar-categories {
    @media screen and (max-width: 639px) {
        margin-bottom: 1rem;
    }
    select {
        font-family: inherit;
        border-radius: 0;
    }
    ul {
        list-style: none;
        margin: 0;
        padding: 0;
        li {
            margin-bottom: 1rem;
            cursor: pointer;
            &.active,
            &:hover {
                // font-weight: bold;
                text-shadow: 1px 0 0 currentColor;
            }
            &.active {
                label {
                    &:not(.with-children):after {
                        content: "";
                        position: absolute;
                        top: 45%;
                        left: 50%;
                        transform: translate(-50%, -50%) rotate(45deg);
                        height: 10px;
                        width: 4px;
                        border-bottom: 2px solid #fff;
                        border-right: 2px solid #fff;
                    }

                    &.with-children:after {
                        content: "";
                        position: absolute;
                        background: #fff;
                        height: 2px;
                        width: calc(100% - 2px);
                        top: 50%;
                        left: 50%;
                        transform: translate(-50%, -50%);
                    }
                }
            }
        }
    }
}

.filterbar-search-icons {
    display: flex;
    flex-flow: row nowrap;
    justify-content: flex-end;
    .search-form-container {
        @media screen and (max-width: 639px) {
            margin-bottom: 1rem;
            width: 100%;
        }

        @media screen and (min-width: 640px) {
            min-width: 15.625rem;
        }

        @media screen and (min-width: 992px) {
            min-width: 20.3125rem;
        }
        svg {
            // margin: 0px 10px;
            margin-left: 0.625rem;
            width: 1.875rem;
            min-width: 1.875rem;
            height: 1.875rem;
            min-height: 1.875rem;

            &:hover {
                cursor: pointer;
            }

            @media screen and (min-width: 640px) {
                margin: 0px 0.625rem;
            }
        }
    }
}

.map-print-icons-container {
    display: flex;
    flex-flow: row nowrap;
    justify-content: flex-end;
    & > * {
        margin-left: 0.5rem;
    }

    svg {
        width: 1.875rem;
        min-width: 1.875rem;
        height: 1.875rem;
        min-height: 1.875rem;
    }
    .text-link {
        display: inline-block;
        &::after {
            content: ">";
            margin: 0 0.25rem;
        }
        &:hover {
            font-weight: bold;
            text-shadow: 1px 0 0 currentColor;
        }
    }
}

// new filter styles for list style
.filtration {
    .module-title {
        margin-bottom: 1rem;
    }
    &.full {
        .filterbar {
            max-width: 1200px;
            margin: auto;
            padding: 0 1rem;
        }
    }
    &.list {
        position: relative;
        padding-bottom: 2rem;
        .filterbar {
            .filterbar-search-icons {
                flex-wrap: wrap;
                width: 100%;
                justify-content: space-between;
                .search-form-container {
                    width: 100%;
                }
                .search-wrap {
                    display: flex;
                    flex-wrap: wrap;
                    width: 100%;
                }
                @media (min-width: 768px) {
                    flex-wrap: nowrap;

                    &.with-toggles .search-wrap {
                        width: auto;
                    }
                    &.no-toggles .search-wrap {
                        justify-content: space-between;
                        .map-print-icons-container {
                            width: auto;
                        }
                    }
                    .module-title {
                        margin-bottom: 0;
                    }
                }
                .mobile-filter-trigger {
                    padding: 0.75rem 1rem;
                    margin: 1rem 0 2rem;
                    width: 100%;
                    background: #f0eeed;
                    display: flex;
                    justify-content: center;
                    cursor: pointer;
                    .arrow {
                        width: 0.75rem;
                        height: 0.75rem;
                        border-top: 3px solid;
                        border-right: 3px solid;
                        transform: rotate(135deg);
                        border-radius: 1px;
                        margin: 0 1rem;
                    }
                    @media (min-width: 768px) {
                        display: none;
                    }
                }
                .map-print-icons-container {
                    justify-content: space-between;
                    width: 100%;
                    a {
                        color: inherit;
                    }
                    @media (min-width: 768px) {
                        justify-content: flex-end;
                        align-items: center;
                        & > div {
                            margin: 0.5rem;
                        }
                    }
                }
            }
        }
        @media (max-width: 767px) {
            .filterbar-categories {
                position: fixed;
                background: #fff;
                left: 0;
                right: 0;
                top: 0;
                bottom: 0;
                z-index: 999999;
                padding: 1.5rem;
                display: none;
                overflow: auto;
                margin: 0;
                &.active {
                    display: block;
                    .search-form-container {
                        display: none;
                    }
                }
                .close:after {
                    position: absolute;
                    content: "\2715";
                    right: 2.25rem;
                    top: 1rem;
                    font-size: 1.5rem;
                    color: #000;
                    cursor: pointer;
                }
            }
        }
        @media (min-width: 768px) {
            .filterbar-search-icons {
                margin-bottom: 2rem;
                .search-form-container {
                    display: none;
                }
            }
            .bulk-container {
                display: flex;
                justify-content: space-between;
                flex-flow: row;
                .filterbar-categories {
                    width: 33%;
                    @media (min-width: 1024px) {
                        width: 20%;
                    }
                    ul {
                        padding: 0 0.5rem 0 0;
                    }
                    .search-form-container {
                        margin: 1rem 0;
                        svg {
                            // margin: 0px 10px;
                            margin-left: 0.625rem;
                            width: 1.875rem;
                            min-width: 1.875rem;
                            height: 1.875rem;
                            min-height: 1.875rem;

                            &:hover {
                                cursor: pointer;
                            }

                            @media screen and (min-width: 640px) {
                                margin: 0px 0.625rem;
                            }
                        }
                    }
                }
                .apply-button {
                    display: none;
                }
                .store-directory-module, .store-directory__simple-columns {
                    width: 66%;
                     @media (min-width: 1024px) {
                        width: 80%;
                    }
                }
            }
        }
    }
    &.dropdown {
        .filterbar {
            .filterbar-search-icons {
                &.with-toggles {
                    flex-flow: wrap;
                }
                .search-wrap {
                    display: flex;
                    width: 100%;
                    flex-wrap: wrap;
                    justify-content: flex-end;
                    .map-print-icons-container {
                        justify-content: flex-end;
                        align-items: center;
                    }
                    @media (min-width: 768px) {
                        display: flex;
                        flex-wrap: nowrap;
                        width: auto;
                        .map-print-icons-container {
                            width: auto;
                            flex-direction: unset;
                        }
                    }
                }
            }
        }
    }
    .toggle-container {
        width: 100%;
        .inner-wrapper {
            display: flex;
            justify-content: space-between;
            width: 100%;
            .toggles {
                margin-right: 2rem;
                display: flex;
            }
        }
        @media (min-width: 768px) {
            display: flex;
            align-items: center;
            width: auto;
        }
    }
}
.mappedin-container {
    position: relative;
    min-height: 550px;

    @media (min-width: 768px) {
        min-height: 1000px;
    }
}
