import React from 'react';
import styled from 'styled-components';
const Clicker = React.lazy(() => import('src/helpers/clicker'));

export const CategoryListItem = styled.li`
    display: flex;
    align-items: center;
    justify-content: space-between;
    .group {
        display: flex;
    }
`
export const Arrow = styled.span`
    border: solid black;
    border-width: 0 3px 3px 0;
    display: inline-block;
    padding: 3px;
    transform: rotate(45deg);
    -webkit-transform: rotate(45deg);
    transition: .2s; 
    ${CategoryListItem}.active & {
        transform: rotate(-135deg);
    }
`
export const CategoryListItemText = styled.span`
    display: inline-block;
    margin-bottom: -1px; // adjust for line-height
`
// TODO | Could move these into a global "CheckBox" partial component??
export const CheckboxWrapper = styled.label`
    position: relative;
    display: inline-block;
    border: 1px solid;
    height: 16px;
    width: 16px;
    min-width: 16px;
    background: transparent;

    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        opacity: 0;
        background: ${props => props?.theme?.colors?.bodyCopy || ''};
        transition: opacity .15s ease;
    }

    ${CategoryListItem} & {
        margin-right: 10px;
    }

    ${CategoryListItem}:hover &::before {
        opacity: .5;
    }

    ${CategoryListItem}.active &::before {
        opacity: 1;
    }

`

export const Checkbox = styled.input`
    display: none;
`

// Styled Toggles
export const Toggles = styled.div`
    border: 1px solid ${props => props?.bodyCopyColor};
    cursor: pointer;
    span {
        position: relative;
        padding: .5rem 1rem;
        display: flex;
        align-items: center;
        &:before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                z-index: -1;
                transition: .2s;
        }
        &.active {
            color: #fff;
            &:before {
                background-color: ${props => props?.primaryColor};
            }
        }
    }
`;

// Styled Printable Button
export const PrintableButton = styled(Clicker)`
    color: ${props => props?.bodyCopyColor};
    border: 1px solid ${props => props?.bodyCopyColor};
    padding: .5rem 1rem;
    display: block;
    transition: .2s;
    &:hover {
        background-color: ${props => props?.primaryColor};
        color: #fff !important;
    }
`;

// Search Form Input
export const SearchInput = styled.input`
    border-color: ${props => props?.filterColor} !important;
    color: ${props => props?.filterColor};
    &::placeholder {
        color: ${props => props?.filterColor};
    }
`;

// Search Icon
export const SearchSVG = styled.svg`
    fill: ${props => props?.filterColor};
`;

// Map, Printable Directory Icons
export const MapPrintIconContainer = styled.div`
    .interactive-map, .printable-directory {
        color: ${props => props?.filterColor};
    }
`;

// ApplyButton
export const ApplyButton = styled.button`
    border-radius: ${props => props.borderRadius};
    background-color: ${props => props.backgroundColor};
    border: none;
    color: #fff;
`;