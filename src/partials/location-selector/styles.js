import styled from 'styled-components'

const desktopWidth = '225px';
const spacer = '4px';

export const LocationSelector = styled.div`
    color: ${props => props.textColor};
    @media (min-width: 1200px) {
        min-width: ${desktopWidth};
        height: 35px;
        margin-top: -5px;
        position: relative;
        .location-selector {
            z-index: 999;
            background-color: ${props => props.bgColor};
        }
        &.with-bg-image {
            .location-selector {
                background-image: url(${props => props.bgImage});
                background-position: center center;
                background-size: cover;
            }
        }
    }
  
`
export const Dropdown = styled.div`
    .current-location {
        display: flex;
        align-items: center;
        position: relative;
        padding: 1rem 2rem 1rem calc(.625rem + ${spacer});
        box-sizing: border-box;
        cursor: pointer;
        @media (max-width: 1199px) {
            height: 56px;
        }
        .heading {
            margin: 0;
            line-height: 1;
        }
        .caret, .icon {
            height: 1rem;
            object-fit: contain;
        }
        .icon {
            margin-right: 1rem;
            width: 1.5rem;
        }
        .caret {
            position: absolute;
            right: calc(0.625rem + ${spacer});
            transition: .3s;
        }
    }
    &.open {
        .current-location .caret {
            transform: scale(-1);
        }
    }
    .location-list {
        max-height: 0;
        overflow: hidden;
        background-color: ${props => props.bgColor};
        color: ${props => props.textColor};
        box-shadow: -1px -1px 5px rgba(0,0,0, .5);
        transition: .3s;
        .list-inner {
            position: relative;
            max-height: 44vh;
            overflow: auto;
            scroll-behavior: smooth;
            & > div:not(.single-location) {
                display: none;
            }
            #loading {
                position: absolute;
                left: 50%;
                top: 50%;
                transform: translate(-50%, -50%);
            }
        }
        a {
            color: ${props => props.textColor};
        }
        &.open {
            max-height: 100vh;
        }
    }
    @media (min-width: 768px) {
        .location-list {
            display: flex;
            .list-wrapper {
                width: 50%;
                max-height: 100vh;
            }
            .list-inner {
                max-height: 75vh;
            }
            &.open {
                max-height: 75vh;
            }
            &:has(.location-search) {
                .list-inner {
                    max-height: calc(75vh - 48px);
                }
            }
        }
    }
    @media (min-width: 1200px) {
        display: flex;
        flex-wrap: wrap;
        min-width: ${desktopWidth};
        .location-list {
            position: absolute;
            right: 0;
            top: 40px;
            width: 1100px;
            box-shadow: 1px 1px 6px rgba(0,0,0,.3);
            z-index: 999;
            .list-inner {
                max-height: 550px;
            }
            &:has(.location-search) {
                .list-inner {
                    max-height: 502px;
                }
            }
            &.open {
                max-height: 550px;
            }
        }
        .current-location {
            min-width: ${desktopWidth};
            box-sizing: border-box;
            padding: 0.5rem 2.75rem .5rem .5rem;
            min-height: 40px;
            .caret {
                transform: scale(-1);
            }
        }
        &.open {
            .current-location .caret {
                transform: scale(1);
            }
        }
    }
    
`
export const SingleLocation = styled.div`
    padding: 1rem;
    position: relative;
    @media (min-width: 1200px) {
        padding: 1.5rem 1.5rem 1rem 3.25rem;
    }
    &.focused {
        background-color: ${props => props.bgColor};
        color: ${props => props.textColor};
        a {
            color: ${props => props.textColor};
            text-decoration: underline;
        }
        @media (min-width: 1024px) {
            display: flex;
            justify-content: space-between;
            align-items: center;
            .content-wrapper {
                width: 50%;
            }
        }
    }
    &:not(.focused) {
        cursor: pointer;
    }
    .content-wrapper {
        > * {
            margin-bottom: .5rem;
            display: block;
        }
        .flag {
            padding: .5rem 1rem .5rem 0;
            background-color: ${props => props.flagBgColor};
            color: ${props => props.flagTextColor};
            display: inline-block;
            position: relative;
            &:before {
                content: '';
                position: absolute;
                right: 100%;
                top: 0;
                height: 100%;
                width: 100%;
                background-color: ${props => props.flagBgColor};
            }
            h6 {
                margin: 0;
                font-size: ${props => props.flagSizeSmall}rem !important;
                @media (min-width: 768px) {
                    font-size: ${props => props.flagSizeLarge}rem !important;
                }
            }
        }
        a, p {
            font-size: ${props => props.txtSizeSmall}rem !important;
            @media (min-width: 1200px) {
                font-size: ${props => props.txtSizeLarge}rem !important;
            }   
        }
    }
    .special-icon {
        max-height: 1rem;
        max-width: 1.5rem;
        object-fit: contain;
        margin-left: 1rem;
    }
    .button-wrapper {
        margin-bottom: .25rem;
        a {
            text-decoration: none;
        }
    }
    .heading {
        position: relative;
        display: flex;
        h4, h6 {
            font-weight: bold;
            margin: 0;
        } 
    }
    .quick-links {
        margin: 1rem 0 1.5rem;
        @media (min-width: 1024px) {
            margin: 1rem 0;
        }
        a {
            margin-right: 1rem;
            padding: 0;
            width: auto;
            display: initial;
        }
    }
    .address, .distance-hours, .todays-hours {
        font-weight: normal !important;
    }
`

export const LocationMap = styled.div`
    .map-container, .google-map {
        height: 250px;
    }
    @media (min-width: 768px) {
        width: 50%;
        order: 2;
        .map-container, .google-map {
            height: 75vh;
        }
    }
    @media (min-width: 1200px) {
        .map-container, .google-map {
            height: 550px;
        }
    }
`

export const MapContainer = styled.div`
    .yNHHyP-marker-view svg {
        fill: ${props => props.primaryColor};
    }
    .gm-style-iw-chr {
        .gm-style-iw-ch {
            display: none;
        }
        button {
            position: absolute !important;
            right: 0;
            width: 30px !important;
            height: 30px !important;
            span {
                margin: .25rem !important;
            }
        }
    }
`

export const InfoWindow = styled.div`
    padding: 1rem 1rem .5rem .5rem;
    min-width: 140px;
    .title {
        font-weight: bold;
    }
    .content-wrapper {
        a {
            text-decoration: underline;
        }
        > * {
            display: block;
            margin-bottom: .5rem;
            &:not(.title) {
                font-size: .875rem !important;
            }
        }
    } 
    a.location-link {
        margin: 1.5rem 0 0;
        padding: 10px 40px;
        text-align: center;
        text-decoration: none;
    }
`

export const LocationSelectorShortcode = styled.span`
    .css-4l2yfx-menu {
        z-index: 10;
    }
    @media (min-width: 768px) {
        & > div {
            display: inline-block;
        }
    }
`

export const LocationSearch = styled.div`
    display: flex;
    justify-content: space-between;
    box-shadow: 1px 1px 4px rgba(0, 0, 0, .3);
    position: relative;
    &.dark {
        &:after {
            content: '';
            position: absolute;
            width: 100%;
            height: 2px;
            background: #fff;
            bottom: 0;
        }
    }
    form {
        width: 100%;
        position: relative;
        input {
            border: none;
            height: 48px;
            background: ${props => props.bgColor};
            color: ${props => props.textColor};
            padding: 6px 5rem 6px .5rem;
            font-size: 1rem;
            &::placeholder {
                color: ${props => props.textColor};
            }
            &:placeholder-shown ~ .reset {
                display: none;
            }
        }   
        .reset {
            position: absolute;
            right: 3.25rem;
            top: 50%;
            transform: translateY(-50%);
            padding: .5rem;
            cursor: pointer;
        }    
        .search-submit {
            border: none;
            background: none;
            width: 1rem;
            padding: .5rem;
            position: absolute;
            right: 1.25rem;
            top: 50%;
            transform: translateY(-50%);
            img {
                width: 1.5rem;
                max-width: 1.5rem;
            }
            svg {
                font-size: 1.25rem;
                color: ${props => props.textColor};
            }
        }
        .error {
            position: absolute;
            left: 0;
            top: 100%;
            font-size: .75rem;
            padding: .25rem .5rem;
            box-sizing: border-box;
            box-shadow: 1px 3px 3px rgba(0, 0, 0, .3);
            z-index: 99;
            color: ${props => props.errorColor};
            background: ${props => props.bgColor};
            display: flex;
            align-items: center;
        }
    }
    &:has(.my-location) {
        form {
            width: calc(100% - 50px);
            position: relative;
            &:after {
                content: '';
                position: absolute;
                height: 25px;
                width: 2px;
                background: ${props => props.textColor};
                right: 0;
                top: 50%;
                transform: translateY(-50%);
            }
        }
    }
    .my-location {
        width: 50px;
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;
        background: ${props => props.bgColor};
        svg {
            font-size: 1.25rem;
            color: ${props => props.textColor};
        }
        #loading {
            .loader {
                font-size: .25rem;
                margin-bottom: 1.25rem;
            }
        }
    }
`

export const ModuleLayout = styled.div`
    .location-search {
        z-index: 1;
        form input, .my-location {
            background: #fff;
        }
    }
    .location-map {
        .map-container, .google-map {
            height: 380px;
        }
    }
    .list-inner {
        display: grid;
        grid-template-columns: 1fr;
        gap: 1rem;
        padding: 1rem 0;
        box-sizing: border-box;
        & > div:not(.single-location) {
            display: none;
        }
        @media (max-width: 767px) {
            max-height: 380px;
            overflow-y: auto;
            scroll-behavior: smooth;
            padding-left: .25rem;
            padding-right: .25rem;
        }
        .single-location {
            padding: 1rem;
            box-shadow: 1px 1px 4px rgba(0, 0, 0, .3);
            overflow: hidden;
            transition: .2s;
            &:not(.focused) {
                background: #fff;
            }
            &:hover {
                box-shadow: 2px 2px 5px rgba(0, 0, 0, .5);
            }
            .button-wrapper {
                a {
                    display: block;
                    text-align: center;
                }
            }
        }
    }
    &.map-top-listing-bottom {
        .list-inner {
            @media (min-width: 640px) {
                grid-template-columns: 1fr 1fr;
            }
            @media (min-width: 1024px) {
                grid-template-columns: 1fr 1fr 1fr;
            }
        }
        @media (min-width: 768px) {
            .location-map {
                width: 100%;
                .map-container, .google-map {
                    height: 550px;
                }
            }
        }
    }
   
    &.listing-left-map-right {
        .list-inner {
            @media (min-width: 640px) {
                grid-template-columns: 1fr 1fr;
            }
        }
        @media (min-width: 768px) {
            display: flex;

            .single-location {
                margin: 0 .75rem 0 .5rem;
            }
            .location-map {
                .map-container, .google-map {
                    height: 640px;
                }
            }
            .location-list {
                width: 50%;
                .list-inner {
                    position: relative;
                    max-height: 640px;
                    overflow: auto;
                    scroll-behavior: smooth;
                    grid-template-columns: 1fr;
                    .single-location {
                        display: block;
                        .content-wrapper {
                            width: auto;
                        }
                    }
                }
              
                &:has(.location-search) {
                    .list-inner {
                        max-height: 592px;
                    }
                }
            }
        }
        @media (min-width: 1024px) {
            .location-list {
                width: 34%;
            }
            .location-map {
                width: 66%;
            }
        }
    }
   
`