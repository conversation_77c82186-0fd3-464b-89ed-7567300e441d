import React, { useEffect, useState, useContext, Suspense } from 'react';
import Base64 from 'base-64';
import axios from 'axios';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faLocationCrosshairs, faMagnifyingGlass, faTimes } from '@fortawesome/free-solid-svg-icons';
// Helpers
import { Coloring } from 'src/helpers';
import Imaging from 'src/helpers/imaging';
import { sortLocations, locationListScroll, isValidZipCode } from 'src/helpers/locations';
import { getItemWithExpiry } from 'src/helpers/localStorage';
// Partials
import Loading from "src/partials/loading";
// Context
import { SettingsContext } from 'src/context';
import { LocationSelectorContext } from '../context';
// Styles 
import * as S from '../styles';

const Start = ({ locations, origin }) => {
    const [settings, setSettings] = useContext(SettingsContext);
    const [locationSelector, setLocationSelector] = useContext(LocationSelectorContext);
    const [permissionState, setPermissionState] = useState(false);
    const [myLocationLoading, setMyLocationLoading] = useState(false);
    const [error, setError] = useState(null);
    const [number] = useState(Math.floor(Math.random() * 10000));

    useEffect(() => {
        const storedLocation = getItemWithExpiry('userLocation');
        setPermissionState((storedLocation));
    }, [locationSelector?.userLocation]);

    const getGeocode = async (term, isZip) => {
        // const gak = locationSelector.gak ? locationSelector.gak : Base64.decode('QUl6YVN5QkhGMUdEQnZMeVcwaUxUbndablNiS1JjSWN4LUZNT3Rn')
        const gak = Base64.decode('QUl6YVN5QkhGMUdEQnZMeVcwaUxUbndablNiS1JjSWN4LUZNT3Rn')
        console.log('search', locationSelector.gak, gak)
        let url = `https://maps.googleapis.com/maps/api/geocode/json?address=${term}&key=${gak}`
        try {
            let response = await axios.get(url)
            const results = response?.data?.results;
            if (results && response?.data?.status === "OK") {
                const location = results[0]?.geometry?.location
                setLocationSelector({
                    ...locationSelector, ...{
                        locationsSorted: sortLocations({ latitude: location.lat, longitude: location.lng }, locations ? locations : settings.locations?.coordinates, isZip ? term : false),
                        focused: origin === 'map-module' ? null : locationSelector.locationsSorted[0]?.slug,
                        searchCoords: {
                            latitude: location.lat, longitude: location.lng
                        },
                        zipSearch: isZip ? term : false
                    }
                })
                locationListScroll(locations ? '#location-module-layout' : '#location-selector-menu')
                setError('')
            } else if (response?.data?.status === "ZERO_RESULTS") {
                setError(`No results found for: ${term}`)
            } else if (response?.data?.status === 'REQUEST_DENIED') {
                setError(response?.data?.error_message)
            } else {
                console.log(response?.data?.error_message)
            }
        } catch (error) {
            console.log(error);
        }
    }

    const search = (e) => {
        e.preventDefault();
        const formData = new FormData(e.target);
        const term = formData.get('query')
        if (term) getGeocode(term, isValidZipCode(term))
    }

    const reset = () => {
        const input = document.getElementById(`location-search-${number}`)
        input.value = ''
        input.focus();
        setError('')
        setLocationSelector({ ...locationSelector, ...{ zipSearch: false } })
    }
    const getUserLocation = () => {
        const storedLocation = getItemWithExpiry('userLocation');
        if (storedLocation) {
            // If location is stored, use it
            const { latitude, longitude } = JSON.parse(storedLocation);
            setLocationSelector({
                ...locationSelector,
                userLocation: { latitude, longitude },
                locationsSorted: sortLocations({ latitude, longitude }, locations ? locations : settings.locations?.coordinates, false),
                focused: origin === 'map-module' ? null : locationSelector.locationsSorted[0]?.slug,
                searchCoords: { latitude, longitude },
                zipSearch: false
            });
            setMyLocationLoading(false);
        } else {
            let userLocation = false;
            if (navigator.geolocation) {
                navigator.geolocation.getCurrentPosition(
                    (position) => {
                        const { latitude, longitude } = position.coords;
                        userLocation = { latitude, longitude }
                        setLocationSelector({
                            ...locationSelector, ...{
                                userLocation: userLocation,
                                locationsSorted: sortLocations({ latitude, longitude }, locations ? locations : settings.locations?.coordinates, false),
                                focused: origin === 'map-module' ? null : locationSelector.locationsSorted[0]?.slug,
                                searchCoords: userLocation,
                                zipSearch: false
                            }
                        })
                        setMyLocationLoading(false);
                    },
                    (error) => {
                        setLocationSelector({
                            ...locationSelector, ...{
                                userLocation: false,
                                locationsSorted: sortLocations(false, locations ? locations : settings.locations?.coordinates, false),
                                focused: origin === 'map-module' ? null : locationSelector.locationsSorted[0]?.slug,
                                searchCoords: { latitude: 31.9686, longitude: 99.9018 },
                                zipSearch: false
                            }
                        })
                        setMyLocationLoading(false);
                        console.log('Error getting user location:', error.message);
                    }
                );
            } else {
                setLocationSelector({ ...locationSelector, ...{ userLocation: false } })
                setMyLocationLoading(false);
            }
        }
    }
    const myLocation = () => {
        setMyLocationLoading(true);
        getUserLocation();
        // clear input value
        document.getElementById(`location-search-${number}`).value = ''
        locationListScroll(locations ? '#location-module-layout' : '#location-selector-menu')
        setError('')
    }

    return (
        <Suspense fallback={<div />}>
            <S.LocationSearch
                className={`location-search ${settings?.locations?.location_selector_background_value}`}
                bgColor={Coloring(settings?.locations?.location_selector_background_color, settings)}
                textColor={settings?.locations?.location_selector_background_value === 'dark' ? '#fff' : Coloring('body_copy_color', settings)}
                errorColor={settings?.locations?.location_selector_background_value === 'dark' ? '#fff' : 'red'}
            >
                <form action={'#'} onSubmit={(e) => search(e)} role="search" aria-label="location search">
                    <label htmlFor="search-input">
                        <span class='show-for-sr'>Search Locations</span>
                        <input id={`location-search-${number}`} type="text" name="query" placeholder="Search by ZIP or City & State" />
                        <div className='reset' role='button' onClick={() => reset()} aria-label='reset search'>
                            <FontAwesomeIcon icon={faTimes} title='Reset' />
                        </div>
                        {error && <span class='error'>{error}</span>}
                    </label>
                    <button className='search-submit' type="submit" aria-label={'submit location search'}>
                        {settings.mvk_theme_config.header?.search_icon_desktop ?
                            <Imaging data={settings.mvk_theme_config.header?.search_icon_desktop} />
                            :
                            <FontAwesomeIcon icon={faMagnifyingGlass} title='Submit Search' />
                        }
                    </button>
                </form>
                {permissionState &&
                    <div className='my-location' role='button' onClick={() => myLocation()} aria-label={'use my location'}>
                        {!myLocationLoading && <FontAwesomeIcon icon={faLocationCrosshairs} title='Use My Location' />}
                        {myLocationLoading && <Loading type='dot-pulse' dotColor={settings?.locations?.location_selector_background_value === 'dark' ? '#fff' : Coloring('primary_color', settings)} />}
                    </div>
                }
            </S.LocationSearch>
        </Suspense>
    )
}

export default Start;
