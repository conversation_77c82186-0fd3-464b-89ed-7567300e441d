import React, { useEffect, useState, useContext, Suspense } from 'react';
import { decode } from 'html-entities';
// Context
import { SettingsContext, LocationsContext } from 'src/context';
import { LocationSelectorContext } from '../context';
// Helpers
import Clicker from 'src/helpers/clicker';
import Imaging from 'src/helpers/imaging';
import Button from 'src/partials/button';
import { Coloring } from 'src/helpers';
import { pxToRem } from 'src/helpers';
import { sortLocations } from 'src/helpers/locations';
import { fontPercentage } from 'src/helpers';
// Styles 
import * as S from '../styles';
// Components
const ReservationsModal = React.lazy(() => import('src/partials/reservations-modal'));
const Loading = React.lazy(() => import('src/partials/loading'));

const Start = ({ locations, handleClick }) => {
    const [settings, setSettings] = useContext(SettingsContext);
    const [locationSelector, setLocationSelector] = useContext(LocationSelectorContext);
    const [list, setList] = useState(null);
    useEffect(() => {
        if (!locationSelector.locationsSorted && (settings.locations?.coordinates.length > 0)) {
            // Compute `locationsSorted` and `focused` together
            const newLocationsSorted = sortLocations(
                locationSelector.userLocation,
                locations ? locations : settings.locations?.coordinates,
                locationSelector.zipSearch
            );
            // const newFocused = locations
            //     ? locationSelector.focused || null
            //     : newLocationsSorted[0]?.slug;
            setList(newLocationsSorted);
            setLocationSelector({
                ...locationSelector,
                ...{
                    locationsSorted: newLocationsSorted,
                    // focused: newFocused
                }
            });
        } else if (!locations && locationSelector.locationsSorted) {
            // Ensure focused is set correctly if locationsSorted already exists
            const newFocused = locations
                ? locationSelector.focused || null
                : locationSelector.locationsSorted[0]?.slug;

            setLocationSelector({
                ...locationSelector,
                ...{ focused: newFocused },
            });
        }
    }, [locationSelector.userLocation, locationSelector.locationsSorted, settings.locations?.coordinates]);

    const sortedLocations = locations || locationSelector.locationsSorted || list;

    return (
        <Suspense fallback={<Loading origin='dot-pulse' />}>
            <Loading origin='dot-pulse' />
            {sortedLocations && <SortedLocations
                locations={sortedLocations}
                handleClick={handleClick}
                origin={locations ? 'map-module' : 'location-selector'}
            />}
        </Suspense>
    );
};

const SortedLocations = ({ locations, handleClick, origin }) => {
    const [locationsContext] = useContext(LocationsContext);

    switch (origin) {
        case 'map-module':
            return locations.length > 0 ? locations.map((location, i) => <SingleLocation index={i} location={location} locationData={locationsContext[location.slug]} handleClick={handleClick} origin={origin} />) : <div className='no-locations'><p>No locations found in this area.</p></div>
        case 'location-selector':
        default:
            return locations ? locations.map((location, i) => <SingleLocation index={i} location={location} locationData={locationsContext[location.slug]} handleClick={handleClick} origin={origin} />) : <Loading origin='dot-pulse' />

    }
}

const SingleLocation = ({ index, location, locationData, handleClick, origin }) => {
    const [settings, setSettings] = useContext(SettingsContext);
    const [locationSelector, setLocationSelector] = useContext(LocationSelectorContext);
    const customFields = locationData ? locationData?.mvk_item_content?.custom_fields : false;
    const address = customFields ? `${customFields?.address_info?.address_line_1 ? `${customFields?.address_info?.address_line_1},` : ''}${customFields?.address_info?.address_line_2 ? ` ${customFields?.address_info?.address_line_2},` : ''}` : '';
    const cityState = customFields ? `${customFields?.address_info?.city ? `${customFields?.address_info?.city}, ` : ''} ${customFields?.address_info?.state_province} ${customFields?.address_info?.postal_code_zip}` : '';
    const directionsLink = `https://www.google.com/maps/place/${customFields?.address_info?.address_line_1.replace(/ /g, '+')}+${customFields?.address_info?.city.replace(/ /g, '+')}+${customFields?.address_info?.state_province.replace(/ /g, '+')}+${customFields?.address_info?.postal_code_zip.replace(/ /g, '+')}`
    const specialIcon = customFields?.special_location_icon;
    const reservationsButton = customFields?.reservations_button_type === 'button-link' && customFields?.reservations_link && settings.restaurant?.enable_reservations_button && settings.restaurant?.reservations_button_placement?.includes('quick-links')
    const reservationsScript = customFields?.reservations_button_type === 'button-script' && customFields?.reservations_script && settings.restaurant?.enable_reservations_button && settings.restaurant?.reservations_button_placement?.includes('quick-links')
    const flag = customFields?.flag ? customFields.flag_text : false;
    const flagSizeSmall = settings?.design?.advanced?.h6?.size_small ? parseInt(settings?.design?.advanced.h6?.size_small) * .75 : '16'
    const flagSizeLarge = settings?.design?.advanced?.h6?.size_large ? parseInt(settings?.design?.advanced.h6?.size_large) * .75 : '16'
    let isFocused;
    if (origin === 'map-module') {
        isFocused = locationSelector.focused ? locationSelector.focused === location.slug : false;
    } else {
        isFocused = locationSelector.focused ? locationSelector.focused === location.slug : (settings.current_location ? settings.current_location === location.slug : index === 0);
    }

    return (
        <S.SingleLocation
            className={`single-location${(isFocused) ? ' focused' : ''}`}
            onClick={() => setLocationSelector({ ...locationSelector, ...{ focused: location.slug, searchCoords: null } })}
            bgColor={Coloring(settings?.locations?.selected_location_background_color, settings)}
            textColor={settings?.locations?.selected_location_background_value === 'dark' ? '#fff' : Coloring('body_copy_color', settings)}
            flagBgColor={Coloring(settings?.locations?.flag_background_color, settings)}
            flagTextColor={settings?.locations?.flag_background_value === 'dark' ? '#fff' : Coloring('body_copy_color', settings)}
            flagSizeSmall={pxToRem(flagSizeSmall)}
            flagSizeLarge={pxToRem(flagSizeLarge)}
            txtSizeSmall={fontPercentage(settings?.design?.advanced ? settings?.design?.advanced?.paragraph?.size_small : '14', 81)}
            txtSizeLarge={fontPercentage(settings?.design?.advanced ? settings?.design?.advanced?.paragraph?.size_large : '16', 81)}
            aria-expanded={locationSelector.focused === location.slug}
        >
            <div className='content-wrapper'>
                {flag && <div className='flag'><h6>{decode(flag)}</h6></div>}
                <div className='heading'><h6>{decode(location.name)}</h6>{specialIcon && <Imaging className='special-icon' data={specialIcon} />}</div>
                {(isFocused) ?
                    <a class='address' href={directionsLink} target='_blank' aria-label={`directions to ${decode(location.name)}`}>{address}{address ? <br /> : ''}{cityState}</a>
                    :
                    <p className='address'>{address}{address ? <br /> : ''}{cityState}</p>
                }
                {(location?.distance && locationSelector.userLocation) &&
                    <p className='distance-hours'>{(Math.round(parseFloat(location?.distance) * 100) / 100).toFixed(1)} miles away</p>
                }
                {locationData?.todays_hours &&
                    <p className='todays-hours'>{locationData?.todays_hours?.closed ? locationData?.todays_hours?.closed : `Open today: ${locationData?.todays_hours.start_time} - ${locationData?.todays_hours.end_time}`}</p>
                }

                {((isFocused) && locationData?.quick_links) &&
                    <div className="quick-links">
                        {locationData?.quick_links.map((item) => <Clicker type="anchor" url={item.url} onClick={(e) => handleClick(e, item.url, true)}>{item.title}</Clicker>)}
                        {(reservationsButton) && <Clicker type="anchor" url={customFields?.reservations_link} target="_blank">{settings?.restaurant?.reservations_button_label}</Clicker>}
                        {(reservationsScript) && <ReservationsModal type='link' settings={settings} script={customFields?.reservations_script} />}
                    </div>
                }
            </div>
            {(isFocused) &&
                <div className='button-wrapper'>
                    <Button className='choose-location-button' title={settings?.locations?.select_location_button_text} onClick={(e) => handleClick(e, location.url)} url={location.url} type={settings?.locations?.select_location_button_style} tone={settings?.locations?.selected_location_background_value} />
                </div>
            }
        </S.SingleLocation>
    )
}

export default Start;