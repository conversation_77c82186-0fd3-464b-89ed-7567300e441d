import React, { useContext, useState, useEffect, Suspense } from 'react';
import { decode } from 'html-entities';
import ReactDOMServer from "react-dom/server";
import { MarkerClusterer } from '@googlemaps/markerclusterer';
import { Loader } from "@googlemaps/js-api-loader"
import { useNavigate } from 'react-router-dom';
import { getSecretCookie } from "src/config";
// Context
import { SettingsContext, LocationsContext } from 'src/context';
import { LocationSelectorContext } from 'src/partials/location-selector/context';
// Styles
import * as S from '../styles';

const Start = ({ focused, locations, origin, handleBoundsChange }) => {
    const [settings, setSettings] = useContext(SettingsContext);
    const [locationsContext] = useContext(LocationsContext);
    const [number] = useState(Math.floor(Math.random() * 10000));
    const [locationSelector, setLocationSelector] = useContext(LocationSelectorContext);
    const [locationList, setLocationList] = useState(locations)
    const [mapState, setMapState] = useState(null);
    const [infoWindowState, setInfoWindowState] = useState(null);
    const [markersState, setMarkersState] = useState([]);
    const [loaderState, setLoaderState] = useState(false);
    const navigate = useNavigate();

    useEffect(() => {
        if (handleBoundsChange) {
            handleBoundsChange(locationList)
        }
    }, [locationList])

    useEffect(() => {
        const googleSecret = getSecretCookie();
        if (!loaderState && (locationSelector.gak || googleSecret)) {
            const gak = locationSelector.gak || googleSecret;
            const loader = new Loader({
                apiKey: gak,
                version: "weekly"
            });
            setLoaderState(loader);
        }
        if (loaderState) {
            loaderState.load().then(async () => {
                const { Map, InfoWindow } = await google.maps.importLibrary("maps");
                const { AdvancedMarkerElement } = await google.maps.importLibrary(
                    "marker",
                );
                // setLocationSelector({ ...locationSelector, ...{ gak: gak} })
                const infowindow = infoWindowState ? infoWindowState : new InfoWindow({ content: '' })
                const openInfoWindow = (index, map, marker, location) => {
                    const content = ReactDOMServer.renderToString(<InfoWindowContent index={index} location={location} locationData={locationsContext[location.slug]} settings={settings} />);
                    infowindow.setContent(content);
                    infowindow.open(map, marker);
                }

                let mapEl = document.getElementById(`map-${number}`);
                const map = mapState ? mapState : (mapEl ? new Map(mapEl, {
                    center: { lat: locationSelector?.userLocation?.latitude ? parseFloat(locationSelector?.userLocation?.latitude) : -34.397, lng: locationSelector?.userLocation?.longitude ? parseFloat(locationSelector?.userLocation?.longitude) : 150.644 },
                    zoom: 15,
                    mapId: settings?.locations?.map_id ? settings?.locations?.map_id : number.toString(),
                    streetViewControl: false,
                    mapTypeControl: false,
                    fullscreenControl: false
                }) : false);

                if (map) {
                    markersState.forEach(marker => marker?.setMap(null));
                    setMarkersState([]);
                    const bounds = new google.maps.LatLngBounds();
                    const newMarkers = locations?.map((location, index) => {
                        const lat = location?.lat ? parseFloat(location?.lat) : false;
                        const lng = location?.lng ? parseFloat(location?.lng) : false;
                        if (!lat || !lng) {
                            return null;
                        }
                        const title = decode(location.name);
                        const path = new URL(location.url).pathname;
                        const customMarker = settings?.locations?.map_icon
                            ? document.createElement("img")
                            : null;
                        if (customMarker) {
                            customMarker.src = `https://${config.domain}${settings.locations.map_icon.url}`;
                        }
                        const markerOptions = {
                            position: { lat: lat, lng: lng },
                            map,
                            title: title,
                            id: location.slug,
                        };
                        if (customMarker) {
                            markerOptions.content = customMarker;
                        }
                        const marker = new AdvancedMarkerElement(markerOptions);

                        if (origin === 'map-module') {
                            if (!locationSelector?.searchCoords) {
                                bounds.extend(new google.maps.LatLng(lat, lng));
                            } else {
                                map.setCenter(new google.maps.LatLng(parseFloat(locationSelector?.searchCoords.latitude), parseFloat(locationSelector?.searchCoords.longitude)));
                                map.setZoom(9);
                            }
                            // click event to navigate to location page
                            google.maps.event.addListener(infowindow, 'domready', () => {
                                const infoButton = document.getElementById(`info-window-${index}`);
                                if (infoButton) {
                                    infoButton.onclick = function (e) {
                                        if (!infoButton.target) {
                                            e.preventDefault()
                                            navigate(path);
                                        }
                                    }
                                }
                            });
                        }
                        if (origin) {
                            marker.addListener('click', () => {
                                setLocationSelector({ ...locationSelector, ...{ focused: location.slug, searchCoords: null } })
                                if (origin === 'map-module') {
                                    openInfoWindow(index, map, marker, location)
                                }
                            });
                        }

                        // Location is Focused
                        if (focused && location.slug === focused) {
                            if ((!origin || origin === 'location-selector') && lat && lng) {
                                map.setCenter(new google.maps.LatLng(lat, lng));
                                map.setZoom(15);
                            }
                            if (origin === 'map-module') {
                                openInfoWindow(index, map, marker, location)
                            }
                        }

                        return marker;
                    });
                    setMarkersState(newMarkers);
                    if (origin === 'map-module' && locations && mapState) {
                        google.maps.event.addListener(map, 'bounds_changed', function () {
                            setLocationList(locations.filter((location) => {
                                const lat = location?.lat ? parseFloat(location?.lat) : false;
                                const lng = location?.lng ? parseFloat(location?.lng) : false;
                                return map.getBounds().contains({ lat: lat, lng: lng });
                            }))
                        });
                    }
                    // Fit bounds first time
                    if (origin === 'map-module' && !mapState) {
                        map.fitBounds(bounds);
                    }
                    if (!mapState) setMapState(map);
                    if (!infoWindowState) setInfoWindowState(infowindow);
                    if (origin === 'location-selector') new MarkerClusterer({ newMarkers, map });
                }
            }).catch((error) => {
                console.log(error);
            });
        }

    }, [loaderState, locationSelector?.gak, focused, locations, locationSelector?.userLocation, locationSelector?.searchCoords ])

    return (
        <Suspense fallback={<div />}>
            <S.MapContainer className='map-container' primaryColor={settings?.design.colors?.primary_color}><div id={`map-${number}`} className='google-map'></div></S.MapContainer>
        </Suspense>
    )
}

const InfoWindowContent = ({ index, location, locationData, settings }) => {
    const customFields = locationData ? locationData?.mvk_item_content?.custom_fields : false;
    const address = `${customFields?.address_info?.address_line_1 ? `${customFields?.address_info?.address_line_1},` : ''}${customFields?.address_info?.address_line_2 ? ` ${customFields?.address_info?.address_line_2},` : ''}`;
    const cityStateZip = `${customFields?.address_info?.city ? `${customFields?.address_info?.city}, ` : ''} ${customFields?.address_info?.state_province} ${customFields?.address_info?.postal_code_zip}`;
    const directionsLink = `https://www.google.com/maps/place/${customFields?.address_info?.address_line_1.replace(/ /g, '+')}+${customFields?.address_info?.city.replace(/ /g, '+')}+${customFields?.address_info?.state_province.replace(/ /g, '+')}+${customFields?.address_info?.postal_code_zip.replace(/ /g, '+')}`
    const phone = customFields?.contact_info?.phone ? customFields?.contact_info?.phone : false;
    // FOR LATER WHEN LOCATIONS MIGHT BE OFFSITE
    const target = location.external_link ? '_blank' : null;

    return (
        <S.InfoWindow className='info-window'>
            <div className="content-wrapper">
                <h6 className='title'>{decode(location.name)}</h6>
                <a className='address' href={directionsLink} target='_blank' aria-label={`directions to ${decode(location.name)}`}>{address}{address ? <br /> : ''}{cityStateZip}</a>
                <a className='phone' href={`tel:${phone}`}>{phone}</a>
                {location?.distance &&
                    <p className='distance-hours'>{(Math.round(parseFloat(location?.distance) * 100) / 100).toFixed(1)} miles away</p>
                }
                {locationData?.todays_hours &&
                    <p className='todays-hours'>{locationData?.todays_hours?.closed ? locationData?.todays_hours?.closed : `Open today: ${locationData?.todays_hours.start_time} - ${locationData?.todays_hours.end_time}`}</p>
                }
                <a id={`info-window-${index}`} target={target} className='location-link white-txt primary-bg' href={location.url}>{settings?.locations?.select_location_button_text}</a>
            </div>
        </S.InfoWindow>
    )
}


export default Start;