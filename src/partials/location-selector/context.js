import React, { useState, createContext, useContext } from 'react';
import { SettingsContext } from 'src/context';

export const LocationSelectorContext = createContext();
export const LocationSelectorProvider = (props) => {
    const [settings, setSettings] = useContext(SettingsContext);

    const [locationSelector, setLocationSelector] = useState({
        userLocation: null,
        locationsSorted: false,
        searchCoords: null,
        zipSearch: false,
        focused: (settings.current_location && !props.data) ? settings.current_location : null,
        gak: null
    });

    return (
        <LocationSelectorContext.Provider value={[ locationSelector, setLocationSelector ]}>
            {props.children}
        </LocationSelectorContext.Provider>
    );
}