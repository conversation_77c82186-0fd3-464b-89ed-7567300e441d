import React, { useState, useEffect, useContext, useRef, Suspense } from 'react';
import { decode } from 'html-entities';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faLocationDot, faChevronUp } from '@fortawesome/free-solid-svg-icons';
import { useNavigate } from 'react-router-dom';
import { getSecretCookie } from "src/config";
// Hooks
import { useOnClickOutside } from 'src/hooks';
// Context
import { SettingsContext, LocationsContext } from 'src/context';
import { LocationSelectorContext, LocationSelectorProvider } from './context';
// Helpers
import Imaging from 'src/helpers/imaging';
import { Coloring } from 'src/helpers';
import { noGeocode, locationListScroll, sortLocations } from 'src/helpers/locations';
import { getItemWithExpiry, setItemWithExpiry } from 'src/helpers/localStorage';
// Components
const GoogleMap = React.lazy(() => import('./sections/location-map'));
const LocationList = React.lazy(() => import('./sections/location-list'));
const LocationSearch = React.lazy(() => import('./sections/location-search'));
const Loading = React.lazy(() => import('src/partials/loading'));
// Styles 
import * as S from './styles';

const Start = ({ data }) => {
    return (
        <Suspense fallback={<Loading origin='dot-pulse' />}>
            <LocationSelectorProvider data={data}>
                <GetUserLocation data={data} />
            </LocationSelectorProvider>
        </Suspense>
    )
}

const GetUserLocation = ({ data }) => {
    const [settings, setSettings] = useContext(SettingsContext);
    const [locationSelector, setLocationSelector] = useContext(LocationSelectorContext);
    const bgColor = settings.design?.colors?.main_nav?.bottom_mobile_bar_background_color ? settings.design?.colors?.main_nav?.bottom_mobile_bar_background_color : '#000';
    const textColor = settings.design?.colors?.main_nav?.bottom_mobile_bar_text_color ? settings.design?.colors?.main_nav?.bottom_mobile_bar_text_color : '#fff';
    const bgImage = settings.mvk_theme_config?.header?.header_background_image ? settings.mvk_theme_config?.header?.header_background_image.url : '';

    const getUserLocation = (gak) => {
        let userLocation = false;
        // Check if the user location is already saved in localStorage
        const storedLocation = getItemWithExpiry('userLocation');
        if (storedLocation) {
            // Parse the stored location and set it
            userLocation = JSON.parse(storedLocation);
            setLocationSelector({ ...locationSelector, ...{ userLocation: userLocation, gak: gak } });
            return;
        }
        if (navigator.geolocation) {
            navigator.geolocation.getCurrentPosition(
                (position) => {
                    const { latitude, longitude } = position.coords;
                    userLocation = { latitude, longitude };
                    // Save location in localStorage for future use
                    // localStorage.setItem('userLocation', JSON.stringify(userLocation));
                    setItemWithExpiry('userLocation', JSON.stringify(userLocation), 3600000);
                    setLocationSelector({ ...locationSelector, ...{ userLocation: userLocation, gak: gak } });
                },
                (error) => {
                    setLocationSelector({ ...locationSelector, ...{ userLocation: false, gak: gak } });
                    console.log('Error getting user location:', error.message);
                }
            );
        } else {
            setLocationSelector({ ...locationSelector, ...{ userLocation: false }, gak: gak });
        }
    }

    useEffect(() => {
        const gak = locationSelector.gak ? locationSelector.gak : getSecretCookie();
        if (!locationSelector.userLocation) {
            getUserLocation(gak);
        }
    }, [locationSelector.userLocation])

    if (data?.layout_options)
        return <ModuleLayout data={data} />
    else {
        return (
            <S.LocationSelector
                className={`location-selector-container${bgImage ? ' with-bg-image' : ''}`}
                bgColor={bgColor}
                textColor={textColor}
                bgImage={bgImage}
            >
                <div className='location-selector'>
                    <Dropdown />
                </div>
            </S.LocationSelector>
        )
    }
}

const ModuleLayout = ({ data }) => {
    const [settings, setSettings] = useContext(SettingsContext);
    const [locationSelector, setLocationSelector] = useContext(LocationSelectorContext);
    const [locationList, setLocationList] = useState(data.locations);
    const navigate = useNavigate();

    useEffect(() => {
        if (locationSelector.focused) {
            locationListScroll('#location-module-layout');
        }
        if (locationSelector.locationsSorted) {
            setLocationList(locationSelector.locationsSorted)
        }
    }, [settings.current_location, locationSelector.focused, locationSelector.locationsSorted]);

    const handleClick = (e, url, isPath) => {
        e.preventDefault();
        const parsed = !isPath ? new URL(url) : false
        const path = parsed ? parsed.pathname : url;
        navigate(path);
        document.body.scrollTop = 0;
        document.documentElement.scrollTop = 0;
    }

    const handleBoundsChange = (locations) => {
        if (locationSelector.userLocation)
            setLocationList(sortLocations(locationSelector.searchCoords ? locationSelector.searchCoords : locationSelector.userLocation, locations, locationSelector.zipSearch));
    }
    // console.log(locationList)
    return (
        <S.ModuleLayout
            id='location-module-layout'
            className={`cell module-layout ${data.layout_options}`}
        >
            <S.LocationMap className={'location-map'}><GoogleMap focused={locationSelector.focused} locations={locationSelector.zipSearch ? locationSelector.locationsSorted : data.locations} origin={'map-module'} handleBoundsChange={handleBoundsChange} /></S.LocationMap>
            <div className='location-list'>
                {data.show_search && <LocationSearch locations={data.locations} origin={'map-module'} />}
                <div className='list-inner'>
                    <LocationList locations={locationSelector.locationsSorted ? locationList : false} handleClick={handleClick} />
                </div>
            </div>
        </S.ModuleLayout>
    )
}

const Dropdown = () => {
    const [settings, setSettings] = useContext(SettingsContext);
    const [locationSelector] = useContext(LocationSelectorContext);
    const [selected, setSelected] = useState(settings.current_location ? settings.locations?.coordinates?.filter(item => item.slug?.includes(settings.current_location)) : 'all');
    const [open, setOpen] = useState(false);
    const navigate = useNavigate();
    const ref = useRef();

    useEffect(() => {
        setSelected(settings.current_location ? settings.locations?.coordinates?.filter(item => item.slug?.includes(settings.current_location)) : 'all')
        if (locationSelector.focused) {
            locationListScroll('#location-selector-menu');
        }
    }, [settings.current_location, locationSelector.focused]);

    const handleClick = (e, url, isPath) => {
        e.preventDefault();
        const parsed = !isPath ? new URL(url) : false
        const path = parsed ? parsed.pathname : url;
        navigate(path);
        document.body.scrollTop = 0;
        document.documentElement.scrollTop = 0;
        setOpen(false);
    }
    useOnClickOutside(ref, () => {
        setOpen(false);
    })

    return (
        <S.Dropdown
            id="location-selector-menu"
            ref={ref}
            className={open ? 'open' : 'closed'}
            bgColor={Coloring(settings?.locations?.location_selector_background_color, settings)}
            textColor={settings?.locations?.location_selector_background_value === 'dark' ? '#fff' : Coloring('body_copy_color', settings)}
        >
            <div className={open ? 'location-list open' : 'location-list closed'}>
                <S.LocationMap className={'location-map'}><GoogleMap focused={locationSelector.focused} locations={locationSelector.locationsSorted ? locationSelector.locationsSorted : settings.locations?.coordinates} origin={'location-selector'} /></S.LocationMap>
                <div className='list-wrapper'>
                    {settings.locations?.enable_location_search && <LocationSearch origin={'location-selector'} />}
                    <div className='list-inner'>
                        <LocationList handleClick={handleClick} />
                    </div>
                </div>
            </div>
            <div className='current-location' onClick={() => setOpen(!open)} role="button" aria-expanded={open} aria-controls="location-selector-menu" aria-label="open location selector" tabIndex="0">
                {settings.locations?.location_pin_icon ?
                    <Imaging className='icon' data={settings.locations?.location_pin_icon} />
                    :
                    <FontAwesomeIcon icon={faLocationDot} class='icon' />
                }
                <h6 className='heading'>{selected === 'all' ? settings.locations.location_selector_text ? settings.locations.location_selector_text : 'ALL LOCATIONS' : `${decode(selected[0]?.name)}`}</h6><FontAwesomeIcon icon={faChevronUp} class='caret' />
            </div>
        </S.Dropdown>
    )
}


export default Start;