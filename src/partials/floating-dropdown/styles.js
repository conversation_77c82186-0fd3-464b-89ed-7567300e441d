import styled, { css } from 'styled-components';

// width 100% to keep component flexible 
// => set a parent wrapper around this with: position:relative & whatever width needed
export const FloatingDropdownContainer = styled.div`
    position: absolute;
    top: ${props => props.offsetTop || '0'};
    right: 0;
    width: ${props => props.width || '100%'};
    padding: 18px 16px;
    background-color: white;
    box-shadow: 0 2px 5px rgb(0 0 0 / 15%);
    transition: .2s ease opacity, .25s transform, .25s visibility;
    visibility: hidden;
    opacity: 0;
    transform: translateY(15px);

    ${({theme}) => theme && css`

        @media (min-width: ${theme?.breakpoints?.xl || '1350px'}) {
            left: 0;
            right: inherit;
        }
    `}

    ${props => props.isOpen && css`
        transition: .2s ease opacity, .25s transform;
        transform: translateY(0);
        opacity: 1;
        visibility: visible;
    `}
`
