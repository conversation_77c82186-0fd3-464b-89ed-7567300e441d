import React, { useState, useEffect } from 'react';

// Helpers
import Modal from 'src/helpers/modal';
import { HtmlCode } from 'src/modules/raw-html';
// Partials
const Button = React.lazy(() => import('src/partials/button'));

const Start = ({ type, settings, script }) => {
    const [showModal, setShowModal] = useState(false);

    const openModal = (e) => {
        e.preventDefault()
        setShowModal(true);
    }
    const close = () => {
        setShowModal(false);
    };

    return (
        <>
            {type === 'link' ?
                <a tabIndex={0} onClick={(e) => openModal(e)} aria-label='open reservations modal'>{settings?.restaurant?.reservations_button_label}</a>
                :
                <Button className='nav-button' onClick={(e) => openModal(e)} title={settings?.restaurant?.reservations_button_label} type={settings?.restaurant?.reservations_button_style} tone={settings?.restaurant?.reservations_background_value} buttonFunction='styled' noIcon={true} aria-label='open reservations modal' />
            }
            <Modal close={close} closeIcon={true} active={showModal} class='iframe'>
                <div className='iframe-container widget'>
                    <HtmlCode data={`<script type='text/javascript' src='${script}'></script>`} />
                </div>
            </Modal>
        </>
    )
}

export default Start;