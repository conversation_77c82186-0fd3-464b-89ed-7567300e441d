import React, { useEffect, useContext, useState } from 'react';
import { addScript, addStylesheet, useDynamicScript } from 'src/hooks';
import { PrincipalContext } from "src/context";

var domain = window.location.hostname;
// ALLOWS .DEV TO VIEW .PREFLIGHT IMAGES WHILE WORKING LOCALLY.
domain = domain.replace('.dev', '.preflight');

const Start = ({ settings, locationId }) => {

    const [principal, setPrincipal] = useContext(PrincipalContext);
    const [mapLang] = useState(principal?.activeTranslation === 'zh-hans' ? 'zh-cn' : principal?.activeTranslation);
    // addScript(`https://d1p5cqqchvbqmy.cloudfront.net/minimap/release/minimap.js`);
    useDynamicScript(`https://d1p5cqqchvbqmy.cloudfront.net/minimap/release/minimap.js`);
    // addScript('https://d1p5cqqchvbqmy.cloudfront.net/web2/release/mappedin-web.js');
    // addStylesheet('https://d1p5cqqchvbqmy.cloudfront.net/web2/release/mappedin-web.css');

    useEffect(() => {
        window.mappedin = {
            clientId: settings.store.mi_id,
            clientSecret: settings.store.mi_key,
            venue: settings.store.mappedin_venue,
            locationId: locationId,
            fullMapUrl: settings.store.mappedin_map_page,
            webAppVersion: "v2",
            language: mapLang || 'en',
        };

    }, [principal])

    return (
        <div id="mappedin-minimap"></div>
    );
};

export default Start;
