import React, { useEffect, useContext, useState } from 'react';
import { addScript, addStylesheet } from 'src/hooks';
// Context
import { PrincipalContext } from "src/context";

var domain = window.location.hostname;
// ALLOWS .DEV TO VIEW .PREFLIGHT IMAGES WHILE WORKING LOCALLY.
domain = domain.replace('.dev', '.preflight');

const Start = ({ settings }) => {
    const [principal, setPrincipal] = useContext(PrincipalContext);
    const [mapLang] = useState(principal?.activeTranslation === 'zh-hans' ? 'zh-cn' : principal?.activeTranslation);

    addScript('https://d1p5cqqchvbqmy.cloudfront.net/web2/release/mappedin-web.js');
    addStylesheet('https://d1p5cqqchvbqmy.cloudfront.net/web2/release/mappedin-web.css');

    useEffect(() => {
     
        window.mappedin = {
            miKey: {
                id: settings.store.mi_id,
                key: settings.store.mi_key,
            },
            venue: settings.store.mappedin_venue,
            vertical: "mall",
            language: mapLang || 'en',
        };
    }, [principal?.activeTranslation])

    return (
        <div data-key="externalId" id="mappedin-map"></div>

    );
};

export default Start;
