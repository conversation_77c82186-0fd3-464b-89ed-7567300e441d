import React, { useEffect, useContext } from 'react';
import { SettingsContext } from "src/context";

import './index.scss';

const Start = ({ locationID }) => {
    const [settings, setSettings] = useContext(SettingsContext);
    const apiUrl = settings?.store?.acquire_digital?.wayfinder_js_api_url;

    useEffect(() => {
        // if already there no need to delay
        if (window.ACQ) {
            // console.log('MAP 1')
            ACQ.init(document.getElementById("wayfinder-map"), {
                apiKey: settings?.store?.acquire_digital?.api_key
            });
        } else {
            // console.log('MAP 2')
            setTimeout(() => {
                ACQ?.addEventListener("error", function (e) {
                    console.log("Sorry we cannot load the map. Error: " + e.detail);
                });

                ACQ?.init(document.getElementById("wayfinder-map"), {
                    apiKey: settings?.store?.acquire_digital?.api_key
                });
            }, 1200);
        }
        if (locationID) {
            setTimeout(() => {
                ACQ.destination.focus(locationID);
                // ACQ.destination.findByMeshId(locationID).then((e) => {
                //     if (e.length) {
                //         if (e[0].floor != WF.map.getActiveMapId())
                //             ACQ.map.loadFloor(e[0].floor).then(() => ACQ.destination.focus(e[0].object));
                //         else 
                //             ACQ.destination.focus(e[0].object)
                //     }
                // });
            }, 2500);
        }
    }, [apiUrl, locationID])

    return (
        <div id="wayfinder-map"></div>
    );
};

export default Start;
