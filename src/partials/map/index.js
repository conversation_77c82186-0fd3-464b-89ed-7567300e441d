import React, { useState, useEffect } from 'react';
// Styles
import './loading.scss';

var domain = window.location.hostname;
// ALLOWS .DEV TO VIEW .PREFLIGHT IMAGES WHILE WORKING LOCALLY.
domain = domain.replace('.dev', '.preflight');

const Start = ({ id, html }) => {
    const [isLoading, setIsLoading] = useState(true);
    const [scriptsLoaded, setScriptsloaded] = useState(false);
    //*** make sure data.map_html is mounted on page before mapplic initialization 
    addScript(`https://code.jquery.com/jquery-3.7.1.min.js`);
    addScript(`https://${domain}/vendor/mapplic/core/mapplic.js`);
    addScript(`https://${domain}/vendor/mapplic/js/jquery.mousewheel.js`);
    addScript(`https://${domain}/vendor/mapplic/js/magnific-popup.js`);
    const loadScripts = () => {
        addStylesheet(`https://${domain}/vendor/mapplic/css/magnific-popup.css`);
        addStylesheet(`https://${domain}/vendor/mapplic/core/mapplic.css`);
        setScriptsloaded(true);
    }

    useEffect(() => {
        loadScripts();
        
        if (scriptsLoaded) {
            let tick = setInterval(function () {
                if (window.jQuery && window.jQuery('#mapplic-id' + id).mapplic) {
                    window.jQuery(`#mapplic-id${id}`).mapplic({
                        iconfile: '/vendor/mapplic/core/images/icons.svg'
                    });
                    setIsLoading(false);
                    clearInterval(tick);
                }
            }, 3000);
            return () => clearInterval(tick);
        }
    }, [scriptsLoaded])

    return (
        <div class={`interactive-map ${isLoading ? 'loading' : 'loaded'}`}>
            <div class="lds-ring"><div></div><div></div><div></div><div></div></div>
            <span class='map-html' dangerouslySetInnerHTML={{ __html: html }} />
        </div>
    );
};

const addScript = (url) => {
    var list = document.getElementsByTagName('script');
    const head = document.querySelector("head");
    var i = list.length, flag = false;
    while (i--) {
        if (list[i].src.includes(url)) {
            flag = true;
            break;
        }
    }
    if (!flag) {
        var script = document.createElement('script');
        script.src = url;
        script.defer = "true";
        head?.appendChild(script);
    }
};

const addStylesheet = (url) => {
    var list = document.getElementsByTagName('link');
    var i = list.length, flag = false;
    while (i--) {
        if (list[i].href === url) {
            flag = true;
            break;
        }
    }

    if (!flag) {
        var stylesheet = document.createElement('link');
        stylesheet.href = url;
        stylesheet.rel = 'stylesheet';
        document?.head?.appendChild(stylesheet);
    }
}
export default Start;
