import React, { useEffect, useContext, useState } from "react";
import { CountUp } from 'countup.js';
import { SettingsContext } from "src/context";
// Helpers
import { Coloring } from 'src/helpers';
import { pxToRem } from 'src/helpers';
// Styles
import { CountUpSpan } from './styles';

const Start = ({ number, fontFamily, fontSizeMobile, fontSizeDesktop, color }) => {
    const [settings, setSettings] = useContext(SettingsContext);
    // set a unique id
    const [id] = useState(`count-id-${Math.floor(Math.random() * 10000)}`);

    const handleContentLoaded = () => {
        // Add options here
        const options = {
            duration: 5,
            enableScrollSpy: true,
            scrollSpyDelay: 500
        }
        const countUp = new CountUp(id, number, options);
        if (!countUp.error) {
            countUp.start();
        } else {
            console.error(countUp.error);
        }
    };

    useEffect(() => {
        handleContentLoaded();
    }, []);

    return (
        <CountUpSpan
            id={id}
            textColor={Coloring(color, settings)}
            font={fontFamily === 'heading' ? settings?.design?.fonts?.heading_google_font_select?.value : settings?.design?.fonts?.body_google_font_select?.value}
            fontSizeMobile={pxToRem(fontSizeMobile)}
            fontSizeDesktop={pxToRem(fontSizeDesktop)}
        >
        </CountUpSpan>
    );
}

export default Start;