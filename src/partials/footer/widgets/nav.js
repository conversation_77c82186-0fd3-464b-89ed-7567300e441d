import React from 'react';
import { decode } from 'html-entities';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { fas as solidIcons } from "@fortawesome/free-solid-svg-icons";
// Helpers
const Clicker = React.lazy(() => import('src/helpers/clicker'));

import { FooterNav, NavItem } from './styles';

const Nav = ({ data }) => {
    return (
        <FooterNav className={`footer-nav ${data.menu_style} mobile-${data.enable_mobile_alignment ? data.mobile_alignment : 'false'} desktop-${data.widget_alignment}`}>
            {data.menu_select?.length > 0 && data.menu_select?.map(item => <Item item={item} />)}
        </FooterNav>
    )
}

const Item = ({ item }) => {
    const classes = (item.classes) ? item.classes.join(' ') : null;

    return (
        <>
            <NavItem className={`nav-item ${item.children ? 'with-children' : ''} item-${item.ID} ${classes}`}>
                <Clicker className={`${classes}`} type="anchor" url={item.url} target={item.target ? item.target : null}>{decode(item.title)}{(item.font_awesome_icon) && <FontAwesomeIcon icon={solidIcons[`${item.font_awesome_icon}`]} className="locked" />}</Clicker>
            </NavItem>
            {item.children && <Children id={item.ID} items={item.children} />}
        </>
    )
}

const Children = ({ id, items }) => {
    return (
        <div className={`children group-${id}`}>
            {items.map(item => <Item item={item} />)}
        </div>
    )
}

export default Nav;