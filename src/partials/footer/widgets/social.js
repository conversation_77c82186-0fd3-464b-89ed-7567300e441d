import React from 'react';
import { faFacebookF, faInstagram, faLinkedin, faYoutube } from '@fortawesome/fontawesome-free-brands';
import { faTiktok, faXTwitter, faThreads } from "@fortawesome/free-brands-svg-icons";
import { decode } from 'html-entities';
// Helpers
import { Coloring } from 'src/helpers';
import Clicker from 'src/helpers/clicker';
// Styles
import { SocialWrapper, SocialIcon } from './styles';

const Social = ({ data, settings }) => {

    return (
        <SocialWrapper className={`social-wrapper mobile-${data.enable_mobile_alignment ? data.mobile_alignment : 'false'} desktop-${data.widget_alignment} ${data?.customize_icons ? data?.icon_style : 'default'}`}>
            {data.social_cta &&
                <div className='social-cta'>{decode(data.social_cta)}</div>
            }
            <div className='icon-wrapper'>
                {settings.social?.social_media_channels && settings.social?.social_media_channels.map((channel) => <Icon data={channel} widget={data} settings={settings} />)}
            </div>
        </SocialWrapper>
    );
}

const Icon = ({ data, widget, settings }) => {
    let socialType = data.channel?.value
    let socialIconColor = Coloring(settings.mvk_theme_config?.footer?.footer_social_icon_color, settings);

    if (socialType === 'facebook') {
        var icon = faFacebookF
    } else if (socialType === 'twitter') {
        var icon = faXTwitter
    } else if (socialType === 'instagram') {
        var icon = faInstagram
    } else if (socialType === 'linkedin') {
        var icon = faLinkedin
    } else if (socialType === 'youtube') {
        var icon = faYoutube
    } else if (socialType === 'tiktok') {
        var icon = faTiktok
    } else if (socialType === 'threads') {
        var icon = faThreads
    }

    return (
        <Clicker type="anchor" url={data.url} rel="noopener" target="_blank" class={"social-icon"} title={data.channel?.label} alt={data.channel?.screenreader_alt_text} ariaLabel={`link to ${data?.channel?.label}`}>
            <SocialIcon
                icon={icon}
                className={`footer-social ${widget.customize_icons ? widget.icon_style : 'default'}`}
                iconColor={socialIconColor}
                textColor={settings?.mvk_theme_config?.footer?.footer_text_color}
                footerBg={settings.mvk_theme_config?.footer?.footer_background_color}
                bgColor={widget.customize_icons && widget.icon_style === 'icon-background' ? Coloring(widget.icon_background_color, settings) : null}
                borderRadius={widget.customize_icons ? widget.icon_border_radius : '50'}
                aria-label={`${data?.channel?.label} logo`}
            />
        </Clicker>
    );

}

export default Social;