import React from 'react';

// HELPERS.
const Clicker = React.lazy(() => import('src/helpers/clicker'));
const Imaging = React.lazy(() => import('src/helpers/imaging'));

const Branding = ({ data, settings }) => {
    // set home url
    const homeUrl = `https://${config.domain}/`;

    let imgData;
    if (data.branding_select === 'alternate_logo') {
        imgData = settings.branding?.alternate_logo;
    } else {
        imgData = settings.branding?.main_logo;
    }

    return (
        <div className={`branding mobile-${data.enable_mobile_alignment ? data.mobile_alignment : 'false'} desktop-${data.widget_alignment}`}>
            <Clicker type={settings.hide_nav ? '' : 'anchor'} url={settings.hide_nav ? null : homeUrl} ariaLabel='home'>
                <Imaging data={imgData} />
            </Clicker>
        </div>
    )
}

export default Branding;