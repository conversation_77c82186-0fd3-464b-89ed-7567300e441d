import styled from 'styled-components';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';

export const WidgetFooter = styled.div`
    padding: 2rem 0;
    background-color: ${props => props.bgColor};
    color: ${props => props.textColor};
    text-align: center;
    font-size: .875rem;
    &.bg-image {
        background-image: url(${props => props.bgImage?.url});
        background-position: ${props => props.bgAlign};
        background-size: cover;
    }
    &.top-border {
        border-top: 2px solid ${props => props.borderColor};
    }
    .widget-row {
        margin-bottom: 1rem;
        &:last-child {
            margin-bottom: 0;
        }
        .widget-column {
            width: 100%;
            box-sizing: border-box;
            & > .mobile-left, & > .mobile-false.desktop-left {
                text-align: left !important;
            }
            & > .mobile-center, & > .mobile-false.desktop-center {
                text-align: center !important;
            }
            & > .mobile-right, & > .mobile-false.desktop-right {
                text-align: right !important;
            }
            & > .image-widget.mobile-fullwidth, & > .mobile-false.image-widget.desktop-fullwidth {
                img {
                    width: 100%;
                }
            }
            .copyright-text, .copyright-text p {
                font-size: .75rem;
            }
            .image-widget {
                display: block;
                margin: 1rem auto;
            }
            & > .image-widget.mobile-fullwidth img {
                width: 100%;
            }
            @media (min-width: 640px) {
                & > .desktop-left {
                    text-align: left !important;
                }
                & > .desktop-center {
                    text-align: center !important;
                }
                & > .desktop-right {
                    text-align: right !important;
                }
                & > .image-widget.desktop-fullwidth img {
                    width: 100%;
                }
            }
        }
    }
    @media (min-width: 640px) {
        text-align: left;
        .widget-row {
            display: flex;
            flex-wrap: wrap;
            &.columns {
                &-2, &-3, &-4, &-5, &-6 {
                    .widget-column {
                        width: 50%;
                        padding-right: 1rem;
                        &:last-child {
                            padding-right: 0;
                        }
                    }
                }
            }
        }
    }

    @media (min-width: 1024px) {
        .widget-row {
            margin-bottom: 2rem;
            &.columns {
                &-3 .widget-column {
                    width: 33.333%;
                }
                &-4 .widget-column {
                    width: 25%;
                }
                &-5 .widget-column {
                    width: 20%;
                }
                &-6 .widget-column {
                    width: 16.666%;
                }
            }
        }
    }

    &.landing-page {
        .footer-search,
        .social-wrapper,
        .footer-nav,
        .form-module {
            display: none;
        }
    }
`;

export const FooterNav = styled.div`
    margin-bottom: 2rem;
    &.horizontal {
        display: flex;
        flex-wrap: wrap;
        margin-bottom: 0;
        .nav-item {
            margin: 0 1rem 0.5rem 0;
            font-size: .875rem;
            @media (min-width: 1024px) {
                margin-right: 2rem;
            }
        }
        &.mobile-center, &.mobile-false.desktop-center {
            justify-content: center;
            .nav-item {
                margin: 0 1rem;
                 @media (min-width: 1024px) {
                    margin: 0 2rem;
                }
            }
        }
        &.mobile-right, &.mobile-false.desktop-right {
            justify-content: flex-end;
            .nav-item {
                margin: 0 0 .5rem 1rem;
                @media (min-width: 1024px) {
                    margin: 0 0 .5rem 2rem;
                }
            }
        }
       @media (min-width: 640px) {
            &.desktop-left {
                justify-content: flex-start;
                .nav-item {
                    margin: 0 1rem .5rem 0;
                    @media (min-width: 1024px) {
                        margin: 0 2rem .5rem 0;
                    }
                }
            }
            &.desktop-center {
                justify-content: center;
                .nav-item {
                    margin: 0 1rem;
                    @media (min-width: 1024px) {
                        margin: 0 2rem;
                    }
                }
            }
            &.desktop-right {
                justify-content: flex-end;
                .nav-item {
                    margin: 0 0 .5rem 1rem;
                    @media (min-width: 1024px) {
                        margin: 0 0 .5rem 2rem;
                    }
                }
            }
       }
    }
    .children {
        margin-bottom: 2rem;
        .nav-item {
            font-size: .875rem;
            margin: 0 0 0.5rem 0;
            font-weight: normal;
        }
    }
`;

export const NavItem = styled.div`
    font-weight: bold;
    font-size: 1rem;
    margin: 1rem 0;
    &.with-children {
        margin: 1rem 0 .5rem;
    }
    &:first-child {
        margin-top: 0;
    }
    a {
        color: inherit;
        &:hover {
            text-decoration: underline;
        }
    }
`;

export const SocialWrapper = styled.div`
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1.5rem;
    &.mobile-center, &.mobile-false.desktop-center {
        justify-content: center;
    }
    &.mobile-right, &.mobile-false.desktop-right {
        justify-content: flex-end;
    }
    .social-cta {
        margin-right: 1rem;
        margin-bottom: .5rem;
    }
    .icon-wrapper {
        display: flex;
        flex-wrap: wrap;
        a {
            margin: 0 .5rem;
        }
    }
    @media (min-width: 640px) {
        &.desktop-left {
            justify-content: flex-start;
        }
        &.desktop-center {
            justify-content: center;
        }
        &.desktop-right {
            justify-content: flex-end;
        }
    }
    @media (min-width: 1024px) {
        margin-bottom: 2rem;
    }

    &.icon-only {
        display: grid;

        .icon-wrapper {
            display: grid;
            grid-auto-flow: column;
            column-gap: 2rem;

            .social-icon {
                margin: 0;
            }

            .footer-social {
                padding: 0;
                // width: min-content;
            }
        }
    }
`;

export const SocialIcon = styled(FontAwesomeIcon)`
    padding: 0.5rem;
    border-radius: ${props => props.borderRadius}%;
    width: 20px;
    height: 20px;
    transition: .2s;
    &.default {
        background-color: ${props => props.iconColor};
        color: #fff;
        &:hover {
            color: ${props => props.iconColor};
            background-color: ${props => props.footerBg || '#fff'};
        }
    }
    &.icon-only {
        padding: 0 0.5rem;
        color: ${props => props.iconColor};
        &:hover {
            color: ${props => props.textColor};
        }
    }
    &.icon-outline {
        color: ${props => props.iconColor};
        border: 2px solid ${props => props.iconColor};
        &:hover {
            background-color: ${props => props.iconColor};
            color: ${props => props.footerBg || '#fff'};
        }
    }
    &.icon-background {
        color: ${props => props.iconColor};
        background-color: ${props => props.bgColor};
        &:hover {
            background-color: ${props => props.iconColor};
            color: ${props => props.bgColor};
        }
    }
`;

export const Text = styled.div`
    color:  ${props => props.textColor};
    hr {
        background: ${props => props.textColor} !important;
        margin: 2rem 0;
    }
`;

export const FooterSearch = styled.div`
    position: relative;
    margin-bottom: 2rem;
    form {
        input {
            background: none;
            border: none;
            border-bottom: 1px solid ${props => props.textColor};
            padding-left: 0;
            color: ${props => props.textColor};
            &::placeholder {
                color: ${props => props.textColor};
            }
        }
    }
    .icon {
        position: absolute;
        top: 0;
        right: 0;
        height: 25px;
        width: 25px;
        cursor: pointer;
        svg {
            fill: ${props => props.textColor};
        }
    }
`;

export const FormWrapper = styled.div`
    padding: 0;
    margin-bottom: 2rem;
    color: ${props => props.textColor};
    form {
        .fields {
            position: relative;
            .form-group {
                .label, .floater, input, input::placeholder, .error, .star {
                    color: ${props => props.inputTextColor} !important;
                    border-color: ${props => props.textColor};
                }
                .error {
                    font-size: .75rem !important;
                }
            }
        }
    }
    &:not(.submit-style-button) {
        form {
            button[type=submit] {
                margin: 0;
                padding: 0;
                border: none !important;
                background: none !important;
                color:  ${props => props.textColor} !important;
                &:hover {
                    text-decoration: underline;
                }
            }
        }
    }
    #loading {
        margin: 0;
        .title {
            font-size: 2rem;
        }
    }
    .response {
        margin-bottom: 1rem;
    }
    button[type="submit"] {
        color: ${props => props.textColor} !important;
        border-color: ${props => props.textColor} !important;
    }
`;