import React, { useMemo, useContext } from "react";
// Components
import { Form } from 'src/modules/gravity-forms';
// Helpers 
import HtmlParser from 'src/helpers/html-parser';
import Clicker from 'src/helpers/clicker';
import Imaging from 'src/helpers/imaging';

// Styles
import { WidgetFooter, FormWrapper, Text } from './styles';
import { decode } from "html-entities";
// Widgets
const Branding = React.lazy(() => import('./branding'));
const Nav = React.lazy(() => import('./nav'));
const FooterSearch = React.lazy(() => import('./footer-search'));
const Social = React.lazy(() => import('./social'));
const Hours = React.lazy(() => import('../../hours'));
import { AppContext } from 'src/contexts/app';

const Start = ({ settings }) => {
    const spot = settings?.sei_settings?.spot ? settings?.sei_settings?.spot : false;
    const seiLicense = settings?.sei_settings?.license && settings?.sei_settings?.license?.location && settings?.sei_settings?.license?.location === 'footer' && typeof spot !== 'string';

    const memoizedRows = useMemo(() => {
        return settings.mvk_theme_config?.footer?.footer_widgets_rows && settings.mvk_theme_config?.footer?.footer_widgets_rows?.map(row => <Row data={row} settings={settings} />)
    }, [settings.mvk_theme_config?.footer?.footer_widgets_rows])

    return (
        <WidgetFooter
            className={`widget-footer clearfix ${settings.mvk_theme_config?.footer?.footer_background_image ? 'bg-image' : 'no-image'} ${settings.hide_nav ? 'landing-page' : ''} ${settings.mvk_theme_config?.footer?.footer_border_color ? 'top-border' : 'no-border'}`}
            bgColor={settings.mvk_theme_config?.footer?.footer_background_color}
            bgImage={settings.mvk_theme_config?.footer?.footer_background_image}
            bgAlign={settings.mvk_theme_config?.footer?.footer_background_align}
            borderColor={settings.mvk_theme_config?.footer?.footer_border_color}
            textColor={settings.mvk_theme_config?.footer?.footer_text_color}
        >
            <div className='grid-container'>
                {memoizedRows}
                {seiLicense &&
                    <p className="sei-license center">{settings?.sei_settings?.license?.text}</p> 
                }
            </div>
        </WidgetFooter>
    );
}

const Row = ({ data, settings }) => {
    const memoizedColumns = useMemo(() => {
        return data.widget_columns && data.widget_columns?.map(column => <Column data={column} settings={settings} />)
    }, [data.widget_columns])

    return (
        <div className={`widget-row ${data.widget_columns ? 'columns-' + data.widget_columns.length : ''}`}>{memoizedColumns}</div>
    )
}

const Column = ({ data, settings }) => {
    const memoizedWidgets = useMemo(() => {
        return data.widgets && data.widgets?.map(widget => <Widget data={widget} settings={settings} />)
    }, [data.widgets])

    return (
        <div className='widget-column'>{memoizedWidgets}</div>
    )
}

const Widget = ({ data, settings }) => {
    const appContext = useContext(AppContext);

    switch (data.widget_type) {
        case 'branding':
            return (<Branding data={data} settings={settings} />)
        case 'copyright':
            return (<div className={`copyright-text mobile-${data.enable_mobile_alignment ? data.mobile_alignment : 'false'} desktop-${data.widget_alignment}`} dangerouslySetInnerHTML={{ __html: settings.mvk_theme_config?.footer?.copyright_text }} />)
        case 'form':
            return (
                <FormWrapper className={`form-module mobile-${data.enable_mobile_alignment ? data.mobile_alignment : 'false'} desktop-${data.widget_alignment} submit-style-${data.submit_button_type}`} textColor={settings.mvk_theme_config?.footer?.footer_text_color} inputTextColor={data.form_style === 'underlined' ? settings.mvk_theme_config?.footer?.footer_text_color : settings?.design?.colors?.body_copy_color}>
                    {data.form_title &&
                        <h4>{decode(data.form_title)}</h4>
                    }
                    <Form data={data.form_select} settings={settings} footerTextColor={settings.mvk_theme_config?.footer?.footer_text_color} styleOverride={data.form_style} buttonStyleOverride={data.submit_button_type === 'button' ? data.button_style : false} buttonTextOverride={data.submit_button_text ? data.submit_button_text : false} />
                </FormWrapper>
            )
        case 'hours':
            data.display_options = 'reduced-widget';
            return (<Hours data={data} />)
        case 'navigation':
        case 'dynamic-navigation':
            return (<Nav data={data} />)
        case 'site-search':
            return (<FooterSearch settings={settings} />)
        case 'social':
            return (<Social data={data} settings={settings} />)
        case 'text':
            return (
                <Text className={`mobile-${data.enable_mobile_alignment ? data.mobile_alignment : 'false'} desktop-${data.widget_alignment}`} textColor={settings.mvk_theme_config?.footer?.footer_text_color}>
                    <HtmlParser html={data.text} data={data} />
                </Text>
            )
        case 'image':
            return (
                <Clicker className={`image-widget mobile-${data.enable_mobile_alignment ? data.image_mobile_alignment : 'false'} desktop-${data.image_widget_alignment}`} url={data.image_link ? data.image_link?.url : ''} target={data.image_link && data.image_link.target ? '_blank' : ''} type={data.image_link ? 'anchor' : ''}>
                    {(appContext.width < 640) ?
                        <Imaging data={data.mobile_image} />
                        : <Imaging data={data.desktop_image} />
                    }
                </Clicker>
            )
        default:
            return (null)
    }
}

export default Start;