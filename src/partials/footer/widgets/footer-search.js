import React, { useContext } from 'react';
import { useNavigate } from 'react-router-dom';

// CONTEXT.
import { PrincipalContext } from "src/context";
// Helpers
import { Coloring } from 'src/helpers';
const Clicker = React.lazy(() => import('src/helpers/clicker'));
const Imaging = React.lazy(() => import('src/helpers/imaging'));
// Styles
import { FooterSearch } from './styles';

const Search = ({ settings }) => {
    const [principal, setPrincipal] = useContext(PrincipalContext);

    const navigate = useNavigate();

    const Submit = (e) => {
        e?.preventDefault();
        var term = document.getElementById('footer-search-input').value;
        if (term && term.length > 0) {
            navigate(principal.subsite ? `/${principal.subsite}/search?s=${term}` : `/search?s=${term}`);
        }
    }

    return (
        <FooterSearch
            className="footer-search"
            textColor={settings.mvk_theme_config?.footer?.footer_text_color}
        >
            <div class="inner-wrapper">
                <form method="post" action="#" autocomplete="off" onSubmit={Submit} role="search" aria-label="sitewide">
                    <label for="footer-search-input" class="show-for-sr">Search</label>
                    <input id="footer-search-input" type="text" placeholder="Search" />
                </form>
                <Clicker class="icon" process={Submit}>
                    {settings.mvk_theme_config.header?.search_icon_desktop ?
                        <Imaging data={settings.mvk_theme_config.header?.search_icon_desktop} />
                        :
                        <svg version="1.1" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" aria-label="search icon">
                            <g id="info" />
                            <g id="icons">
                                <path d="M22.4,19.6l-4.8-4.8c0.9-1.4,1.4-3,1.4-4.8c0-5-4-9-9-9s-9,4-9,9s4,9,9,9c1.8,0,3.4-0.5,4.8-1.4l4.8,4.8   c0.4,0.4,0.9,0.6,1.4,0.6c1.1,0,2-0.9,2-2C23,20.4,22.8,19.9,22.4,19.6z M5,10c0-2.8,2.2-5,5-5s5,2.2,5,5s-2.2,5-5,5S5,12.8,5,10z" id="icon-search" />
                            </g>
                        </svg>
                    }
                </Clicker>
            </div>
        </FooterSearch>
    );
}

export default Search;