/*******************************************************************************************************
   Copyright 2020 - Calise Partners LLC d/b/a Imaginuity. All rights are reserved.
   Reproduction or transmission in whole or in part, in any form or by any means, electronic, mechanical
or otherwise, is prohibited without the prior written consent of Calise Partners LLC d/b/a Imaginuity.
********************************************************************************************************
   Project : 10618 Maverick Platform Development
   Author : Imaginuity Developers (<PERSON>, <PERSON>)
   Description : main footer component that will render a separate footer component based on type
   Creation Date : Fri Nov 20 2020
   Documentation : [documentation url - point to default wiki for project if no specific documentation for script]
 ********************************************************************************************************/

import React, { Suspense, useContext } from 'react';
import { useInView } from 'react-intersection-observer';
// CONTEXT.
import { SettingsContext } from "src/context";
const Widgets = React.lazy(() => import('./widgets'));
const FooterBanner = React.lazy(() => import('./footer-banner'));
const FooterBorderImage = React.lazy(() => import('./footer-border-image'));

const Start = (props) => {
    const [settings, setSettings] = useContext(SettingsContext);
    const { ref, inView } = useInView({
        triggerOnce: true,
        fallbackInView: true,
        threshold: 0.01
    });

    return (
        <Suspense fallback={<div/>}>
            <div ref={ref} id="footer" style={!inView ? { minHeight: '375px' } : null} >
                {inView ?
                    <>
                        {settings?.mvk_theme_config?.footer?.enable_footer_border_image &&
                            <FooterBorderImage data={settings?.mvk_theme_config?.footer} />
                        }
                        <Widgets settings={settings} />
                        {settings?.mvk_theme_config?.footer?.enable_footer_banner &&
                            <FooterBanner settings={settings} />
                        }
                        {settings?.mvk_theme_config?.footer?.enable_footer_bottom_border &&
                            <div class='footer__bottom-border' />
                        }
                    </>
                    : null}
            </div>
        </Suspense>
    );
};

export default Start;
