import React from "react";
import styled from 'styled-components';
import { useInView } from 'react-intersection-observer';
// Helpers
const HtmlParser = React.lazy(() => import('src/helpers/html-parser'));


const Start = ({ settings }) => {
    const { ref, inView } = useInView({
        triggerOnce: true,
        fallbackInView: true,
        threshold: 0
    });

    return (
        <FooterBanner
            ref={ref}
            className={`footer-banner ${inView && settings?.mvk_theme_config?.footer?.footer_banner_background_image ? 'bg-image' : ''}`}
            bgColor={settings?.mvk_theme_config?.footer?.footer_banner_background}
            bgImage={settings?.mvk_theme_config?.footer?.footer_banner_background_image}
        >
            <div className="grid-container">
                <HtmlParser html={settings?.mvk_theme_config?.footer?.footer_banner_content} />
            </div>
        </FooterBanner>
    );
}

const FooterBanner = styled.div`
    color: #fff;
    background-color: ${props => props.bgColor};
    &.bg-image {
        background-image: url(${props => props.bgImage?.url});
        background-position: center center;
        background-size: cover;
    }
    padding: .5rem 0;
    p:last-child {
        margin-bottom: 0;
    }
`;

export default Start;