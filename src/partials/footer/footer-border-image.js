import React from 'react';
import styled from 'styled-components';

const Start = ({ data }) => {

    return (
        <FooterBorderImage
            className='footer-border-image'
            bgImage={data?.footer_border_image ? data?.footer_border_image.url : ''}
            heightMobile={data?.border_image_height_mobile}
            heightDesktop={data?.border_image_height_desktop}
        />
    )
}



const FooterBorderImage = styled.div`
    background-image: url(${props => props.bgImage});
    background-size: cover;
    min-height: ${props => props.heightMobile}px;
    @media (min-width: 768px) {
        min-height: ${props => props.heightDesktop}px;
    }
`

export default Start;

