.modal-container {
  position: fixed;
  overflow-y: scroll;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 99999;
  padding: 120px 0 40px;
  @media (min-width: 1200px) {
    padding: 180px 0 40px;
  }
}
#modal-popup {
  // position: fixed;
  position: relative;
  // z-index: 999;
  background: #fff;
  width: 100%;
  border: 4px solid;
  border-color: darkgray;
  text-align: center;
  padding: 2rem;
  // overflow: auto;
  // max-height: 60vh;
  -webkit-animation: fadein 1s;
  -moz-animation: fadein 1s;
  -ms-animation: fadein 1s;
  -o-animation: fadein 1s;
  animation: fadein 1s;
  padding: 2rem;
  width: auto;
  // left: 15px;
  // right: 15px;
  // bottom: auto;
  // top: 240px;
  //   bottom: 0;

  margin: auto;

  @keyframes fadein {
    from {
      top: -1000px;
      opacity: 0;
    }

    to {
      top: 0;
      opacity: 1;
    }
  }

  @media screen and (min-width: 768px) {
    padding: 2rem;
    width: 600px;
    // left: 0;
    // right: 0;
  }

  @media screen and (min-width: 1200px) {
    max-width: 800px;
    padding: 4rem;
    // left: 0;
    // right: 0;
    // top: 250px;
  }

  .inner-wrapper {
    display: flex;
    flex-direction: column;

    @media screen and (min-width: 768px) {
      display: flex;
      flex-direction: row;
    }
  }

  .close-button {
    position: absolute;
    // top: 0.75rem;
    // right: 1rem;
    // font-size: 2.5rem;
    top: 0.4rem;
    right: 0.75rem;
    font-size: 1.75rem;
    font-weight: 500;
    opacity: 1;
    color: darkgray;
    cursor: pointer;
  }

  .image-wrapper {
    @media screen and (min-width: 768px) {
      margin-right: 2rem;
    }

    img {
      max-width: 200px;
      padding-bottom: 5px;

      @media screen and (min-width: 1200px) {
        max-width: 300px;
      }
    }
  }

  .content-wrapper {
    max-width: 500px;
    margin: auto;
  }

  .modal-heading {
    margin: 1rem 0;
    // text-transform: uppercase;
    display: inline-block;
    border-bottom: 1px solid;
    border-bottom-color: lightgray;
    padding-bottom: 0.5rem;
  }

  .modal-blurb {
    font-size: 0.875rem;
    padding: 2rem 0;
    // overflow: auto;
    // max-height: 70vh;
    text-align: left;
  }

  .modal-button {
    display: inline-flex;
    background: none;
    border-radius: 3px;
    // padding: 8px;
    color: #5a5c66;
    background: none;
    border: 1px solid lightgray;
    // text-decoration: underline;

    &:hover {
      background-color: #e6e6e6;
      text-decoration: none;
    }
  }
  .form-module {
    text-align: left;
  }
}
