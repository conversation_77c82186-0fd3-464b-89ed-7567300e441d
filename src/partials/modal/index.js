import React, { useState, useEffect } from 'react';
const Imaging = React.lazy(() => import('src/helpers/imaging'));
const Clicker = React.lazy(() => import('src/helpers/clicker'));
const Button = React.lazy(() => import('src/partials/button'));
import { BackgroundColor } from 'src/helpers/theme';
import { Form } from 'src/modules/gravity-forms';
import './index.scss';

import { decode } from 'html-entities';


const Modal = ({ settings }) => {
    const [viewModal, setViewModal] = useState(true);
    const borderColor = BackgroundColor(settings.modal.border_color);

    useEffect(() => {
        let visited = sessionStorage["alreadyVisited"];
        if (visited) {
            setViewModal(false);
            document.body.classList.remove('modal-open');
        } else {
            sessionStorage["alreadyVisited"] = true;
            setViewModal(true);
            document.body.classList.add('modal-open');
        }
    }, [])

    function closeModal() {
        document.body.classList.remove('modal-open');
        setViewModal(false);
    };

    if (viewModal) {
        return (
            <div class='modal-container'>
                <div id='modal-popup' class='modal' style={{ borderColor: borderColor }}>
                    <div class='inner-wrapper'>

                        <span class='close-button' style={{ color: borderColor }} onClick={closeModal}>X</span>

                        {settings.modal.image &&
                            <div class='image-wrapper'>
                                <Imaging data={settings.modal.image} />
                            </div>
                        }

                        <div class='content-wrapper'>
                            {settings.modal.heading &&
                                <h3 class='modal-heading'>{decode(settings.modal.heading)}</h3>
                            }
                            {settings.modal.blurb &&
                                <div class='modal-blurb' dangerouslySetInnerHTML={{ __html: settings.modal.blurb }} />
                            }
                            {settings.modal.anchor_link &&
                                <Button class='modal-button' title={settings.modal.anchor_link.title} url={settings.modal.anchor_link.url} type='anchor' target={settings.modal.anchor_link.target} closeModal={closeModal} />
                            }
                            {settings.modal.form &&
                                <div class='form-module'>
                                    <Form data={settings.modal.form} settings={settings} />
                                </div>
                            }
                        </div>

                    </div>
                </div>
            </div>
        );
    } else {
        return null;
    }
};

export default Modal;