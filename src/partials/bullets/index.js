import React from "react";
import { decode } from 'html-entities';
import { <PERSON>et<PERSON>ontainer, BulletWrapper } from './styles';
const Imaging = React.lazy(() => import('src/helpers/imaging'));

const Start = ({ bulletStyle, fillColor, bullets, imgOverride }) => {

    if (bullets) {
        return (
            <BulletContainer>
                {bullets.map((bullet) =>
                    <BulletWrapper
                        className='mvk-bullet'
                        fillColor={fillColor}
                    >
                        <Bullet bulletStyle={bulletStyle} imgOverride={imgOverride} />
                        <div className='bullet-content' dangerouslySetInnerHTML={{__html: bullet}} />
                    </BulletWrapper>
                )}
            </BulletContainer>
        );
    } else {
        return null;
    }
}

const Bullet = ({ bulletStyle, imgOverride = false }) => {

    if (imgOverride) {
        let imageData = {
            url: imgOverride,
            alt: `custom bullet`
        }
        return (
            <Imaging data={imageData} class="custom-bullet" />
        )
    } else {
        switch (bulletStyle) {
            case 'arrow':
                return (
                    <svg xmlns="http://www.w3.org/2000/svg" width="15.115" height="10.543" viewBox="0 0 15.115 10.543" aria-label="arrow">
                        <g data-name="Group 7417" transform="translate(-93.575 -1823.902)">
                            <path data-name="Path 13738" d="M42.1,10.819a1,1,0,0,1-.681-1.733l3.811-3.539L41.421,2.009A1,1,0,1,1,42.782.543l4.6,4.271a1,1,0,0,1,0,1.466l-4.6,4.271A1,1,0,0,1,42.1,10.819Z" transform="translate(60.988 1823.627)" fill="#fff" />
                            <path data-name="Path 13739" d="M15.115,1H2A1,1,0,0,1,1,0,1,1,0,0,1,2-1H15.115a1,1,0,0,1,1,1A1,1,0,0,1,15.115,1Z" transform="translate(92.575 1829.174)" fill="#fff" />
                        </g>
                    </svg>
                )
            case 'marker':
                return (
                    <svg xmlns="http://www.w3.org/2000/svg" width="14.76" height="13.73" viewBox="0 0 14.76 13.73" aria-label="checkmark">
                        <path data-name="Path 13721" d="M2043.5,9.062l2.367,3.727a2.059,2.059,0,0,0,3.588-.237c1.781-3.486,4.5-8.708,8.242-11.391a.642.642,0,0,0-.579-1.128c-1.086.359-4.525,1.984-9.2,8.955l-1.179-1.939a1.905,1.905,0,0,0-2.974-.359h0a1.905,1.905,0,0,0-.263,2.371" transform="translate(-2043.2 0)" fill="#fff" />
                    </svg>
                )
            case 'rounded':
                return (
                    <svg xmlns="http://www.w3.org/2000/svg" width="16.606" height="12.885" viewBox="0 0 16.606 12.885" aria-label="checkmark">
                        <path data-name="Path 13726" d="M5.828,28.19a.726.726,0,0,1-1.026,0L.393,23.781a1.339,1.339,0,0,1,0-1.894h0a1.339,1.339,0,0,1,1.894,0l3.028,3.028L14.32,15.91A1.339,1.339,0,1,1,16.214,17.8Z" transform="translate(-0.001 -15.518)" fill="#fff" />
                    </svg>
                )
            case 'triangle':
                return (
                    <svg xmlns="http://www.w3.org/2000/svg" width="11" height="12" viewBox="0 0 11 12" aria-label="triangle">
                        <path data-name="Polygon 4" d="M6,0l6,11H0Z" transform="translate(11) rotate(90)" fill="#fff" />
                    </svg>
                )
            default:
                return null;
        }
    }

}

export default Start;