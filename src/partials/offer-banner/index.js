import React, { useContext, useEffect, useState } from "react";
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faTimes } from '@fortawesome/free-solid-svg-icons';
// CONTEXT.
import { SettingsContext } from "src/context";
// Helpers
import HtmlParser from 'src/helpers/html-parser';
// Partials
import Button from 'src/partials/button';
// Styles
import { OfferBanner } from './styles';

const Start = (props) => {
    const [settings, setSettings] = useContext(SettingsContext);
    const [showBanner, setShowBanner] = useState(true);
    const OfferBannerData = settings?.mvk_theme_config?.header?.offer_banner;
    const ButtonOne = OfferBannerData?.button_one ? OfferBannerData?.button_one : false;
    const ButtonTwo = OfferBannerData?.button_two ? OfferBannerData?.button_two : false;
    const offerBannerClosed = sessionStorage["offerBannerClosed"];

    useEffect(() => {
        if (offerBannerClosed) {
            setShowBanner(false);
        }
    }, [offerBannerClosed])

    const closeBanner = () => {
        setShowBanner(false);
        sessionStorage["offerBannerClosed"] = true;
        const detectHero = settings.mvk_theme_config.header?.header_type === 'semi-absolute' || settings.mvk_theme_config.header?.header_type === 'gradient';
        const marginTop = settings.mvk_theme_config.header?.header_type != 'static' && settings.mvk_theme_config.header?.header_type != 'absolute';
        setTimeout(() => {
            const appEl = document.getElementById('app');
            const headerEl = document.getElementById('header');
            if (marginTop && (headerEl.classList.contains('no-hero') || !detectHero)) {
                appEl.style.marginTop = `${headerEl.offsetHeight}px`;
            }
        }, 300)
    }

    if (showBanner) {
        return (
            <OfferBanner
                className={`offer-banner`}
                bgColor={OfferBannerData?.background_color}
                textColor={OfferBannerData?.background_value === 'dark' ? '#fff' : settings?.design?.colors?.body_copy_color}
            >
                <FontAwesomeIcon role='button' aria-label='close offer banner' className={'close-banner'} icon={faTimes} onClick={() => closeBanner()} />
                <div className="grid-container">
                    <div className="grid-x">
                        {OfferBannerData?.content &&
                            <div className="cell content-wrapper">
                                <HtmlParser html={OfferBannerData?.content} />
                            </div>
                        }
                        {(ButtonOne || ButtonTwo) &&
                            <div className={`cell button-wrapper ${OfferBannerData.button_alignment}`}>
                                {ButtonOne &&
                                    <Button type={OfferBannerData.button_style} title={ButtonOne.title} url={ButtonOne.url} target={ButtonOne.target} tone={OfferBannerData.background_value} closeBanner={closeBanner} />
                                }
                                {ButtonTwo &&
                                    <Button type={OfferBannerData.button_style} title={ButtonTwo.title} url={ButtonTwo.url} target={ButtonTwo.target} tone={OfferBannerData.background_value} closeBanner={closeBanner} />
                                }
                            </div>
                        }
                    </div>
                </div>
            </OfferBanner>
        );
    } else {
        return null;
    }
}

export default Start;