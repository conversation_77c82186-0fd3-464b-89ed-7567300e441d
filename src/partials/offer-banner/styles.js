import styled from 'styled-components';

export const OfferBanner = styled.div`
    position: relative;
    background-color: ${props => props.bgColor};
    color: ${props => props.textColor};
    padding: 1rem 0;
    .grid-x {
        padding: 0 2rem;
    }
    .content-wrapper {
        h1, h2, h3, h4, h5, h6 {
            margin-bottom: .5rem;
        }
        p:last-child {
            margin-bottom: 0;
        }
    }
    .button-wrapper {
        margin-top: 1rem;
        a {
            margin-bottom: 1rem;
        }
        @media (min-width: 768px) {
            &.left {
                a {
                    margin: 0 1rem 0 0;
                }
            }
            &.center {
                a {
                    margin: 0 1rem;
                }
            }
            &.right {
                a {
                    margin: 0 0 0 1rem;
                }
            }
        }
    }
    .close-banner {
        position: absolute;
        top: 5%;
        right: 5%;
        color: ${props => props.textColor};
        font-size: 2rem;
        transition: 0.2s;
        cursor: pointer;
        &:hover {
            transform: scale(1.1);
        }
    }
`