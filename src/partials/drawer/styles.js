import styled from 'styled-components';

export const Drawer = styled.div`
    position: fixed;
    transform: rotate(270deg) translateX(100%);
    transform-origin: right bottom;
    right: 0;
    top: 50%;
    z-index: 899; // just under nav
    a {
        color: ${props => props.styles.color};
        background-color: ${props => props.styles.backgroundColor};
        display: flex;
        align-items: center;
        padding: .5rem 1rem;
        font-size: 0.813rem;
        font-weight: bold;
        border-radius: 5px 5px 0 0;
        box-shadow: 0 3px 6px rgba(0, 0, 0, 0.4);
        white-space: nowrap;
        @media (min-width: $break-desktop) {
            padding: .75rem 1rem;
        }
        img {
            max-height: 20px;
            margin-left: .75rem;
            width: auto;
        }
    }
`;