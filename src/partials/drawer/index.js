import React, { useState, useEffect } from 'react';
import { useInView } from 'react-intersection-observer';

//Helpers
const Clicker = React.lazy(() => import('src/helpers/clicker'));
const Imaging = React.lazy(() => import('src/helpers/imaging'));

import { Drawer } from './styles';

const Start = ({ settings }) => {

    if (settings.multiple) {
        return settings.multiple.map(drawer => <Single settings={drawer} />);
    } else {
        return <Single settings={settings} />;
    }

}

const Single = ({ settings }) => {
    const [display, setDisplay] = useState(true);
    const { ref, inView } = useInView({
        triggerOnce: true,
        fallbackInView: true
    });
    const drawerStyles = {
        color: settings.text_color,
        backgroundColor: settings.background_color
    }
    const data = {
        url: settings.graphic,
        alt: `${settings.tab_text} icon`,
        width: 100,
        height: 20
    }

    useEffect(() => {
        if (settings.limit_to) {
            const compare = item => new URL(item).pathname.replace(/\//g, '') === location.pathname.replace(/\//g, '');
            let match = settings.limit_to.some(compare);
            setDisplay(match);
        }

    }, [settings.limit_to, location.pathname])

    return (
        <Drawer ref={ref} className={`drawer ${display ? 'show' : 'hide'}`} styles={drawerStyles}>
            {inView ?
                <Clicker type='anchor' url={settings.link_url} target={settings.link_target} ariaLabel={`link to ${settings.tab_text}`}>
                    {settings.tab_text}
                    {settings.graphic && <Imaging data={data} />}
                </Clicker>
                : null}
        </Drawer>
    );
}
export default Start;