import React, { useContext } from 'react';
import { decode } from 'html-entities';


// CONTEXT.
import { SettingsContext } from 'src/context';
//Helpers
import { Coloring } from "src/helpers";
// Partials
const Button = React.lazy(() => import('src/partials/button'));
import { Form } from 'src/modules/gravity-forms';
// Styled
import { StoreCTA } from './styles.js';

const Start = ({ type, data }) => {
    const [settings, setSettings] = useContext(SettingsContext);
    const bgColor = Coloring(data.background_color, settings);
    const color = data.background_value === 'dark' ? '#fff' : Coloring('body_copy_color', settings);

    return (
        <StoreCTA
            className={`store-cta ${type} ${data.background_type}`}
            backgroundColor={bgColor}
            color={color}
            backgroundImage={data.background_type === 'image' ? data.background_image?.url : ''}
        >
            <div className='grid-container'>
                <div className='content-wrapper'>
                    {data.heading &&
                        <h2>{decode(data.heading)}</h2>
                    }
                    {data.blurb &&
                        <div className='blurb' dangerouslySetInnerHTML={{ __html: data.blurb }} />
                    }
                    {data.button &&
                        <Button className='button' title={data.button?.title} url={data.button.url} target={data.button.target} type={data.button_style} tone={data.background_value} />
                    }
                </div>
                {data.form &&
                    <div class='form-module'>
                        <Form module={{background_value: data.background_value}} data={data.form} settings={settings} />
                    </div>
                }
            </div>
        </StoreCTA>
    );
}

export default Start;