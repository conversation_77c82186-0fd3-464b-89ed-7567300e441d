
import styled from 'styled-components';

export const StoreCTA = styled.div`
    padding: 3rem 0;
    &.gift-card {
        text-align: center;
    }
    color: ${props => props.color};
    &.color {
        background-color: ${props => props.backgroundColor};
    }
    &.image {
        background-image: url(${props => props.backgroundImage});
        background-position: center center;
        background-size: cover;
        background-repeat: no-repeat;
    }
    .button {
        margin-top: 1rem;
    }
    h2 {
        margin-bottom: .5rem;
    }
    &.form {
        .form-module {
            padding: 0;
            margin: 0;
            .label, .floater, input, input::placeholder, .error, .star {
                color: ${props => props.color} !important;
                border-color: ${props => props.color} !important;
            }
            button[type=submit] {
                margin: 0;
            }
            #loading {
                margin: 0;
                .title {
                    margin: 0;
                    font-size: 1.5rem;
                    text-align: left;
                }
            }
        }
        @media (min-width: 768px) {
            .grid-container {
                display: flex;
                .content-wrapper {
                    width: 50%;
                }
                .form-module {
                    width: 50%;
                    margin: 0 0 0 1.5rem;
                }
            }
        }
        @media (min-width: 1024px) {
            .grid-container {
                .content-wrapper {
                    width: 66%;
                }
                .form-module {
                    width: 34%;
                }
            }
        }
    }
`;