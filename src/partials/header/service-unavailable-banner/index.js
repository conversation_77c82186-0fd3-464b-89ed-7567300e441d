import React, { useContext, useState, useEffect } from 'react';
import styled from 'styled-components';
import { useParams } from "react-router-dom";

// CONTEXT.
import { SettingsContext, ServicesContext } from "src/context";
// Helpers
import { Coloring } from 'src/helpers';
import HtmlParser from 'src/helpers/html-parser'

const Start = (props) => {
    const [settings, setSettings] = useContext(SettingsContext);  
    const [showBanner, setShowBanner] = useState(false);
    const params = useParams();
    var urlData = {
        slug: params.slug || false,
        first: params.first || false,
        second: params.second || false,
        third: params.third || false,
        fourth: params.fourth || false,
        fifth: params.fifth || false
    };
    const [serviceOptions] = useState(['services']);
    const [servicesList] = useState(settings?.services?.no_subdirectory ? settings?.services?.list : []);
    const servicesNoSubdirectory = settings?.services?.no_subdirectory;
    const [services, setServices] = settings.services && (!servicesNoSubdirectory && Object.keys(urlData).some((key) => serviceOptions.includes(urlData[key]) && !serviceOptions.includes(params.slug))) || (servicesNoSubdirectory && Object.keys(urlData).some((key) => servicesList.includes(urlData[key]))) ? useContext(ServicesContext) : [];

    useEffect(() => {
        if (services) {
            setShowBanner(services[params.slug].mvk_disabled);
        } else {
            setShowBanner(false);
        }
    }, [params, services])

    if (showBanner) {
        return (
            <ServiceUnavailableBanner
                id="service-unavailable-banner"
                bgColor={Coloring(settings?.services?.service_banner_background_color, settings)}
                txtColor={settings?.services?.service_banner_background_value === 'dark' ? "#fff" : Coloring('body_copy_color', settings)}
            >
                <div class="grid-container">
                    <div className='grid-x'>
                        <div className='cell'>
                            <HtmlParser html={settings?.services?.service_banner_content} />
                        </div>
                    </div>
                </div>
            </ServiceUnavailableBanner>
        );
    } else return null;
};

const ServiceUnavailableBanner = styled.div`
    background-color: ${props => props.bgColor};
    color: ${props => props.txtColor};
    padding: .5rem 0;
    & > * {
        margin: 0;
    }
`
export default Start;
