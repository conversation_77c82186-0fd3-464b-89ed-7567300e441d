import React, { useState, useEffect, createContext, useCallback } from "react";

export const HeaderContext = createContext();
export const HeaderProvider = ({ settings, children, ...otherProps }) => {
    const [ loading, setLoading ] = useState(true);
    const [ toggled, setToggled ] = useState({ utility:false, main:false, hamburger:false });
    const [ enabled, setEnabled ] = useState({ search:settings?.mvk_theme_config?.header?.search, utility:settings?.mvk_theme_config?.nav?.utility });
    const [ data, setData ]       = useState({ branding:settings.branding, design:settings.design, theme:settings.mvk_theme_config });

    useEffect(() => setLoading(false), []);

    const Toggle = useCallback((key, value) => {
        setToggled({ ...toggled, ...{ [key]:value }});
    });

    return (
        <HeaderContext.Provider value={{ loading, toggled, enabled, data, Toggle  }}>
            {children}
        </HeaderContext.Provider>
    );
};