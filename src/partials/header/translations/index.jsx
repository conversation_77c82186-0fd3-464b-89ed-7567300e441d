import React, { useState, useEffect, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { ActiveTranslationItem, TranslationDropdownItem, TranslationsContainer } from './styles';

import FloatingDropdown from 'src/partials/floating-dropdown/index.jsx';

import { useTranslation } from 'src/hooks';
import { useOnClickOutside } from 'src/hooks';
import { TextUnderline } from 'src/helpers/text-underline';

const Start = ({ translations, defaultLang }) => {
    const [translation, setTranslation] = useTranslation();
    const [isDropdownOpen, setIsDropdownOpen] = useState(false);
    const [isHovered, setIsHovered] = useState(false);
    const navigate = useNavigate();

    const ref = useRef();
    const updateTranslations = (lang) => {
        setTranslation(lang ? lang.replace(/\/$/, '') : '');
        navigate({ search: `lang=${lang}` });
        navigate(0); // Doing this for now to avoid rewriting everything that wasn't prepared for translations to be changed on a whim.‰
    }

    const openDropdown = () => setIsDropdownOpen(true);
    const closeDropdown = () => setIsDropdownOpen(false);
    const handleMouseEnter = () => setIsHovered(true);
    const handleMouseLeave = () => {
        setIsHovered(false);
        closeDropdown();
    }
    const handleOnClick = (e) => {
        if (isDropdownOpen) {
            closeDropdown();
        } else {
            openDropdown();
        }
    }
    const handleTimeout = () => {
        if (!isDropdownOpen) openDropdown();
    }

    useOnClickOutside(ref, () => {
        if (isDropdownOpen) {
            handleMouseLeave();
            closeDropdown();
        }
    })

    useEffect(() => {
        const possibleLangs = ['en', 'fr', 'zh-hans'];
        const langParam = new URLSearchParams(location.search).get('lang')?.replace(/\//g, '');

        if (langParam && langParam !== translation && possibleLangs.includes(langParam)) {
            updateTranslations(langParam.replace(/\/$/, ''));
        }
        if (!langParam && defaultLang && !translation) {
            updateTranslations(defaultLang, true);
        }
        let transCode = langParam ? langParam : translation;

        if (transCode && possibleLangs.includes(transCode)) {
            document.cookie = `translation=${transCode.replace(/\/$/, '')};path=/;`;
            document.documentElement.lang = `${transCode.replace(/\/$/, '')}`;
        }
        // Adding slight hover delay on animation
        // => Stops users from being ANGRYYYY >:( if it pops up when trying to go somewhere else
        const timer = isHovered && setTimeout(handleTimeout, 275);
        return () => {
            clearTimeout(timer);
        };
    }, [isHovered]);

    if (translations && Object.entries(translations).length > 0) return (

        <TranslationsContainer id="translations-container"
            onMouseEnter={handleMouseEnter}
            onMouseLeave={handleMouseLeave}
            ref={ref}
        >
            <ActiveTranslationItem
                onClick={handleOnClick}
            >
                {translations[translation] ? translations[translation] : 'English'}
            </ActiveTranslationItem>

            <FloatingDropdown
                isOpen={isDropdownOpen}
                width={'140px'}
            >
                <TranslationDropdownItem
                    onClick={() => updateTranslations('en')}
                    isDisabled={!translation || translation === 'en'}
                >
                    <TextUnderline
                        isActive={!translation || translation === 'en'}
                    >
                        English
                    </TextUnderline>
                </TranslationDropdownItem>

                {Object.entries(translations).map(([key, value]) => (
                    <TranslationDropdownItem
                        onClick={() => updateTranslations(key)}
                        isDisabled={key === translation}
                    >
                        <TextUnderline
                            isActive={key === translation}
                        >
                            {value}
                        </TextUnderline>
                    </TranslationDropdownItem>
                )
                )}
            </FloatingDropdown>
        </TranslationsContainer>
    )
    return null;
}

export default Start