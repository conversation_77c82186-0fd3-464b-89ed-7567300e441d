import styled, { css } from 'styled-components';

export const TranslationsContainer = styled.div`
    padding: 5px 8px;
    position: relative;
    z-index: 1101;
    width: fit-content;
`

export const ActiveTranslationItem = styled.p`
    font-size: 0.813rem;
    text-decoration: underline;
    margin: -2px 0px 0 0;
    cursor: pointer;
`

export const TranslationDropdownItem = styled.p`
    font-size: 0.813rem;
    margin: 0;
    padding: 5px 0px;
    cursor: pointer;
    color: #000;
    ${({theme}) => theme && css`

        @media (min-width: ${theme?.breakpoints?.md || '640px'}) {
            font-size: 0.875rem;
        }

        @media (min-width: ${theme?.breakpoints?.lg || '1024px'}) {
            font-size: 0.938rem;
        }
    `}

    ${props => props.isDisabled && css`
        opacity: .5;
        cursor: default;
    `}
`