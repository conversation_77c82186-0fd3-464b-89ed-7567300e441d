import React, { useContext, useState } from 'react'
import { decode } from 'html-entities';
// Settings
import { SettingsContext } from 'src/context';
// HELPERS.
const Clicker = React.lazy(() => import('src/helpers/clicker'));
import HtmlParser from 'src/helpers/html-parser';
import { Coloring } from 'src/helpers';
import Imaging from 'src/helpers/imaging';
import * as S from './styles';

const Start = ({ id, name, meganavSettings, meganavVisible, hideMeganav }) => {
    const [settings, setSettings] = useContext(SettingsContext);
    const meganav = meganavSettings?.find(item => item.name === name);
     
    return (
        <S.Meganav
            id={`nav-children-${id}`}
            className={`meganav${meganav.featured_cta ? ' with-cta' : ''} ${meganavVisible ? 'active' : ''}`}
        >
            <div className='page-links'>
                {meganav && meganav?.page_links?.map((item) => <Child item={item} hideMeganav={hideMeganav} />)}
            </div>
            {meganav.featured_cta &&
                <S.FeaturedCTA
                    className='featured-cta'
                    borderColor={Coloring(meganav.featured_cta?.border_color, settings)}
                >
                    <div className='inner-wrapper'>
                        {meganav.featured_cta?.cta_image && <Imaging className='cta-image' data={meganav.featured_cta?.cta_image} />}
                        <HtmlParser html={meganav.featured_cta?.content} />
                    </div>
                </S.FeaturedCTA>
            }
        </S.Meganav>
    )
}

const Child = ({ item, hideMeganav }) => {
    return (
        <Clicker className={'page-link'} type='anchor' url={item.url} process={hideMeganav}>
            {item.page_icon &&
                <Imaging className='page-icon' data={item.page_icon} />
            }
            <div class={`name`}>{decode(item.name)}</div>
        </Clicker>
    )
}

export default Start;