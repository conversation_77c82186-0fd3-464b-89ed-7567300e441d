#header {
    margin: 0px;
    padding: 0px;
    width: 100%;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15);
    @media (max-width: 1199px) {
        &:has(.floating-hamburger) {
            box-shadow: none;
            position: fixed !important;
            z-index: 1000;
            top: 0px;
            left: 0px;
            right: 0px;
        }
    }
    &.static {
        position: relative;
    }

    &.fixed,
    &.semi-absolute,
    &.gradient {
        position: fixed;
        z-index: 1000;
        top: 0px;
        left: 0px;
        right: 0px;
        @media screen and (max-width: 767px) {
            position: fixed !important;
        }
    }

    &.sticky {
        position: sticky;
        z-index: 1000;
        top: 0px;
        left: 0px;
        right: 0px;
    }

    &.absolute {
        position: absolute;
        z-index: 1000;
        box-shadow: none;
        &.no-hero {
            position: relative;
        }
        &:not(.no-hero) {
            @media screen and (min-width: 1200px) {
                background: transparent !important;
            }
        }
        @media screen and (max-width: 1199px) {
            position: static;
        }
    }

    @media screen and (max-width: 767px) {
        position: static !important;
    }
    @media screen and (max-width: 1199px) {
        &:not(.gradient):not(.with-bg-image) {
            background-image: none !important;
        }
        & > .grid-container {
            padding-left: 0;
            padding-right: 0;
        }
    }
}

#chassis {
    > .logo-box {
        position: absolute;
        width: auto;
        z-index: 999;
        img {
            height: 50px;
            object-fit: contain;
            width: auto;
        }
    }
    &:has(.hamburger-right) > .logo-box {
        top: 1.5rem;
        left: 1rem;
    }
    &:has(.hamburger-left) > .logo-box {
        top: 1.5rem;
        right: 1rem;
    }
    &:has(.floating-hamburger.active) > .logo-box {
        display: none;
    }
    #header:has(#modal) ~ #bottomMobileBar {
        z-index: unset;
    }
    @media (min-width: 1200px) {
        > .logo-box {
            display: none;
        }
    }
}