/*******************************************************************************************************
   Copyright 2020 - Calise Partners LLC d/b/a Imaginuity. All rights are reserved.
   Reproduction or transmission in whole or in part, in any form or by any means, electronic, mechanical
or otherwise, is prohibited without the prior written consent of Calise Partners LLC d/b/a Imaginuity.
********************************************************************************************************
   Project : 10618 Maverick Platform Development
   Author : Imaginuity Developers (<PERSON>, <PERSON>)
   Description : Header initial loading script.
   Creation Date : Sun Dec 13 2020
   Documentation : [documentation url - point to default wiki for project if no specific documentation for script]
 ********************************************************************************************************/

import React, { useContext } from 'react';

// CONTEXT.
import { PrincipalContext, SettingsContext } from "src/context";
import { NavigationProvider, NavigationContext } from "src/context";
import { HeaderProvider, HeaderContext } from "src/partials/header/context";
import Logo from "src/partials/header/style-one/logo";

import Styles from "src/partials/header/styles";

const Start = (props) => {
    const [ principal, setPrincipal ] = useContext(PrincipalContext);
    const [ settings, setSettings ] = useContext(SettingsContext);

    // INIT CONTEXT FOR BOTH MAIN & UTILITY NAVIGATION.
    return (
        <NavigationProvider subsite={principal.subsite} translation={principal?.activeTranslation || ''} main2={(settings.mvk_theme_config?.nav?.navigation_style === 'logo-centered')} utility={(settings.mvk_theme_config?.nav?.utility)}>
            <LoadNavigation />
        </NavigationProvider>
    );
};

const LoadNavigation = (props) => {
    const [ settings, setSettings ] = useContext(SettingsContext);
    const [ navigation, setNavigation ] = useContext(NavigationContext);

    if (navigation?.loading) {
        return (null);
    } else {
        return (
            <HeaderProvider settings={settings}>
                {settings?.mvk_theme_config?.nav?.navigation_type === 'floating-hamburger' && <Logo />}
                <LoadHeader />
            </HeaderProvider>
        );
    }
};

const LoadHeader = (props) => {
    const headerContext = useContext(HeaderContext);

    if (headerContext.loading) {
        return null;
    } else {
        return (<Styles />);
    }
};

export default Start;
