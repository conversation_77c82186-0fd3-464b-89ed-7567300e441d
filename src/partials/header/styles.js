import React, { useContext, useRef, useLayoutEffect } from 'react';
import styled from 'styled-components';
// CONTEXT.
import { SettingsContext } from 'src/context';
import { HeaderContext } from "src/partials/header/context";

const NotificationBanner = React.lazy(() => import("src/partials/header/notification-banner"));
const OfferBanner = React.lazy(() => import("src/partials/offer-banner"));
const ServiceUnavailableBanner = React.lazy(() => import("src/partials/header/service-unavailable-banner"));
const Utility = React.lazy(() => import("src/partials/header/utility"));
const StyleOne = React.lazy(() => import("src/partials/header/style-one"));
const StyleThree = React.lazy(() => import("src/partials/header/style-three"));

// SCSS, CSS.
import "src/partials/header/styles.scss";
import { ScrollHandler } from 'src/helpers/scroll';
import { Coloring } from 'src/helpers';

const Start = (props) => {
    const [settings, setSettings] = useContext(SettingsContext);
    const headerContext = useContext(HeaderContext);
    const headerRef = useRef(null);
    const scroll = ScrollHandler();
    const bgColor = headerContext.data.design.colors.main_nav.nav_bg_color;
    const headerGradientColor = headerContext.data.theme.header.header_type && settings.mvk_theme_config?.header?.gradient_color !== 'black' && settings.mvk_theme_config?.header?.gradient_color !== 'white' ? Coloring(settings.mvk_theme_config?.header?.gradient_color, settings) : settings.mvk_theme_config?.header?.gradient_color;
    const gradientColor = `linear-gradient(to bottom, ${headerGradientColor}, transparent)`;
    let styles = {};
    if (headerContext.data.design.colors.main_nav.nav_background_gradient) {
        styles = {
            backgroundColor: headerContext.data.design.colors.main_nav.nav_bg_color,
            backgroundImage: `linear-gradient(to bottom left, ${headerContext.data.design.colors.main_nav.nav_bg_color}, ${headerContext.data.design.colors.main_nav.nav_bg_color_2})`
        };
    }
    const underlineAccent = headerContext.data.theme.header.header_type === 'gradient' && settings.mvk_theme_config?.header?.enable_underline_accent ? headerContext.data.design.colors.main_nav.nav_txt_color : '';
    const offerBannerClosed = sessionStorage['offerBannerClosed']


    useLayoutEffect(() => {
        setSettings({ ...settings, ['header_height']: headerRef.current.clientHeight })
        if (scroll) {
            headerRef.current.classList.add('scrolled')
        } else {
            headerRef.current.classList.remove('scrolled')
        }
    }, [headerRef, scroll])

    return (
        <Header
            ref={headerRef}
            id="header"
            className={`${settings.mvk_theme_config?.header?.header_type}${settings.mvk_theme_config?.header?.header_background_image ? ' with-bg-image' : ''}`}
            style={styles}
            underlineAccent={underlineAccent}
            bgColor={bgColor}
            bgImage={settings.mvk_theme_config?.header?.header_background_image ? settings.mvk_theme_config?.header?.header_background_image.url : ''}
            gradientColor={gradientColor}
        >
            {settings?.mvk_theme_config?.other?.enable_color_splashes &&
                <div className='color-splash-header'></div>
            }
            {settings?.services?.enable_service_unavailable_banner && <ServiceUnavailableBanner />}
            {(!offerBannerClosed && settings?.mvk_theme_config?.header?.offer_banner?.enable_offer_banner) && <OfferBanner />}
            <NotificationBanner />
            {settings.mvk_theme_config.nav?.utility && <Utility />}
            <div class={`grid-container ${settings.mvk_theme_config.nav?.navigation_style === 'logo-centered' ? 'full' : ''}`}>
                <Container />
            </div>
            {settings?.mvk_theme_config?.header?.enable_bottom_border &&
                <div class='header__bottom-border' />
            }
        </Header>
    );
};

const Container = (props) => {
    const [settings, setSettings] = useContext(SettingsContext);

    switch (settings.mvk_theme_config.header?.header_style) {
        case 'header-style-3':
            return <StyleThree settings={settings} />;
        case 'header-style-1':
        default:
            return (<StyleOne />);
    }
};

const Header = styled.header`
    background: ${props => props.bgColor};
    &.with-bg-image:has(#style-one:not(.active)) {
        background-image: url('${props => props.bgImage}');
        background-position: center center;
        background-size: cover;
        #style-one, .nav-primary-item {
            background-color: transparent !important;
        }
    }
    @media (max-width: 1199px) {
        &:has(.floating-hamburger) {
            &:has(.floating-hamburger:not(.active)) {
                pointer-events: none;
                background: transparent;
                background-image: none !important;
                .mobile-logo a {
                    display: none !important;
                }
            }
            #nav-dropdown {
                .hamburger-box {
                    background: rgba(198,198,198, .9);
                    border-radius: 50%;
                    width: 50px;
                    height: 50px;
                    pointer-events: all;
                }
                .hamburger-inner, .hamburger-inner::before, .hamburger-inner::after {
                    background-color: #000 !important;
                    width: 25px;
                }
                .hamburger-inner {
                    left: 50%;
                    transform: translateX(-50%);
                }
                .hamburger.is-active .hamburger-inner {
                    transform: translateX(-50%) rotate(225deg);
                }
                .mobile-logo {
                    img {
                        max-height: 50px;
                        object-fit: contain;
                        width: auto;
                        margin-left: 6px;
                    }
                }
            }
        }
    }
    &#header.gradient {
        #style-one .logo-box.no-min-height .logo {
            padding: 16px 0;
        }
        &.scrolled {
            background: ${props => props.bgColor} !important; 
        }
        &.has-hero:not(.scrolled) { 
            background: ${props => props.gradientColor};
            #style-one:not(.active) {
                #nav-dropdown > .grid-container > .grid-x {
                    border-bottom: 2px solid ${props => props.underlineAccent}95;
                }
            }
        }
        @media (min-width: 1200px) {
            &.has-hero:not(.scrolled) {
                #style-one {
                    border-bottom: 2px solid ${props => props.underlineAccent}95;
                }
            }
        }
    }
`;

export default Start;
