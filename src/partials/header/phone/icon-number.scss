/*******************************************************************************************************
   Copyright 2021 - Calise Partners LLC d/b/a Imaginuity. All rights are reserved.
   Reproduction or transmission in whole or in part, in any form or by any means, electronic, mechanical
or otherwise, is prohibited without the prior written consent of Calise Partners LLC d/b/a Imaginuity.
********************************************************************************************************
   Project : 10618 Maverick Platform Development
   Author : Imaginuity Developers (<PERSON>, <PERSON>)
   Description : style sheet for phone icon and number component
   Creation Date : Sun Aug 15 2021
   Documentation : [documentation url - point to default wiki for project if no specific documentation for script]
 ********************************************************************************************************/

.icon-and-number__component {
    display: flex;
    justify-content: center;
    align-items: center;
    .phone-icon-container {
        @media screen and (min-width: 640px) {
            margin: 0 0.3rem;
        }
        a {
            display: flex;

            svg {
                height: 1.5rem;
                width: auto;
                margin-bottom: .25rem;
            }
            .phone-icon-desktop {
                height: 24px;
                width: 19px;
                @media screen and (max-width: 1199px) {
                    display: none;
                }
            }
            .phone-icon-mobile {
                height: 24px;
                width: 19px;
                @media screen and (min-width: 1200px) {
                    display: none;
                }
            }
        }
    }

    .phone-number-container {
        @media screen and (max-width: 639px) {
            display: none;
        }
        &.number-margin {
            margin-left: 0.3rem;
        }

        .full-phone-number {
            font-size: 1.5rem;
            font-weight: 900;
            letter-spacing: -1px;
            @media (max-width: 1199px) {
                color: inherit !important;
            }
        }
    }
    &.phone-style-number {
        @media (min-width: 640px) {
            .phone-icon-container {
                display: none;;
            }
        }
    }
}
