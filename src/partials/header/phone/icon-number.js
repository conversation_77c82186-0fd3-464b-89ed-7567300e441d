/*******************************************************************************************************
   Copyright 2021 - Calise Partners LLC d/b/a Imaginuity. All rights are reserved.
   Reproduction or transmission in whole or in part, in any form or by any means, electronic, mechanical
or otherwise, is prohibited without the prior written consent of Calise Partners LLC d/b/a Imaginuity.
********************************************************************************************************
   Project : 10618 Maverick Platform Development
   Author : Imaginuity Developers (<PERSON>, <PERSON>)
   Description : phone icon and number component for header/navigation
   Creation Date : Sun Aug 15 2021
   Documentation : [documentation url - point to default wiki for project if no specific documentation for script]
 ********************************************************************************************************/

import React, { useEffect, useContext, useState } from 'react';
import { useParams } from "react-router-dom";

import './icon-number.scss';

import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faPhoneAlt } from "@fortawesome/free-solid-svg-icons";
import { PagesContext, PrincipalContext } from 'src/context';

const Imaging = React.lazy(() => import('src/helpers/imaging'));

const Start = ({ settings }) => {
    const [principal, setPrincipal] = useContext(PrincipalContext);
    const [pages, setPages] = useContext(PagesContext);
    const [customNumber, setCustomNumber] = useState(false);
    const params = useParams();
    const slug = params?.slug?.toLowerCase();
    const first = params?.first?.toLowerCase();
    const page = (first) ? slug : (principal.subsite && slug && principal.subsite != slug) ? slug : (!principal.subsite && slug) ? slug : settings.front_page;
    const pageData = pages[page] ? pages[page] : false;

    useEffect(() => {
        // check for custom phone number
        setCustomNumber(pageData?.mvk_item_content?.custom_fields?.custom_phone_number ? pageData?.mvk_item_content?.custom_fields?.custom_phone_number : false);
    }, [pageData]);

    return (
        <div class={`icon-and-number__component cell shrink grid-x phone-style-${settings.mvk_theme_config.header?.phone_number_style}`}>
            <Container settings={settings} customNumber={customNumber} />
        </div>
    );
};

const Container = ({ settings, customNumber }) => {
    switch (settings.mvk_theme_config.header?.phone_number_style) {
        case 'icon':
            return <PhoneIcon settings={settings} customNumber={customNumber} />
        default:
            return (
                <>
                    <PhoneIcon settings={settings} customNumber={customNumber} />
                    <PhoneNumber settings={settings} customNumber={customNumber} />
                </>
            );
    }
};

const PhoneIcon = ({ settings, customNumber }) => {
    const iconStyles = {
        color: (settings?.mvk_theme_config?.header?.phone_number_icon_color) ? settings?.mvk_theme_config?.header?.phone_number_icon_color : settings?.design?.colors?.main_nav?.nav_txt_color
    };

    let navIcon;
    settings.mvk_theme_config.header?.phone_icon_desktop || settings.mvk_theme_config.header?.phone_icon_mobile ?
        navIcon =
        <>
            {settings.mvk_theme_config.header?.phone_icon_desktop && <Imaging class='phone-icon-desktop' data={settings.mvk_theme_config.header?.phone_icon_desktop} />}
            {settings.mvk_theme_config.header?.phone_icon_mobile && <Imaging class='phone-icon-mobile' data={settings.mvk_theme_config.header?.phone_icon_mobile} />}
        </>
        : navIcon = <FontAwesomeIcon icon={faPhoneAlt} class='phone-icon' style={iconStyles} />

    return (
        <span class='phone-icon-container cell shrink'>
            <a href={"tel:" + (customNumber || settings.contact?.phone)}>
                {navIcon}
                <span class='show-for-sr'>Phone Icon</span>
            </a>
        </span>
    );
};

const PhoneNumber = ({ settings, customNumber }) => {
    const numberStyles = {
        color: (settings?.mvk_theme_config?.header?.phone_number_icon_color) ? settings?.mvk_theme_config?.header?.phone_number_icon_color : settings?.design?.colors?.main_nav?.nav_txt_color
    };

    let phoneStyle = settings.mvk_theme_config.header?.phone_number_style;

    return (
        <span class={`phone-number-container cell auto ${phoneStyle === 'icon-number' && 'number-margin'}`}>
            <a class='full-phone-number' href={"tel:" + (customNumber || settings.contact?.phone)} style={numberStyles}>
                {customNumber || settings.contact?.phone}
            </a>
        </span>
    );
};

export default Start;
