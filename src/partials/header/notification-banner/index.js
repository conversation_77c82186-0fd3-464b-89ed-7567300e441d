import React, { useContext } from 'react';
import styled from 'styled-components';

// CONTEXT.
import { SettingsContext } from "src/context";

// SCSS, CSS.
import "./index.scss";

const Start = (props) => {
    const [settings, setSettings] = useContext(SettingsContext);  

    // for show/hide toggle
    var currentDate = new Date();
    var startDate = settings?.mvk_theme_config?.header?.emergency?.start_date?.length > 0 ? new Date(settings?.mvk_theme_config?.header?.emergency?.start_date) : null;
    var endDate = settings?.mvk_theme_config?.header?.emergency?.end_date?.length > 0 ? new Date(settings?.mvk_theme_config?.header?.emergency?.end_date) : null;
    var isActive;

    if (startDate && endDate) {
        (currentDate >= startDate && currentDate < endDate) ? isActive = true : isActive = false;
    } else if (startDate && !endDate) {
        (currentDate >= startDate) ? isActive = true : isActive = false;
    } else if (!startDate && endDate) {
        (currentDate < endDate) ? isActive = true : isActive = false;
    }

    if (settings?.mvk_theme_config?.header?.emergency?.emergency_banner_text?.length > 0) {
        if (!settings?.mvk_theme_config?.header?.emergency?.schedule_show_hide || settings?.mvk_theme_config?.header?.emergency?.module_status === 'show' && isActive || settings?.mvk_theme_config?.header?.emergency?.module_status === 'hide' && !isActive) {
            return (<Container settings={settings} />);
        }
    } else {
        return null;
    }
};

const Container = ({settings}) => {
    var styles = { 
        backgroundColor: settings?.mvk_theme_config?.header?.emergency?.emergency_banner_background,
        color: '#fff'
     };

    return (
        <NotificationBanner
            id="notification-banner"
            oStyles={styles}
        >
            <div class="grid-container" dangerouslySetInnerHTML={{__html:settings?.mvk_theme_config?.header?.emergency?.emergency_banner_text}}></div>
        </NotificationBanner>
    );
};

const NotificationBanner = styled.div`
    background-color: ${props => props.oStyles.backgroundColor};
    color: ${props => props.oStyles.color};
`
export default Start;
