/*******************************************************************************************************
   Copyright 2021 - Calise Partners LLC d/b/a Imaginuity. All rights are reserved.
   Reproduction or transmission in whole or in part, in any form or by any means, electronic, mechanical
or otherwise, is prohibited without the prior written consent of Calise Partners LLC d/b/a Imaginuity.
********************************************************************************************************
   Project : 10618 Maverick Platform Development
   Author : Imaginuity Developers (<PERSON>, <PERSON>)
   Description : floating hamburger navigation style for RV2
   Creation Date : Fri Aug 6 2021
   Documentation : [documentation url - point to default wiki for project if no specific documentation for script]
 ********************************************************************************************************/

import React, { useState, useEffect, useContext } from 'react';
import { Link } from 'react-router-dom';
import { decode } from 'html-entities';


import './hamburger.scss';
import 'src/scss/hamburgers/hamburgers.scss';

import { NavigationContext } from 'src/context';
import HoursToday from "src/partials/hours/hours-today";
// HOOKS
import { useQuery } from 'src/hooks/query';
// HELPERS, YO.
const Imaging = React.lazy(() => import('src/helpers/imaging'));
const Clicker = React.lazy(() => import('src/helpers/clicker'));
import { withinDateRange } from "src/helpers/date";

const Start = ({ settings }) => {
    const [navigation, setNavigation] = useContext(NavigationContext);
    const navStyle = settings.mvk_theme_config.nav?.navigation_style;
    const [hamburgerToggle, setHamburgerToggle] = useState(false);

    useEffect(() => {
        var element = document.getElementById('hamburger');
        hamburgerToggle ? element.classList.add('is-active') : element.classList.remove('is-active');
    }, [hamburgerToggle])


    const Click = (e) => {
        var toggleValue = hamburgerToggle ? false : true;
        setHamburgerToggle(toggleValue);
    };

    var logoContainerStyles = {
        backgroundColor: pylot.design.hexToRGB(settings?.branding?.main_logo_background_color, settings?.branding?.main_logo_background_opacity)
    };

    return (
        <div class='hamburger__header'>
            <Floating settings={settings} navigation={navigation} hamburgerToggle={hamburgerToggle} checkActive={Click} />
            <div class='logo-container' style={logoContainerStyles}>
                <Link to={{ pathname: '/' }}>
                    <Imaging data={settings.branding?.main_logo} imageWidth={150} />
                </Link>
            </div>
            <div class='nav-hours-container'>
                {(settings.mvk_theme_config.header?.enable_hours && settings.mvk_theme_config.header?.hours_type === 'main') &&
                    <div class='hours-today'><HoursToday settings={settings} /></div>
                }
                <button id="hamburger" aria-label="Hamburger Menu Button" class={`hamburger hamburger--${settings.mvk_theme_config?.nav?.hamburger_type === 'animated' ? settings.mvk_theme_config?.nav?.hamburger_animation : 'vortex'}`} type="button" onClick={Click}>
                    <span class="hamburger-box">
                        <span class="hamburger-inner dark"></span>
                    </span>
                </button>
            </div>
        </div>
    );
};

const Floating = ({ settings, navigation, hamburgerToggle, checkActive }) => {
    const query = useQuery();
    const simulateDate = query.get('simulate-date');
    const navItemClick = (event) => {
        event.target.closest('.dropdown').classList.toggle('active');
    };
    const setState = (e) => {
        checkActive(e);
    }
    const reTest = new RegExp("^(http|https)://", "i");

    return (
        <nav class={hamburgerToggle ? 'nav-menu active' : 'nav-menu'}>
            {navigation?.main && Object.keys(navigation.main).map((item, index) => {
                const isActive = settings.holiday?.enable_holiday ? withinDateRange(false, false, true, false, simulateDate) : true;
                const showNavItem = (!settings.holiday?.enable_holiday && !item.classes?.includes('mvk-holiday-show')) || (!item.classes?.includes('mvk-holiday-show') && !item.classes?.includes('mvk-holiday-hide')) || (settings.holiday?.enable_holiday && item.classes?.includes('mvk-holiday-show') && isActive) || (item.classes?.includes('mvk-holiday-hide') && !isActive);

                if (showNavItem) {
                    return (
                        navigation.main[item].children ? (
                            <div key={index} class='dropdown'>
                                <p class='menu-dropdown' onClick={navItemClick}>
                                    {decode(navigation.main[item].title)}
                                    <span>{decode('&#9660;')}</span>
                                </p>
                                <ul class='dropdown-items'>
                                    {navigation.main[item].children.map((child, index) =>
                                        (reTest.test(child.url)) ?
                                            <li key={index}>
                                                <Clicker type='anchor' class='menu-item' url={child.url} target={"_blank"}>{decode(child.title)}</Clicker>
                                            </li>
                                            :
                                            <li key={index}>
                                                <Link class='menu-item dropdown-item' to={child.url} target={child.target} onClick={setState}>{decode(child.title)}</Link>
                                            </li>
                                    )}
                                </ul>
                            </div>
                        )
                            :

                            (reTest.test(navigation.main[item].url)) ?
                                <Clicker key={index} type='anchor' class='menu-item' url={navigation.main[item].url} target={"_blank"}>{decode(navigation.main[item].title)}</Clicker>
                                :
                                (
                                    <Link key={index} class='menu-item' to={navigation.main[item].url} target={navigation.main[item].target} onClick={setState}>{decode(navigation.main[item].title)}</Link>
                                )
                    );
                } else {
                    return null;
                }
            })}
        </nav>
    );
};
export default Start;
