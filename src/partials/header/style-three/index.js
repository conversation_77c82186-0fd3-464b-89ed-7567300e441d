import React, { Suspense } from 'react';
import { useEffect } from 'react';

const Hamburger = React.lazy(() => import('./hamburger'));

const Start = ({ settings }) => {

    useEffect(() => {
        // header style 3 - hamburger, floating
        var StyleSheet = document.createElement('style');
        var StyleStrings = ``;

        StyleStrings += ` .hamburger__header nav.nav-menu { background-color: ${settings.design?.colors?.main_nav.nav_bg_color}; } `;
        
        // hamburger button styles
        StyleStrings += ` .hamburger__header .hamburger .hamburger-inner { background-color: ${settings.design?.colors?.main_nav?.nav_txt_color}; } `;
        StyleStrings += ` .hamburger__header .hamburger .hamburger-inner:before { background-color: ${settings.design?.colors?.main_nav?.nav_txt_color}; } `;
        StyleStrings += ` .hamburger__header .hamburger .hamburger-inner:after { background-color: ${settings.design?.colors?.main_nav?.nav_txt_color}; } `;

        // hamburger is active styles
        StyleStrings += ` .hamburger__header .hamburger.is-active .hamburger-inner { background-color: #fff; } `;
        StyleStrings += ` .hamburger__header .hamburger.is-active .hamburger-inner:before { background-color: #fff; } `;
        StyleStrings += ` .hamburger__header .hamburger.is-active .hamburger-inner:after { background-color: #fff; } `;

        // hamburger nav menu hover styles
        StyleStrings += ` .hamburger__header .nav-menu .menu-item:hover { color: ${settings.design?.colors?.main_nav?.nav_txt_color }; } `;
        StyleStrings += ` .hamburger__header .nav-menu .menu-dropdown:hover { color: ${settings.design?.colors?.main_nav?.nav_txt_color }; } `;
        StyleStrings += ` .hamburger__header .nav-menu .dropdown-item:hover { color: ${settings.design?.colors?.main_nav?.nav_txt_color }; } `;
        StyleStrings += ` .hamburger__header .nav-menu .menu-item:hover { background-color: ${settings.design?.colors?.main_nav.nav_txt_hv_color }; } `;
        StyleStrings += ` .hamburger__header .nav-menu .menu-dropdown:hover { background-color: ${settings.design?.colors?.main_nav.nav_txt_hv_color }; } `;
        StyleStrings += ` .hamburger__header .nav-menu .dropdown-item:hover { background-color: ${settings.design?.colors?.main_nav.nav_txt_hv_color }; } `;

        StyleSheet.innerHTML = StyleStrings;
        document.getElementsByTagName('head')[0].appendChild(StyleSheet);
    }, [])
    
    return (
        <Suspense fallback={<div />}>
            <Hamburger settings={settings} />
        </Suspense>
    );
};

export default Start;