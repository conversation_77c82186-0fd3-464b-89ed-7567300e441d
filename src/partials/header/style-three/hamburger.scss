/*******************************************************************************************************
   Copyright 2021 - Calise Partners LLC d/b/a Imaginuity. All rights are reserved. 
   Reproduction or transmission in whole or in part, in any form or by any means, electronic, mechanical 
or otherwise, is prohibited without the prior written consent of Calise Partners LLC d/b/a Imaginuity.
********************************************************************************************************
   Project : 10618 Maverick Platform Development
   Author : Imaginuity Developers (<PERSON>, <PERSON>)
   Description : stylesheet for hamburger nav
   Creation Date : Fri Aug 6 2021
   Documentation : [documentation url - point to default wiki for project if no specific documentation for script]
 ********************************************************************************************************/

.hamburger__header {
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;

    @media screen and (min-width: 640px) {
        justify-content: space-between;
        align-items: flex-start;
    }

    .logo-container {
        @media screen and (max-width: 767px) {
            background-color: transparent !important;
        }
        
        @media screen and (min-width: 768px) {
            padding: 1rem;
        }
    }

    .nav-hours-container {
        display: flex;
        padding-top: 1rem;

        @media screen and (max-width: 639px) {
            position: absolute;
            right: 0;
        }

        @media screen and (min-width: 640px) {
            flex-flow: row nowrap;
            align-items: center;
        }
    }

    .hours-today {
        color: #fff;
        @media screen and (max-width: 639px) {
            display: none;
        }
    }

    #hamburger {
        z-index: 999;

        .hamburger-inner {
            @media screen and (min-width: 768px) {
                background-color: #fff;
                &:before {
                    background-color: #fff;
                }

                &::after {
                    background-color: #fff;
                }
            }
        }
    }

    .nav-menu {
        position: absolute;
        top: 0;
        right: 0;
        margin: 0 auto;
        // margin-left: -9.375rem;
        z-index: 998;
        width: 18.75rem;
        text-align: center;
        padding: 0;
        max-height: 0;
        -webkit-transition: all 0.5s linear;
        -moz-transition: all 0.5s linear;
        transition: all 0.5s linear;
        overflow: hidden;

        &.active {
            max-height: 37.5rem;
            padding-top: 7.1875rem;
            padding-bottom: 1rem;

            @media screen and (min-width: 640px) {
                padding-top: 5rem;
            }
        }
    }

    .menu-item {
        text-decoration: none;
        color: #fff;
        font-size: 1rem;
        width: 100%;
        display: inline-block;
        padding: 0.375rem 0;
        cursor: pointer;
    }

    .dropdown {
        padding: 0;
        overflow: hidden;
        p.menu-dropdown {
            color: #fff;
            padding: 0.375rem 0;
            margin-bottom: 0;
            cursor: pointer;
            font-size: 1rem;
            span {
                font-size: 0.75rem;
                padding-left: 0.5rem;
            }
        }

        &.active {
            .dropdown-items {
                max-height: 31.25rem;
            }
        }
    }

    .dropdown-items {
        background-color: rgba(0, 0, 0, 0.2);
        max-height: 0;
        list-style-type: none;
        padding-left: 0;
        margin-top: 0;
        margin-bottom: 0;
        -webkit-transition: all 0.5s linear;
        -moz-transition: all 0.5s linear;
        transition: all 0.5s linear;
    }
}
