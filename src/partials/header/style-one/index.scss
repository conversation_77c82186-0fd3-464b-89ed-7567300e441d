#header {
    #style-one {
        .logo-box {
            height: 100%;

            @media screen and (max-width: 1199px) {
                display: none;
            }

            @media screen and (min-width: 1200px) {
                display: flex;
                align-items: center;

                &.add-min-height {
                    min-height: 140px;
                }

                &.no-min-height {
                    .logo {
                        padding: 0 0 16px 0;
                    }
                }
            }

            &:hover {
                cursor: pointer;
            }

            .logo {
                max-width: 300px;
                padding: 16px;

                @media screen and (max-width: 1200px) {
                    margin: 0px 10px;
                }

                img[src$=".svg"] {
                    width: 100%;
                }
            }
        }

        &.scrolled {
            .logo-box {
                min-height: 0;

                .logo {
                    padding-top: 0;
                }
            }
        }

        .logo-centered {
            .logo-box .logo {
                padding: 16px;

                img {
                    max-height: 80px;
                    object-fit: contain;
                }
            }
        }

        #style-one {
            #search-icon {
                #search-box {
                    margin: 15px 0px;
                    // height:100px;
                    float: right;
                    padding: 0px;
                    text-align: right;

                    #search-input {
                        border: 0px;
                        border-bottom: 2px solid rgba(0, 0, 0, 0.3);
                        background-color: white;
                        max-width: 300px;
                        float: right;
                        border-radius: 0px;
                    }

                    svg,
                    img {
                        margin: 0px 10px;
                        width: 30px;
                        min-width: 30px;
                        height: 30px;
                        min-height: 30px;
                        color: black;
                        fill: black;

                        &:hover {
                            cursor: pointer;
                        }
                    }
                }
            }
        }

        #nav-dropdown {
            min-width: 100%;
            // background-color: #fff;
        }

        .hours-container {
            position: relative;

            .hours-today {
                position: absolute;
                right: 0;
                padding: 10px;
                font-size: 1rem;
            }

            @media (max-width: 1199px) {
                display: none;
            }
        }

        #search-icon {
            @media screen and (min-width: 1200px) {
                display: flex;
                justify-content: center;
                align-items: center;

                svg,
                img {
                    width: 1.5625rem;
                    cursor: pointer;
                }
            }
        }
        &.three-quarters {
            #nav-dropdown .mobile-search-nav .mobile-menu {
                .mobile-logo {
                    margin: 10px 0;
                    justify-content: center;
                    transition: 0.3s;
                    // img {
                    //     width: 100%;
                    // }
                }
                .layout-spacer {
                    width: 70px;
                }
                &.with-label {
                    .layout-spacer {
                        width: 120px;
                    }
                }
            }
            &.active {
                #nav-dropdown .mobile-logo {
                    opacity: 0;
                }
            }
        }
        > .grid-x > .icon-and-number__component {
            @media screen and (max-width: 1199px) {
                display: none;
            }
        }
        &.accordion {
            #dropdown {
                .dropdown-item {
                    .dropdown-item-children {
                        &:has(.page-icon) {
                            margin-left: 0;
                        }
                    }
                }
            }
        }
    }
}
