/*******************************************************************************************************
   Copyright 2020 - Calise Partners LLC d/b/a Imaginuity. All rights are reserved.
   Reproduction or transmission in whole or in part, in any form or by any means, electronic, mechanical
or otherwise, is prohibited without the prior written consent of Calise Partners LLC d/b/a Imaginuity.
********************************************************************************************************
   Project : 10618 Maverick Platform Development
   Author : Imaginuity Developers (<PERSON>, <PERSON>)
   Description : Hamburger icon that toggles hover/dropdown menus.
   Creation Date : Tue Nov 24 2020
   Documentation : [documentation url - point to default wiki for project if no specific documentation for script]
 ********************************************************************************************************/

import React, { useContext, useEffect, useState, useCallback } from 'react';

// HELPERS.
const Clicker = React.lazy(() => import('src/helpers/clicker'));
const Imaging = React.lazy(() => import('src/helpers/imaging'));
import IconOnly from 'src/partials/header/phone/icon-only';
import IconNumber from 'src/partials/header/phone/icon-number';
import { ScrollHandler } from 'src/helpers/scroll';

// CONTEXT.
import { SettingsContext, LocationsContext } from "src/context";
import { HeaderContext } from "src/partials/header/context";

// SCSS, CSS.
import 'src/partials/header/style-one/hamburger.scss';
import 'src/scss/hamburgers/hamburgers.scss';

const Start = (props) => {
    return (
        <div id="nav-dropdown" class="flex1">
            <Container />
        </div>
    );
};

const Container = (props) => {
    const headerContext = useContext(HeaderContext);
    const [settings, setSettings] = useContext(SettingsContext);

    useEffect(() => {
        var element = document.getElementById(`hamburger`);
        if (headerContext.toggled.hamburger) {
            element.classList.add('is-active');
        } else {
            element.classList.remove('is-active');
        }
    }, [headerContext.toggled.hamburger]);

    const Click = useCallback((e) => {
        var other = (headerContext.toggled.hamburger) ? false : true;
        headerContext.Toggle('hamburger', other)
    });

    var navIcon;
    if (settings.mvk_theme_config.header?.enable_phone_number) {
        if (settings.mvk_theme_config.header?.phone_number_style === 'icon') {
            navIcon = <IconOnly settings={settings} />;
        } else {
            navIcon = <IconNumber settings={settings} />;
        }
    } else {
        navIcon = null;
    }
    let style = {
        backgroundColor: headerContext?.data?.design?.colors?.main_nav?.nav_mobile_txt_color ? headerContext?.data?.design?.colors?.main_nav?.nav_mobile_txt_color : headerContext.data.design.colors.main_nav?.nav_txt_color
    }
    const closedLabel = settings.mvk_theme_config?.nav?.closed_hamburger_label ? settings.mvk_theme_config?.nav?.closed_hamburger_label : '';
    const openLabel = settings.mvk_theme_config?.nav?.open_hamburger_label ? settings.mvk_theme_config?.nav?.open_hamburger_label : '';

    return (
        <div class="flexbox column end mobile-search-nav grid-container">
            <div class={`grid-x flex1 mobile-menu hamburger-${settings.mvk_theme_config?.nav?.hamburger_position}${(closedLabel || openLabel) ? ' with-label' : ''}`}>
                <Logo />
                {navIcon ? navIcon : headerContext.data.theme.header.header_type === 'gradient' ? <div className='layout-spacer' /> : null}
                <Hamburger settings={settings} style={style} click={Click} toggled={headerContext.toggled.hamburger} closedLabel={closedLabel} openLabel={openLabel} />
            </div>
        </div>
    );
};

const Hamburger = ({ settings, style, click, toggled, closedLabel, openLabel }) => {
    const withCustom = settings.mvk_theme_config?.nav?.hamburger_type === 'custom' && settings.mvk_theme_config?.nav?.custom_hamburger_icon ? true : false;

    return (
        <div class={`mobile-nav cell shrink`}>
            <Clicker id="hamburger" ariaLabel="Hamburger Menu Button" class={`hamburger hamburger--${settings.mvk_theme_config?.nav?.hamburger_type === 'animated' ? settings.mvk_theme_config?.nav?.hamburger_animation : 'vortex'}${withCustom ? ' with-custom' : ''}`} type="button" process={click}>
                {settings.mvk_theme_config?.nav?.hamburger_position === 'right' && (closedLabel || openLabel) &&
                    <span className='label' style={{ color: style.backgroundColor }}>{toggled ? openLabel : closedLabel}</span>
                }
                {withCustom ?
                    <>
                        {!toggled ? <Imaging className='custom-hamburger' data={settings.mvk_theme_config?.nav?.custom_hamburger_icon} />
                            :
                            <span class="hamburger-box">
                                <span class="hamburger-inner dark" style={style}></span>
                            </span>
                        }
                    </>
                    :
                    <span class="hamburger-box">
                        <span class="hamburger-inner dark" style={style}></span>
                    </span>
                }
                {settings.mvk_theme_config?.nav?.hamburger_position === 'left' && (closedLabel || openLabel) &&
                    <span className='label' style={{ color: style.backgroundColor }}>{toggled ? openLabel : closedLabel}</span>
                }
            </Clicker>
        </div>
    )
}

export const Logo = () => {
    const [settings, setSettings] = useContext(SettingsContext);
    const headerContext = useContext(HeaderContext);
    const [locations] = settings.current_location ? useContext(LocationsContext) : [];
    const scroll = ScrollHandler();
    const [homeUrl, setHomeUrl] = useState(settings.fleet_home_url ? new URL(settings.fleet_home_url).pathname : `https://${config.domain}/`)

    useEffect(() => {
        setHomeUrl(settings.current_location && locations[settings.current_location].mvk_item_content?.custom_fields?.logo_to_location_home ? new URL(locations[settings.current_location].mvk_item_meta?.url).pathname : `https://${config.domain}/`);
    }, [settings.current_location])

    var styles = {};
    if (settings?.branding?.main_logo_background_color) {
        styles.backgroundColor = pylot.design.hexToRGB(settings?.branding?.main_logo_background_color, settings?.branding?.main_logo_background_opacity);
    }

    const Click = useCallback((e) => {
        headerContext.Toggle('hamburger', false);
    });

    let logoData = (settings?.mvk_theme_config?.nav?.navigation_style === 'logo-centered' && settings.branding?.alternate_logo && scroll) ||
        (settings?.mvk_theme_config?.nav?.navigation_style === 'logo-centered' && settings.branding?.alternate_logo && headerContext.toggled.hamburger)
        ? settings.branding?.alternate_logo
        : settings.branding?.main_logo;

    if (settings.current_location && locations[settings.current_location].mvk_item_content?.custom_fields?.location_logo) {
        logoData = locations[settings.current_location].mvk_item_content?.custom_fields?.location_logo;
    }

    return (
        <div className={`mobile-logo cell auto ${settings.mvk_theme_config.nav?.navigation_style}`}>
            <Clicker type={settings.hide_nav ? '' : 'anchor'} url={settings.hide_nav ? null : homeUrl} process={Click} ariaLabel='home'>
                <Imaging data={logoData} />
            </Clicker>
        </div>
    );
}

export default Start;
