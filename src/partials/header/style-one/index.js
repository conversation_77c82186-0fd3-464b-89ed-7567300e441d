import React, { useContext, useCallback, useState, useEffect, useRef } from 'react';
import { useNavigate } from 'react-router-dom';

// CONTEXT.
import { PrincipalContext, SettingsContext, NavigationContext } from "src/context";
import { HeaderContext } from "src/partials/header/context";

// HELPERS.
const Clicker = React.lazy(() => import('src/helpers/clicker'));
const Imaging = React.lazy(() => import('src/helpers/imaging'));
import { ScrollHandler } from 'src/helpers/scroll';
import { useOnClickOutside } from 'src/hooks';
// PARTIALS
import HoursToday from "src/partials/hours/hours-today";
import Logo from "src/partials/header/style-one/logo";
import Expanded from 'src/partials/header/style-one/expanded';
import Hamburger from 'src/partials/header/style-one/hamburger';
import Dropdown from 'src/partials/header/style-one/dropdown';
import IconNumber from 'src/partials/header/phone/icon-number';

// SCSS.
import 'src/partials/header/style-one/index.scss';
import { StyleOne } from './styles';

const Start = (props) => {
    const [settings, setSettings] = useContext(SettingsContext);
    const [navigation, setNavigation] = useContext(NavigationContext);
    const [icons, setIcons] = useState(false);
    const headerContext = useContext(HeaderContext);
    const scroll = ScrollHandler();

    useEffect(() => {
        if (navigation.main) {
            navigation?.main?.forEach((navItem) => {
                if (navItem.icon) {
                    setIcons(true);
                }
            })
        }
    }, []);

    var desktopStyle = headerContext.data.design.colors.main_nav.nav_background_gradient ? 'transparent' : headerContext.data.design.colors.main_nav.nav_bg_color;
    var mobileStyle = headerContext.data.design.colors.main_nav.nav_mobile_bg_color;
    var hoursStyle = { color: settings.design?.colors?.main_nav?.nav_txt_color };

    return (
        <StyleOne desktopBg={desktopStyle} mobileBg={mobileStyle} id="style-one" className={`${(icons && headerContext.data.theme.header.header_type === 'fixed' && scroll) ? 'icons scrolled' : icons ? 'icons' : ''} ${settings?.mvk_theme_config?.nav?.navigation_type} ${headerContext.toggled?.hamburger ? 'active' : ''}`}>
            {(settings.mvk_theme_config.header.enable_hours && !settings.mvk_theme_config.nav.utility) &&
                <div id="hours-container" class='hours-container grid-x'>
                    <div class='hours-today' style={hoursStyle}>
                        <HoursToday settings={settings} delimiter={':'} />
                    </div>
                </div>
            }
            <div class="grid-x">
                {settings.mvk_theme_config.nav?.navigation_style !== 'logo-centered' && <Logo />}
                {!headerContext.toggled.main && <Expanded />}
                {headerContext.toggled.main && <div class="cell auto" />}
                {settings.mvk_theme_config.header?.search && settings.mvk_theme_config.header?.search_type == 'main' && settings.mvk_theme_config.header?.search_style == 'icon' && <SearchBox />}
                {settings.mvk_theme_config.header?.enable_phone_number && settings.mvk_theme_config.header?.phone_number_type == 'main' && <IconNumber settings={settings} />}
                <Hamburger />
            </div>
            <Dropdown />
        </StyleOne>
    );
};



const SearchBox = (props) => {
    const [principal, setPrincipal] = useContext(PrincipalContext);
    const [settings, setSettings] = useContext(SettingsContext);
    const headerContext = useContext(HeaderContext);
    const ref = useRef();

    const navigate = useNavigate();

    const Click = useCallback((e) => {
        var searchInput = document.getElementById('search-input');
        if (searchInput) {
            searchInput.focus();
        }
        var other = headerContext.toggled.search ? false : true;
        if (searchInput.value?.length <= 0 || other) {
            headerContext.Toggle('search', other);
        } else {
            Submit();
        }
    });

    const Submit = (e) => {
        e?.preventDefault();
        var term = document.getElementById('search-input').value;
        if (term && term.length > 0) {
            navigate(principal.subsite ? `/${principal.subsite}/search?s=${term}` : `/search?s=${term}`);
        }
    }
    useOnClickOutside(ref, () => {
        headerContext.Toggle('search', false);
    })
    var styles = { fill: settings.design?.colors?.main_nav?.nav_txt_color };

    return (
        <div ref={ref} id="search-icon" class="cell shrink">
            <div id="search-box" class="flexbox align closable">
                <div class={`flex1 ${headerContext.toggled.search ? 'search-open' : 'search-closed'}`}>
                    <form id="search-form" method="post" action="#" autocomplete="off" onSubmit={Submit} role="search" aria-label="sitewide">
                        <label for="search-input" class="show-for-sr">Search</label>
                        <input id="search-input" type="text" placeholder="Search" />
                    </form>
                </div>
                <Clicker class="flex" process={Click} ariaLabel={'search submit'}>
                    {settings.mvk_theme_config.header?.search_icon_desktop ?
                        <Imaging data={settings.mvk_theme_config.header?.search_icon_desktop} />
                        :
                        <svg version="1.1" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" style={styles} aria-label="search icon">
                            <g id="info" />
                            <g id="icons">
                                <path d="M22.4,19.6l-4.8-4.8c0.9-1.4,1.4-3,1.4-4.8c0-5-4-9-9-9s-9,4-9,9s4,9,9,9c1.8,0,3.4-0.5,4.8-1.4l4.8,4.8   c0.4,0.4,0.9,0.6,1.4,0.6c1.1,0,2-0.9,2-2C23,20.4,22.8,19.9,22.4,19.6z M5,10c0-2.8,2.2-5,5-5s5,2.2,5,5s-2.2,5-5,5S5,12.8,5,10z" id="icon-search" />
                            </g>
                        </svg>
                    }
                </Clicker>
            </div>
        </div>
    );
};

export default Start;
