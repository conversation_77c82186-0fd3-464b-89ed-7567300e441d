import React, { useContext } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { faSearch } from '@fortawesome/free-solid-svg-icons'
import { useNavigate } from 'react-router-dom';

// HELPERS.
const Imaging = React.lazy(() => import('src/helpers/imaging'));

// CONTEXT.
import { PrincipalContext, SettingsContext } from "src/context";
import { HeaderContext } from "src/partials/header/context";

export const MobileSearch = (props) => {
    const [principal, setPrincipal] = useContext(PrincipalContext);
    const headerContext = useContext(HeaderContext);
    const [settings, setSettings] = useContext(SettingsContext);
    const navigate = useNavigate();

    var style = {
        borderColor: 'black',
        color: 'black'
    };

    const Submit = (e) => {
        e.preventDefault();
        var term = document.getElementById('search-input2').value;
        if (term && term.length > 0) {
            headerContext.Toggle('hamburger', false);
            navigate(principal.subsite ? `/${principal.subsite}/search?s=${term}` : `/search?s=${term}`);
        }
    }

    return (
        <div id="mobile-search-box" class="flex1 search-box">
            <div id='mobile-search'>
                <form id="search-form" method="post" action="#" autocomplete="off" onSubmit={Submit} role="search" aria-label="sitewide">
                    <div class="mobile-container">
                        <input id="search-input2" type="text" placeholder="Search" style={style} />
                        {settings.mvk_theme_config.header?.search_icon_mobile ?
                            <Imaging data={settings.mvk_theme_config.header?.search_icon_mobile} className="mobile-search-icon" onClick={Submit} aria-label="submit search"/>
                            :
                            <FontAwesomeIcon icon={faSearch} className="mobile-search-icon" onClick={Submit} aria-label="submit search"/>
                        }
                    </div>
                </form>
            </div>
        </div>
    );
}