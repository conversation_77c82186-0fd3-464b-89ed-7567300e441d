/*******************************************************************************************************
   Copyright 2020 - Calise Partners LLC d/b/a Imaginuity. All rights are reserved.
   Reproduction or transmission in whole or in part, in any form or by any means, electronic, mechanical
or otherwise, is prohibited without the prior written consent of Calise Partners LLC d/b/a Imaginuity.
********************************************************************************************************
   Project : 10618 Maverick Platform Development
   Author : Imaginuity Developers (<PERSON>, <PERSON>)
   Description : Dropdown menu for mobile devices.
   Creation Date : Mon Nov 23 2020
   Documentation : [documentation url - point to default wiki for project if no specific documentation for script]
 ********************************************************************************************************/

// Packages
import React, { useState, useContext, useCallback, useMemo, useEffect } from 'react';
import styled from 'styled-components';
import { decode } from 'html-entities';

// HELPERS.
const Clicker = React.lazy(() => import('src/helpers/clicker'));
const Imaging = React.lazy(() => import('src/helpers/imaging'));
import { withinDateRange } from "src/helpers/date";
// HOOKS
import { useQuery } from 'src/hooks/query';
// CONTEXT.
import { SettingsContext, NavigationContext, LocationsContext } from 'src/context';
import { HeaderContext } from 'src/partials/header/context';
// PARTIALS
import { Logo } from 'src/partials/header/style-one/hamburger';
import { MobileSearch } from 'src/partials/header/style-one/mobileSearch';
const Button = React.lazy(() => import('src/partials/button'));
const ReservationsModal = React.lazy(() => import('src/partials/reservations-modal'));
const ServiceTitanWidget = React.lazy(() => import('src/partials/service-titan-widget'));
// SCSS.
import 'src/partials/header/style-one/dropdown.scss';
import { StyledButton } from './styles';

const Start = (props) => {
    const headerContext = useContext(HeaderContext);
    const [settings, setSettings] = useContext(SettingsContext);

    if (headerContext.toggled.hamburger || settings.mvk_theme_config.nav?.navigation_type === 'three-quarters') {
        return (<Toggled settings={settings} />);
    } else {
        return (<div />);
    }
}

const Toggled = ({ settings }) => {
    const headerContext = useContext(HeaderContext);
    const [navigation, setNavigation] = useContext(NavigationContext);
    const [locations] = settings.current_location ? useContext(LocationsContext) : [];
    const [buttonType, setButtonType] = useState(settings?.mvk_theme_config?.nav?.navigation_button_style);
    const txtColor = headerContext?.data?.design?.colors?.main_nav?.nav_mobile_txt_color ? headerContext?.data?.design?.colors?.main_nav?.nav_mobile_txt_color : headerContext?.data?.design?.colors?.main_nav?.nav_txt_color;
    const [reservationsButton, setReservationsButton] = useState(((settings.current_location && locations[settings.current_location]?.mvk_item_content?.custom_fields?.reservations_button_type === 'button-link' && locations[settings.current_location]?.mvk_item_content?.custom_fields?.reservations_link) || (!settings.current_location && settings.restaurant?.reservations_button_type === 'button-link' && settings.restaurant?.reservations_link)) && settings.restaurant?.enable_reservations_button && settings.restaurant?.reservations_button_placement?.includes('main-navigation'))
    const [reservationsScript, setReservationsScript] = useState(((settings.current_location && locations[settings.current_location]?.mvk_item_content?.custom_fields?.reservations_button_type === 'button-script' && locations[settings.current_location]?.mvk_item_content?.custom_fields?.reservations_script) || (!settings.current_location && settings.restaurant?.reservations_button_type === 'button-script' && settings.restaurant?.reservations_script)) && settings.restaurant?.enable_reservations_button && settings.restaurant?.reservations_button_placement?.includes('main-navigation'))
    const [serviceTitanButton, setServiceTitanButton] = useState(((settings.current_location && locations[settings.current_location]?.mvk_item_content?.custom_fields?.location_st_api_key) || (!settings.current_location && settings.service_titan?.api_key)) && settings.service_titan?.placement?.includes('main-navigation'))

    useEffect(() => {
        setReservationsButton(((settings.current_location && locations[settings.current_location]?.mvk_item_content?.custom_fields?.reservations_button_type === 'button-link' && locations[settings.current_location]?.mvk_item_content?.custom_fields?.reservations_link) || (!settings.current_location && settings.restaurant?.reservations_button_type === 'button-link' && settings.restaurant?.reservations_link)) && settings.restaurant?.enable_reservations_button && settings.restaurant?.reservations_button_placement?.includes('main-navigation'));
        setReservationsScript(((settings.current_location && locations[settings.current_location]?.mvk_item_content?.custom_fields?.reservations_button_type === 'button-script' && locations[settings.current_location]?.mvk_item_content?.custom_fields?.reservations_script) || (!settings.current_location && settings.restaurant?.reservations_button_type === 'button-script' && settings.restaurant?.reservations_script)) && settings.restaurant?.enable_reservations_button && settings.restaurant?.reservations_button_placement?.includes('main-navigation'));
        setServiceTitanButton(((settings.current_location && locations[settings.current_location]?.mvk_item_content?.custom_fields?.location_st_api_key) || (!settings.current_location && settings.service_titan?.api_key)) && settings.service_titan?.placement?.includes('main-navigation'));
    }, [settings.current_location]);

    let style = {
        backgroundColor: headerContext?.data?.design?.colors?.main_nav?.nav_mobile_bg_color ? headerContext?.data?.design?.colors?.main_nav?.nav_mobile_bg_color : headerContext?.data?.design?.colors?.main_nav?.nav_bg_color,
        color: txtColor,
        borderColor: headerContext?.data?.design?.colors?.primary_color
    };

    let homeStyle = style;
    if (headerContext?.data?.design?.colors?.main_nav?.nav_background_gradient) {
        style = {
            backgroundImage: `linear-gradient(to bottom left, ${headerContext?.data?.design?.colors?.main_nav?.nav_bg_color}, ${headerContext?.data?.design?.colors?.main_nav?.nav_bg_color_2})`,
            color: txtColor,
        };
        homeStyle = {
            backgroundColor: 'transparent',
            color: txtColor
        }
    }

    const MouseOver = () => setButtonType(settings?.mvk_theme_config?.nav?.navigation_button_style === 'primary' ? 'secondary' : 'primary');
    const MouseOut = () => setButtonType(settings?.mvk_theme_config?.nav?.navigation_button_style);

    return (
        <div id="dropdown" class={`${headerContext?.data?.design?.colors?.main_nav?.nav_background_gradient ? 'with-gradient' : ''} ${settings?.mvk_theme_config?.nav?.navigation_style}`}>
            <div class='dropdown-inner-container' style={style}>
                {(settings.mvk_theme_config.header?.search && settings.mvk_theme_config.header?.search_type === 'menu-utility') &&
                    <MobileSearch />
                }
                {settings.mvk_theme_config.nav?.navigation_type === 'three-quarters' &&
                    <Logo />
                }
                {((!settings.current_location && navigation?.main) || (settings.current_location && !settings.locations?.enable_location_navigation)) &&
                    <Iterate items={navigation?.main} />
                }
                {(settings.current_location && locations[settings.current_location]?.location_navigation) &&
                    <Iterate items={locations[settings.current_location]?.location_navigation} />
                }
                {(settings.mvk_theme_config.nav?.navigation_style === 'logo-centered' && navigation?.main_2) &&
                    <Iterate items={navigation?.main_2} />
                }
                {(reservationsButton) && <div className='dropdown-item'><Button className='nav-button' title={settings?.restaurant?.reservations_button_label} url={settings.current_location ? locations[settings.current_location]?.mvk_item_content?.custom_fields?.reservations_link : settings.restaurant?.reservations_link} target={'_blank'} type={settings?.restaurant?.reservations_button_style} tone={settings?.restaurant?.reservations_background_value} noIcon={true} /></div>}
                {(reservationsScript) && <div className='dropdown-item'><ReservationsModal type='button' settings={settings} script={settings.current_location ? locations[settings.current_location]?.mvk_item_content?.custom_fields?.reservations_script : settings.restaurant?.reservations_script} /></div>}
                {(serviceTitanButton) && <div className='dropdown-item'><ServiceTitanWidget apiKey={settings.current_location ? locations[settings.current_location]?.mvk_item_content?.custom_fields?.location_st_api_key : settings.service_titan?.api_key} settings={settings.service_titan?.main_navigation} type='main-navigation' /></div>}
                {(settings.mvk_theme_config.nav?.utility && navigation?.utility) &&
                    <Iterate items={navigation?.utility} groupClass={`utility ${settings?.mvk_theme_config?.nav?.utility_mobile_layout} ${settings?.mvk_theme_config?.nav?.enable_utility_separators ? settings?.mvk_theme_config?.nav?.utility_separators : ''}`} />
                }
                {(settings.mvk_theme_config.nav?.navigation_style === 'logo-centered' && settings.mvk_theme_config.nav?.navigation_button) &&
                    <div className='flexbox'>
                        <StyledButton color={txtColor} className='mobile-menu-button' onMouseOver={MouseOver} onMouseOut={MouseOut} title={settings.mvk_theme_config.nav?.navigation_button.title} url={settings.mvk_theme_config.nav?.navigation_button.url} target={settings.mvk_theme_config.nav?.navigation_button.target} type={buttonType} tone={'dark'} />
                    </div>
                }
            </div>
        </div>
    );
}

const Iterate = ({ items, groupClass }) => {
    return (
        <div class={`flexbox column ${groupClass ? groupClass : ''}`}>
            {Object.keys(items).sort((a, b) => items[a].order - items[b].order).map(key => <Item item={items[key]} />)}
        </div>
    );
};

const Item = ({ item }) => {
    const headerContext = useContext(HeaderContext);
    const [settings, setSettings] = useContext(SettingsContext);
    const [showChildren, setChildren] = useState(false);
    const txtColor = headerContext?.data?.design?.colors?.main_nav?.nav_mobile_txt_color ? headerContext?.data?.design?.colors?.main_nav?.nav_mobile_txt_color : headerContext?.data?.design?.colors?.main_nav?.nav_txt_color;
    var classes = (item.classes) ? item.classes.join(' ') : null;
    const query = useQuery();
    const simulateDate = query.get('simulate-date');

    // IDs, NEEDED FOR AXIAL SCRAPE JS.
    const IDs = useMemo(() => {
        return {
            primary: item.ID ? `dropdown-item-${item.ID}` : '',
            children: item.ID ? `dropdown-children-${item.ID}` : ''
        };
    }, []);

    let style = {
        backgroundColor: headerContext?.data?.design?.colors?.main_nav?.nav_mobile_bg_color ? headerContext?.data?.design?.colors?.main_nav?.nav_mobile_bg_color : headerContext?.data?.design?.colors?.main_nav?.nav_bg_color,
        color: txtColor,
        borderColor: headerContext?.data?.design?.colors?.primary_color
    };
    if (headerContext?.data?.design?.colors?.main_nav?.nav_background_gradient) {
        style = {
            backgroundImage: `linear-gradient(to bottom left, ${headerContext?.data?.design?.colors?.main_nav?.nav_bg_color}, ${headerContext?.data?.design?.colors?.main_nav?.nav_bg_color_2})`,
            color: txtColor,
        };
    }
    const CloseHamburger = useCallback((e) => {
        headerContext.Toggle('hamburger', false);
    }, []);

    const ToggleChildren = (e) => {
        e.preventDefault();
        setChildren(!showChildren);
    };
    // Logic to show / hide nav items for holiday functionalilty
    const isActive = settings.holiday?.enable_holiday ? withinDateRange(false, false, true, false, simulateDate) : true;
    const showNavItem = (!settings.holiday?.enable_holiday && !item.classes?.includes('mvk-holiday-show')) || (!item.classes?.includes('mvk-holiday-show') && !item.classes?.includes('mvk-holiday-hide')) || (settings.holiday?.enable_holiday && item.classes?.includes('mvk-holiday-show') && isActive) || (item.classes?.includes('mvk-holiday-hide') && !isActive);
    const paged = settings.mvk_theme_config.nav?.navigation_type === 'expanded' || settings.mvk_theme_config.nav?.navigation_type === 'three-quarters';

    if (showNavItem) {
        return (
            <div id={IDs.primary} class={`flex dropdown-item${showChildren ? ' active' : ''}`} style={showChildren && paged ? style : null}>
                {(paged && showChildren) &&
                    <div class='back-button' onClick={ToggleChildren}>
                        <div class={`arrow`}></div>Back to Main Menu
                    </div>
                }
                {(item.url || !showChildren || !paged) &&
                    <div
                        class={`first-level${(item.children || item.meganav) ? ' with-children' : ''}${showChildren ? ' active' : ''}`}
                        onClick={((item.children || item.meganav) && paged && !showChildren) ? ToggleChildren : null}
                    >
                        {item.button ?
                            <Button className='nav-button' title={item.title} url={item.url} process={CloseHamburger} target={item.target} type={item.button_style} tone={item.background_value} noIcon={true} />
                            :
                            item.url === '#' ?
                                <div onClick={ToggleChildren} class={classes}>
                                    {item.nav_icon &&
                                        <Imaging data={item.nav_icon} />
                                    }
                                    <div class={`dropdown-item-title hover`}>{decode(item.title)}</div>
                                </div>
                                :
                                <Clicker type='anchor' url={item.url} target={item.target ? item.target : null} process={CloseHamburger} class={classes}>
                                    {item.nav_icon &&
                                        <Imaging data={item.nav_icon} />
                                    }
                                    <div class={`dropdown-item-title hover`}>{decode(item.title)}</div>
                                </Clicker>
                        }
                        {(item.children || item.meganav) && <div class='arrow-wrap' onClick={ToggleChildren}><div class={`arrow`}></div></div>}
                    </div>
                }
                {(!item.meganav && item.children && showChildren) && <Children IDs={IDs} items={item.children} style={style} paged={paged} />}
                {(item.meganav && settings.meganav && showChildren) && <MegaChildren name={item.meganav} meganavSettings={settings.meganav} style={style} paged={paged} />}
            </div>
        );
    } else {
        return null;
    }
}

const MegaChildren = ({ name, meganavSettings, style, paged }) => {
    const meganav = meganavSettings?.find(item => item.name === name);
    return (
        <div class="dropdown-item-children">
            {meganav && meganav?.page_links?.map((item) => <Child item={item} style={style} page={paged} />)}
        </div>
    );
}

const Children = ({ IDs, items, style, paged }) => {
    return (
        <div id={IDs?.children} class="dropdown-item-children">
            {items && Object.keys(items).map((key) => (<Child item={items[key]} style={style} paged={paged} />))}
        </div>
    );
}

const Child = ({ item, style, paged }) => {
    const headerContext = useContext(HeaderContext);
    const [settings, setSettings] = useContext(SettingsContext);
    const [showChildren, setChildren] = useState(false);

    var classes = (item.classes) ? item.classes.join(' ') : '';

    const Click = useCallback((e) => {
        headerContext.Toggle('hamburger', false);
    });

    const ToggleChildren = () => {
        setChildren(!showChildren);
    }

    return (
        <div class={`child-wrap${showChildren ? ' active' : ''}`} style={showChildren && paged ? style : null}>
            {(paged && showChildren) &&
                <div class='back-button' onClick={ToggleChildren}><div class={`arrow`}></div>Back</div>
            }
            <div class={`second-level${item.children ? ' with-children' : ''}${showChildren ? ' active' : ''}`} onClick={(item.children && paged && !showChildren) ? ToggleChildren : null}>
                <Clicker type='anchor' url={item.url} target={item.target ? item.target : null} process={Click} class={classes}>
                    {item.page_icon &&
                        <Imaging className='page-icon' data={item.page_icon} />
                    }
                    <div class={`dropdown-item-child ${classes}`}>{item.name ? decode(item.name) : decode(item.title)}</div>
                </Clicker>
                {item.children && <div class='arrow-wrap' onClick={ToggleChildren}><div class={`arrow`}></div></div>}
            </div>
            {(item.children && showChildren) && <Children items={item.children} />}
        </div>
    );
}

export default Start;
