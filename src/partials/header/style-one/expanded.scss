#header {
    #style-one {
        position: relative;
        // #search-expanded {
        #search-box {
            // margin: 10px 0px 5px;
            float: right;
            padding: 0px;
            text-align: right;
            @media screen and (max-width: 1199px) {
                display: none !important;
            }
            &.closable {
                .flex1 {
                    transform: scaleX(0);
                    transform-origin: right;
                    transition: 0.2s;
                    &.search-open {
                        transform: scaleX(1);
                    }
                }
                #search-input {
                    position: absolute;
                    right: 0;
                    top: 50%;
                    transform: translateY(-50%);
                }
            }

            #search-input {
                margin: 0px;
                border: 0px;
                border-bottom: 2px solid rgba(0, 0, 0, 0.3);
                background-color: white;
                min-width: 300px;
                float: right;
                border-radius: 0px;
            }

            svg,
            img {
                margin: 0px 1rem;
                width: 30px;
                min-width: 30px;
                height: 30px;
                min-height: 30px;
                color: black;
                fill: black;

                &:hover {
                    cursor: pointer;
                }
            }
        }
        // }

        #nav-dropdown {
            max-width: 70px;
        }

        #nav-expanded {
            width: 100%;

            .nav-primary-items {
                width: 100%;
                height: 100%;
                font-size: 0.625rem;
                height: 100%;
                vertical-align: middle;

                @media screen and (max-width: 1199px) {
                    display: none !important;
                }

                .nav-primary-item {
                    margin: 10px;
                    &:hover {
                        cursor: pointer;
                    }

                    .nav-primary-item-main {
                        &:hover {
                            cursor: pointer;
                        }

                        .nav-primary-item-main-title {
                            font-size: 1rem;
                            letter-spacing: -0.25px;
                            white-space: nowrap;
                        }
                    }

                    .nav-primary-item-children {
                        position: absolute;
                        z-index: 999;
                        padding: 13px 0;
                        transform-origin: top;
                        transition: 0.4s;
                        // min-width: 100px;
                        max-height: none;
                        list-style-type: none;
                        box-shadow: 0 3px 6px rgba(0, 0, 0, 0.16);

                        @media (min-width: 1200px) {
                            min-width: 215px;
                            max-width: 215px;
                        }

                        @media (min-width: 1366px) {
                            min-width: 245px;
                            max-width: 245px;
                        }

                        .nav-primary-item-child {
                            text-align: left;
                            font-size: 0.875rem;
                            color: inherit;
                            padding: 0.5rem 1.75rem;
                            // white-space: nowrap;
                            &:hover {
                                cursor: pointer;
                            }
                            &.with-third-level::after {
                                content: ">";
                                position: absolute;
                                right: 15px;
                            }
                            a:hover {
                                border-bottom: 1px solid;
                            }
                        }
                        // for 3rd level items
                        .nav-primary-item-children {
                            left: 100%;
                            margin-top: -2.25rem;
                            // white-space: nowrap;

                            &.fliparoo {
                                right: 100%;
                                left: unset;
                                margin-top: -1rem;
                            }
                        }
                    }
                    &.with-meganav {
                        &:hover:after {
                            content: '';
                            position: absolute;
                            z-index: 9998;
                            height: 50%;
                            width: 100%;
                            left: 0;
                        }
                    }
                }
                &.end {
                    .nav-primary-item {
                        &:last-child {
                            position: relative;
                            .nav-primary-item-children {
                                right: 0;
                                .nav-primary-item-child.with-third-level {
                                    &::after {
                                        content: "<";
                                        right: unset;
                                        left: 12px;
                                    }
                                    .nav-primary-item-children {
                                        left: unset;
                                        right: 100%;
                                    }
                                }
                            }
                        }
                    }
                }
            }
            &.arrows {
                .nav-primary-items {
                    flex-wrap: wrap;
                    .nav-primary-item {
                        &:last-child .nav-primary-item-main-title {
                            &.with-children {
                                margin-right: 1.75rem;
                            }
                            &:not(.with-children) {
                                margin-right: 0.25rem;
                            }
                        }
                        .nav-primary-item-main-title {
                            // margin-right: 1.5rem;
                            position: relative;
                            &.with-children {
                                margin-right: 2rem;
                                .arrow {
                                    width: 0;
                                    height: 3px;
                                    border-left: 5px solid transparent;
                                    border-right: 5px solid transparent;
                                    border-top: 5px solid;
                                    position: absolute;
                                    top: 50%;
                                    transform: translateY(-50%);
                                    transition: .2s;
                                    left: calc(100% + 10px);
                                    &.custom {
                                        width: 18px;
                                        height: auto;
                                        border: none;
                                    }
                                }
                                &.active {
                                    .arrow {
                                        transform: translateY(-50%) scale(-1, -1);
                                    }
                                }
                            }
                        }
                        .nav-primary-item-children .nav-primary-item-child {
                            position: relative;
                            &.with-third-level {
                                &::after {
                                    content: "";
                                }
                                .arrow {
                                    width: 0;
                                    height: 0;
                                    border-top: 5px solid transparent;
                                    border-bottom: 5px solid transparent;
                                    border-left: 5px solid;
                                    position: absolute;
                                    right: 10px;
                                }
                            }
                        }
                        &:last-child {
                            .nav-primary-item-child.with-third-level a .arrow {
                                right: unset;
                                left: 10px;
                                transform: rotate(180deg);
                            }
                        }
                    }
                }
            }
            &.logo-centered {
                display: flex;
                justify-content: center;
                .logo-box {
                    width: auto;
                }
                .nav-primary-items {
                    width: auto;
                    flex: 1;
                    margin: 0 1rem;
                    .nav-primary-item {
                        margin: 10px 16px;
                        &:hover .nav-primary-item-main-title {
                            text-shadow: 1px 0 currentColor;
                        }
                    }
                }
                .desktop-menu-button {
                    margin-left: 1.5rem;
                }
            }
            .nav-button {
                padding: 5px 20px;
            }

            .nav-primary-item-children {
                &.hidden {
                    opacity: 0;
                }
            }
            .reservation-button {
                margin-left: .5rem;
            }
        }
        &.icons {
            #nav-expanded {
                .nav-primary-items {
                    align-items: flex-end;
                    padding-bottom: 1rem;
                    .nav-primary-item {
                        margin: 5px;
                        .nav-primary-item-main-title {
                            a,
                            > div {
                                display: flex;
                                flex-direction: column;
                                justify-content: center;
                                font-weight: bold;
                                margin: 0 0.25rem;
                                text-align: center;
                                img {
                                    filter: grayscale(100%);
                                    opacity: 0.2;
                                    transition: 0.2s;
                                    margin: 0 auto 0.5rem;
                                }
                            }
                            &.active,
                            a:hover,
                            > div:hover {
                                text-decoration: underline;
                                img {
                                    filter: grayscale(0);
                                    opacity: 1;
                                }
                            }
                        }
                    }
                }
            }
        }
        // &.scrolled {
        //     #nav-expanded {
        //         .nav-primary-items .nav-primary-item .nav-primary-item-main-title img {
        //             height: 0;
        //         }
        //     }
        // }
    }
    &.has-hero:not(.scrolled) {
        box-shadow: none;
        &:not(.gradient) {
            background: transparent !important;
        }
        #style-one {
            @media (max-width: 1199px) {
                &:not(.active) {
                    background: transparent !important;
                }
            }
            @media (min-width: 1200px) {
                background: transparent !important;
            }
            .nav-primary-item {
                background: transparent !important;
            }
        }
    }
    &.no-hero,
    &.scrolled {
        .desktop-menu-button {
            border-color: #fff !important;
        }
        // #style-one {
        //     &.icons {
        //         #nav-expanded {
        //             .nav-primary-items {
        //                 .nav-primary-item {
        //                     margin: 5px 8px;
        //                     .nav-primary-item-main-title {
        //                         img {
        //                             width: 0;
        //                         }
        //                     }
        //                 }
        //             }
        //         }
        //     }
        // }
    }

    #style-one {
        &.icons {
            #nav-expanded {
                .nav-primary-items {
                    .nav-primary-item {
                        .nav-primary-item-main-title {
                            min-width: 100px;
                        }
                    }
                }
            }
        }
    }
}
