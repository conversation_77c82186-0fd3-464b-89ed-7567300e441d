import React, { useContext, useState, useEffect } from 'react';
import Clicker from 'src/helpers/clicker';

// CONTEXT.
import { PrincipalContext, SettingsContext, LocationsContext } from "src/context";
// HELPERS.
const Imaging = React.lazy(() => import('src/helpers/imaging'));
import { ScrollHandler } from 'src/helpers/scroll';
// STYLES
import * as S from './styles';

const Logo = (props) => {
    const [settings, setSettings] = useContext(SettingsContext);
    const [locations] = settings.current_location ? useContext(LocationsContext) : [];
    const scroll = ScrollHandler();
    const [homeUrl, setHomeUrl] = useState(settings.fleet_home_url ? new URL(settings.fleet_home_url).pathname : `https://${config.domain}/`)
    const [logoData, setLogoData] = useState(settings?.mvk_theme_config?.nav?.navigation_style === 'logo-centered' && settings.branding?.alternate_logo && scroll ? settings.branding?.alternate_logo : settings.branding?.main_logo)
    
    useEffect(() => {
        setHomeUrl(settings.current_location && locations[settings.current_location].mvk_item_content?.custom_fields?.logo_to_location_home ? new URL(locations[settings.current_location].mvk_item_meta?.url).pathname : `https://${config.domain}/`);
        setLogoData(settings.current_location && locations[settings.current_location].mvk_item_content?.custom_fields?.location_logo ? locations[settings.current_location].mvk_item_content?.custom_fields?.location_logo : settings.branding?.main_logo);
    }, [settings.current_location])
   
    return (
        <Clicker class={`cell shrink logo-box ${settings.branding?.logo_padding === false ? 'no-min-height' : 'add-min-height'}`} type={settings.hide_nav ? '' : 'anchor'} url={settings.hide_nav ? null : homeUrl} ariaLabel='home'>
            <S.Logo
                className={`logo${settings?.branding?.logo_height ? ' set-height' : ''}`}
                bgColor={settings?.branding?.main_logo_background_color ? pylot.design.hexToRGB(settings?.branding?.main_logo_background_color, settings?.branding?.main_logo_background_opacity) : 'transparent'}
                logoHeight={settings?.branding?.logo_height}
            >
                <Imaging className='logo-image' data={logoData} />
            </S.Logo>
        </Clicker>
    );
}

export default Logo;
