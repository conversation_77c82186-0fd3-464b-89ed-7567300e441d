import React from 'react';
import styled from 'styled-components';
const Button = React.lazy(() => import('src/partials/button'));

export const StyleOne = styled.div`
    @media (max-width: 1199px) {
        background-color: ${props => props.mobileBg};
        &.floating-hamburger {
            &:not(.active) {
                background: transparent;
            }
            #dropdown {
                border-top: none;
                .dropdown-item {
                    border-bottom: none;
                }
                &:has(#modal) {
                    z-index: unset;
                }
            }
        }
    }
    @media (min-width: 1200px) {
        background-color: ${props => props.desktopBg};
    }
`;

export const NavLink = styled.div`
    .current {
        position: relative;
        &:after {
            content: '';
            position: absolute;
            // top: 100%;
            bottom: -8px;
            left: 0;
            width: 100%;
            height: ${props => props.underlineHeight}px;
            background-color: ${props => props.underlineColor};
        }
    }
`;

export const NavChildren = styled.div`
    background-color: ${props => props.bgColor};
    color: ${props => props.color};
    &.bg-gradient {
        background-image: ${props => props.bgGradient};
    }
    &.use-hover-color {
        background-color: ${props => props.bgHover};
    }
`;

export const StyledButton = styled(Button)`
    border: 2px solid ${props => props.color} !important;
`;

export const Logo = styled.div`
    background-color: ${props => props.bgColor};
    @media (min-width: 1200px) {
        &.set-height {
            height: ${props => props.logoHeight}px;
            .logo-image {
                width: 100%;
                height: 100%;
                object-fit: contain;
            }
        }
    }
   
`
