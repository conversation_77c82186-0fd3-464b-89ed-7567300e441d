/*******************************************************************************************************
   Copyright 2020 - Calise Partners LLC d/b/a Imaginuity. All rights are reserved. 
   Reproduction or transmission in whole or in part, in any form or by any means, electronic, mechanical 
or otherwise, is prohibited without the prior written consent of Calise Partners LLC d/b/a Imaginuity.
********************************************************************************************************
   Project : 10618 Maverick Platform Development
   Author : Imaginuity Developers (<PERSON>, <PERSON>)
   Description : Dropdown menu for mobile devices.
   Creation Date : Mon Nov 23 2020
   Documentation : [documentation url - point to default wiki for project if no specific documentation for script]
 ********************************************************************************************************/
@import "src/scss/variables.scss";

#dropdown {
    // position: absolute;
    position: relative;
    z-index: 900;
    // top:200px;
    top: 0;
    left: 0px;
    right: 0px;
    margin-top: 0px;
    padding: 0px;
    background-color: white;
    border-top: 1px solid rgba(0, 0, 0, 0.1);
    &.with-gradient {
        border-top: none;
    }
    @media (min-width: 1200px) {
        display: none;
    }
    .dropdown-inner-container {
        position: absolute;
        width: 100%;
        background-color: white;
        @media (max-width: 1199px) {
            height: 75vh;
            overflow-y: auto;
            -webkit-overflow-scrolling: touch;
            padding-bottom: 200px; // offset height of nav at tablet size
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15);
        }

        .search-box {
            width: 100%;
            #mobile-search {
                margin: 20px;
                #search-form {
                    position: relative;
                    #search-input2 {
                        border: 1px solid;
                        padding: 1rem 3.5rem 1rem 1rem;
                        border-radius: 0;
                        -webkit-border-radius: 0;
                        font-size: 1rem;
                        &:focus {
                            outline: none;
                        }
                    }

                    .mobile-search-icon {
                        width: 30px;
                        height: 25px;
                        position: absolute;
                        right: 15px;
                        bottom: 16px;
                    }
                }
            }
        }
        .mobile-menu-button {
            margin: 1rem auto;
        }
        .flexbox.column.utility.horizontal {
            justify-content: center;
            flex-flow: row;
            &.pipes .dropdown-item {
                position: relative;
                padding: 10px !important;
                &:after {
                    content: '|';
                    position: absolute;
                    top: 50%;
                    right: -1px;
                    transform: translateY(-50%);                
                }
                &:last-child:after {
                    content: '';
                }
            }
            &.dots .dropdown-item {
                position: relative;
                padding: 10px !important;
                &:after {
                    content: "\2022";
                    position: absolute;
                    top: 50%;
                    right: -2px;
                    transform: translateY(-50%);                
                }
                &:last-child:after {
                    content: '';
                }
            }
        }
    }

    // button {
    //     padding: unset;
    //     color: white;
    // }

    // .search-box {
    //     margin: 5px 0px 10px;
    //     padding: 5px;

    //     .button {
    //         margin-left: 10px;
    //     }
    // }

    .home {
        padding: 5px 10px;
        font-size: 1rem;
        font-weight: 600;
        a {
            display: block;
            padding: 5px 0;
            color: inherit;
        }
    }

    .dropdown-item {
        padding: 10px 20px;
        @media (min-width: 768px) {
            border-bottom: 1px solid;
        }
        .first-level,
        .second-level {
            position: relative;
            display: flex;
            justify-content: space-between;
            align-items: center;
            .arrow-wrap {
                display: flex;
                align-items: center;
                padding: 0.5rem;
            }
            .arrow {
                width: 0;
                height: 0;
                border-top: 3px solid;
                border-right: 3px solid;
                transform: rotate(135deg);
                border-radius: 1px;
                margin: 0 1rem;
                padding: 0.45rem;
            }
            &.active .arrow {
                transform: rotate(-45deg);
            }
            a {
                color: inherit;
            }
        }
        .first-level {
            a {
                display: flex;
                align-items: center;
                color: inherit;
                img {
                    display: none;
                }
            }
            @media (min-width: 768px) {
                a img {
                    display: block;
                    margin-right: 1rem;
                    width: 42px;
                    height: 42px;
                    object-fit: contain;
                    filter: grayscale(100%);
                    opacity: 0.2;
                    transition: 0.2s;
                }
                a:hover,
                &.active {
                    text-decoration: underline;
                    img {
                        filter: grayscale(0);
                        opacity: 1;
                    }
                }
            }
        }
        .dropdown-item-title {
            padding: 5px 0px;
            font-size: 1rem;
            color: inherit;
            font-weight: bold;
        }

        .dropdown-item-children {
            margin-left: 1rem;
            .dropdown-item-child {
                padding: 10px 0px;
                font-size: 1rem;
                font-weight: bold;
                color: inherit;
            }
            .dropdown-item-children .dropdown-item-child {
                font-weight: normal;
            }
            a:has(.page-icon) {
                display: flex;
                align-items: center;
                margin-bottom: .5rem;
                .page-icon {
                    width: 35px;
                    height: 35px;
                    object-fit: contain;
                    margin-right: 1rem;
                }
            }
        }
    }
    &.logo-centered {
        border: none;
    }
}

#style-one.expanded,
#style-one.three-quarters {
    #dropdown {
        .dropdown-item,
        .child-wrap {
            border-bottom: none; // maybe we want this, I'm not sure.
            .first-level,
            .second-level {
                .dropdown-item-title {
                    font-weight: normal;
                }
                .arrow {
                    transform: rotate(45deg);
                }
                a:hover {
                    text-decoration: none;
                }
                &.with-children > a {
                    pointer-events: none;
                }
            }

            &.active {
                position: absolute;
                top: 0;
                left: 0;
                padding: 0;
                width: 100%;
                height: 100vh;
                z-index: 1;
                .first-level {
                    margin-left: 1rem;
                    text-decoration: none;
                    .dropdown-item-title {
                        padding: 1rem 0;
                        font-weight: bold;
                    }
                    .arrow-wrap {
                        display: none;
                    }
                    &.active > a {
                        pointer-events: all;
                    }
                }
                .back-button {
                    display: flex;
                    padding: 1rem 0.5rem;
                    margin: 0 0 .5rem 0;
                    border-bottom: 1px solid rgba(0, 0, 0, 0.2);
                    .arrow {
                        width: 0;
                        height: 0;
                        border-top: 3px solid;
                        border-right: 3px solid;
                        transform: rotate(-135deg);
                        border-radius: 1px;
                        margin: 0 1rem;
                        padding: 0.45rem;
                    }
                }
            }

            .dropdown-item-children {
                .dropdown-item-child {
                    font-weight: normal;
                    padding: 1rem 0;
                }
                .child-wrap.active {
                    .second-level.active {
                        margin-left: 1rem;
                        .dropdown-item-child {
                            font-weight: bold;
                        }
                        .arrow-wrap {
                            display: none;
                        }
                        & > a {
                            pointer-events: all;
                        }
                    }
                }
            }
        }
    }
}
#style-one.three-quarters {
    #dropdown {
        border-top: none;
        transform: translateX(-100%);
        transition: 0.3s;
        max-width: 80%;
        .dropdown-inner-container {
            height: 100vh;
        }
        .mobile-logo {
            padding: 10px 20px;
            box-sizing: border-box;
            img {
                height: 46px;
                object-fit: contain;

            }
        }
    }
    &.active {
        #dropdown {
            transform: translateX(0);
            .dropdown-item {
                top: 66px;
            }
        }
    }
}
@media (min-width: 768px) {
    #style-one {
        // &:not(.icons) #dropdown .dropdown-item {
        //     border-color: rgba(255,255,255,0.7) !important;
        // }
        &.icons #dropdown .dropdown-item > .dropdown-item-children {
            margin-left: 5rem;
        }
    }
}
