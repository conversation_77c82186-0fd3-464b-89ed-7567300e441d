@import "src/scss/variables.scss";

#header {
    #style-one {
        #nav-dropdown {
            position: relative;
            z-index: 1100;
            width: 100%;
            // height: 100%;
            min-height: 74px;
            display: none;
            // padding: 10px 20px;

            // @media screen and (max-width: 1100px) {
            @media screen and (max-width: 1199px) {
                display: block;
            }

            .mobile-search-nav {
                display: flex;
                flex-direction: column;
                align-items: flex-end;
                // margin: 10px 20px;

                #search-form {
                    width: 100%;
                    margin-top: 1rem;
                }

                .mobile-container {
                    position: relative;
                }

                .mobile-menu {
                    // margin: 10px 20px;
                    width: 100%;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;

                    .mobile-logo {
                        margin: 10px 20px 10px 0;
                        > a {
                            display: block;
                            img {
                                display: block;
                                @media (min-width: 480px) {
                                    max-width: 300px;
                                }
                            }
                        }
                    }

                    .mobile-nav {
                        margin: 10px 0 10px 10px;
                        #hamburger.with-custom {
                            height: 70px;
                        }
                        .label {
                            font-size: .75rem;
                            padding-right: 0.25rem;
                        }
                    }
                    &.hamburger-left {
                        .mobile-nav {
                            order: 1;
                            margin: 10px 20px 10px 0;

                            .label {
                                padding-left: 0.25rem;
                            }
                        }
                        .mobile-logo {
                            order: 2;
                            margin: 10px 0 10px 20px;
                            display: flex;
                            justify-content: flex-end;
                        }
                        .phone-icon-only-container,
                        .layout-spacer {
                            order: 3;
                        }
                    }
                    &.with-label {
                        .mobile-nav {
                            width: 120px;
                        }
                    }
                }

                #mobile-search {
                    margin: 10px 20px;
                }

                .search-box {
                    width: 100%;

                    #search-input2 {
                        border: 1px solid;
                        padding: 1rem 3.5rem 1rem 1rem;
                        border-radius: 0;
                        -webkit-border-radius: 0;
                        font-size: 1rem;
                        &:focus {
                            outline: none;
                        }
                    }

                    .mobile-search-icon {
                        width: 30px;
                        height: 25px;
                        position: absolute;
                        right: 15px;
                        bottom: 16px;
                    }
                }
            }

            #hamburger {
                position: relative;
                z-index: 1200;

                &:hover {
                    opacity: 1;
                }
                span.hamburger-inner,
                span.hamburger-inner::before,
                span.hamburger-inner::after {
                    background-color: inherit;
                }
            }

            #dropdown {
                position: absolute;
                z-index: 900;
                top: 130px;
                left: 0px;
                right: 0px;
                margin-top: 10px;
                padding: 10px;
                width: 100%;
                background-color: white;
                border-top: 1px solid rgba(0, 0, 0, 0.1);

                .search-box {
                    margin: 5px 0px 10px;
                    padding: 5px;

                    .button {
                        margin-left: 10px;
                    }
                }

                .home {
                    padding: 5px 10px;
                    font-size: 0.875rem;
                    font-weight: 600;
                }

                .dropdown-item {
                    padding: 0px 10px;

                    .dropdown-item-title {
                        padding: 5px 0px;
                        font-size: 0.875rem;
                    }

                    .dropdown-item-children {
                        padding: 0px 5px;
                        .dropdown-item-child {
                            padding: 5px 0px;
                            font-size: 0.75rem;
                        }
                    }
                }
            }
        }
    }
    .custom-hamburger {
        max-width: 40px;
    }
}
