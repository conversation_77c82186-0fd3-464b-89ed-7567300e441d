#header {
    #head-utility {
        width: 100%;

        @media screen and (max-width: 1199px) {
            &:not(.hours-mobile) {
                display: none;
            }
            #search-box {
                display: none;
            }
        }
        .grid-container {
            margin: 0px auto;
            padding-top: 5px;
            padding-bottom: 5px;
            // max-width: 1200px;
            &:has(.sei-license) {
                display: flex;
                align-items: center;
                justify-content: space-between;
                .sei-license {
                    margin: 0 1rem 0 0;
                }
            }
            .nav-utility-items {
                justify-content: center;
                align-items: center;
                gap: 24px;
                @media screen and (min-width: 768px) {
                    justify-content: flex-end;
                }
                .nav-utility-item {
                    gap:15px;
                    .nav-utility-item-main {
                        margin: 0px;

                        &:hover {
                            cursor: pointer;
                        }
                        &.dots:after {
                            content: "\2022";
                        }
                        &.pipes:after {
                            content: '|';
                        }
                        &.dots, &.pipes {
                            position: relative;
                            padding: 0 5px;
                            &:after {
                                position: absolute;
                                top: 50%;
                                transform: translateY(-50%);
                                left: -16px;                            
                            }
                        }
                        .nav-utility-item-main-title {
                            display: flex;
                            align-items: center;
                            text-align: right;
                            font-size: 0.813rem;
                            line-height: 30px;
                            * {
                                color: inherit;
                            }
                            svg:not(.locked) {
                                height: 20px;
                                width: auto;
                                margin-left: 10px;
                                vertical-align: middle;
                            }

                            .dropdown {
                                position: relative;
                                display: inline-block;
                            }

                            .dropdown-content {
                                position: absolute;
                                right: 0;
                                z-index: 1;

                                .dropdown-content-wrapper {
                                    margin-top: 16px;
                                    padding: 8px 16px;

                                    .nav-utility-item-main {
                                        white-space: pre;
                                        margin: 4px 6px;
                                    }
                                }
                            }
                            .utility-button {
                                margin: 0.5rem 0;
                                padding: 0.5rem 0.75rem;
                                font-size: 0.813rem;
                                line-height: 1;
                                letter-spacing: 0;
                                border-width: 2px !important; // request... we shall see
                                // font-weight: 100 !important; // request... we shall see
                            }
                        }
                    }
                    &:first-child .nav-utility-item-main:after {
                        content: '';
                    }
                }
                .hours-today {
                    display: flex;
                    font-size: 0.813rem;
                    line-height: 30px;
                    position: relative;
                    margin-right: 1rem;
                    a.utility-nav-color {
                        color: inherit;
                    }

                    @media (min-width: 768px) {
                        .no-link {
                            margin-right: 1rem;
                        }
                    }
                }
            }

            #search-box {
                height: 100%;
                float: right;
                padding: 0px;
                text-align: right;
                &.closable {
                    .flex1 {
                        #search-form {
                            transform: scaleX(0);
                            transform-origin: right;
                            transition: 0.2s;
                            width: 0;
                        }

                        &.search-open #search-form {
                            transform: scaleX(1);
                            width: 300px;
                        }
                    }
                    // #search-form {
                    //     // position: absolute;
                    //     right: 0;
                    //     top: 50%;
                    //     transform: translateY(-50%);
                    //     width: 300px;
                    // }
                }
                #search-input {
                    margin: 0px;
                    border: 0px;
                    background-color: white;
                    max-width: 300px;
                    float: right;
                    border-radius: 0px;
                    height: 34px;
                }

                svg, img {
                    margin: 0px 10px;
                    width: 25px;
                    min-width: 25px;
                    height: 30px;
                    min-height: 30px;
                    color: white;
                    fill: white;

                    &:hover {
                        cursor: pointer;
                    }
                }
            }
        }
    }
}
