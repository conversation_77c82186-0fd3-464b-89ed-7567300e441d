import React, { useContext, useState, useEffect } from 'react';
import { useNavigate, Link } from 'react-router-dom';
import { isMobile } from 'react-device-detect';
import { faCaretDown, faCaretUp } from '@fortawesome/free-solid-svg-icons';
import { fas as solidIcons } from "@fortawesome/free-solid-svg-icons";
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';

import { decode } from 'html-entities';


// HELPERS.
const Clicker = React.lazy(() => import('src/helpers/clicker'));
const Imaging = React.lazy(() => import('src/helpers/imaging'));
import { Coloring } from 'src/helpers';

// PARTIALS
import HoursToday from "src/partials/hours/hours-today";
import IconNumber from 'src/partials/header/phone/icon-number';
const Button = React.lazy(() => import('src/partials/button'));
import Translations from 'src/partials/header/translations/index.jsx';
const LocationSelector = React.lazy(() => import('src/partials/location-selector'));

// CONTEXT.
import { PrincipalContext, SettingsContext, NavigationContext } from "src/context";
import { HeaderContext } from "src/partials/header/context";
import { AppContext } from 'src/contexts/app';

// SCSS, CSS.
import "src/partials/header/utility/index.scss";
import { HeadUtility, HoursContainer, UtilityNavItem } from './styles';

const Start = (props) => {
    return (<Container />);
};

const Container = (props) => {
    const [settings, setSettings] = useContext(SettingsContext);
    const [navigation, setNavigation] = useContext(NavigationContext);
    const appContext = useContext(AppContext);
    const spot = settings?.sei_settings?.spot ? settings?.sei_settings?.spot : false;
    const seiLicense = settings?.sei_settings?.license && settings?.sei_settings?.license?.location && settings?.sei_settings?.license?.location !== 'footer' && typeof spot !== 'string';
    var utilityBgColor = settings.design?.colors?.util_nav?.utility_nav_custom_background === false ? 'transparent' : settings.design?.colors?.util_nav?.ut_nav_bg_color;
    var utilityTxtColor = settings.design?.colors?.util_nav?.ut_nav_txt_color ? settings.design?.colors?.util_nav?.ut_nav_txt_color : settings.design?.colors?.main_nav?.nav_txt_color;
    var hoursTxtColor = settings.mvk_theme_config.header?.hours_color === 'utility-nav-color' ? utilityTxtColor : null;

    return (
        <HeadUtility
            id="head-utility"
            className={(settings.mvk_theme_config.header?.enable_hours && settings.mvk_theme_config.header?.hours_mobile) ? 'hours-mobile' : null}
            utilityBgColor={utilityBgColor}
            utilityTxtColor={utilityTxtColor}
        >
            <div class="grid-container">
                {seiLicense &&
                    <p className="sei-license">{settings?.sei_settings?.license?.text}</p>
                }
                <div class="grid-x nav-utility-items">
                    {(settings.mvk_theme_config.header?.enable_hours && settings.mvk_theme_config.nav?.utility && settings.mvk_theme_config.header?.hours_type === 'utility') &&
                        <div id="hours-container" class='hours-container grid-x'>
                            <HoursContainer
                                className={`hours-today ${settings.mvk_theme_config?.header?.hours_color}`}
                                hoursTxtColor={hoursTxtColor}
                            >
                                <HoursToday settings={settings} delimiter={':'} />
                            </HoursContainer>
                        </div>
                    }
                    {navigation?.utility && Object.keys(navigation?.utility).sort((a, b) => navigation.utility[a].order - navigation.utility[b].order).map(key => <NavItems item={navigation.utility[key]} utilityTxtColor={utilityTxtColor} />)}
                    <Translations translations={settings?.translations} defaultLang={settings?.default_language ? settings?.default_language?.value : false} />
                    {settings.mvk_theme_config.header?.search && settings.mvk_theme_config.header?.search_type?.includes('utility') && <SearchBox settings={settings} />}
                    {settings.mvk_theme_config.header?.enable_phone_number && settings.mvk_theme_config.header?.phone_number_type === 'utility' && <IconNumber settings={settings} />}
                    {(appContext.width >= 1200 && settings.locations?.enable_location_selector) && <LocationSelector />}
                </div>
            </div>
        </HeadUtility>
    );
};

const NavItems = ({ item, utilityTxtColor }) => {
    return (
        <div class="cell shrink nav-utility-item">
            <NavItem item={item} utilityTxtColor={utilityTxtColor} />
        </div>
    );
};

const NavItem = ({ item, utilityTxtColor }) => {
    const [expanded, setExpanded] = useState(false);
    const [settings, setSettings] = useContext(SettingsContext);
    const hasChildren = (item.children && item.children.length > 0);
    const MouseOver = () => setExpanded(true);
    const MouseOut = () => setExpanded(false);

    var classes = (item.classes) ? item.classes.join(' ') : '';
    var isExternal = (item.target == '_blank') ? true : false;
    var utilityTxtHoverColor = settings.design?.colors?.util_nav?.ut_nav_txt_hv_color ? settings.design?.colors?.util_nav?.ut_nav_txt_hv_color : null;

    return (
        <div id={`nav-utility-${item.ID}`} class={`flex nav-utility-item-main ${settings?.mvk_theme_config?.nav?.enable_utility_separators ? settings?.mvk_theme_config?.nav?.utility_separators : ''}`} onMouseOver={MouseOver} onMouseOut={MouseOut}>
            <UtilityNavItem
                className={`nav-utility-item-main-title ${classes}`}
                utilityTxtColor={utilityTxtColor}
                utilityTxtHoverColor={utilityTxtHoverColor}
            >
                {item.url && !item.button && <Clicker className={classes} type="anchor" url={item.url} isExternal={isExternal} ariaLabel={`link to ${item.title}`}>{decode(item.title)}{(item.font_awesome_icon) && <FontAwesomeIcon icon={solidIcons[`${item.font_awesome_icon}`]} className="locked" />}</Clicker>}
                {item.url && item.button && <Button className='utility-button' title={item.title} url={item.url} target={item.target} type={item.button_style} tone={item.background_value} isUtility={true} />}
                {!item.url && <div class={classes}>{decode(item.title)}{(item.font_awesome_icon) && <FontAwesomeIcon icon={solidIcons[`${item.font_awesome_icon}`]} className="locked" />}</div>}
                {hasChildren &&
                    <>
                        <FontAwesomeIcon icon={expanded ? faCaretUp : faCaretDown} size="lg" />
                        <Dropdown item={item} expanded={expanded} />
                    </>
                }
            </UtilityNavItem>
        </div>
    );
};

const Dropdown = ({ item, expanded }) => {
    const [settings, setSettings] = useContext(SettingsContext);
    var styles = { backgroundColor: settings.design?.colors?.util_nav?.utility_nav_custom_background ? settings.design?.colors?.util_nav?.ut_nav_bg_color : settings.design?.colors?.main_nav?.nav_bg_color };

    return (
        <div class="dropdown">
            <div class={`dropdown-content ${expanded ? 'display-block' : 'hide'}`}>
                <div class="dropdown-content-wrapper" style={styles}>
                    {item.children.map(key => <NavItem item={key} />)}
                </div>
            </div>
        </div>
    );
};

const SearchBox = ({ settings }) => {
    const [principal, setPrincipal] = useContext(PrincipalContext);
    const headerContext = useContext(HeaderContext);
    const navigate = useNavigate();

    useEffect(() => {
        var searchInput = document.getElementById('search-input');
        if (searchInput) {
            searchInput.focus();
        }
    }, []);

    const Click = (e) => {
        var searchInput = document.getElementById('search-input');
        if (searchInput) {
            searchInput.focus();
        }
        var other = headerContext.toggled.search ? false : true;
        if (!searchInput || searchInput.value?.length <= 0 || other) {
            headerContext.Toggle('search', other);
        } else {
            Submit();
        }
    };

    const Submit = (e) => {
        e?.preventDefault();
        var term = document.getElementById('search-input').value;
        if (term && term.length > 0) {
            navigate(principal.subsite ? `/${principal.subsite}/search?s=${term}` : `/search?s=${term}`);
        }
    }

    var styles = { fill: settings.design?.colors?.util_nav?.ut_nav_txt_color ? settings.design?.colors?.util_nav?.ut_nav_txt_color : settings.design?.colors?.main_nav?.nav_txt_color };
    return (
        <div id="search-box" class={`cell shrink ${settings.mvk_theme_config.header?.search_style == 'icon' ? 'closable' : ''}`}>
            <div class="flexbox">
                <div id="search-expand" class={`flex1 slide-in from-right ${(headerContext.toggled.search || settings.mvk_theme_config.header?.search_style == 'expanded') ? 'search-open' : 'search-closed'}`}>
                    <form id="search-form" method="post" action="#" autocomplete="off" onSubmit={Submit} role="search" aria-label="sitewide">
                        <label htmlFor="search-input">
                            <span class='show-for-sr'>Search</span>
                            <input id="search-input" type="text" placeholder="Search" />
                        </label>
                    </form>
                </div>
                <Clicker id="search-glass" class="flex" process={settings.mvk_theme_config.header?.search_style == 'expanded' ? Submit : Click} role='button' ariaLabel='search submit'>
                    {settings.mvk_theme_config.header?.search_icon_desktop ?
                        <Imaging data={settings.mvk_theme_config.header?.search_icon_desktop} />
                        :
                        <svg version="1.1" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" style={styles} aria-label="search icon">
                            <g id="info" />
                            <g id="icons">
                                <path d="M22.4,19.6l-4.8-4.8c0.9-1.4,1.4-3,1.4-4.8c0-5-4-9-9-9s-9,4-9,9s4,9,9,9c1.8,0,3.4-0.5,4.8-1.4l4.8,4.8   c0.4,0.4,0.9,0.6,1.4,0.6c1.1,0,2-0.9,2-2C23,20.4,22.8,19.9,22.4,19.6z M5,10c0-2.8,2.2-5,5-5s5,2.2,5,5s-2.2,5-5,5S5,12.8,5,10z" id="icon-search" />
                            </g>
                        </svg>
                    }
                </Clicker>
            </div>
        </div>
    );
};

export default Start;
