import React from 'react';
import { decode } from 'html-entities';
import { replaceScTagsWithPlaceholders } from 'src/helpers/html-parser';
import { Helmet, HelmetProvider } from 'react-helmet-async';
import { useLocation } from 'react-router-dom';

const Start = ({ data, settings, placeholders }) => {
    const location = useLocation();
    const faqSchema = {
        "@context": "https://schema.org",
        "@type": "FAQPage",
        "@id": location.pathname,
        "mainEntity": data.accordion_items.map((faq) => {
            const question = placeholders?.single_line && faq.heading_selection === 'dynamic'
                ? placeholders.single_line[faq.heading] || faq.heading
                : faq.heading;
            const parsedQuestion = replaceScTagsWithPlaceholders(question, {
                ...(settings?.faq?.mvk_placeholders ?? {}),
                ...(settings?.global?.mvk_placeholders ?? {})
            })
            const parsedAnswer = replaceScTagsWithPlaceholders(faq.content, {
                ...(settings?.faq?.mvk_placeholders ?? {}),
                ...(settings?.global?.mvk_placeholders ?? {})
            })
            return {
                "@type": "Question",
                "name": decode(parsedQuestion.replace(/<[^>]*>/g, '').replace(/\n/g, ' ')),
                "acceptedAnswer": {
                    "@type": "Answer",
                    "text": decode(parsedAnswer.replace(/<[^>]*>/g, '').replace(/\n/g, ' ')),
                },
            };
        }),
    };

    return (
        <HelmetProvider>
            <Helmet defer={false}>
                <script type="application/ld+json">
                    {JSON.stringify(faqSchema)}
                </script>
            </Helmet>
        </HelmetProvider>
    );
};

export default Start;