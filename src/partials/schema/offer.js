import React from 'react';
import { Helmet, HelmetProvider } from 'react-helmet-async'


const Start = ({ data }) => {

    const validData = Array.isArray(data) ? data : [data];

    const generateOfferSchema = (offer) => {
        if(offer?.url == undefined) return;
        const baseUrl = offer.url.replace(/(https:\/\/[^\/]+)\/.*/, "$1");
        const offerUrl = `${baseUrl}${offer.slug}`;

        if (offer?.sale_offer_type === "price") {
            return {
                "@context": "https://schema.org",
                "@type": "Offer",
                "price": offer.price,
                "priceCurrency": offer.currency || "USD",
                "availability": `http://schema.org/${offer.availability.label}`,
                "url": offerUrl,
                "validFrom": offer.start_date,
                "validThrough": offer.end_date
            };
        }

        if (offer?.sale_offer_type === "discount" && offer?.discount_type === "percentage") {
            return {
                "@context": "https://schema.org",
                "@type": "Offer",
                "priceSpecification": {
                    "@type": "UnitPriceSpecification",
                    "priceCurrency": offer.currency || "USD",
                    "description": offer.description || "Discount applied",
                    "eligibleTransactionVolume": {
                        "@type": "QuantitativeValue",
                        "value": offer.discount,
                        "unitCode": "P1 / USD",
                        "unitText": "percent / USD"
                    }
                },
                "validFrom": offer.start_date,
                "validThrough": offer.end_date,
                "availability": `https://schema.org/${offer.availability.label}`,
                "url": offerUrl
            };
        }

        if (offer?.offer_type === "discount" && offer?.discount_type === "amount") {
            return {
                "@context": "https://schema.org",
                "@type": "Offer",
                "priceSpecification": {
                    "@type": "UnitPriceSpecification",
                    "price": offer.discount,
                    "priceCurrency": offer.currency || "USD",
                    "description": offer.description || `Discount of $${offer.discount} applied`,
                    "eligibleTransactionVolume": {
                        "@type": "QuantitativeValue",
                        "value": 1,
                        "unitCode": "EA",
                        "description": "Minimum purchase of 1 item"
                    }
                },
                "validFrom": offer.start_date,
                "validThrough": offer.end_date,
                "availability": `https://schema.org/${offer.availability.label}`,
                "url": offerUrl
            };
        }

        return null;
    };

    const structuredData = validData.map(generateOfferSchema).filter(Boolean);

    return (
        <HelmetProvider>
            <Helmet defer={false}>
                <script type="application/ld+json">
                    {JSON.stringify(structuredData)}
                </script>
            </Helmet>
        </HelmetProvider>
    );
};

export default Start;
