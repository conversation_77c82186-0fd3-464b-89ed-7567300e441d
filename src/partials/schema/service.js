import React from 'react';
import { decode } from 'html-entities';
import { replaceScTagsWithPlaceholders } from 'src/helpers/html-parser';
import { Helmet, HelmetProvider } from 'react-helmet-async';
import { WeeksHours } from 'src/helpers/hours';

const Start = ({ data, settings }) => {
    // const hours = WeeksHours('reduced', false, false, true);
    const areaServed = settings?.sei_settings?.spot?.webDetails?.serviceArea ? settings?.sei_settings?.spot?.webDetails?.serviceArea : settings?.seo?.area_served_schema;
    let blurb = data?.mvk_randomized_blurb ? data?.mvk_randomized_blurb : data?.mvk_item_content?.excerpt
    let serviceSchema = {
        "@context": "https://schema.org/",
        "@type": "Service",
        "name": `${data?.mvk_item_content?.title}`,
        "serviceType": `${data?.mvk_item_content?.title} services`,
        "description": `${blurb ? blurb.replace(/<[^>]*>/g, '').replace(/\n/g, ' ') : ''}`,
        "url": `${data?.mvk_item_meta?.url}`,
        "provider": {
            "@type": `${settings?.seo?.business_type_schema ? settings?.seo?.business_type_schema : 'Organization'}`,
            "name": `${settings?.branding?.site_title}`,
            "url": `https://${config.domain}`
        },
        "availableChannel": {
            "@type": "ServiceChannel",
            "serviceLocation": {
                "@type": "Place",
                "address": {
                    "@type": "PostalAddress",
                    "streetAddress": `${settings?.address?.address_1}`,
                    "addressLocality": `${settings?.address?.city}`,
                    "addressRegion": `${settings?.address?.state?.value}`,
                    "postalCode": `${settings?.address?.zip}`,
                    "addressCountry": "US"
                }
            }
        }

    }
    if (areaServed) {
        serviceSchema.areaServed = areaServed
    }
    return (
        <HelmetProvider>
            <Helmet defer={false}>
                <script type="application/ld+json">
                    {JSON.stringify(serviceSchema)}
                </script>
            </Helmet>
        </HelmetProvider>
    );
};

export default Start;