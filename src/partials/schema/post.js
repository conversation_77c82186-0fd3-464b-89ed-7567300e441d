import React from 'react';
import { decode } from 'html-entities';
import { replaceScTagsWithPlaceholders } from 'src/helpers/html-parser';
import { Helmet, Helmet<PERSON>rovider } from 'react-helmet-async';

const Start = ({ data, settings }) => {
    if (!data?.mvk_item_content?.custom_fields?.schema_type) return null;
    let blurb = data?.mvk_randomized_blurb ? data?.mvk_randomized_blurb : data?.mvk_item_content?.excerpt
    let postSchema = {
        "@context": "https://schema.org/",
        "@type": `${data?.mvk_item_content?.custom_fields?.schema_type}`,
        "@id": `${data?.mvk_item_meta?.url}#${data?.mvk_item_content?.custom_fields?.schema_type}`,
        "mainEntityOfPage": `${data?.mvk_item_meta?.url}`,
        "headline": `${data?.mvk_randomized_title ? data?.mvk_randomized_title : data?.mvk_item_content?.title}`,
        "name": `${data?.mvk_randomized_title ? data?.mvk_randomized_title : data?.mvk_item_content?.title}`,
        "description": `${blurb ? blurb.replace(/<[^>]*>/g, '').replace(/\n/g, ' ') : ''}`,
        "datePublished": `${data?.mvk_item_content?.date_published}T00:00:00-06:00`,
        "dateModified": `${data?.mvk_item_content?.date_modified}T00:00:00-06:00`,
        "publisher": {
            "@type": "Organization",
            "@id": `https://${config.domain}/`,
            "name": `${settings?.branding?.site_title}`,
            "logo": {
                "@type": "ImageObject",
                "@id": `https://${config.domain}${settings?.branding?.main_logo?.url}`,
                "url": `https://${config.domain}${settings?.branding?.main_logo?.url}`,
                "width": `${settings?.branding?.main_logo?.width}`,
                "height": `${settings?.branding?.main_logo?.height}`
            }
        },
        "url": `${data?.mvk_item_meta?.url}`,
    }

    if (data?.mvk_item_content?.feat_image) {
        postSchema.image = {
            "@type": "ImageObject",
            "@id": `https://${config.domain}${data?.mvk_item_content?.feat_image?.url}`,
            "url": `https://${config.domain}${data?.mvk_item_content?.feat_image?.url}`,
            "height": `${data?.mvk_item_content?.feat_image?.height}`,
            "width": `${data?.mvk_item_content?.feat_image?.width}`
        }
    }
    return (
        <HelmetProvider>
            <Helmet defer={false}>
                <script type="application/ld+json">
                    {JSON.stringify(postSchema)}
                </script>
            </Helmet>
        </HelmetProvider>
    );
};

export default Start;