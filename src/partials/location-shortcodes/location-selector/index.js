import React, { useContext, useState, useEffect } from 'react';
import Select from 'react-select';
import { SettingsContext } from 'src/context';
import { useNavigate } from 'react-router-dom';
// Helpers
import { Coloring } from "src/helpers";
// Styles
import { LocationSelectorShortcode } from '../../location-selector/styles';

const Start = ({ placeholder, locations, bgColor, bgValue }) => {
    const [settings, setSettings] = useContext(SettingsContext);
    const [options, setOptions] = useState([]);
    const navigate = useNavigate();

    useEffect(() => {
        if (locations) {
            let optionArray = [];
            locations.map((location) => {
                if (!settings.current_location || !location?.url?.includes(settings.current_location)) {
                    const path = new URL(location.url).pathname
                    optionArray.push({ value: location.label, label: location.label, url: path })
                }
            })
            setOptions(optionArray);
        }
    }, [settings.current_location]);

    const backgroundColor = Coloring(bgColor, settings);
    const textColor = bgValue === 'dark' ? '#fff' : settings.design.colors.body_copy_color;

    const Styles = {
        control: (styles, state) => ({
            ...styles,
            backgroundColor: backgroundColor,
            borderRadius: `${settings.mvk_theme_config?.other?.button_border_radius_size}px`,
            border: 'none',
            cursor: 'pointer',
            padding: '0 40px',
            fontWeight: settings.mvk_theme_config.other?.button_font_weight,
            minHeight: '48px'
        }),
        menu: (styles, state) => ({
            ...styles,
            border: `2px solid ${backgroundColor}`,
            backgroundColor: backgroundColor
        }),
        option: (styles, state) => ({
            ...styles,
            color: state.isFocused ? backgroundColor : textColor,
            backgroundColor: state.isFocused ? textColor : backgroundColor,
            cursor: 'pointer',
            fontWeight: settings.mvk_theme_config.other?.button_font_weight
        }),
        placeholder: (styles, state) => ({
            ...styles,
            color: textColor,
            fontWeight: settings.mvk_theme_config.other?.button_font_weight
        }),
        indicatorSeparator: (styles, state) => ({ ...styles, color: 'transparent', backgroundColor: 'transparent' }),
        dropdownIndicator: (styles, state) => ({
            ...styles,
            color: textColor
        }),
        singleValue: (styles, state) => ({
            ...styles,
            color: textColor,
            fontWeight: settings.mvk_theme_config.other?.button_font_weight
        })
    }

    return (
        <LocationSelectorShortcode className='location-selector-shortcode'>
            <Select
                name='select location'
                onChange={e => navigate(e.url)}
                aria-label='navigate to location page'
                options={options}
                placeholder={placeholder ? placeholder : 'SELECT A LOCATION'}
                styles={Styles}
                isSearchable={false}
            />
        </LocationSelectorShortcode>
    )
}

export default Start;