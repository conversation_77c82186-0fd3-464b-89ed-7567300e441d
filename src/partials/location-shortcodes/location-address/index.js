import React, { useContext, useState, useEffect } from 'react';
// Context
import { SettingsContext, LocationsContext } from "src/context";

const Start = () => {
    const [settings] = useContext(SettingsContext);
    const [locations] = useContext(LocationsContext);
    const [address, setAddress] = useState((settings.current_location) ? `${locations[settings.current_location]?.mvk_item_content?.custom_fields?.address_info?.address_line_1 ? `${locations[settings.current_location]?.mvk_item_content?.custom_fields?.address_info?.address_line_1},` : ''}${locations[settings.current_location]?.mvk_item_content?.custom_fields?.address_info?.address_line_2 ? ` ${locations[settings.current_location]?.mvk_item_content?.custom_fields?.address_info?.address_line_2},` : ''} ${locations[settings.current_location]?.mvk_item_content?.custom_fields?.address_info?.city}, ${locations[settings.current_location]?.mvk_item_content?.custom_fields?.address_info?.state_province}` : false);
    const [directionsLink, setDirectionsLink] = useState((settings.current_location) ? `https://www.google.com/maps/place/${locations[settings.current_location]?.mvk_item_content?.custom_fields?.address_info?.address_line_1.replace(/ /g, '+')}+${locations[settings.current_location]?.mvk_item_content?.custom_fields?.address_info?.city.replace(/ /g, '+')}+${locations[settings.current_location]?.mvk_item_content?.custom_fields?.address_info?.state_province.replace(/ /g, '+')}+${locations[settings.current_location]?.mvk_item_content?.custom_fields?.address_info?.postal_code_zip.replace(/ /g, '+')}` : false)

    useEffect(() => {
        setAddress((settings.current_location) ? `${locations[settings.current_location]?.mvk_item_content?.custom_fields?.address_info?.address_line_1 ? `${locations[settings.current_location]?.mvk_item_content?.custom_fields?.address_info?.address_line_1},` : ''}${locations[settings.current_location]?.mvk_item_content?.custom_fields?.address_info?.address_line_2 ? ` ${locations[settings.current_location]?.mvk_item_content?.custom_fields?.address_info?.address_line_2},` : ''} ${locations[settings.current_location]?.mvk_item_content?.custom_fields?.address_info?.city}, ${locations[settings.current_location]?.mvk_item_content?.custom_fields?.address_info?.state_province}` : false)
        setDirectionsLink((settings.current_location) ? `https://www.google.com/maps/place/${locations[settings.current_location]?.mvk_item_content?.custom_fields?.address_info?.address_line_1.replace(/ /g, '+')}+${locations[settings.current_location]?.mvk_item_content?.custom_fields?.address_info?.city.replace(/ /g, '+')}+${locations[settings.current_location]?.mvk_item_content?.custom_fields?.address_info?.state_province.replace(/ /g, '+')}+${locations[settings.current_location]?.mvk_item_content?.custom_fields?.address_info?.postal_code_zip.replace(/ /g, '+')}` : false)
    }, [settings.current_location]);

    if (address) {
        return (
            <a class='address underline body-copy-txt' href={directionsLink} target='_blank' aria-label={`directions to this locations address`}>{address}</a>
        )
    } else {
        return null;
    }

}

export default Start;