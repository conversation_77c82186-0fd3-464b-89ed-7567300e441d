import React, { useContext, useState, useEffect } from 'react';
// Context
import { SettingsContext, LocationsContext } from "src/context";
// Helpers
import Clicker from 'src/helpers/clicker';

const Start = () => {
    const [settings] = useContext(SettingsContext);
    const [locations] = useContext(LocationsContext);
    const [phoneNumber, setPhoneNumber] = useState((settings.current_location && locations[settings.current_location]?.mvk_item_content?.custom_fields?.contact_info?.phone) ? locations[settings.current_location]?.mvk_item_content?.custom_fields?.contact_info?.phone : false);

    useEffect(() => {
        setPhoneNumber((settings.current_location && locations[settings.current_location]?.mvk_item_content?.custom_fields?.contact_info?.phone) ? locations[settings.current_location]?.mvk_item_content?.custom_fields?.contact_info?.phone : false)
    }, [settings.current_location]);

    if (phoneNumber) {
        return (
            <Clicker type='anchor' class='location-phone underline body-copy-txt' url={`tel:${phoneNumber.replace(/[^0-9.]/g, '')}`} ariaLabel={'call this location'}>{phoneNumber}</Clicker>
        )
    } else {
        return null;
    }
   
}

export default Start;