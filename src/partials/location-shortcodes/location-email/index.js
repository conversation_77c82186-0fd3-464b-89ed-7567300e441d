import React, { useContext, useState, useEffect } from 'react';
// Context
import { SettingsContext, LocationsContext } from "src/context";
// Helpers
import Clicker from 'src/helpers/clicker';

const Start = () => {
    const [settings] = useContext(SettingsContext);
    const [locations] = useContext(LocationsContext);
    const [email, setEmail] = useState((settings.current_location && locations[settings.current_location]?.mvk_item_content?.custom_fields?.contact_info?.email) ? locations[settings.current_location]?.mvk_item_content?.custom_fields?.contact_info?.email : false);

    useEffect(() => {
        setEmail((settings.current_location && locations[settings.current_location]?.mvk_item_content?.custom_fields?.contact_info?.email) ? locations[settings.current_location]?.mvk_item_content?.custom_fields?.contact_info?.email : false)
    }, [settings.current_location]);

    if (email) {
        return (
            <Clicker type='anchor' class='location-email underline body-copy-txt' url={`mailto:${email.replace(/[^0-9.]/g, '')}`} ariaLabel={'call this location'}>{email}</Clicker>
        )
    } else {
        return null;
    }
   
}

export default Start;