import React, { useContext, useState, useEffect } from 'react';
// Context
import { SettingsContext, LocationsContext } from "src/context";
// Hooks
import { useQuery } from 'src/hooks/query';
// Styles
import * as S from '../styles';

const Start = () => {
    const [settings] = useContext(SettingsContext);
    const [locations] = useContext(LocationsContext);
    const [currentLocationHours, setCurrentLocationHours] = useState((settings.current_location && locations[settings.current_location]?.location_hours) ? locations[settings.current_location]?.location_hours : false);
    const dayLabels = settings.mvk_theme_config?.labels?.day_labels;
    const query = useQuery();
    const simulateDate = query.get('simulate-date');
    const [currentDate] = useState(simulateDate ? new Date(simulateDate) : new Date());
    const days = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'];

    useEffect(() => {
        setCurrentLocationHours((settings.current_location && locations[settings.current_location]?.location_hours) ? locations[settings.current_location]?.location_hours : false)
    }, [settings.current_location]);
    return (
        <S.LocationHours className='location-hours'>
            {currentLocationHours && Object.entries(currentLocationHours).map(([key, val]) => {
                const activeDate = days[currentDate.getDay()] == key;
                return (
                    <div className={`day ${activeDate ? 'current-day' : ''}`}>{dayLabels[key]}: {val.closed ? val.closed : `${val.start_time} - ${val.end_time}`}</div>
                )
            })}
        </S.LocationHours>
    );
}

export default Start;