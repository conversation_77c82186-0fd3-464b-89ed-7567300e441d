import React, { useEffect } from 'react'
import Button from 'src/partials/button';
import { addScript } from 'src/hooks';

const Start = ({ eventID, buttonStyle, buttonText }) => {
    addScript('https://www.eventbrite.com/static/widgets/eb_widgets.js');

    const handleClick = (id) => {
        window.EBWidgets.createWidget({
            widgetType: "checkout",
            eventId: id,
            modal: true,
            modalTriggerElementId: `eventbrite-widget-modal-trigger-${id}`
        });
    }


    return (
        <Button id={`eventbrite-widget-modal-trigger-${eventID}`} onClick={() => handleClick(eventID)} title={buttonText} buttonFunction='styled' type={buttonStyle} />
    )
}

export default Start;