$default: 255, 255, 255;
$primary: 66, 120, 182;
$success: 99, 187, 93;
$danger: 174, 2, 47;

button,
a[role="button"],
a.button {
    display: inline-flex;
    justify-content: center;
    padding: 10px 40px;
    font-size: 1rem;
    line-height: 16px;
    letter-spacing: 1.1px;
    font-weight: 600;
    outline: none;
    cursor: pointer;
    vertical-align: middle;
    align-items: center;
    > span {
        pointer-events: none;
    }
    @media (min-width: 768px) {
        position: relative;
        display: inline-flex;
    }
    @media screen and (max-width: 767px) {
        width: 100%;
        box-sizing: border-box;
    }

    @media screen and (max-width: 800px) {
        font-size: 1.125rem;
    }

    &:focus {
        outline: 0;
    }

    &.icon {
        padding: 0px 11px;
        border: none !important;
        background: none !important;
        font-size: 0.813rem;
    }

    &.center {
        margin: auto;
        text-align: center;
    }

    &.right {
        margin-left: auto !important;
        margin-right: 0px !important;
    }

    &.black {
        background-color: rgba(30, 30, 30, 1);
        color: white;
        border: none;
    }

    &.warning {
        background-color: goldenrod;
        color: white;
        border: 1px solid rgba(255, 255, 255, 0.2);
    }

    &.plain {
        background-color: white;
        color: rgba(0, 0, 0, 0.9);
        border: 1px solid rgba(0, 0, 0, 0.2);
    }

    &.default {
        background-color: rgba(var($default), 1);
        color: rgba(0, 0, 0, 0.9);
        border: 1px solid rgba(0, 0, 0, 0.2);

        &:hover {
            background-color: rgba(var($default), 0.9);
        }
    }

    &.primary {
        background-color: rgba(var($primary), 0.7);
        color: white;
        border: 1px solid rgba(255, 255, 255, 0.3);

        &:hover {
            background-color: rgba(var($primary), 1);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
    }

    &.success {
        background-color: rgba(var($success), 0.7);
        color: white;
        border: 1px solid rgba(255, 255, 255, 0.2);

        &:hover {
            background-color: rgba(var($success), 1);
        }
    }

    &.danger {
        background-color: rgba(var($danger), 0.7);
        color: white;
        border: 1px solid rgba(255, 255, 255, 0.2);

        &:hover {
            background-color: rgba(var($danger), 1);
        }
    }

    &.shadow {
        box-shadow: 2px 2px 0px rgba(0, 0, 0, 0.1) !important;
    }

    &[disabled] {
        opacity: 0.4;
        cursor: not-allowed;
    }
    // ICON ANIMATION
    &[data-icon="true"] {
        > span {
            transition: 0.3s;
        }
        .icon {
            margin-left: 0.75rem;
            width: 1.5rem;
            font-size: 125%;
            pointer-events: none;
        }

        @media (min-width: 768px) {
            &:not([type="submit"]) {
                .icon {
                    position: absolute;
                    transform: translateX(-100%);
                    transition: 0.3s;
                    margin-left: 0;
                    opacity: 0;
                    right: 1.5rem;
                }
                &:hover {
                    span {
                        transform: translateX(-1rem);
                    }
                    .icon {
                        transform: translateX(0);
                        opacity: 1;
                    }
                }
            }
        }
    }
    a {
        color: inherit;
    }
    &.shortcode-button {
        margin: 5px auto;
    }
    &:has(.front-icon) .front-icon {
        margin-right: .5rem;
        max-width: 30px;
    }
}
