#loading {
    text-align: center;

    &.loading-start {
        margin: 200px auto;
        width: 70px;
    }

    &.loading-circle {
        border: 16px solid #f3f3f3;
        border-radius: 50%;
        border-top: 16px solid #3498db;
        width: 100px;
        height: 100px;
        -webkit-animation: spin 2s linear infinite; /* Safari */
        animation: spin 2s linear infinite;
    }
    
    &.loading-page {
        margin: 200px auto;
        -webkit-animation: lines 2s linear infinite;
        animation: lines 1s linear infinite;
        opacity: 0.5;

        &.beat-white {
            animation: beat-white 2s linear infinite;
        }

        .title {
            font-family: '<PERSON>', sans-serif;
            font-size: 3.75rem;
            text-align: center;
            text-shadow: 2px 2px 0px rgba(0, 0, 0, 0.1);
            letter-spacing: 3px;
        }
    }

    &.loading-text {
        margin: 200px auto;
        -webkit-animation: lines 2s linear infinite;
        animation: lines 1s linear infinite;
        opacity: 0.5;

        .title {
            font-size: 2.5rem;
            text-align: center;
            text-shadow: 2px 2px 0px rgba(0, 0, 0, 0.1);
            letter-spacing: 3px;
        }
    }



    &.loading-block {
        margin: 0px auto 30px;
        padding: 40px 20px;
        -webkit-animation: waves 2s linear infinite;
        animation: waves 2s linear infinite;
        opacity: 0.2;
        font-family: 'Verdana' !important;
        font-size: 1.875rem;
        text-align: center;
        color: white;
    }

    &.gradient {
        background: linear-gradient(270deg, rgba(200, 200, 150, 0.1), rgba(100, 100, 100, 0.1), rgba(200, 200, 200, 0.1), rgba(100, 100, 100, 0.1), rgba(200, 200, 150, 0.1));
        background-size: 400% 400%;
        animation: GradientBackground 2s ease infinite;
        border-radius: 10px;

        font-family: 'Verdana' !important;
        font-size: 1.875rem;
        text-align: center;
        color: white;
        opacity: 0.2;
    }
}
    

/* Safari */
@-webkit-keyframes spin {
    0% { -webkit-transform: rotate(0deg); }
    100% { -webkit-transform: rotate(360deg); }
}
  
@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Safari */
@-webkit-keyframes lines {
    0% { color:red; }
    50% { color:blue; }
    100% { color:red; }
}
  
@keyframes lines {
    0% { color:red; }
    50% { color:blue; }
    100% { color:red; }
}

@-webkit-keyframes beat-white {
    0% { color:white; }
    50% { color:red; }
    100% { color:white; }
}
  
@keyframes beat-white {
    0% { color:white; }
    50% { color:red; }
    100% { color:white; }
}

/* Safari */
@-webkit-keyframes waves {
    0% { background-color:transparent; }
    50% { background-color:DodgerBlue; }
    100% { background-color:transparent; }
}
  
@keyframes waves {
    0% { background-color:transparent; }
    50% { background-color:DodgerBlue; }
    100% { background-color:transparent; }
}

@-webkit-keyframes GradientBackground {
    0%{background-position:0% 50%}
    50%{background-position:100% 50%}
    100%{background-position:0% 50%}
}
@-moz-keyframes GradientBackground {
    0%{background-position:0% 50%}
    50%{background-position:100% 50%}
    100%{background-position:0% 50%}
}
@keyframes GradientBackground {
    0%{background-position:0% 50%}
    50%{background-position:100% 50%}
    100%{background-position:0% 50%}
}




