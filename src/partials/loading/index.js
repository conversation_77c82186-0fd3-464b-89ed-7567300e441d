import React from "react";

// SCSS, CSS.
import "src/partials/loading/index.scss";
import { Dots } from './styles';

const Start = (props) => {
    switch (props.type) {
        case "loading-start":
            return <LoadingStart />;
        case "loading-text":
            return <LoadingText text={props.text} />;
        case "loading-page":
            return <LoadingPage />;
        case "loading-module-page":
            return <LoadingModule />;
        case "gradient":
            return <Gradient props={props} />;
        case "loading-block":
            return <LoadingBlock props={props} />;
        case "redirect":
            return <LoadRedirect props={props} />;
        case "dot-pulse":
            return <DotPulse props={props} />;
        case "nothing":
        default:
            return <Nothing />;
    }
};

const LoadingStart = () => {
    return (
        <div id="loading" class="loading-start">
            <div class="loader quantum-spinner"></div>
        </div>
    );
};



const LoadingPage = () => {
    return (
        <div id="loading" class="loading-page">
            <div class="title">PYLOT</div>
        </div>
    );
};

const LoadingText = (props) => {
    return (
        <div id="loading" class="loading-text">
            <div class="title">{props.text}</div>
        </div>
    );
};


const LoadingModule = () => {
    return (
        <div id="loading" class="loading-module-page">
            LOADING...
        </div>
    );
};

const Gradient = (props) => {
    var styles = {
        padding: props.height ? `${props.height / 2}px 0px` : "100px 0px"
    };

    return (
        <div id="loading" class="gradient" style={styles}>
            LOADING
        </div>
    );
};

const LoadingBlock = (props) => {
    var styles = {
        height: props.height ? props.height : "200px"
    };

    return (
        <div id="loading" class="loading-block">
            LOADING
        </div>
    );
};

const LoadRedirect = () => {
    var styles = {
        padding: "100px 50px",
        textAlign: 'center'
    };

    return (
        <div id="loading" style={styles}>
            Redirecting...
        </div>
    );
};

const DotPulse = ({ props }) => {

    return (
        <div id="loading">
            <Dots dotColor={props.dotColor}>
                <div class='loader'></div>
            </Dots>
        </div>
    )
}

const Nothing = () => {
    return <div></div>;
};

export default Start;
