import React, { useState, useContext } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faArrowUp } from '@fortawesome/free-solid-svg-icons';
import { ScrollTopButton } from './styles';
import { useInView } from 'react-intersection-observer';

// Context
import { SettingsContext } from "src/context";
// Helpers
const Imaging = React.lazy(() => import('src/helpers/imaging'));

const ScrollButton = () => {
    const [settings, setSettings] = useContext(SettingsContext);
    const [visible, setVisible] = useState(false)
    const { ref, inView } = useInView({
        triggerOnce: true,
        fallbackInView: true
    });

    const toggleVisible = () => {
        const scrolled = document.documentElement.scrollTop;
        if (scrolled > 300) {
            setVisible(true)
        } else if (scrolled <= 300) {
            setVisible(false)
        }
    };

    const scrollToTop = () => {
        window.scrollTo({
            top: 0,
            behavior: 'smooth'
        });
    };

    window.addEventListener('scroll', toggleVisible);

    return (
        <ScrollTopButton ref={ref} className={`back-to-top ${visible ? 'active' : ''}`}>
            {inView ?
                <>
                    {settings?.mvk_theme_config?.other?.back_to_top_icon ?
                        <Imaging data={settings?.mvk_theme_config?.other?.back_to_top_icon} onClick={scrollToTop} />
                        :
                        <FontAwesomeIcon className='default-icon' icon={faArrowUp} onClick={scrollToTop} />
                    }
                </>
                : null}
        </ScrollTopButton>
    );
}

export default ScrollButton;