import React from "react";
import './index.scss';
const Clicker = React.lazy(() => import('src/helpers/clicker'));

import { decode } from 'html-entities';


const Start = (props) => {
    const background = props.data.background;
    const breadcrumbs = props.data.breadcrumb_list;

    let textColor = {
        color: background === 'bg-dark' ? '#fff' : `${props.settings.design?.colors?.body_copy_color}`
    }
    let bgColor = {};
    if (background === "bg-light") {
        bgColor = {
            backgroundColor: `${props.settings.design?.colors?.background_color}`
        }
    } else if (background === 'bg-dark'){
        bgColor = {
            backgroundColor:`${props.settings.design?.colors?.primary_color}`
        }
    } 

    return (
        <div class={`breadcrumbs`} style={bgColor}>
            <div class='grid-container'>
                <div class='grid-x'>
                    <div class='cell'>
                        {breadcrumbs.map((breadcrumb, index) => {
                            let path = new URL(breadcrumb.url).pathname;
                            return (
                                <Clicker key={index} class='breadcrumb' type='anchor' url={path} style={textColor}>
                                    {decode(breadcrumb.title)}
                                </Clicker>
                            );
                        })}
                    </div>
                </div>
            </div>
        </div>
    );
}

export default Start;