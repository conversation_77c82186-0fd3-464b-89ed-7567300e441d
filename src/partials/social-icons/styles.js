import styled from 'styled-components';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';

export const SocialWrapper = styled.div`
    display: flex;
    flex-wrap: wrap;
    align-items: center;

    &.center {
        justify-content: center;
    }
    &.right {
        justify-content: flex-end;
    }
    &.below-feed {
        margin-top: 1rem;
    }
    .social-icon {
        margin: 0 .5rem .5rem .5rem;
    }
`;

export const SocialIcon = styled(FontAwesomeIcon)`
    padding: 0.5rem;
    border-radius: ${props => props.borderRadius}%;
    width: 20px;
    height: 20px;
    transition: .2s;
    &.default {
        background-color: ${props => props.iconColor};
        color: #fff;
        &:hover {
            color: ${props => props.iconColor};
            background-color: #fff;
        }
    }
    &.icon-only {
        color: ${props => props.iconColor};
        &:hover {
            color: ${props => props.textColor};
        }
    }
    &.icon-outline {
        color: ${props => props.iconColor};
        border: 2px solid ${props => props.iconColor};
        &:hover {
            background-color: ${props => props.iconColor};
            color: #fff;
        }
    }
    &.icon-background {
        color: ${props => props.iconColor};
        background-color: ${props => props.backgroundColor};
        &:hover {
            background-color: ${props => props.iconColor};
            color: ${props => props.backgroundColor};
        }
    }
`;