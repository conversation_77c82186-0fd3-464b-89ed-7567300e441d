import React from 'react';
import { faFacebookF, faInstagram, faLinkedin, faYoutube } from '@fortawesome/fontawesome-free-brands';
import { faTiktok, faXTwitter, faThreads } from "@fortawesome/free-brands-svg-icons";

// Helpers
import { Coloring } from 'src/helpers';
const Clicker = React.lazy(() => import('src/helpers/clicker'));
// Styles
import { SocialWrapper, SocialIcon } from './styles';

const Social = ({ data, settings }) => {

    return (
        <SocialWrapper className={`social-wrapper ${data?.icons_placement} ${data?.icons_alignment}`}>
            {settings.social?.social_media_channels && settings.social?.social_media_channels.map((channel) => <Icon data={channel} module={data} settings={settings} />)}
        </SocialWrapper>
    );
}

export const Icon = ({ data, module, settings }) => {
    let socialType = data?.channel?.value
    let socialIconColor = module?.icon_color ? Coloring(module?.icon_color, settings) : Coloring(settings?.mvk_theme_config?.footer?.footer_social_icon_color, settings);

   
    if (socialType === 'facebook') {
        var icon = faFacebookF
    } else if (socialType === 'twitter') {
        var icon = faXTwitter
    } else if (socialType === 'instagram') {
        var icon = faInstagram
    } else if (socialType === 'linkedin') {
        var icon = faLinkedin
    } else if (socialType === 'youtube') {
        var icon = faYoutube
    } else if (socialType === 'tiktok') {
        var icon = faTiktok
    } else if (socialType === 'threads') {
        var icon = faThreads
    }

    return (
        <Clicker type="anchor" url={data?.url} rel="noopener" target="_blank" class={"social-icon"} title={data?.channel?.label} alt={data?.channel?.screenreader_alt_text} ariaLabel={`link to ${data?.channel?.label}`}>
            <SocialIcon
                icon={icon}
                className={module?.icon_style ? module?.icon_style : 'default'}
                iconColor={socialIconColor}
                textColor={settings?.mvk_theme_config?.footer?.footer_text_color}
                footerBg={settings?.mvk_theme_config?.footer?.footer_background_color}
                backgroundColor={module?.icon_style && module?.icon_style === 'icon-background' ? Coloring(module?.icon_background_color, settings) : ''}
                borderRadius={module?.icon_border_radius ? module?.icon_border_radius : '50'}
                aria-label={`${data?.channel?.label} logo`}
            />
        </Clicker>
    );

}

export default Social;