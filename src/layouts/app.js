import React, { useLayoutEffect, useEffect, useState, useRef, useTransition, useContext } from 'react';
import { useLocation, useNavigationType } from 'react-router-dom';

// CONTEXT.
import { AppContext } from 'src/contexts/app';

const Start = ({ children, ...otherProps }) => {
    const appContext = useContext(AppContext);
    const [ isPending, startTransition ] = useTransition();
    const [ position, setPosition ]      = useState(window.pageYOffset);

    const location = useLocation();
    const navType  = useNavigationType();
    const keyRef   = useRef(location.key);

    // TRIGGER: RESTORE SCROLL POSITION. Ignore if false, restore if integer.
    const [ restore, setRestore ] = useState(false);
    // OBJECT WITH ALL SCROLL POINTS.
    const [ offsets, setOffsets ] = useState({});

    // RUN FIRST, RUN ONLY ONCE.
    useLayoutEffect(() => {
        window.onscroll = () => setPosition(window.pageYOffset);
    }, []);

    // UPDATE OFFSETS OBJECT WITH SCROLL POSITION IF ANY SCROLLING.
    useEffect(() => {
        // ONLY UPDATE OFFSETS IF REF MATCHES LOCATION KEY & TRANSITION ISN'T PENDING.
        if (keyRef.current == location.key && !isPending) setOffsets({ ...offsets, ...{ [location.key]:position }});
    }, [ position ]);

    // RUN AFTER PAGE IS DONE LOADING.
    useEffect(() => {
        setRestore(false);
        startTransition(() => {
            if (navType == 'PUSH') {
                keyRef.current = location.key;
            } else if (navType == 'POP' && keyRef.current != location.key && offsets[location.key] >= 20) {
                setRestore(offsets[location.key]);
            } else {
                setRestore(false);
            }

        });
    }, [ location.key ]);

    // RUN AFTER TRANSITION IS COMPLETE, RESTORE SCROLL POSITION IF REQUESTED.
    useEffect(() => {
        // CONDITIONS, RUN AFTER TRANSITION.
        // 1) Transition isn't pending.
        // 2) Restore state has an integer.
        // 3) App context has 'true' for the location's key.
        // 4) Body element has a height larger than the browser window height.

        switch (true) {
            // TRANSITION IS STILL PENDING.
            case isPending:
                // console.warn('DOOR 1: still pending.');
                break;
            // ALL CONDITIONS MET, RESTORE SCROLL POSITION.
            case !isPending && restore > 1 && appContext?.restores?.[location.key]?.scrollback && document.body.offsetHeight > window.innerHeight:
                keyRef.current = location.key;
                document.body.scrollTop = restore;
                document.documentElement.scrollTop = restore;
                setRestore(false);
                // console.warn('DOOR 2: not pending, scrolling back.');
                break;
            case !isPending:
                // console.warn('DOOR 3: not pending, not scrolling back.', keyRef.current, location.key);
                break;
        };
    }, [ isPending, restore, document.body.offsetHeight ]);

    return (<div id="app" children={children} {...otherProps} />);
};
Start.displayName = 'layouts, app: Start';

export default Start;
