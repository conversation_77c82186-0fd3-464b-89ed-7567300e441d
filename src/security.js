 import React, { Suspense, useState } from "react";
var AmazonCognitoIdentity = require('amazon-cognito-identity-js');

const Cognito = React.lazy(() => import("src/cognito"));
const Routing = React.lazy(() => import("src/routing"));

const Start = ({ principal }) => {
    const [ locked, setLocked ] = useState(false);

    useState(() => {
        if (principal?.settings?.cognito) {
            var userPool = new AmazonCognitoIdentity.CognitoUserPool({ UserPoolId: principal.settings?.cognito?.user_pool_id, ClientId: principal.settings?.cognito?.app_client_id });
            var cognitoUser = userPool.getCurrentUser();
            if (cognitoUser) {
                setLocked(false);
            } else {
                setLocked(true);
            }
        }
    }, []);

    if (locked) {
        // SEND THEM TO COGNITO.
        return (
            <Suspense fallback={<div />}>
                <Cognito principal={principal} />
            </Suspense>
        );
    } else {
        // SEND THEM TO ROUTING.
        return (
            <Suspense fallback={<div />}>
                <Routing principal={principal} />
            </Suspense>
        );
    }
};

export default Start;