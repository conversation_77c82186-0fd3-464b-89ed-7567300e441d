import { getUA, deviceType, isMobileOnly, isTablet, isDesktop } from 'react-device-detect';
import Base64 from 'base-64';
import axios from 'axios';

// HELPERS.
import * as Cookies from 'src/helpers/cookies';
import { getSecrets } from 'src/api/requests';

var domain = window.location.hostname.toLowerCase();
var devEnv = cache.dev = domain.includes('dev') ? true : false;
domain = domain.replace('mypylot.dev.', 'www.');
domain = domain.replace('mypylot.approach.', 'www.');
domain = domain.replace('.approach', '');
domain = domain.replace('approach.dev.', 'www.');
domain = domain.replace('approach.', 'www.');
domain = domain.replace('test', 'com');

// FOR LOCAL ENV TESTING.
const regex2 = /\.dev\d?/ig;
domain = domain.replace(regex2, '.preflight');

// BASE DOMAIN FOR API.
const base = (domain.includes('preflight')) ? 'api.preflight.mypylot.io' : 'api.mypylot.io';
const global = (domain.includes('preflight')) ? 'preflight.mypylot.com' : 'admin.mypylot.com';
// QUERY STRING.
const urlParams = new URLSearchParams(window.location.search);

const config = {
    // DEVICES.
    useragent: getUA,
    device: deviceType,
    mobile: isMobileOnly,
    tablet: isTablet,
    browser: isDesktop,
    desktop: isDesktop,

    // API USE.
    base: base,
    hostname: window.location.hostname.toLowerCase(),
    domain: domain,
    global: global,
    // PREVIEW MODE.
    preview: {
        enabled: urlParams.has('preview') ? previewSecurity() : false,
        baseDomain: urlParams.has('preview') ? previewBaseDomain() : false,
        basePath: urlParams.has('preview') ? previewBasePath() : false,
        // USED FOR HIDING PART OF THE URL FROM THE CODE BASE.
        decoded: Base64.decode('d3AtanNvbg=='),
        decodeContent: Base64.decode('d3AtY29udGVudA=='),
        decodeShort: Base64.decode('d3A=')
    },

    dev: devEnv,

    // FUTURE USE.
    down: false,
    cookies: Cookies.getall(),
    status: 'normal',
    protocol: location.protocol,
    random: Math.floor(Math.random() * 10000),

    // CURRENT ENV.
    env: {
        origin: location.origin,
        domain: window.location.hostname,
        port: location.port,
        protocol: location.protocol,
        referrer: document.referrer,
    },

    // SCREENS.
    screens: { xsmall:360, small:390, mobile:428, medium:640, tablet:768, large:960, larger:1024, largest:1280, desktop:1280, maximum:1280 }
};

// SET GLOBAL.
if (!window.config) window.config = config;

export default config;

function previewSecurity () {
    return Base64.decode('b2ZiRjNvRFdMcGpMWHhtbkR5TEp2ZlpiYUZRUHRIb0ZxZHBvbzg4c3pKc2g3') == urlParams.get('preview');
};

function previewBaseDomain() {
    if (Base64.decode('b2ZiRjNvRFdMcGpMWHhtbkR5TEp2ZlpiYUZRUHRIb0ZxZHBvbzg4c3pKc2g3') == urlParams.get('preview')) {
        switch (true) {
            case urlParams.has('domain'):
                return urlParams.get('domain');
            case urlParams.has('url'):
                const url = urlParams.get('url');
                const new_url = new URL(url);
                return new_url.host;
            default:
                return base.replace('api', domain.split('.')[0]);
        };
    } else return false;
};

function previewBasePath() {
    if (Base64.decode('b2ZiRjNvRFdMcGpMWHhtbkR5TEp2ZlpiYUZRUHRIb0ZxZHBvbzg4c3pKc2g3') == urlParams.get('preview')) {
        return Base64.decode('d3AtanNvbi93cC92Mg==');
    } else return false;
};

export function getSecretCookie() {
    const result = document.cookie.split('; ').find((row) => row.startsWith('www.mypylot.com'))?.split('=')[1];

    if (result) {
        return result;
    } else {
        getSecrets();
    }
};
