/*******************************************************************************************************
   Copyright 2020 - Calise Partners LLC d/b/a Imaginuity. All rights are reserved.
   Reproduction or transmission in whole or in part, in any form or by any means, electronic, mechanical
or otherwise, is prohibited without the prior written consent of Calise Partners LLC d/b/a Imaginuity.
********************************************************************************************************
   Project : 10618 Maverick Platform Development
   Author : Imaginuity Developers (<PERSON>, <PERSON>)
   Description : Used for making API/GET requests to the backend. Accessed through context.js
   Creation Date : Mon Nov 23 2020
   Documentation : [documentation url - point to default wiki for project if no specific documentation for script]
 ********************************************************************************************************/

import axios from 'axios';

// CONFIG.
import config from 'src/config';

const APIauto = axios.create({
    baseURL: `https://us-autocomplete-pro.api.smartystreets.com`,
    withCredentials: false,
    headers: {
        // 'Content-Type': 'multipart/form-data'
    }
});

const APIstreet = axios.create({
    baseURL: `https://us-street.api.smartystreets.com/`,
    withCredentials: false,
    headers: {
        // 'Content-Type': 'multipart/form-data'
    }
});

// SMARTY STREETS.
export const Autocomplete = (query) => {
    var key = '9201756337460193'
    return APIauto.get(`/lookup`, { params: { key:key, search:query } }).then(res => res.data).catch(error => console.log(error));
};

// SMARTY STREETS.
export const Street = (street, city, state) => {
    var key = '9201756337460193'
    return APIstreet.get(`/street-address`, { params: { key:key, street:street, city:city, state:state } }).then(res => res.data).catch(error => console.log(error));
};

// https://us-street.api.smarty.com/street-address?&callback=jQuery34006018087718815137_1644566915729&auth-id=21102174564513388&street=350%20Avenue%20E&city=Dallas&state=TX&_=1644566915733