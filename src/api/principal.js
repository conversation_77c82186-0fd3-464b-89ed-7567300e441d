import axios from 'axios';
import { withinDateRange } from "src/helpers/date";

// CONFIG.
import config from 'src/config';
// API.
import { API } from 'src/api/axios';

const Start = {};
Start.Request = async (translation) => {
    const urlParams = new URLSearchParams(window.location.search);
    const simulateDate = urlParams.get('simulate-date');

    const requests = [
        {
            name: 'settings',
            endpoint: (config.preview.enabled) ? `${config.preview.decoded}/mvk_custom_rest/v1/mvk_settings?preview=ofbF3oDWLpjLXxmnDyLJvfZbaFQPtHoFqdpoo88szJsh7` : 'settings.json',
            attempt: true
        },
        {
            name: 'pages',
            endpoint: (config.preview.enabled) ? `${config.preview.decoded}/${config.preview.decodeShort}/v2/pages/?per_page=1000&mvk_min&preview=ofbF3oDWLpjLXxmnDyLJvfZbaFQPtHoFqdpoo88szJsh7` : 'pages.json',
            attempt: true
        },
        {
            name: 'settingsTranslated',
            endpoint: (config.preview.enabled) ? `${config.preview.decoded}/mvk_custom_rest/v1/mvk_settings&lang=${translation}&preview=ofbF3oDWLpjLXxmnDyLJvfZbaFQPtHoFqdpoo88szJsh7` : `settings.${translation}.json`,
            attempt: (translation && translation !== 'en')
        },
        {
            name: 'pagesTranslated',
            endpoint: (config.preview.enabled) ? `${config.preview.decoded}/${config.preview.decodeShort}/v2/pages/?per_page=1000&mvk_min&lang=${translation}&preview=ofbF3oDWLpjLXxmnDyLJvfZbaFQPtHoFqdpoo88szJsh7` : `pages.${translation}.json`,
            attempt: (translation && translation !== 'en')
        }
    ];

    return axios.all(requests.filter((item) => item.attempt).map((item) => {
        return API.get(item.endpoint).then(resp => {
            return { ...item, ...{ data: resp.data || false } };
        }).catch(error => console.log(error));
    })).then((results) => {
        var settings = results.find((item) => item?.name == 'settings') || false;
        var pages = results.find((item) => item?.name == 'pages') || false;
        var settingsTranslated = results.find((item) => item?.attempt && item?.name == 'settingsTranslated') || false;
        var pagesTranslated = results.find((item) => item?.attempt && item?.name == 'pagesTranslated') || false;
        const satelliteDetails = settings.data?.sei_settings?.spot?.satelliteDetails;
        const webDetails = settings.data?.sei_settings?.spot?.webDetails;
        if (webDetails) {
            if (settings?.data?.design?.colors) {
                updateSettings(settings?.data?.design?.colors, webDetails, [
                    { 'primary_color': 'primaryColor' },
                    { 'secondary_color': 'secondaryColor' },
                    { 'tertiary_color': 'tertiaryColor' },
                    { 'background_color': 'backgroundColor' },
                    { 'body_copy_color': 'bodyCopyColor' },
                    { 'main_nav.bottom_mobile_bar_background_color': 'bottomMobileBarBackgroundColor' },
                    { 'main_nav.bottom_mobile_bar_text_color': 'bottomMobileBarTextColor' },
                    { 'main_nav.nav_bg_color': 'navigationBackgroundColor' },
                    { 'main_nav.nav_mobile_bg_color': 'navigationMobileBackgroundColor' },
                    { 'main_nav.nav_mobile_txt_color': 'navigationMobileTextColor' },
                    { 'main_nav.nav_txt_color': 'navigationTextColor' },
                    { 'main_nav.nav_txt_hv_color': 'navigationTextHoverColor' },
                    { 'util_nav.ut_nav_bg_color': 'utilityNavigationBackgroundColor' },
                    { 'util_nav.ut_nav_txt_color': 'utilityNavigationTextColor' },
                    { 'util_nav.ut_nav_txt_hv_color': 'utilityNavigationTextHoverColor' }
                ]);
            }
            if (settings?.data?.service_titan) {
                updateSettings(settings?.data?.service_titan, webDetails, [
                   {'api_key': 'serviceTitanRequestWidget'}
                ]);
            }
            if (settings?.data?.social?.social_media_channels) {
                updateArrayFieldsForChannels(
                    settings?.data?.social.social_media_channels,
                    webDetails, // Source: webDetails object
                    [
                        { facebook: 'facebookLink' },
                        { instagram: 'instagramLink' },
                        { linkedin: 'linkedinLink' },
                        { twitter: 'xLink' },
                        { tiktok: 'tiktokLink' }
                    ]
                );
            }
        }
        let isActive = false;
        if (settings.data?.holiday?.enable_holiday) {
            let currentDate = simulateDate ? new Date(simulateDate) : new Date();
            const startDate = settings.data?.holiday?.start ? new Date(settings.data?.holiday?.start).setHours(0, 0, 0, 0) : false;
            const endDate = settings.data?.holiday?.end ? new Date(settings.data?.holiday?.end).setHours(0, 0, 0, 0) : false;
            if (startDate && endDate) {
                isActive = (currentDate >= startDate && currentDate < endDate);
            } else if (startDate && !endDate) {
                isActive = (currentDate >= startDate);
            } else if (!startDate && endDate) {
                isActive = (currentDate < endDate);
            }
        }
        if (isActive) {
            // Site
            if (settings?.data?.design?.colors) {
                settings.data.design.colors.primary_color = settings?.data?.holiday?.styles?.primary_color ? settings?.data?.holiday?.styles?.primary_color : settings?.data?.design?.colors?.primary_color
                settings.data.design.colors.tertiary_color = settings?.data?.holiday?.styles?.tertiary_color ? settings?.data?.holiday?.styles?.tertiary_color : settings?.data?.design?.colors?.tertiary_color
                settings.data.design.colors.secondary_color = settings?.data?.holiday?.styles?.secondary_color ? settings?.data?.holiday?.styles?.secondary_color : settings?.data?.design?.colors?.secondary_color
                settings.data.design.colors.body_copy_color = settings?.data?.holiday?.styles?.body_copy_color ? settings?.data?.holiday?.styles?.body_copy_color : settings?.data?.design?.colors?.body_copy_color
                settings.data.design.colors.background_color = settings?.data?.holiday?.styles?.background_color ? settings?.data?.holiday?.styles?.background_color : settings?.data?.design?.colors?.background_color
            }
            // Main Nav
            if (settings?.data?.design?.colors?.main_nav) {
                settings.data.design.colors.main_nav.nav_mobile_bg_color = settings?.data?.holiday?.styles?.nav_mobile_bg_color ? settings?.data?.holiday?.styles?.nav_mobile_bg_color : settings?.data?.design?.colors?.main_nav?.nav_mobile_bg_color
                settings.data.design.colors.main_nav.nav_bg_color = settings?.data?.holiday?.styles?.nav_bg_color ? settings?.data?.holiday?.styles?.nav_bg_color : settings?.data?.design?.colors?.main_nav?.nav_bg_color
                settings.data.design.colors.main_nav.nav_txt_color = settings?.data?.holiday?.styles?.nav_txt_color ? settings?.data?.holiday?.styles?.nav_txt_color : settings?.data?.design?.colors?.main_nav?.nav_txt_color
                settings.data.design.colors.main_nav.nav_txt_hv_color = settings?.data?.holiday?.styles?.nav_txt_hv_color ? settings?.data?.holiday?.styles?.nav_txt_hv_color : settings?.data?.design?.colors?.main_nav?.nav_txt_hv_color
            }
            // Util Nav
            if (settings?.data?.design?.colors?.util_nav) {
                settings.data.design.colors.util_nav.ut_nav_txt_color = settings?.data?.holiday?.styles?.ut_nav_txt_color ? settings?.data?.holiday?.styles?.ut_nav_txt_color : settings?.data?.design?.colors?.util_nav?.ut_nav_txt_color
                settings.data.design.colors.util_nav.ut_nav_bg_color = settings?.data?.holiday?.styles?.ut_nav_bg_color ? settings?.data?.holiday?.styles?.ut_nav_bg_color : settings?.data?.design?.colors?.util_nav?.ut_nav_bg_color
                settings.data.design.colors.util_nav.ut_nav_txt_hv_color = settings?.data?.holiday?.styles?.ut_nav_txt_hv_color ? settings?.data?.holiday?.styles?.ut_nav_txt_hv_color : settings?.data?.design?.colors?.util_nav?.ut_nav_txt_hv_color
                settings.data.design.colors.util_nav.utility_nav_custom_background = settings?.data?.holiday?.styles?.utility_nav_custom_background ? settings?.data?.holiday?.styles?.utility_nav_custom_background : settings?.data?.design?.colors?.util_nav?.utility_nav_custom_background
            }
            // Footer
            if (settings?.data?.mvk_theme_config?.footer) {
                settings.data.mvk_theme_config.footer.footer_text_color = settings?.data.holiday?.styles?.footer_text_color ? settings?.data.holiday?.styles?.footer_text_color : settings?.data?.mvk_theme_config?.footer?.footer_text_color
                settings.data.mvk_theme_config.footer.footer_background_color = settings?.data?.holiday?.styles?.footer_background_color ? settings?.data?.holiday?.styles?.footer_background_color : settings?.data?.mvk_theme_config?.footer?.footer_background_color
                settings.data.mvk_theme_config.footer.footer_border_color = settings?.data?.holiday?.styles?.footer_border_color ? settings?.data?.holiday?.styles?.footer_border_color : settings?.data?.mvk_theme_config?.footer?.footer_border_color
                settings.data.mvk_theme_config.footer.footer_social_icon_color = settings?.data?.holiday?.styles?.footer_social_icon_color ? settings?.data?.holiday?.styles?.footer_social_icon_color : settings?.data?.mvk_theme_config?.footer?.footer_social_icon_color
                settings.data.mvk_theme_config.footer.footer_background_image = settings?.data?.holiday?.styles?.footer_background_image ? settings?.data?.holiday?.styles?.footer_background_image : settings?.data?.mvk_theme_config?.footer?.footer_background_image
                settings.data.mvk_theme_config.footer.footer_background_align = settings?.data?.holiday?.styles?.footer_background_align ? settings?.data?.holiday?.styles?.footer_background_align : settings?.data?.mvk_theme_config?.footer?.footer_background_align
                settings.data.mvk_theme_config.footer.footer_background_value = settings?.data?.holiday?.styles?.footer_background_value ? settings?.data?.holiday?.styles?.footer_background_value : settings?.data?.mvk_theme_config?.footer?.footer_background_value
            }
            // langs
            if (settingsTranslated) {
                // Site
                if (settingsTranslated?.data?.design?.colors) {
                    settingsTranslated.data.design.colors.primary_color = settingsTranslated?.data?.holiday?.styles?.primary_color ? settingsTranslated?.data?.holiday?.styles?.primary_color : settingsTranslated?.data?.design?.colors?.primary_color
                    settingsTranslated.data.design.colors.tertiary_color = settingsTranslated?.data?.holiday?.styles?.tertiary_color ? settingsTranslated?.data?.holiday?.styles?.tertiary_color : settingsTranslated?.data?.design?.colors?.tertiary_color
                    settingsTranslated.data.design.colors.secondary_color = settingsTranslated?.data?.holiday?.styles?.secondary_color ? settingsTranslated?.data?.holiday?.styles?.secondary_color : settingsTranslated?.data?.design?.colors?.secondary_color
                    settingsTranslated.data.design.colors.body_copy_color = settingsTranslated?.data?.holiday?.styles?.body_copy_color ? settingsTranslated?.data?.holiday?.styles?.body_copy_color : settingsTranslated?.data?.design?.colors?.body_copy_color
                    settingsTranslated.data.design.colors.background_color = settingsTranslated?.data?.holiday?.styles?.background_color ? settingsTranslated?.data?.holiday?.styles?.background_color : settingsTranslated?.data?.design?.colors?.background_color
                }
                // Main Nav
                if (settingsTranslated?.data?.design?.colors?.main_nav) {
                    settingsTranslated.data.design.colors.main_nav.nav_mobile_bg_color = settingsTranslated?.data?.holiday?.styles?.nav_mobile_bg_color ? settingsTranslated?.data?.holiday?.styles?.nav_mobile_bg_color : settingsTranslated?.data?.design?.colors?.main_nav?.nav_mobile_bg_color
                    settingsTranslated.data.design.colors.main_nav.nav_bg_color = settingsTranslated?.data?.holiday?.styles?.nav_bg_color ? settingsTranslated?.data?.holiday?.styles?.nav_bg_color : settingsTranslated?.data?.design?.colors?.main_nav?.nav_bg_color
                    settingsTranslated.data.design.colors.main_nav.nav_txt_color = settingsTranslated?.data?.holiday?.styles?.nav_txt_color ? settingsTranslated?.data?.holiday?.styles?.nav_txt_color : settingsTranslated?.data?.design?.colors?.main_nav?.nav_txt_color
                    settingsTranslated.data.design.colors.main_nav.nav_txt_hv_color = settingsTranslated?.data?.holiday?.styles?.nav_txt_hv_color ? settingsTranslated?.data?.holiday?.styles?.nav_txt_hv_color : settingsTranslated?.data?.design?.colors?.main_nav?.nav_txt_hv_color
                }
                // Util Nav
                if (settingsTranslated?.data?.design?.colors?.util_nav) {
                    settingsTranslated.data.design.colors.util_nav.ut_nav_txt_color = settingsTranslated?.data?.holiday?.styles?.ut_nav_txt_color ? settingsTranslated?.data?.holiday?.styles?.ut_nav_txt_color : settingsTranslated?.data?.design?.colors?.util_nav?.ut_nav_txt_color
                    settingsTranslated.data.design.colors.util_nav.ut_nav_bg_color = settingsTranslated?.data?.holiday?.styles?.ut_nav_bg_color ? settingsTranslated?.data?.holiday?.styles?.ut_nav_bg_color : settingsTranslated?.data?.design?.colors?.util_nav?.ut_nav_bg_color
                    settingsTranslated.data.design.colors.util_nav.ut_nav_txt_hv_color = settingsTranslated?.data?.holiday?.styles?.ut_nav_txt_hv_color ? settingsTranslated?.data?.holiday?.styles?.ut_nav_txt_hv_color : settingsTranslated?.data?.design?.colors?.util_nav?.ut_nav_txt_hv_color
                    settingsTranslated.data.design.colors.util_nav.utility_nav_custom_background = settingsTranslated?.data?.holiday?.styles?.utility_nav_custom_background ? settingsTranslated?.data?.holiday?.styles?.utility_nav_custom_background : settingsTranslated?.data?.design?.colors?.util_nav?.utility_nav_custom_background
                }
                // Footer
                if (settingsTranslated?.data?.mvk_theme_config?.footer) {
                    settingsTranslated.data.mvk_theme_config.footer.footer_text_color = settingsTranslated?.data.holiday?.styles?.footer_text_color ? settingsTranslated?.data.holiday?.styles?.footer_text_color : settingsTranslated?.data?.mvk_theme_config?.footer?.footer_text_color
                    settingsTranslated.data.mvk_theme_config.footer.footer_background_color = settingsTranslated?.data?.holiday?.styles?.footer_background_color ? settingsTranslated?.data?.holiday?.styles?.footer_background_color : settingsTranslated?.data?.mvk_theme_config?.footer?.footer_background_color
                    settingsTranslated.data.mvk_theme_config.footer.footer_border_color = settingsTranslated?.data?.holiday?.styles?.footer_border_color ? settingsTranslated?.data?.holiday?.styles?.footer_border_color : settingsTranslated?.data?.mvk_theme_config?.footer?.footer_border_color
                    settingsTranslated.data.mvk_theme_config.footer.footer_social_icon_color = settingsTranslated?.data?.holiday?.styles?.footer_social_icon_color ? settingsTranslated?.data?.holiday?.styles?.footer_social_icon_color : settingsTranslated?.data?.mvk_theme_config?.footer?.footer_social_icon_color
                    settingsTranslated.data.mvk_theme_config.footer.footer_background_image = settingsTranslated?.data?.holiday?.styles?.footer_background_image ? settingsTranslated?.data?.holiday?.styles?.footer_background_image : settingsTranslated?.data?.mvk_theme_config?.footer?.footer_background_image
                    settingsTranslated.data.mvk_theme_config.footer.footer_background_align = settingsTranslated?.data?.holiday?.styles?.footer_background_align ? settingsTranslated?.data?.holiday?.styles?.footer_background_align : settingsTranslated?.data?.mvk_theme_config?.footer?.footer_background_align
                    settingsTranslated.data.mvk_theme_config.footer.footer_background_value = settingsTranslated?.data?.holiday?.styles?.footer_background_value ? settingsTranslated?.data?.holiday?.styles?.footer_background_value : settingsTranslated?.data?.mvk_theme_config?.footer?.footer_background_value
                }
            }

        }
        return {
            settings: (settingsTranslated) ? { ...settings.data, ...settingsTranslated.data } : settings.data,
            pages: (pagesTranslated) ? { ...pages.data, ...pagesTranslated.data } : pages.data,
            primaryHours: { ...settings.data.hours },
        };
    });
};

const updateSettings = (target, source, fields) => {
    if (!target || !source) return;
    fields.forEach(field => {
        if (typeof field === 'string') {
            if (source && source[field] !== undefined && source[field] !== null) {
                target[field] = source[field];
            }
        } else if (typeof field === 'object') {
            const [targetPath, sourceField] = Object.entries(field)[0];
            const sourceValue = source?.[sourceField];
            const targetKeys = targetPath.split('.');
            let targetRef = target;
            for (let i = 0; i < targetKeys.length - 1; i++) {
                const key = targetKeys[i];
                if (!targetRef[key]) targetRef[key] = {};
                targetRef = targetRef[key];
            }
            const lastKey = targetKeys[targetKeys.length - 1];
            if (sourceValue !== undefined && sourceValue !== null) {
                targetRef[lastKey] = sourceValue;
            }
        }
    });
};

function updateArrayFieldsForChannels(channels, webDetails, fieldMappings) {
    channels.forEach((channelItem) => {
        const channelValue = channelItem.channel.value;
        fieldMappings.forEach((mapping) => {
            const [platform, webDetailField] = Object.entries(mapping)[0];
            if (channelValue === platform && webDetails[webDetailField]) {
                channelItem.url = webDetails[webDetailField];
            }
        });
    });
}

export default Start;
