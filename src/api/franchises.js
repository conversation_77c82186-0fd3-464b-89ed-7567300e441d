import axios from 'axios';

// CONFIG.
import config from 'src/config';

// https://api.mypylot.io/mvk-api/v1/content/www.propertymanagementinc.com/franchises.json
// https://api.preflight.mypylot.io/mvk-api/v1/content/pmi-preflight.mypylot.com/franchises.json
// https://pmileadrotation.imag-dev.com/form/lead/satellitebyfips?api_key=90ac1778-cf95-4167-9fe9-7e1da6575114&fips=42003
// https://pmileadrotation.imag-dev.com/form/lead/satellitebyfips?key=90ac1778-cf95-4167-9fe9-7e1da6575114&fips=48113

export const API1 = axios.create({
    baseURL: `https://${config.base}/mvk-api/v1/content/${config.domain}/`,
    withCredentials: false,
    headers: {
        'Content-Type': 'application/json'
    }
});

export const API2 = axios.create({
    baseURL: `https://pmileadrotation.imag-dev.com/form/lead/`,
    withCredentials: false,
    headers: {
        'Content-Type': 'application/json'
    }
});

// SMARTY STREETS.
export const Franchises = async () => {
    return API1.get(`franchises.json`).then(res => res.data).catch(error => console.log(error));
};

// LEAD ROTATION
export const LeadRotation = async (fips) => {
    return API2.get('satellitebyfips', { params: { api_key:'90ac1778-cf95-4167-9fe9-7e1da6575114', fips:fips } }).then(res => res.data).catch(error => console.log(error));
};