import axios from 'axios';
import Base64 from 'base-64';

// CONFIG.
import config from 'src/config';

// API.
import { API } from 'src/api/axios';

const Start = {};
Start.Request = async (subsite = false, translation) => {
    const requests = [
        {
            name: 'settings',
            endpoint: (config.preview.enabled) ? `${config.preview.decoded}/mvk_custom_rest/v1/mvk_settings&domain=${config.hostname}&preview=ofbF3oDWLpjLXxmnDyLJvfZbaFQPtHoFqdpoo88szJsh7` : `sites/${subsite}/settings.json`,
            attempt: true
        },
        {
            name: 'pages',
            endpoint: (config.preview.enabled) ? `${config.preview.decoded}/${config.preview.decodeShort}/v2/pages/?per_page=1000&mvk_min&domain=${config.hostname}&preview=ofbF3oDWLpjLXxmnDyLJvfZbaFQPtHoFqdpoo88szJsh7` : `sites/${subsite}/pages.json`,
            attempt: true
        },
        {
            name: 'settingsTranslated',
            endpoint: (config.preview.enabled) ? `${config.preview.decoded}/mvk_custom_rest/v1/mvk_settings&lang=${translation}&domain=${config.hostname}&preview=ofbF3oDWLpjLXxmnDyLJvfZbaFQPtHoFqdpoo88szJsh7` : `sites/${subsite}/settings.${translation}.json`,
            attempt: (translation && translation !== 'en')
        },
        {
            name: 'pagesTranslated',
            endpoint: (config.preview.enabled) ? `${config.preview.decoded}/${config.preview.decodeShort}/v2/pages/?per_page=1000&mvk_min&lang=${translation}&domain=${config.hostname}&preview=ofbF3oDWLpjLXxmnDyLJvfZbaFQPtHoFqdpoo88szJsh7` : `sites/${subsite}/pages.${translation}.json`,
            attempt: (translation && translation !== 'en')
        }
    ];

    return axios.all(requests.filter((item) => item.attempt).map((item) => {
        return API.get(item.endpoint).then(resp => {
            return { ...item, ...{ data:resp.data || false }};
        }).catch(error => console.log(error));
    })).then((results) => {
        var settings           = results.find((item) => item?.name == 'settings') || false;
        var pages              = results.find((item) => item?.name == 'pages') || false;
        var settingsTranslated = results.find((item) => item?.attempt && item?.name == 'settingsTranslated') || false;
        var pagesTranslated    = results.find((item) => item?.attempt && item?.name == 'pagesTranslated') || false;
        return {
            settings: (settingsTranslated) ? { ...settings.data, ...settingsTranslated.data } : settings.data,
            pages: (pagesTranslated) ? { ...pages.data, ...pagesTranslated.data } : pages.data
        };
    });
};

export default Start;