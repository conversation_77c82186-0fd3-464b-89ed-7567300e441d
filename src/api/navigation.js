import axios from 'axios';

// CONFIG.
import config from 'src/config';

// API.
import { API } from 'src/api/axios';

const Start = {};
Start.Request = (subsite, translation, main2, utility) => {
    const requests = createRequests(subsite, translation, main2, utility);
    return axios.all(Object.keys(requests).map((key) => {
        return API.get(requests[key]).then(resp => {
            return { key:key, data:resp.data };
        }).catch(error => console.log(error));
    })).then((results) => {
        const main    = results.find((item) => item?.key == 'main') || false;
        const main_2  = results.find((item) => item?.key == 'main_2') || false;
        const utility = results.find((item) => item?.key == 'utility') || false;
        return { main:main.data, main_2:main_2.data, utility:utility.data };
    });
};

export default Start;

function createRequests(subsite, translation, main2, utility) {
    let main;
    let main_2;
    let util;
    let combinedRequests;

    switch (true) {
        case config.preview.enabled:
            main = { 'main': (translation && translation != 'en') ? `${config.preview.decoded}/mvk_custom_rest/v1/mvk_menus/main?per_page=1000&lang=${translation}&preview=ofbF3oDWLpjLXxmnDyLJvfZbaFQPtHoFqdpoo88szJsh7` : `${config.preview.decoded}/mvk_custom_rest/v1/mvk_menus/main?per_page=1000&preview=ofbF3oDWLpjLXxmnDyLJvfZbaFQPtHoFqdpoo88szJsh7` }
            main_2 = main2 ? { 'main_2': (translation && translation != 'en') ? `${config.preview.decoded}/mvk_custom_rest/v1/mvk_menus/main_2?per_page=1000&lang=${translation}&preview=ofbF3oDWLpjLXxmnDyLJvfZbaFQPtHoFqdpoo88szJsh7` : `${config.preview.decoded}/mvk_custom_rest/v1/mvk_menus/main_2?per_page=1000&preview=ofbF3oDWLpjLXxmnDyLJvfZbaFQPtHoFqdpoo88szJsh7` } : false;
            util = utility ? { 'utility': (translation && translation != 'en') ? `${config.preview.decoded}/mvk_custom_rest/v1/mvk_menus/utility?per_page=1000&lang=${translation}&preview=ofbF3oDWLpjLXxmnDyLJvfZbaFQPtHoFqdpoo88szJsh7` : `${config.preview.decoded}/mvk_custom_rest/v1/mvk_menus/utility?per_page=1000&preview=ofbF3oDWLpjLXxmnDyLJvfZbaFQPtHoFqdpoo88szJsh7` } : false;
            combinedRequests = main_2 ? {...main, ...main_2} : main;
            combinedRequests = util ? { ...combinedRequests, ...util } : combinedRequests;
            return combinedRequests;
        case !config.preview.enabled && typeof subsite == 'string':
            main = { 'main': (translation && translation != 'en') ? `sites/${subsite}/nav.main.${translation}.json` : `sites/${subsite}/nav.main.json` }
            main_2 = main2 ? { 'main_2': (translation && translation != 'en') ? `sites/${subsite}/nav.main_2.${translation}.json` : `sites/${subsite}/nav.main_2.json` } : false;
            util = utility ? { 'utility': (translation && translation != 'en') ? `sites/${subsite}/nav.utility.${translation}.json` : `sites/${subsite}/nav.utility.json` } : false;
            combinedRequests = main_2 ? {...main, ...main_2} : main;
            combinedRequests = util ? { ...combinedRequests, ...util } : combinedRequests;
            return combinedRequests;
        default:
            main = { 'main': (translation && translation != 'en') ? `nav.main.${translation}.json` : `nav.main.json` }
            main_2 = main2 ? { 'main_2': (translation && translation != 'en') ? `nav.main_2.${translation}.json` : `nav.main_2.json` } : false;
            util = utility ? { 'utility': (translation && translation != 'en') ? `nav.utility.${translation}.json` : `nav.utility.json` } : false;
            combinedRequests = main_2 ? {...main, ...main_2} : main;
            combinedRequests = util ? { ...combinedRequests, ...util } : combinedRequests;
            return combinedRequests;
    };
};
