import axios from 'axios';

// CONFIG.
import config from 'src/config';

export const API = axios.create({
    baseURL: config.preview.enabled ? `https://${config.preview.baseDomain}/` : `https://${config.base}/mvk-api/v1/content/${config.domain}/`,
    withCredentials: false,
    headers: {
        'Content-Type': 'application/json'
    }
});

export const APIGlobal = axios.create({
    baseURL: config.preview.enabled ? `https://${config.preview.baseDomain}/` : `https://${config.base}/mvk-api/v1/content/creativerefresh.preflight.mypylot.com/`,
    withCredentials: false,
    headers: {
        'Content-Type': 'application/json'
    }
});

export const APIjson = axios.create({
    baseURL: `https://${config.base}/mvk-api/v1/content/${config.domain}/`,
    withCredentials: false,
    headers: {
        'Content-Type': 'application/json'
    }
});

export const APIsearch = axios.create({
    baseURL: `https://${config.base}`,
    withCredentials: false,
    headers: {
        'Content-Type': 'application/json'
    }
});

export const APIforms = axios.create({
    baseURL: `https://${config.base}/mvk-api/v1/forms`,
    withCredentials: false,
    headers: {
        // 'Content-Type': 'multipart/form-data'
    }
});