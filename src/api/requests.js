import axios from 'axios';

// CONFIG.
import config from 'src/config';

// API.
import { API, APIGlobal, APIsearch, APIforms } from 'src/api/axios';

// SEARCH.
export const Search = (term, headers) => {
    return APIsearch.get('/mvk-api/v1/search/', { params: { site:config.domain, term: term }, headers: headers }).then(res => res.data).catch(error => console.log(error));
};

// Form Submission
export const FormSubmit = (url, fields, headers) => {
    return APIforms.post(url, fields, { headers: headers }).then(res => res.data).catch(error => console.log(error));
};

export const ContentTypes = (subsite) => {
    var request = (subsite) ? `sites/${subsite}/content_types.json` : `content_types.json`;
    return API.get(request).then(res => res.data).catch(error => console.log(error));
};

export const Stores = (subsite, translation) => {
    if (config.preview.enabled) {
        var request = `/${config.preview.basePath}/stores/?per_page=1000&mvk_min&lang=${translation}&domain=${config.hostname}&preview=ofbF3oDWLpjLXxmnDyLJvfZbaFQPtHoFqdpoo88szJsh7`;
    } else if (translation && translation !== 'en') {
        var request = (subsite) ? `sites/${subsite}/stores.${translation}.json` : `stores.${translation}.json`;
    } else {
        var request = (subsite) ? `sites/${subsite}/stores.json` : `stores.json`;
    }
    return API.get(request).then(res => res.data).catch(error => console.log(error));
};

export const Sales = (subsite, translation) => {
    if (config.preview.enabled) {
        var request = `/${config.preview.basePath}/sales/?per_page=1000&mvk_min&lang=${translation}&domain=${config.hostname}&preview=ofbF3oDWLpjLXxmnDyLJvfZbaFQPtHoFqdpoo88szJsh7`;
    } else if (translation && translation !== 'en') {
        var request = (subsite) ? `sites/${subsite}/sales.${translation}.json` : `sales.${translation}.json`;
    } else {
        var request = (subsite) ? `sites/${subsite}/sales.json` : `sales.json`;
    }
    return API.get(request).then(res => res.data).catch(error => console.log(error));
};

export const GlobalSales = (subsite, translation) => {
    if (config.preview.enabled) {
        var request = `/${config.preview.basePath}/global_sales/?per_page=1000&mvk_min&lang=${translation}&domain=${config.hostname}&preview=ofbF3oDWLpjLXxmnDyLJvfZbaFQPtHoFqdpoo88szJsh7`;
    } else if (translation && translation !== 'en') {
        var request = (subsite) ? `sites/${subsite}/global_sales.${translation}.json` : `global_sales.${translation}.json`;
    } else {
        var request = (subsite) ? `sites/${subsite}/global_sales.json` : `global_sales.json`;
    }
    return APIGlobal.get(request).then(res => res.data).catch(error => console.log(error));
};

export const Posts = (subsite, translation) => {
    if (config.preview.enabled) {
        var request = `/${config.preview.basePath}/posts/?per_page=1000&mvk_min&lang=${translation}&domain=${config.hostname}&preview=ofbF3oDWLpjLXxmnDyLJvfZbaFQPtHoFqdpoo88szJsh7`;
    } else if (translation && translation !== 'en') {
        var request = (subsite) ? `sites/${subsite}/posts.${translation}.json` : `posts.${translation}.json`;
    } else {
        var request = (subsite) ? `sites/${subsite}/posts.json` : `posts.json`;
    }
    return API.get(request).then(res => res.data).catch(error => console.log(error));
};

export const BlogTemplates = (subsite, translation) => {
    if (config.preview.enabled) {
        var request = `/${config.preview.basePath}/mvk_custom_rest/v1/mvk_blog_templates/?per_page=1000&mvk_min&lang=${translation}&domain=${config.hostname}&preview=ofbF3oDWLpjLXxmnDyLJvfZbaFQPtHoFqdpoo88szJsh7`;
    } else if (translation && translation !== 'en') {
        var request = (subsite) ? `sites/${subsite}/custom_blog_templates.${translation}.json` : `custom_blog_templates.${translation}.json`;
    } else {
        var request = (subsite) ? `sites/${subsite}/custom_blog_templates.json` : `custom_blog_templates.json`;
    }
    return API.get(request).then(res => res.data).catch(error => console.log(error));
};

export const CustomTemplates = (subsite, translation) => {
    if (config.preview.enabled) {
        var request = `/${config.preview.basePath}/mvk_custom_rest/v1/mvk_templates?per_page=1000&mvk_min&lang=${translation}&domain=${config.hostname}&preview=ofbF3oDWLpjLXxmnDyLJvfZbaFQPtHoFqdpoo88szJsh7`;
    } else if (translation && translation !== 'en') {
        var request = (subsite) ? `sites/${subsite}/custom_page_templates.${translation}.json` : `custom_page_templates.${translation}.json`;
    } else {
        var request = (subsite) ? `sites/${subsite}/custom_page_templates.json` : `custom_page_templates.json`;
    }
    return API.get(request).then(res => res.data).catch(error => console.log(error));
};

export const Services = (subsite, translation) => {
    if (config.preview.enabled) {
        var request = `/${config.preview.basePath}/services/?per_page=1000&mvk_min&lang=${translation}&domain=${config.hostname}&preview=ofbF3oDWLpjLXxmnDyLJvfZbaFQPtHoFqdpoo88szJsh7`;
    } else if (translation && translation !== 'en') {
        var request = (subsite) ? `sites/${subsite}/services.${translation}.json` : `services.${translation}.json`;
    } else {
        var request = (subsite) ? `sites/${subsite}/services.json` : `services.json`;
    }
    return API.get(request).then(res => res.data).catch(error => console.log(error));
};

export const ServiceTemplates = (subsite, translation) => {
    if (config.preview.enabled) {
        var request = `/${config.preview.basePath}/mvk_custom_rest/v1/mvk_service_templates/?per_page=1000&mvk_min&lang=${translation}&domain=${config.hostname}&preview=ofbF3oDWLpjLXxmnDyLJvfZbaFQPtHoFqdpoo88szJsh7`;
    } else if (translation && translation !== 'en') {
        var request = (subsite) ? `sites/${subsite}/custom_service_templates.${translation}.json` : `custom_service_templates.${translation}.json`;
    } else {
        var request = (subsite) ? `sites/${subsite}/custom_service_templates.json` : `custom_service_templates.json`;
    }
    return API.get(request).then(res => res.data).catch(error => console.log(error));
};

export const Events = (subsite, translation) => {
    if (config.preview.enabled) {
        var request = `/${config.preview.basePath}/events/?per_page=1000&mvk_min&lang=${translation}&domain=${config.hostname}&preview=ofbF3oDWLpjLXxmnDyLJvfZbaFQPtHoFqdpoo88szJsh7`;
    } else if (translation && translation !== 'en') {
        var request = (subsite) ? `sites/${subsite}/mec-events.${translation}.json` : `mec-events.${translation}.json`;
    } else {
        var request = (subsite) ? `sites/${subsite}/mec-events.json` : `mec-events.json`;
    }
    return API.get(request).then(res => res.data).catch(error => console.log(error));
};

export const Jobs = (subsite, translation) => {
    if (config.preview.enabled) {
        var request = `/${config.preview.basePath}/jobs/?per_page=1000&mvk_min&lang=${translation}&domain=${config.hostname}&preview=ofbF3oDWLpjLXxmnDyLJvfZbaFQPtHoFqdpoo88szJsh7`;
    } else if (translation && translation !== 'en') {
        var request = (subsite) ? `sites/${subsite}/jobs.${translation}.json` : `jobs.${translation}.json`;
    } else {
        var request = (subsite) ? `sites/${subsite}/jobs.json` : `jobs.json`;
    }
    return API.get(request).then(res => res.data).catch(error => console.log(error));
};

export const Spaces = (subsite, translation) => {
    if (config.preview.enabled) {
        var request = `/${config.preview.basePath}/spaces/?per_page=1000&mvk_min&lang=${translation}&domain=${config.hostname}&preview=ofbF3oDWLpjLXxmnDyLJvfZbaFQPtHoFqdpoo88szJsh7`;
    } else if (translation && translation !== 'en') {
        var request = (subsite) ? `sites/${subsite}/spaces.${translation}.json` : `spaces.${translation}.json`;
    } else {
        var request = (subsite) ? `sites/${subsite}/spaces.json` : `spaces.json`;
    }
    return API.get(request).then(res => res.data).catch(error => console.log(error));
};

export const Team = (subsite, translation) => {
    if (config.preview.enabled) {
        var request = `/${config.preview.basePath}/team/?per_page=1000&mvk_min&lang=${translation}&domain=${config.hostname}&preview=ofbF3oDWLpjLXxmnDyLJvfZbaFQPtHoFqdpoo88szJsh7`;
    } else if (translation && translation !== 'en') {
        var request = (subsite) ? `sites/${subsite}/team.${translation}.json` : `team.${translation}.json`;
    } else {
        var request = (subsite) ? `sites/${subsite}/team.json` : `team.json`;
    }
    return API.get(request).then(res => res.data).catch(error => console.log(error));
};

export const Testimonials = (subsite, translation) => {
    if (config.preview.enabled) {
        var request = `/${config.preview.basePath}/testimonials/?per_page=1000&mvk_min&lang=${translation}&domain=${config.hostname}&preview=ofbF3oDWLpjLXxmnDyLJvfZbaFQPtHoFqdpoo88szJsh7`;
    } else if (translation && translation !== 'en') {
        var request = (subsite) ? `sites/${subsite}/testimonials.${translation}.json` : `testimonials.${translation}.json`;
    } else {
        var request = (subsite) ? `sites/${subsite}/testimonials.json` : `testimonials.json`;
    }
    return API.get(request).then(res => res.data).catch(error => console.log(error));
};

export const Properties = (subsite, translation) => {
    if (config.preview.enabled) {
        var request = `/${config.preview.basePath}/properties/?per_page=1000&mvk_min&lang=${translation}&domain=${config.hostname}`;
    } else if (translation && translation !== 'en') {
        var request = (subsite) ? `sites/${subsite}/properties.${translation}.json` : `properties.${translation}.json`;
    } else {
        var request = (subsite) ? `sites/${subsite}/properties.json` : `properties.json`;
    }
    return API.get(request).then(res => res.data).catch(error => console.log(error));
};

export const Floorplans = (subsite, translation) => {
    if (config.preview.enabled) {
        var request = `/${config.preview.basePath}/floorplans/?per_page=1000&mvk_min&lang=${translation}&domain=${config.hostname}`;
    } else if (translation && translation !== 'en') {
        var request = (subsite) ? `sites/${subsite}/floorplans.${translation}.json` : `floorplans.${translation}.json`;
    } else {
        var request = (subsite) ? `sites/${subsite}/floorplans.json` : `floorplans.json`;
    }
    return API.get(request).then(res => res.data).catch(error => console.log(error));
};


export const Aircraft = (subsite, translation) => {
    if (config.preview.enabled) {
        var request = `/${config.preview.basePath}/aircraft/?per_page=1000&mvk_min&lang=${translation}&domain=${config.hostname}`;
    } else if (translation && translation !== 'en') {
        var request = (subsite) ? `sites/${subsite}/aircraft.${translation}.json` : `aircraft.${translation}.json`;
    } else {
        var request = (subsite) ? `sites/${subsite}/aircraft.json` : `aircraft.json`;
    }
    return API.get(request).then(res => res.data).catch(error => console.log(error));
};

export const MarketReports = (subsite, translation) => {
    if (config.preview.enabled) {
        var request = `/${config.preview.basePath}/market_reports/?per_page=1000&mvk_min&lang=${translation}&domain=${config.hostname}`;
    } else if (translation && translation !== 'en') {
        var request = (subsite) ? `sites/${subsite}/market_reports.${translation}.json` : `market_reports.${translation}.json`;
    } else {
        var request = (subsite) ? `sites/${subsite}/market_reports.json` : `market_reports.json`;
    }
    return API.get(request).then(res => res.data).catch(error => console.log(error));
};

export const Locations = (subsite, translation) => {
    if (config.preview.enabled) {
        var request = `/${config.preview.basePath}/locations/?per_page=1000&mvk_min&lang=${translation}&domain=${config.hostname}`;
    } else if (translation && translation !== 'en') {
        var request = (subsite) ? `sites/${subsite}/locations.${translation}.json` : `locations.${translation}.json`;
    } else {
        var request = (subsite) ? `sites/${subsite}/locations.json` : `locations.json`;
    }
    return API.get(request).then(res => res.data).catch(error => console.log(error));
};

export const PageTemplates = (subsite, translation) => {
    if (config.preview.enabled) {
        var request = `/${config.preview.basePath}/page_templates/?per_page=1000&mvk_min&lang=${translation}&domain=${config.hostname}`;
    } else if (translation && translation !== 'en') {
        var request = (subsite) ? `sites/${subsite}/page_templates.${translation}.json` : `page_templates.${translation}.json`;
    } else {
        var request = (subsite) ? `sites/${subsite}/page_templates.json` : `page_templates.json`;
    }
    return API.get(request).then(res => res.data).catch(error => console.log(error));
};
export function getSecrets() {
    // make sure cookie exists and if not fetch new cookie
    // save cookie into www.mypylot.com cookie name regardless if it's client provided or fallback
    
    // request for client provided secret
    axios.get(`https://me87wwn89f.execute-api.us-east-1.amazonaws.com/prod/secretsmanager-prod-pylot-gak?site=${config?.domain}`, {
    })
    .then(function (response) {

        if (response?.status === 204) {
            // request for the fallback secret if the client key is not found
            axios.get(`https://me87wwn89f.execute-api.us-east-1.amazonaws.com/prod/secretsmanager-prod-pylot-gak?site=www.mypylot.com`, {
            })
            .then(function (response) {
                //max age of cookie is set to 1 month (in seconds)
                document.cookie = `www.mypylot.com=${response?.data}; max-age=2592000; Secure`;
            })
            .catch(function (error) {
                console.log('error', error);
                console.log('fallback secret not found');
            });
        } else {
            //max age of cookie is set to 1 month (in seconds)
            document.cookie = `www.mypylot.com=${response?.data}; max-age=2592000; Secure`;
        }
    })
    .catch(function (error) {
        console.log('error', error);
        console.log('client secret not found');

        // request for the fallback secret if there's an error with the client key
        axios.get(`https://me87wwn89f.execute-api.us-east-1.amazonaws.com/prod/secretsmanager-prod-pylot-gak?site=www.mypylot.com`, {
        })
        .then(function (response) {
            //max age of cookie is set to 1 month (in seconds)
            document.cookie = `www.mypylot.com=${response?.data}; max-age=2592000; Secure`;
        })
        .catch(function (error) {
            console.log('error', error);
            console.log('fallback secret not found');
        });
    });
};
