import { useEffect, useState, useRef } from "react"

export const elementOnScreen = (options) => {
    const containerRef = useRef(null)
    const [isVisible, setisVisible] = useState(false)

    const callbackFunction = (entries) => {
        const [entry] = entries
        setisVisible(entry.isIntersecting)
    }

    useEffect(() => {
        const observer = new IntersectionObserver(callbackFunction, options)
        if (containerRef.current) observer.observe(containerRef.current)
       
        return () => {
            if(containerRef.current) observer.unobserve(containerRef.current)
        }
    }, [containerRef, isVisible])

    return [containerRef, isVisible]
}