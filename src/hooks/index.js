import { useEffect, useContext, useState, useMemo } from 'react';
import { useNavigate, useLocation } from 'react-router';
import { PrincipalContext } from "src/context";
import { TranslationContext } from '../context';

export const addScript = (url) => {
    useEffect(() => {
        var list = document.getElementsByTagName('script');
        var i = list.length, flag = false;
        while (i--) {
            if (list[i].src.includes(url)) {
                flag = true;
                break;
            }
        }
        if (!flag) {
            var script = document.createElement('script');
            script.src = url;
            script.async = true;
            document?.body?.appendChild(script);
        }

        // return () => {
        //     document?.body?.removeChild(document?.body?.script);
        // }
    }, [url]);
};

export const useDynamicScript = (url) => {
    useEffect(() => {
        if (!url) {
            return;
        }
        let scriptElement = null; // Store the script element here

        const addScript = () => {
            const list = document.getElementsByTagName("script");
            let i = list.length;
            let flag = false;
            while (i--) {
                if (list[i].src.includes(url)) {
                    flag = true;
                    scriptElement = list[i];
                    break;
                }
            }
            if (!flag) {
                scriptElement = document.createElement("script");
                scriptElement.src = url;
                scriptElement.async = true;
                document.body.appendChild(scriptElement);
            }
        };

        const removeScript = () => {
            if (scriptElement) {
                try {
                    document.body.removeChild(scriptElement);
                } catch (error) {
                    console.error(error);
                }
                scriptElement = null;
            } else {
                const list = document.getElementsByTagName("script");
                let i = list.length;
                while (i--) {
                    if (list[i].src.includes(url)) {
                        try {
                            document.body.removeChild(list[i]);
                        } catch (error) {
                            console.error(error);
                        }
                        break;
                    }
                }
            }
        };

        addScript();

        return () => {
            removeScript();
        };
    }, [url]);
};

export const addScriptDefer = (url) => {
    var list = document.getElementsByTagName('script');
    const head = document.querySelector("head");
    var i = list.length, flag = false;
    while (i--) {
        if (list[i].src.includes(url)) {
            flag = true;
            break;
        }
    }
    if (!flag) {
        var script = document.createElement('script');
        script.src = url;
        script.defer = "true";
        head?.appendChild(script);
    }
};

export const addStylesheet = (url) => {
    useEffect(() => {
        var list = document.getElementsByTagName('link');
        var i = list.length, flag = false;
        while (i--) {
            if (list[i].href === url) {
                flag = true;
                break;
            }
        }

        if (!flag) {
            var stylesheet = document.createElement('link');
            stylesheet.href = url;
            stylesheet.rel = 'stylesheet';
            document?.head?.appendChild(stylesheet);
        }

        // return () => {
        //     document?.head?.removeChild(stylesheet);
        // }
    }, [url]);
}

export const addGTM = (gtmCode, skipGTM) => {
    useEffect(() => {
        if (!skipGTM) {
            var list = document.getElementsByTagName('script');
            var i = list.length, flag = false;
            while (i--) {
                if (list[i].src.includes('googletagmanager')) {
                    flag = true;
                    break;
                }
            }
            if (!flag) {
                // GTM script with defer=true
                var script = document.createElement('script');
                script.innerHTML = `(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
            new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
            j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.defer=true;j.src=
            'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
            })(window,document,'script','dataLayer','${gtmCode}');`;
                document?.head?.appendChild(script);
                // GTM noscript tag if javascript is disabled
                var noscript = document.createElement('noscript');
                noscript.innerText = `<iframe src="https://www.googletagmanager.com/ns.html?id=${gtmCode}&gtm_auth=&gtm_preview=&gtm_cookies_win=x" height="0" width="0" style="display:none;visibility:hidden" id="tag-manager"></iframe>`;
                document.body.insertBefore(noscript, document.body.firstChild);
            }
        }
        // return () => {
        //     document?.head?.removeChild(script);
        // }
    }, [gtmCode, skipGTM]);
};

export const triggerActiveCampaign = (activeCampaignID) => {
    useEffect(() => {
        var list = document.getElementsByTagName('script');
        var i = list.length, flag = false;
        while (i--) {
            if (list[i].src.includes('diffuser-cdn')) {
                flag = true;
                break;
            }
        }
        if (!flag) {
            window.dataLayer = window.dataLayer || [];
            window?.dataLayer.push({
                'event': 'triggerActiveCampaign',
                'activeCampaignID': activeCampaignID
            });
            flag = true;
        }
    }, [activeCampaignID]);
}

export const useTranslation = () => {
    const [translationContext, setTranslationContext] = useContext(TranslationContext);
    const [langSent, setLangSent] = useState(false);
    const [translation, setTranslation] = useState(translationContext);

    const sendLang = (transCode) => {
        window.dataLayer = window.dataLayer || [];
        window.dataLayer?.push({
            'event': 'language',
            'lang': transCode || 'en'
        });
        setLangSent(true);
    }

    useMemo(() => {
        if (translation && translationContext !== translation) {
            setTranslationContext(translation.replace(/\/$/, ''));
            localStorage.setItem("translation", translation.replace(/\/$/, ''));
        }
        if (!langSent) {
            sendLang(translation);
        }

        const translationLocalStorage = localStorage.getItem("translation");
        if (translationLocalStorage !== translationContext) {
            setTranslationContext(translationLocalStorage);
        }
    }, [translation, langSent])

    return [translation, setTranslation];
}

// Hook
export const useOnClickOutside = (ref, handler) => {
    useEffect(
        () => {
            const listener = (event) => {
                // Do nothing if clicking ref's element or descendent elements
                if (!ref.current || ref.current.contains(event.target)) {
                    return;
                }
                handler(event);
            };
            document.addEventListener("mousedown", listener);
            document.addEventListener("touchstart", listener);
            return () => {
                document.removeEventListener("mousedown", listener);
                document.removeEventListener("touchstart", listener);
            };
        },

        // Add ref and handler to effect dependencies
        // can be optimized to reduce re-renders by using "useCallback" when passing handler
        [ref, handler]
    );
}