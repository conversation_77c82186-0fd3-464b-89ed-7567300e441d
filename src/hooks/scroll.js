import { useEffect, useState } from "react";

export const useScrollPosition = () => {
    const [ scrollPosition, setScrollPosition ] = useState(0);

    useEffect(() => {
        const updatePosition = () => {
            setScrollPosition(window.pageYOffset);
            // console.log(window.pageYOffset);
        };
        window.addEventListener("scroll", updatePosition);
        updatePosition();
        return () => window.removeEventListener("scroll", updatePosition);
    }, []);

    return scrollPosition;
}; 

export const useOnScreen = (element) => {
    const [ status, setStatus ] = useState(false);
    const [ amount, setAmount ] = useState(0);

    useEffect(() => {
        const updatePosition = () => {
            var stuff = element.current.getBoundingClientRect();
            setStatus(stuff.top < window.innerHeight);
            setAmount(window.innerHeight - stuff.top);
        };
        window.addEventListener('scroll', updatePosition);
        updatePosition();
        return () => window.removeEventListener("scroll", updatePosition);
    }, [ element ]);

    return { status, amount };
};