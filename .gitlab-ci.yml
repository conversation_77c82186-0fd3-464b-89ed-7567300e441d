stages:
  - build
  - test
  - deploy-preprod
  - pre-golive
  - deploy-prod
  - postdeploy

#variables may be overridden in Gitlab->project->Settings->CI/CD->Variables
variables:
  AWS_DEPLOYMENT_CONFIG: CodeDeployDefault.OneAtATime
  CODEDEPLOY_APP: pylot-frontend


default:
  artifacts:
    expire_in: 1 week

.deployment_template: &codedeploy_config
  tags:
    - saas-linux-small-amd64
  image: 
    name: amazon/aws-cli:latest
    entrypoint: [""]
  before_script:
    - set -euo pipefail
    # create post-deployment script
    # TODO: this would be nice, but we couldn't get the pm2 command to work
    - |
      cat > post-deploy.sh <<EOL
      #!/bin/bash
      /home/<USER>/.nvm/versions/node/v16.17.1/bin/pm2 restart all
      EOL
    # create CodeDeploy appspec file
    - |
      cat > appspec.yml <<EOL
      version: 0.0
      os: linux
      files:
        - source: ./public
          destination: $DESTINATION_DIR/public   
        - source: ./server
          destination: $DESTINATION_DIR/server
      file_exists_behavior: OVERWRITE
      #hooks:
      #  AfterInstall:
      #    - location: post-deploy.sh
      #      timeout: 60
      #      runas: ec2-user
      EOL
    # push to S3
    - S3_KEY=$S3_DEPLOYMENT_DIR/$S3_ARCHIVE_NAME
    - |
      pushResponse=$(\
        aws deploy push \
          --application-name "$CODEDEPLOY_APP" \
          --s3-location s3://$S3_BUCKET/$S3_KEY \
      )
    # handle versioned or non-versioned archives
    - versionPattern="version=([^ ]+)"
    - etagPattern="etag=([^ ]+)"
    - |
      if [[ $pushResponse =~ $versionPattern ]]
      then
        version="${BASH_REMATCH[1]}"
        archiveLocation="bucket=${S3_BUCKET},key=${S3_KEY},bundleType=zip,version=${version}"
      elif [[ $pushResponse =~ $etagPattern ]]
      then
        etag="${BASH_REMATCH[1]}"
        archiveLocation="bucket=${S3_BUCKET},key=${S3_KEY},bundleType=zip,eTag=${etag}"
      else 
        echo "Error deploying revision to AWS. Response of push command: ${pushResponse}"
        exit 1
      fi
    # create deployment from previously pushed archive
    - |
      echo "Deployment archive will be at s3://$S3_BUCKET/$S3_KEY"
      DEPLOYMENT_ID=$(\
        aws deploy create-deployment \
          --application-name "$CODEDEPLOY_APP" \
          --deployment-group-name "$CODEDEPLOY_DEPLOYMENTGROUP" \
          --s3-location "$archiveLocation" \
          --query 'deploymentId' \
          --output text\
      )
  script:
    - echo "Started CodeDeploy deployment - $DEPLOYMENT_ID"
    - aws deploy wait deployment-successful --deployment-id "$DEPLOYMENT_ID"
  artifacts:
    paths:
      - public/
      - server/

#################
# Preflight Pipeline
#################
build-preflight:
  tags:
    #- node
    - saas-linux-small-amd64
  stage: build
  only:
    - preflight
  image: node
  script:
    - echo "Using NPM:"
    - npm --version
    - npm config get prefix
    - npm ci
    - npm run build
  artifacts:
    paths:
      - public/
      - server/

deploy-preflight:
  <<: *codedeploy_config
  stage: deploy-preprod
  only:
      - preflight
  variables:
    GIT_STRATEGY: none
    S3_ARCHIVE_NAME: Pylot_Frontend_preflight.zip
    CODEDEPLOY_DEPLOYMENTGROUP: preflight
    DESTINATION_DIR: /data01/web/preflight.mypylot.com
  

#################
# Approach/Production Pipeline
#################
debug:
  tags:
    - saas-linux-small-amd64
  stage: build
  rules:
    - when: always
  image: node
  script:
    - echo $CI_PIPELINE_SOURCE
    - echo $CI_MERGE_REQUEST_EVENT_TYPE
    - echo $CI_MERGE_REQUEST_TARGET_BRANCH_NAME
    - echo $CI_MERGE_REQUEST_SOURCE_BRANCH_NAME

build-prod-release: # this job runs after an MR from release/* to approach is merged
  tags:
    - saas-linux-small-amd64
  stage: build
  rules:
    - if: |
          ($CI_PIPELINE_SOURCE == "merge_request_event" && $CI_MERGE_REQUEST_EVENT_TYPE == "merged_result")
  image: node
  script:
    - npm --version
    - npm config get prefix
    - npm ci
    - npm run build
    - versionNumber=$(echo $CI_MERGE_REQUEST_SOURCE_BRANCH_NAME | sed -E 's/release\///')
    - sed -i -E 's/\?v=[0-9\.]+/\?v='${versionNumber}'/g' public/index.html   # this changes version number in index.html
    - npm version $versionNumber -m "automated - update package version to %s and bust cache"  # this changes version number in package and package-lock, and commits
    - git push -o ci.skip         # ensure we don't kick off another pipeline
  artifacts:
    paths:
      - public/
      - server/
      - package.json
      - package-lock.json

deploy-approach:
  <<: *codedeploy_config
  stage: deploy-preprod
  rules:
    - if: |
          ($CI_PIPELINE_SOURCE == 'merge_request_event' && $CI_MERGE_REQUEST_EVENT_TYPE == 'merged_result'
          && $CI_MERGE_REQUEST_TARGET_BRANCH_NAME == 'approach'
          && $CI_MERGE_REQUEST_SOURCE_BRANCH_NAME =~ 'release/.+')
  variables:
    GIT_STRATEGY: none
    S3_ARCHIVE_NAME: Pylot_Frontend_approach.zip
    CODEDEPLOY_DEPLOYMENTGROUP: approach
    DESTINATION_DIR: /data01/web/zApproach/mypylot.com

deploy-prod-hold:
  stage: pre-golive
  rules:
    - if: |
          ($CI_PIPELINE_SOURCE == 'merge_request_event' && $CI_MERGE_REQUEST_EVENT_TYPE == 'merged_result'
          && $CI_MERGE_REQUEST_TARGET_BRANCH_NAME == 'approach'
          && $CI_MERGE_REQUEST_SOURCE_BRANCH_NAME =~ 'release/.+')
  when: manual
  allow_failure: false  # by default, manual doesn't block further jobs. this makes it block
  cache: []
  variables:
    GIT_STRATEGY: none
  script:
    - exit 0

deploy-prod:
  <<: *codedeploy_config
  stage: deploy-prod
  rules:
    - if: |
          ($CI_PIPELINE_SOURCE == 'merge_request_event' && $CI_MERGE_REQUEST_EVENT_TYPE == 'merged_result'
          && $CI_MERGE_REQUEST_TARGET_BRANCH_NAME == 'approach'
          && $CI_MERGE_REQUEST_SOURCE_BRANCH_NAME =~ 'release/.+')
  cache: []
  variables:
    GIT_STRATEGY: none
    S3_ARCHIVE_NAME: Pylot_Frontend_prod.zip
    CODEDEPLOY_DEPLOYMENTGROUP: prod
    DESTINATION_DIR: /data01/web/mypylot.com
